// IC封测CIM系统 - 按钮组件样式
// 基于极简主义设计的按钮样式系统

.btn {
  // 基础样式
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  font-family: inherit;
  font-weight: var(--font-weight-medium);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  border-radius: var(--radius-base);
  transition: all var(--transition-fast);
  white-space: nowrap;
  
  &:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
    pointer-events: none;
  }

  // 尺寸变体
  &--small {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-xs);
    height: 28px;
  }

  &--medium {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-sm);
    height: 36px;
  }

  &--large {
    padding: var(--spacing-3) var(--spacing-5);
    font-size: var(--font-size-base);
    height: 44px;
  }

  // 类型变体
  &--primary {
    background-color: var(--color-primary);
    color: var(--color-text-inverse);
    border-color: var(--color-primary);
    
    &:hover:not(:disabled) {
      background-color: var(--color-primary-hover);
      border-color: var(--color-primary-hover);
    }
    
    &:active {
      background-color: var(--color-primary-active);
      border-color: var(--color-primary-active);
    }
  }

  &--secondary {
    background-color: var(--color-btn-secondary-bg);
    color: var(--color-text-primary);
    border-color: var(--color-btn-secondary-border);
    
    &:hover:not(:disabled) {
      background-color: var(--color-btn-secondary-hover);
      border-color: var(--color-primary);
      color: var(--color-primary);
    }
  }

  &--success {
    background-color: var(--color-success);
    color: var(--color-text-inverse);
    border-color: var(--color-success);
    
    &:hover:not(:disabled) {
      background-color: var(--color-success-light);
      border-color: var(--color-success-light);
    }
  }

  &--warning {
    background-color: var(--color-warning);
    color: var(--color-text-inverse);
    border-color: var(--color-warning);
    
    &:hover:not(:disabled) {
      background-color: var(--color-warning-light);
      border-color: var(--color-warning-light);
    }
  }

  &--error {
    background-color: var(--color-error);
    color: var(--color-text-inverse);
    border-color: var(--color-error);
    
    &:hover:not(:disabled) {
      background-color: var(--color-error-light);
      border-color: var(--color-error-light);
    }
  }

  &--text {
    background-color: transparent;
    color: var(--color-text-secondary);
    border-color: transparent;
    
    &:hover:not(:disabled) {
      color: var(--color-primary);
      background-color: var(--color-bg-hover);
    }
  }

  &--ghost {
    background-color: transparent;
    color: var(--color-primary);
    border-color: var(--color-primary);
    
    &:hover:not(:disabled) {
      background-color: var(--color-primary);
      color: var(--color-text-inverse);
    }
  }

  // 形状变体
  &--round {
    border-radius: var(--radius-full);
  }

  &--square {
    border-radius: 0;
  }

  // 特殊状态
  &--loading {
    pointer-events: none;
    
    .btn__icon {
      animation: spin 1s linear infinite;
    }
  }

  &--block {
    display: flex;
    width: 100%;
  }

  // 内部元素
  &__icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  &__text {
    flex: 1;
  }
}

// 按钮组
.btn-group {
  display: inline-flex;
  
  .btn {
    border-radius: 0;
    
    &:first-child {
      border-top-left-radius: var(--radius-base);
      border-bottom-left-radius: var(--radius-base);
    }
    
    &:last-child {
      border-top-right-radius: var(--radius-base);
      border-bottom-right-radius: var(--radius-base);
    }
    
    &:not(:first-child) {
      margin-left: -1px;
    }
    
    &:hover,
    &:focus {
      z-index: 1;
    }
  }
  
  &--vertical {
    flex-direction: column;
    
    .btn {
      &:first-child {
        border-radius: var(--radius-base) var(--radius-base) 0 0;
      }
      
      &:last-child {
        border-radius: 0 0 var(--radius-base) var(--radius-base);
      }
      
      &:not(:first-child) {
        margin-left: 0;
        margin-top: -1px;
      }
    }
  }
}

// 动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}