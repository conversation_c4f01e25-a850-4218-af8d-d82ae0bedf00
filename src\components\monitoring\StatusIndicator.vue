<template>
  <div :class="indicatorClass">
    <div class="status-indicator__icon">
      <el-icon v-if="!customIcon" :class="iconClass">
        <component :is="statusIcon" />
      </el-icon>
      <component
:is="customIcon" v-else
:class="iconClass"
/>
    </div>

    <div class="status-indicator__content">
      <div class="status-indicator__label">
        {{ label }}
      </div>
      <div v-if="value" class="status-indicator__value">
        {{ value }}
      </div>
      <div v-if="description" class="status-indicator__description">
        {{ description }}
      </div>
    </div>

    <div
      v-if="showPulse && (status === 'running' || status === 'active')"
      class="status-indicator__pulse"
    />
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import {
    CircleCheck,
    Warning,
    CircleClose,
    Clock,
    Loading,
    QuestionFilled,
    Refresh,
    Connection,
    Monitor
  } from '@element-plus/icons-vue'

  export type StatusType =
    | 'success'
    | 'running'
    | 'active'
    | 'online'
    | 'healthy'
    | 'connected'
    | 'warning'
    | 'idle'
    | 'maintenance'
    | 'pending'
    | 'error'
    | 'danger'
    | 'critical'
    | 'offline'
    | 'disconnected'
    | 'alarm'
    | 'info'
    | 'unknown'
    | 'loading'

  interface Props {
    /** 状态类型 */
    status: StatusType
    /** 显示标签 */
    label: string
    /** 数值显示 */
    value?: string | number
    /** 描述信息 */
    description?: string
    /** 指示器大小 */
    size?: 'small' | 'medium' | 'large'
    /** 自定义图标 */
    customIcon?: any
    /** 是否显示脉冲动画 */
    showPulse?: boolean
    /** 是否垂直布局 */
    vertical?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    size: 'medium',
    showPulse: true,
    vertical: false
  })

  // 状态图标映射
  const statusIcon = computed(() => {
    switch (props.status) {
      case 'success':
      case 'healthy':
      case 'online':
      case 'connected':
        return CircleCheck
      case 'running':
      case 'active':
        return Monitor
      case 'warning':
      case 'idle':
      case 'maintenance':
      case 'pending':
        return Warning
      case 'error':
      case 'danger':
      case 'critical':
      case 'offline':
      case 'disconnected':
      case 'alarm':
        return CircleClose
      case 'loading':
        return Loading
      default:
        return QuestionFilled
    }
  })

  // 指示器样式类
  const indicatorClass = computed(() => [
    'status-indicator',
    `status-indicator--${props.status}`,
    `status-indicator--${props.size}`,
    {
      'status-indicator--vertical': props.vertical
    }
  ])

  // 图标样式类
  const iconClass = computed(() => [
    'status-icon',
    {
      'status-icon--pulse':
        props.showPulse && (props.status === 'running' || props.status === 'active'),
      'status-icon--loading': props.status === 'loading'
    }
  ])
</script>

<style lang="scss" scoped>
  .status-indicator {
    position: relative;
    display: flex;
    align-items: center;
    padding: var(--spacing-2);
    border-radius: var(--radius-base);
    transition: all 0.3s ease;

    &--vertical {
      flex-direction: column;
      text-align: center;

      .status-indicator__icon {
        margin-bottom: var(--spacing-2);
      }

      .status-indicator__content {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
    }

    &__icon {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      margin-right: var(--spacing-3);

      .status-indicator--vertical & {
        margin-right: 0;
      }
    }

    .status-icon {
      transition: all 0.3s ease;

      &--pulse {
        animation: pulse 2s infinite;
      }

      &--loading {
        animation: spin 1s linear infinite;
      }
    }

    &__content {
      flex: 1;
      min-width: 0;
    }

    &__label {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      line-height: 1.4;
      color: var(--color-text-primary);
    }

    &__value {
      margin-top: var(--spacing-1);
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-semibold);
      color: var(--color-text-primary);
    }

    &__description {
      margin-top: var(--spacing-1);
      font-size: var(--font-size-xs);
      line-height: 1.3;
      color: var(--color-text-secondary);
    }

    &__pulse {
      position: absolute;
      inset: 8px;
      pointer-events: none;
      border-radius: inherit;
      animation: statusPulse 2s infinite;
    }

    // 尺寸变体
    &--small {
      padding: var(--spacing-1);

      .status-icon {
        font-size: 14px;
      }

      .status-indicator__label {
        font-size: var(--font-size-xs);
      }

      .status-indicator__value {
        font-size: var(--font-size-sm);
      }
    }

    &--medium {
      padding: var(--spacing-2);

      .status-icon {
        font-size: 16px;
      }
    }

    &--large {
      padding: var(--spacing-3);

      .status-icon {
        font-size: 20px;
      }

      .status-indicator__label {
        font-size: var(--font-size-base);
      }

      .status-indicator__value {
        font-size: var(--font-size-lg);
      }
    }

    // 状态颜色
    &--success,
    &--healthy,
    &--online,
    &--connected {
      .status-icon {
        color: var(--color-success);
      }

      .status-indicator__pulse {
        background: radial-gradient(circle, var(--color-success-light), transparent);
      }
    }

    &--running,
    &--active {
      .status-icon {
        color: var(--color-primary);
      }

      .status-indicator__pulse {
        background: radial-gradient(circle, var(--color-primary-light), transparent);
      }
    }

    &--warning,
    &--idle,
    &--maintenance,
    &--pending {
      .status-icon {
        color: var(--color-warning);
      }

      .status-indicator__pulse {
        background: radial-gradient(circle, var(--color-warning-light), transparent);
      }
    }

    &--error,
    &--danger,
    &--critical,
    &--offline,
    &--disconnected,
    &--alarm {
      .status-icon {
        color: var(--color-danger);
      }

      .status-indicator__pulse {
        background: radial-gradient(circle, var(--color-danger-light), transparent);
      }
    }

    &--info,
    &--unknown {
      .status-icon {
        color: var(--color-info);
      }
    }

    &--loading {
      .status-icon {
        color: var(--color-primary);
      }
    }
  }

  // 动画
  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }

    50% {
      opacity: 0.8;
      transform: scale(1.1);
    }
  }

  @keyframes statusPulse {
    0% {
      opacity: 0.6;
      transform: scale(0.8);
    }

    50% {
      opacity: 0.2;
      transform: scale(1.2);
    }

    100% {
      opacity: 0;
      transform: scale(1.4);
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }
</style>
