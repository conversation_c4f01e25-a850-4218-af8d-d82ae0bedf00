# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository. 任何时候请用中文与我沟通！优先使用powershell命令行！

## 常用开发命令

### 前端开发命令
```powershell
# 安装依赖
npm install

# 开发环境启动 (端口3000，支持热重载)
npm run dev

# 类型检查
npm run type-check

# 代码检查和自动修复
npm run lint

# 样式检查和自动修复
npm run lint:style

# 代码格式化
npm run format

# 构建生产版本
npm run build

# 构建开发版本（保留源映射）
npm run build:dev

# 构建预发布版本
npm run build:staging

# 预览构建结果
npm run preview

# 单元测试
npm run test:unit

# E2E测试开发模式
npm run test:e2e:dev

# E2E测试运行
npm run test:e2e
```

### 数据库相关命令（如需要）
```powershell
# 创建MySQL数据库
mysql -u root -p -e "CREATE DATABASE ic_cim_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 部署数据库架构
mysql -u root -p ic_cim_system < ic-packaging-database-design/01-基础数据管理模块/数据库表结构.sql
```

### 项目维护命令
```powershell
# 检查依赖更新
npm outdated

# 更新依赖
npm update

# 清理node_modules和重新安装
Remove-Item -Recurse -Force node_modules
Remove-Item package-lock.json
npm install
```

## 项目架构

这是一个 **IC封装测试工厂CIM系统前端项目**，基于Vue 3 + TypeScript构建，采用现代前端架构和极简主义设计理念：

### 前端架构特点
- **Vue 3 Composition API**: 使用最新的组合式API模式
- **TypeScript严格模式**: 完整的类型安全和智能提示
- **组件化设计**: 严格的组件层次结构和复用原则
- **极简主义设计系统**: 基于CSS变量的双主题系统
- **模块化架构**: 功能模块化，便于维护和扩展

### 技术栈架构

**核心技术栈:**
- **框架**: Vue 3.4+ (Composition API)
- **语言**: TypeScript 5.3+ (严格模式)
- **构建工具**: Vite 5.1+ (快速热重载)
- **状态管理**: Pinia (轻量级)
- **路由**: Vue Router 4
- **UI组件**: Element Plus 2.5+ + Vant 4
- **样式**: SCSS + CSS变量系统

**开发工具链:**
- **代码规范**: ESLint + Prettier + Stylelint
- **类型检查**: vue-tsc
- **测试**: Vitest (单元测试) + Cypress (E2E测试)
- **自动导入**: unplugin-auto-import + unplugin-vue-components

**设计系统:**
- **主题系统**: 双主题(明/暗模式)支持，基于CSS变量
- **响应式**: 移动优先设计，支持多设备适配
- **图标系统**: 内嵌SVG图标 + Element Plus Icons
- **图表**: ECharts 5 + D3.js (晶圆图等特殊图表)
- **极简主义**: 专业的IC封测工厂界面设计
- **专业色彩**: IC封测专业色彩系统 (晶圆色、die状态色等)

### 项目结构

```
src/
├── components/          # 组件库
│   ├── base/           # 基础组件 (CButton、CCard、CTable等)
│   ├── business/       # 业务组件 (DataTable、StatusBadge等)
│   ├── equipment/      # 设备管理组件
│   ├── manufacturing/  # 制造执行组件 (WaferMapViewer等)
│   ├── monitoring/     # 监控相关组件
│   ├── quality/        # 质量管理组件 (SPCChart等)
│   ├── orders/         # 订单相关组件
│   └── layout/         # 布局组件 (ThemeToggle等)
├── views/              # 页面视图
│   ├── manufacturing/  # 制造执行页面 (CP测试、封装、FT测试)
│   ├── equipment/      # 设备管理页面
│   ├── quality/        # 质量管理页面
│   ├── monitoring/     # 监控中心页面
│   └── backup/         # 备份页面
├── composables/        # 组合式函数 (useTheme、useEquipment等)
├── stores/             # Pinia状态管理 (auth、order、equipment等)
├── utils/              # 工具函数
│   ├── mockData/       # 模拟数据
│   └── spcCalculations.ts # SPC计算工具
├── types/              # TypeScript类型定义
│   ├── manufacturing.ts # 制造相关类型
│   ├── equipment.ts    # 设备相关类型
│   ├── order.ts        # 订单相关类型
│   └── quality.ts      # 质量相关类型
├── api/                # API接口封装
│   ├── manufacturing/  # 制造相关API
│   └── config.ts       # API配置
├── assets/             # 静态资源
│   └── styles/         # 样式文件 (双主题CSS变量系统)
├── styles/             # 额外样式文件
│   ├── themes/         # 主题样式
│   ├── components/     # 组件样式
│   ├── pages/          # 页面样式
│   └── professional/   # 专业功能样式 (晶圆图、设备等)
└── router/             # 路由配置 (分层路由结构)
```

### 代码组织原则
- **单一职责**: 每个组件和函数只负责一个功能
- **组件分层**: 基础组件 → 业务组件 → 页面视图
- **类型优先**: 严格的TypeScript类型定义
- **样式隔离**: Scoped样式 + BEM命名规范
- **性能优化**: 懒加载、代码分割、预构建优化

## 当前项目状态

### 已完成的核心功能模块
- ✅ **基础组件库**: CButton、CCard、CTable、CModal等基础组件
- ✅ **双主题系统**: 完整的明/暗主题切换，基于CSS变量
- ✅ **路由架构**: 分层路由结构，支持制造/设备/质量/监控模块
- ✅ **状态管理**: Pinia store架构，包含认证、订单、设备等模块
- ✅ **制造执行**: CP测试、封装工艺、最终测试页面框架
- ✅ **设备管理**: 设备状态监控、SECS/GEM管理基础
- ✅ **质量管理**: SPC控制图、质量分析报告基础
- ✅ **监控中心**: 实时监控大屏框架
- ✅ **API框架**: 模拟API和真实API接口准备
- ✅ **类型系统**: 完整的TypeScript类型定义

### 开发优先级
1. **高优先级**: 完善制造执行模块的数据流和业务逻辑
2. **中优先级**: 设备管理的SECS/GEM集成
3. **低优先级**: 高级AI功能和自动化特性

## 开发规范与最佳实践

### Vue组件开发规范

1. **组件命名**: 使用PascalCase，业务组件必须多词命名
2. **组件结构**: 统一使用`<template>` → `<script setup>` → `<style scoped>`顺序
3. **Props定义**: 使用TypeScript接口定义Props类型
4. **事件命名**: 使用kebab-case，如`@update:value`
5. **组合式函数**: 以`use`开头，返回响应式数据和方法

```typescript
// 示例组件结构
<template>
  <div class="component-name">
    <!-- 内容 -->
  </div>
</template>

<script setup lang="ts">
interface Props {
  value: string
  disabled?: boolean
}

interface Emits {
  'update:value': [value: string]
  'change': [value: string]
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
})

const emit = defineEmits<Emits>()
</script>

<style lang="scss" scoped>
.component-name {
  // 样式代码
}
</style>
```

### 样式开发规范

1. **BEM命名**: 使用Block__Element--Modifier模式
2. **CSS变量**: 优先使用主题变量，确保主题切换生效
3. **响应式**: 使用移动优先的响应式设计
4. **性能**: 避免深度嵌套选择器，控制在3层以内

```scss
// 正确的样式组织
.order-card {
  background-color: var(--color-bg-primary);
  border-radius: var(--radius-base);
  
  &__header {
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--color-border-light);
  }
  
  &__content {
    padding: var(--spacing-4);
    
    &--loading {
      opacity: 0.6;
    }
  }
}
```

### API集成规范

1. **接口封装**: 统一使用axios，封装在api/modules/下
2. **错误处理**: 统一错误处理和用户提示
3. **类型定义**: 为所有API响应定义TypeScript类型
4. **Loading状态**: 使用组合式函数管理加载状态
5. **模拟数据**: 使用MockApiHelper进行开发阶段的API模拟
6. **响应缓存**: 合理使用响应缓存提高性能

```typescript
// API封装示例
export interface OrderAPI {
  getOrders: (params: OrderQueryParams) => Promise<OrderListResponse>
  createOrder: (data: CreateOrderData) => Promise<Order>
}

// 组合式函数示例
export function useOrders() {
  const loading = ref(false)
  const orders = ref<Order[]>([])
  
  const fetchOrders = async (params: OrderQueryParams) => {
    loading.value = true
    try {
      const response = await orderApi.getOrders(params)
      orders.value = response.data
    } catch (error) {
      // 统一错误处理
      console.error('获取订单失败:', error)
    } finally {
      loading.value = false
    }
  }
  
  return {
    loading: readonly(loading),
    orders: readonly(orders),
    fetchOrders
  }
}
```

## Project Overview

This is a specialized CIM (Computer Integrated Manufacturing) system project for **IC Assembly and Testing (OSAT - Outsourced Semiconductor Assembly and Test)** operations. The system manages the complete IC packaging and testing manufacturing process from wafer probe testing (CP) to final test (FT), covering the entire back-end semiconductor manufacturing value chain.

### Strategic Goals (战略目标)
- **成本可控的智能化升级**: 三年总投资2300-3800万，实现ROI 1.9-2.8年回收
- **世界先进的自动化水平**: 最终达到85%以上自动化率，接近黑灯工厂水平
- **完全满足IATF16949认证**: 严格按照汽车行业质量管理体系要求设计
- **分阶段风险可控实施**: 三阶段渐进式升级，每阶段验证ROI后进入下一阶段

### Industry Background
**IC封装测试行业特点**:
- **后端制程**: 专注于半导体制造后端工序（封装、测试、成品）
- **高精度工艺**: 涉及微米级精度的封装工艺控制
- **严格质量标准**: 遵循JEDEC、IPC、ISO/TS 16949等行业标准
- **多样化封装形式**: 支持QFP、BGA、CSP、FC等多种封装类型
- **大规模测试**: 支持每日百万级芯片测试产能
- **完整追溯**: 从晶圆到成品的完整质量追溯链

## System Architecture

The system follows a **three-phase evolution architecture** with cost-controlled implementation:

### Phase-Based Architecture Evolution (分阶段架构演进)
- **Phase 1 (6 months)**: 基础数字化 - 传统MES + 基础自动化
- **Phase 2 (6-12 months)**: 智能化升级 - AI预测 + 局部自动化
- **Phase 3 (12-24 months)**: 高度自动化 - 接近黑灯工厂水平

### Overall Architecture Pattern
Microservices architecture with front-end and back-end separation, evolving from monolithic to distributed intelligent systems:

### Technology Stack Evolution (技术栈演进路线)

#### Phase 1: Foundation Technology Stack (第一阶段技术栈) - Mature & Stable
**Philosophy: 使用成熟稳定技术，确保系统可靠性**

**Frontend Technology Stack**
- **Core Framework**: Vue.js 3 + TypeScript (成熟稳定)
- **UI Component Library**: Element Plus (丰富组件库)
- **State Management**: Pinia (简单易用)
- **Routing**: Vue Router (标准路由)
- **Charts**: ECharts (生产报表), D3.js (wafer maps)
- **Build Tool**: Vite (快速构建)
- **Mobile Adaptation**: Responsive design + PWA
- **Real-time Communication**: WebSocket

**Backend Technology Stack**
- **Core Framework**: Spring Boot (简化料架构, 非 Spring Cloud)
- **Programming Language**: Java 17 (主要服务)
- **API Style**: RESTful API (标准REST)
- **Data Access**: Spring Data JPA + MyBatis
- **Message Queue**: RabbitMQ (中等规模够用)
- **Cache**: Redis (缓存 + 会话)
- **Database**: MySQL (业务数据), Redis (缓存)

**Deployment & Monitoring**
- **Containerization**: Docker + Docker Compose (简单部署)
- **Monitoring**: Prometheus + Grafana (开源方案)
- **Logging**: ELK Stack (Elasticsearch + Logstash + Kibana)

#### Phase 2: Intelligence Enhancement (第二阶段智能化升级)
**Philosophy: 在稳定基础上添加AI能力**

**Added Technologies:**
- **Big Data Processing**: Hadoop + Spark (开源大数据处理)
- **Machine Learning**: Python + TensorFlow + Jupyter (开源AI栈)
- **Real-time Stream**: Apache Kafka + Flink (实时数据流)
- **Data Warehouse**: Apache Hive + Apache Superset (数据仓库)
- **Time Series DB**: InfluxDB (设备数据)
- **Orchestration**: Kubernetes (云原生升级)

**Enhanced Backend:**
- **Microservices**: Spring Cloud Alibaba (微服务化)
- **Service Governance**: Nacos (service discovery), Sentinel (traffic control)
- **API Gateway**: Spring Cloud Gateway
- **Workflow Engine**: Flowable (工作流管理)

#### Phase 3: Advanced Intelligence (第三阶段高级智能)
**Philosophy: 部署高级AI技术，实现真正智能化**

**Advanced AI Technologies:**
- **Deep Learning**: PyTorch + CUDA (深度学习 + GPU加速)
- **Knowledge Graph**: Neo4j (知识图谱引擎)
- **Computer Vision**: OpenCV + YOLO (视觉检测)
- **Edge Computing**: NVIDIA Jetson + TensorRT (边缘计算)
- **Model Serving**: TensorFlow Serving + MLflow (模型部署)

**Advanced Data Platform:**
- **Data Lake**: Apache Iceberg + MinIO (数据湖平台)
- **Feature Store**: Feast (特征存储)
- **ML Pipeline**: Kubeflow (机器学习流水线)
- **Real-time Analytics**: Apache Druid (实时分析)

### Database Architecture Evolution (数据库架构演进)

**Phase 1:**
- **Relational Database**: MySQL 8.0 (业务数据)
- **Cache Database**: Redis 6+ (会话 + 高频数据)

**Phase 2:**
- **+ Time Series Database**: InfluxDB (设备监控数据)
- **+ Search Engine**: Elasticsearch (日志 + 全文搜索)
- **+ Data Warehouse**: Apache Hive (历史数据分析)

**Phase 3:**
- **+ Graph Database**: Neo4j (知识图谱)
- **+ Vector Database**: Milvus (向量搜索)
- **+ Distributed Database**: TiDB (分布式事务)

## Core Functional Modules (三阶段实施的模块设计)

### Phase 1: Foundation Digitalization (基础数字化) - 6 Months
**Investment: 500-800万 | Target: Traditional MES + Basic Automation**

#### 1. Order & Production Planning Management (订单和生产计划管理)
- Customer order lifecycle management with packaging type specifications
- Master production scheduling optimized for OSAT operations  
- Work order management for CP/Assembly/FT processes
- Capacity planning based on test and assembly equipment availability

#### 2. Material & Inventory Management (物料与库存管理)
- Semiconductor-specific warehouse management (ESD-safe, temperature-controlled)
- Advanced material tracking with Die Bank management
- Lead frame, substrate, and packaging material control
- Chemical and consumable inventory for assembly processes

#### 3. Manufacturing Execution Management (制造执行管理) - Basic MES
- **Wafer Probe Testing (CP)**: 普通模式的晴圆电测管控
  - Basic probe card management and PM tracking
  - Standard wafer map generation and yield analysis
  - Manual electrical parameter testing and binning
  - Basic SECS/GEM equipment integration
- **Assembly Process Control**: 基础封装工艺管控
  - Manual die attach process control
  - Basic wire bonding parameter monitoring
  - Standard molding process management
  - Manual trim & form inspection
- **Final Test (FT)**: 基础成品测试管控
  - Single-site testing capability
  - Basic burn-in process management
  - Manual device characterization
  - Standard handler operation

#### 4. Quality Control Management (质量管理) - IATF16949 Foundation
- **Incoming Quality Control**: Basic wafer acceptance testing
- **In-Process Quality**: Manual SPC monitoring
- **Basic Traceability**: Lot-level traceability per SEMI standards
- **Document Management**: IATF16949 basic document control

#### 5. Equipment Management (设备管理) - Basic Integration
- **Test Equipment**: Basic ATE management
- **Assembly Equipment**: Standard equipment monitoring
- **Basic SECS/GEM**: Standard equipment communication
- **Manual PM Scheduling**: Traditional preventive maintenance

#### 6. Personnel & Performance Management (人员与绩效管理)
- Employee information and basic skill tracking
- Shift scheduling and attendance tracking
- Basic performance reporting

#### 7. Basic Monitoring Center (基础监控中心)
- **Basic Production Dashboard**: Essential KPIs and status
- **Equipment Status Monitoring**: Real-time equipment health
- **Environmental Monitoring**: Basic cleanroom conditions

#### 8. Reports & Analytics (报表和分析)
- Standard production reports (daily/weekly/monthly)
- Basic quality analysis reports
- Equipment performance reports
- Manual report generation

### Phase 2: Intelligence Upgrade (智能化升级) - 6-12 Months 
**Investment: +800-1200万 | Target: AI Prediction + Partial Automation**

#### Enhanced Modules with AI Capabilities:

#### 1. **Smart Production Planning** (智能生产计划)
- AI-driven demand forecasting
- Intelligent production scheduling optimization
- Real-time capacity adjustment
- Predictive bottleneck analysis

#### 2. **Intelligent Material Management** (智能物料管理)
- AI demand prediction for materials
- Partial automated warehouse (2-3 zones)
- Smart supplier integration
- Predictive inventory optimization

#### 3. **Advanced Manufacturing Execution** (高级制造执行)
- **AI-Enhanced CP Testing**: 智能化晴圆电测
  - Predictive probe card maintenance
  - AI-driven wafer map analysis
  - Intelligent parameter optimization
  - Advanced yield prediction
- **Smart Assembly Control**: 智能封装控制
  - AI-guided die attach optimization
  - Intelligent wire bonding parameter control
  - Predictive molding quality control
  - Automated dimensional inspection
- **Intelligent Final Test**: 智能成品测试
  - Multi-site parallel testing optimization
  - Predictive burn-in scheduling
  - AI device characterization
  - Smart handler coordination

#### 4. **Predictive Quality System** (预测性质量系统)
- **AI-Powered SPC**: Automated statistical process control
- **Predictive Quality Control**: Defect prediction and prevention
- **Intelligent Traceability**: Enhanced traceability with AI analysis
- **Automated IATF16949 Compliance**: Automated compliance checking

#### 5. **Predictive Equipment Management** (预测性设备管理)
- **Predictive Maintenance**: AI-driven maintenance scheduling
- **Equipment Health Monitoring**: Real-time equipment condition analysis
- **Smart Recipe Management**: AI-optimized recipe deployment
- **Intelligent Equipment Coordination**: Basic equipment collaboration

### Phase 3: High Automation (高度自动化) - 12-24 Months
**Investment: +1000-1800万 | Target: Near Lights-Out Factory**

#### 1. **AMC - Autonomous Manufacturing Center** (智能制造决策中心)
- **AI Production Orchestrator**: Reinforcement learning-based scheduling
- **Autonomous Work Order Engine**: Self-managing work orders
- **Smart Recipe Management**: AI-driven process optimization
- **Real-time Decision Engine**: Millisecond-level production decisions

#### 2. **AQS - Autonomous Quality System** (全自动质量管控系统)
- **AI-Powered FMEA Engine**: Intelligent risk identification
- **Predictive Quality Controller**: Deep learning defect prediction
- **Autonomous SPC System**: Self-managing statistical control
- **IATF16949 Compliance Engine**: Automated compliance management

#### 3. **UEO - Ultra Equipment Orchestration** (超级设备协同平台)
- **Equipment Digital Twin Engine**: Complete digital mirrors
- **Autonomous Maintenance System**: Self-healing equipment
- **Smart Equipment Orchestration**: Equipment swarm intelligence
- **Advanced SECS/GEM+**: Deep equipment collaboration

#### 4. **ZTL - Zero-Touch Logistics** (零接触物料管理)
- **AI Demand Forecasting**: Intelligent demand prediction
- **Autonomous Warehouse System**: Fully automated material flow
- **Smart Supply Chain Integration**: Direct supplier connection
- **Material Digital Passport**: Blockchain-based traceability

#### 5. **EAB - Enterprise AI Brain** (企业级AI大脑)
- **Multi-Modal Data Fusion**: Comprehensive data integration
- **Deep Learning Model Hub**: Advanced AI model management
- **Reinforcement Learning Optimizer**: Continuous production optimization
- **Knowledge Graph Engine**: IC process knowledge automation

#### 6. **COP - Customer Operation Platform** (客户运营平台)
- **Customer Portal Engine**: Real-time customer access
- **Real-time Order Tracking**: Complete order transparency 
- **Quality Report Automation**: Automated quality reporting
- **Customer Requirement Engine**: Intelligent requirement management

## Investment Budget & ROI Analysis (投资预算与ROI分析)

### Total Investment Overview (投资总览)
**Three-Year Total Investment: 2,300-3,800 万RMB**
- Compared to original ultra-automation plan: **Cost savings of 4,700-11,200 万RMB (60-70% reduction)**
- Phased investment reduces risk and ensures controlled spending
- Each phase validated before proceeding to next phase

### Phase-by-Phase Investment & Returns (分阶段投资回报)

#### Phase 1: Foundation Digitalization (第一阶段：基础数字化)
**Investment Period**: 6 months (during factory renovation)
- **Total Investment**: 500-800 万RMB
  - Infrastructure (Network + Servers + Basic Systems): 280 万
  - Core MES System Development: 300 万
  - SECS/GEM Equipment Integration: 80 万
- **Annual Operating Cost Savings**: 200-300 万RMB
- **ROI Recovery Period**: 2.5-3 years
- **Key Benefits**: 20% management efficiency improvement, quality standardization

#### Phase 2: Intelligence Upgrade (第二阶段：智能化升级)
**Investment Period**: 6-12 months after production start
- **Additional Investment**: 800-1,200 万RMB
- **Cumulative Investment**: 1,300-2,000 万RMB
  - Data Intelligence Platform: 300 万
  - Partial Automation Systems: 600 万
  - System Integration & Optimization: 300 万
- **Annual Operating Cost Savings**: 600-900 万RMB
- **ROI Recovery Period**: 2.2-2.8 years
- **Key Benefits**: 15-25% production efficiency increase, 20% labor cost reduction

#### Phase 3: High Automation (第三阶段：高度自动化)
**Investment Period**: 12-24 months after production start
- **Additional Investment**: 1,000-1,800 万RMB
- **Cumulative Investment**: 2,300-3,800 万RMB
  - AI-Driven Optimization Systems: 800 万
  - Full Factory Automation: 1,000 万
- **Annual Operating Cost Savings**: 1,200-1,800 万RMB
- **ROI Recovery Period**: 1.9-2.5 years
- **Key Benefits**: 85%+ automation rate, near lights-out factory capability

### Cost Control Measures (成本控制措施)
1. **Practical Technology Selection**: Use mature open-source technologies to reduce license costs
2. **Phased Implementation**: Validate each phase before proceeding to reduce risk
3. **Self-Development Focus**: In-house development of core systems to minimize outsourcing costs
4. **Infrastructure Reuse**: Maximize reuse of infrastructure and platforms across phases
5. **Strict Budget Control**: Phase-by-phase budget monitoring with revalidation if exceeded

### ROI Validation Criteria (投资回报验证标准)
- **Phase 1**: Must achieve 15%+ management efficiency improvement
- **Phase 2**: Must achieve 20%+ production efficiency improvement + 15%+ labor cost reduction
- **Phase 3**: Must achieve 80%+ automation rate + 25%+ additional efficiency improvement
- **Overall Target**: Complete ROI recovery within 2.5 years, with continued annual savings of 1,200-1,800 万RMB

## Implementation Timeline & Milestones (实施时间表与里程碑)

### Master Timeline Overview (总体时间规划)
**Total Implementation Period**: 24 months (2 years)
- **Phase 1**: Month 1-6 (concurrent with factory renovation)
- **Phase 2**: Month 7-18 (6-12 months after production start) 
- **Phase 3**: Month 19-24 (12-24 months after production start)

### Phase 1: Foundation Digitalization Timeline (第一阶段详细时间计划)
**Duration**: 6 months | **Concurrent with factory renovation**

#### Month 1-2: System Design & Preparation (系统设计与准备)
**Milestones:**
- [x] Business requirements analysis and process mapping
- [x] System architecture design and technology selection  
- [x] Team recruitment and training program
- [x] Development environment setup
- [x] Infrastructure procurement (servers, network equipment)

#### Month 3-4: Core Development (核心功能开发)
**Milestones:**
- [ ] Order management and production planning modules
- [ ] Basic MES functionality development
- [ ] Database design and API development
- [ ] Frontend interface development (Vue 3 + Element Plus)
- [ ] Basic SECS/GEM integration framework

#### Month 5-6: Integration & Deployment (集成测试与部署)
**Milestones:**
- [ ] SECS/GEM equipment integration testing
- [ ] System integration testing and performance tuning
- [ ] User acceptance testing and training
- [ ] Production deployment and go-live
- [ ] Phase 1 ROI validation (15%+ management efficiency improvement)

### Phase 2: Intelligence Upgrade Timeline (第二阶段详细时间计划)
**Duration**: 12 months | **6-18 months after production start**

#### Month 7-9: Data Intelligence Platform (数据智能化平台)
**Milestones:**
- [ ] Historical data warehouse construction
- [ ] Basic AI prediction models development (quality, equipment health)
- [ ] Automated reporting system implementation
- [ ] Statistical Process Control (SPC) automation
- [ ] Data analytics dashboard deployment

#### Month 10-12: Partial Automation Implementation (局部自动化实施)
**Milestones:**
- [ ] Key process automation (2-3 critical processes)
- [ ] Intelligent warehouse system (partial zones with AGV)
- [ ] Predictive maintenance system deployment
- [ ] Customer portal development and integration
- [ ] Mobile applications for management

#### Month 13-18: System Integration & Optimization (系统集成优化)
**Milestones:**
- [ ] ERP system deep integration
- [ ] Performance optimization and system scaling
- [ ] User experience enhancement
- [ ] Business process optimization
- [ ] Phase 2 ROI validation (20%+ efficiency + 15%+ labor cost reduction)

### Phase 3: High Automation Timeline (第三阶段详细时间计划)
**Duration**: 6 months | **18-24 months after production start**

#### Month 19-21: AI-Driven Optimization (AI驱动优化)
**Milestones:**
- [ ] Deep learning model training and deployment
- [ ] AI production scheduling optimization
- [ ] Quality prediction model implementation
- [ ] Equipment collaboration AI system
- [ ] Supply chain AI optimization

#### Month 22-24: Full Factory Automation (全厂自动化完善)
**Milestones:**
- [ ] Fully automated material management system
- [ ] Lights-out production zones implementation
- [ ] Self-healing system deployment
- [ ] Digital twin system for critical equipment
- [ ] Phase 3 ROI validation (80%+ automation rate achieved)

### Critical Success Factors (关键成功要素)
1. **Factory Renovation Synchronization**: Phase 1 must complete during renovation period
2. **Production Stability**: Phase 2 can only start after production stability achieved
3. **ROI Validation**: Each phase requires ROI validation before proceeding
4. **Team Competency**: Continuous training and skill development throughout
5. **Change Management**: Gradual user adoption and process transition

### Risk Mitigation Timeline (风险缓解时间规划)
- **Month 3**: First technical review and risk assessment
- **Month 6**: Phase 1 success validation checkpoint
- **Month 12**: Phase 2 mid-term review and adjustment
- **Month 18**: Phase 2 completion validation
- **Month 24**: Final system validation and handover

## Team Structure & Personnel Requirements (团队结构与人员配置)

### Phase-Based Team Evolution (分阶段团队演进)

#### Phase 1 Team Structure (第一阶段团队：12-15人)
**Core Development Team:**
- **Project Manager** (1): Overall project control and coordination
- **System Architect** (1): Technical architecture design and guidance
- **Backend Developers** (4): Java Spring Boot development
- **Frontend Developers** (3): Vue.js 3 + TypeScript development  
- **Database Engineer** (1): MySQL/Redis database design and optimization
- **Test Engineers** (2): Functional and performance testing
- **DevOps Engineers** (2): System deployment and maintenance
- **Business Analyst** (1): Requirements analysis and business process mapping

**Estimated Monthly Cost**: 80-120万 (including salary, benefits, training)

#### Phase 2 Team Expansion (第二阶段团队扩展：+8人)
**Added Specialized Roles:**
- **AI Algorithm Engineers** (2): Machine learning and data mining specialists
- **Big Data Engineers** (2): Spark/Kafka experts for real-time processing
- **Automation Engineers** (2): Equipment integration and automation specialists  
- **Quality Engineer** (1): IATF16949 compliance expert
- **UI/UX Designer** (1): User experience optimization specialist

**Total Team Size**: 20-23 people
**Estimated Monthly Cost**: 140-200万

#### Phase 3 Team Completion (第三阶段团队完善：+5人)
**Advanced Specialists:**
- **Deep Learning Engineers** (2): Neural network and advanced AI specialists
- **DevOps Engineer** (1): CI/CD and cloud-native specialist  
- **Security Engineer** (1): Information security and compliance expert
- **Data Scientist** (1): Advanced data analysis and modeling expert

**Total Team Size**: 25-28 people  
**Estimated Monthly Cost**: 180-250万

### Key Competency Requirements (核心能力要求)

#### Technical Competencies (技术能力要求)
- **Java/Spring Boot**: Senior level (5+ years experience)
- **Vue.js 3 + TypeScript**: Intermediate to senior level
- **Database Design**: MySQL/Redis optimization expertise
- **AI/ML Technologies**: Python, TensorFlow/PyTorch experience
- **Semiconductor Industry**: IC packaging and testing domain knowledge
- **SECS/GEM Protocol**: Equipment integration experience preferred

#### Domain Knowledge Requirements (领域知识要求)
- **IC Packaging & Testing**: Understanding of OSAT operations
- **Quality Management**: IATF16949/ISO9001 knowledge
- **Manufacturing Systems**: MES/ERP system integration experience
- **Automation Systems**: Industrial automation and robotics experience

### Training & Development Plan (培训与发展计划)
1. **Phase 1**: Basic domain training + technology orientation (2 weeks)
2. **Phase 2**: AI/ML skills development + advanced domain training (4 weeks)
3. **Phase 3**: Advanced automation + leadership development (6 weeks)
4. **Continuous**: Monthly technical sharing and skill enhancement

## Risk Control & Success Assurance (风险控制与成功保障)

### Major Risk Categories & Mitigation Strategies (主要风险类别与应对策略)

#### Technical Risks (技术风险)
**Risk 1: Inappropriate Technology Selection Leading to Scalability Issues**
- **Mitigation**: Use mature open-source technologies with proven scalability
- **Backup Plan**: Technical evaluation at end of each phase with adjustment capability

**Risk 2: AI Algorithm Performance Below Expectations**
- **Mitigation**: Start with traditional algorithms, gradually introduce AI components
- **Backup Plan**: Partnership with universities or AI companies for technical support

**Risk 3: System Performance Cannot Meet Production Requirements**
- **Mitigation**: Phased load testing and architectural optimization
- **Backup Plan**: Cloud scaling options for performance elasticity

#### Personnel Risks (人员风险)
**Risk 4: Key Personnel Turnover Affecting Project Progress**
- **Mitigation**: Core technical documentation + backup personnel training
- **Backup Plan**: Established partnerships with outsourcing companies

**Risk 5: Team Technical Capability Insufficient**
- **Mitigation**: Continuous training and external expert guidance
- **Backup Plan**: Critical module outsourcing with internal learning transition

#### Cost Risks (成本风险)
**Risk 6: Budget Overrun Leading to Project Termination**
- **Mitigation**: Strict phase-by-phase budget control with ROI validation
- **Backup Plan**: Prioritize core functions, defer non-essential features

**Risk 7: Equipment Integration Costs Exceeding Expectations**
- **Mitigation**: Prioritize standard SECS/GEM equipment integration
- **Backup Plan**: Phased equipment upgrades without affecting production

#### Business Risks (业务风险)
**Risk 8: Low User Acceptance Affecting System Usage**
- **Mitigation**: Extensive user research and user-friendly interface design
- **Backup Plan**: Phased training with parallel legacy system transition

### Success Assurance Measures (成功保障措施)

#### 1. Phase-by-Phase Validation (分阶段验证保障)
- Each phase has clear success criteria and ROI validation
- No progression to next phase without meeting current phase objectives
- Regular milestone reviews with stakeholder approval gates

#### 2. Conservative Technology Approach (技术保守策略)
- Prioritize proven, mature technologies to minimize technical risk
- Avoid cutting-edge technologies in critical path components
- Maintain technology upgrade paths for future enhancement

#### 3. Strict Budget Management (严格预算管控)
- Phase-by-phase budget monitoring with variance analysis
- Budget revalidation required for any overruns exceeding 10%
- Clear cost-benefit analysis for all major expenditures

#### 4. Personnel Continuity Assurance (人员连续性保障)
- Critical role redundancy with backup personnel trained
- Comprehensive knowledge documentation and transfer processes
- Competitive compensation and retention programs

#### 5. Business Alignment Guarantee (业务协同保障)
- Deep involvement of business departments in requirements and testing
- Regular user feedback sessions and iterative improvements
- Change management program with proper user training

### Quality Assurance Framework (质量保障框架)

#### Code Quality Standards (代码质量标准)
- **Code Review**: Mandatory peer review for all code changes
- **Testing Coverage**: Minimum 80% unit test coverage
- **Performance Testing**: Load testing for all critical components
- **Security Testing**: Regular vulnerability assessments

#### System Quality Metrics (系统质量指标)
- **Availability**: 99.5% uptime target (Phase 1), 99.9% (Phase 2-3)
- **Response Time**: <2 seconds for web interfaces, <500ms for APIs
- **Data Accuracy**: 99.95% data integrity for critical business data
- **Scalability**: Support 2x current load without performance degradation

#### Business Quality Validation (业务质量验证)
- **IATF16949 Compliance**: Full compliance verification at each phase
- **User Satisfaction**: >85% user satisfaction in quarterly surveys
- **Process Efficiency**: Measurable efficiency improvements per phase
- **ROI Achievement**: Documented cost savings meeting projection targets

### Contingency Planning (应急预案)

#### Technical Contingencies (技术应急预案)
- **System Failure**: 4-hour recovery time objective with full backup systems
- **Data Loss**: Daily backup with 1-hour recovery point objective
- **Security Breach**: Immediate isolation protocols and incident response team
- **Performance Issues**: Automatic scaling and load balancing procedures

#### Business Contingencies (业务应急预案)
- **User Resistance**: Enhanced training programs and phased rollout approach
- **Regulatory Changes**: Rapid compliance update procedures
- **Market Changes**: Flexible architecture allowing quick feature adjustments
- **Vendor Issues**: Multi-vendor strategies and backup supplier arrangements

### Continuous Improvement Framework (持续改进框架)

#### Regular Review Cycles (定期评审机制)
- **Weekly**: Technical progress reviews and issue resolution
- **Monthly**: Business metrics review and user feedback analysis
- **Quarterly**: ROI validation and strategic alignment assessment
- **Annually**: Complete system audit and upgrade planning

#### Innovation Integration Process (创新集成流程)
- **Technology Scouting**: Regular evaluation of emerging technologies
- **Pilot Programs**: Small-scale testing of new capabilities
- **Gradual Integration**: Controlled rollout of validated innovations
- **Knowledge Sharing**: Regular technical conferences and best practice sharing

## Microservice Architecture Evolution (微服务架构演进)

### Phase 1: Simplified Service Architecture (第一阶段：简化服务架构)
**Traditional Monolithic to Basic Services:**
- **Core MES Service**: Order, Production, Quality, Equipment management
- **Material Management Service**: Inventory and warehouse operations
- **User Management Service**: Authentication and authorization
- **Reporting Service**: Basic reports and analytics
- **Integration Service**: SECS/GEM and external system interfaces

### Phase 2: Intelligent Service Expansion (第二阶段：智能服务扩展) 
**Enhanced with AI and Analytics:**
- **+ AI Prediction Service**: Quality and equipment health prediction
- **+ Data Analytics Service**: Real-time data processing and insights
- **+ Automation Service**: Partial automation and workflow management
- **+ Customer Service**: External customer portal and API gateway
- **+ Mobile Service**: Mobile applications and responsive interfaces

### Phase 3: Advanced Microservice Ecosystem (第三阶段：高级微服务生态)
**Full Microservice Architecture with Advanced Capabilities:**
- **AMC Service Cluster**: Autonomous manufacturing center services
- **AQS Service Cluster**: Autonomous quality system services  
- **UEO Service Cluster**: Ultra equipment orchestration services
- **ZTL Service Cluster**: Zero-touch logistics services
- **EAB Service Cluster**: Enterprise AI brain services
- **COP Service Cluster**: Customer operation platform services

## Integration Requirements (半导体行业标准集成)

### Equipment Integration (半导体设备标准协议)
- **SECS/GEM (SEMI E4/E5)**: Standard semiconductor equipment communication
- **SECS-II Message Protocol**: Real-time equipment status and recipe management
- **GEM (Generic Equipment Model)**: Equipment state management and alarm handling
- **HSMS (High Speed Message Service)**: High-performance message transport
- **EDA (Equipment Data Acquisition)**: Real-time production and quality data collection
- **SEMI Standards Compliance**: E30 (GEM), E39 (OHT), E40 (Processing Management)

### External System Integration (OSAT业务集成)
- **ERP Integration**: SAP/Oracle integration for semiconductor operations
- **Customer Portal**: Real-time production status and quality reports
- **Supply Chain**: Wafer supplier and material vendor integration  
- **Quality Systems**: Integration with customer quality databases
- **Logistics**: Semiconductor-specific shipping and tracking systems
- **Engineering Systems**: CAD/CAM integration for new product introduction

## 开发工作流

### 功能开发流程

1. **需求分析**: 理解业务需求，确定技术方案
2. **类型定义**: 先定义TypeScript类型和接口
3. **组件开发**: 遵循组件设计原则，自底向上开发
4. **样式实现**: 使用设计系统变量，确保主题一致性
5. **功能测试**: 编写单元测试，进行功能验证
6. **集成测试**: 与后端API联调，验证完整功能
7. **代码审查**: 运行lint检查，确保代码质量

### Git工作流

```powershell
# 功能开发分支
git checkout -b feature/order-management

# 开发完成后
git add .
git commit -m "feat: 添加订单管理功能"

# 代码检查
npm run lint
npm run type-check

# 构建测试
npm run build

# 提交前的最后检查
npm run test:unit
```

### 实时数据处理

- **WebSocket连接**: 用于实时设备状态和生产数据更新
- **轮询机制**: 5-10秒间隔刷新非关键数据
- **错误恢复**: 自动重连机制和错误提示
- **性能优化**: 数据缓存和防抖处理

### 质量保证

- **类型安全**: 严格的TypeScript配置，无any类型
- **代码规范**: ESLint + Prettier自动格式化
- **样式规范**: Stylelint检查SCSS代码质量
- **测试覆盖**: 关键功能必须有单元测试
- **构建验证**: CI/CD流程自动验证代码质量

## 前端技术要求

### 性能要求
- **首屏加载**: < 2秒（生产环境）
- **路由切换**: < 300ms
- **实时数据**: 5-10秒间隔刷新，关键数据1-2秒
- **内存占用**: < 100MB（复杂页面）
- **网络请求**: 并发请求 < 10个

### 兼容性要求
- **浏览器**: Chrome 90+, Firefox 88+, Edge 90+
- **设备支持**: 桌面端 + 平板 + 手机
- **屏幕适配**: 320px-2560px宽度范围
- **网络环境**: 支持弱网和离线情况

### 安全要求
- **数据传输**: HTTPS加密传输
- **身份验证**: JWT Token + 刷新机制
- **权限控制**: 基于角色的精细权限控制
- **数据校验**: 前端输入验证 + 后端数据校验
- **XSS防护**: 自动转义用户输入

## 业务域知识

### IC封装测试行业特点
- **封装类型**: 支持QFP/BGA/CSP/FC等多种封装形式
- **精度要求**: 微米级精度的封装工艺控制
- **质量标准**: JEDEC、IPC、IATF16949等行业标准
- **生产规模**: 支持每日百万级芯片测试产能
- **追溯要求**: 从晶圆到成品的完整质量追溯链

### 制造流程
1. **CP(晶圆测试)**: 晶圆级电测，包括探针卡管理和良率分析
2. **切割(Dicing)**: 晶圆切割成单个芯片
3. **贴片(Die Attach)**: 芯片固定在基板上
4. **线键合(Wire Bond)**: 金线连接芯片和引脚
5. **塑封(Molding)**: 树脂封装保护芯片
6. **FT(成品测试)**: 最终电测和功能验证

### 质量管理
- **IATF16949**: 汽车行业质量管理体系认证
- **SPC**: 统计过程控制，实时Cpk计算
- **FMEA**: 失效模式与影响分析
- **Yield管理**: 晶圆图分析和良率统计

## 重要架构决策与约定

### 自动导入配置
项目使用unplugin-auto-import和unplugin-vue-components实现组件和API的自动导入：
- **Vue API**: vue、vue-router、pinia、@vueuse/core自动导入
- **组件库**: Element Plus和Vant组件自动导入
- **工具库**: lodash-es、dayjs、axios的常用方法自动导入
- **类型生成**: 自动生成类型定义文件到src/types/

### 样式架构
- **SCSS预处理**: 全局导入主题变量和混入
- **CSS变量**: 使用:root定义全局CSS变量支持主题切换
- **BEM命名**: 组件样式使用BEM命名约定
- **作用域样式**: 所有组件样式使用scoped确保样式隔离

### 构建优化
- **代码分割**: vendor、element-plus、charts、utils等分包策略
- **资源管理**: 按类型分类静态资源 (js/css/images/fonts)
- **环境配置**: 支持development、staging、production多环境
- **性能监控**: 开发环境保留sourcemap，生产环境移除console

### 测试架构
- **单元测试**: Vitest + jsdom环境
- **E2E测试**: Cypress端到端测试
- **覆盖率**: 要求80%+代码覆盖率
- **设置文件**: tests/unit/setup.ts全局测试设置

## 常见问题与解决方案

### 开发环境问题

**Q: 如何解决npm install失败？**
A: 尝试以下方法：
```powershell
# 1. 清理缓存
npm cache clean --force

# 2. 删除node_modules和package-lock.json
Remove-Item -Recurse -Force node_modules, package-lock.json

# 3. 使用国内镜像
npm install --registry=https://registry.npmmirror.com
```

**Q: 如何解决TypeScript类型错误？**
A: 
1. 检查`tsconfig.json`配置是否正确
2. 确保所有类型定义文件存在于`src/types/`
3. 运行`npm run type-check`检查具体错误

**Q: 如何解决样式不生效问题？**
A:
1. 确认SCSS文件语法正确
2. 检查CSS变量是否正确引用
3. 运行`npm run lint:style`检查样式规范

### 构建部署问题

**Q: 构建后白屏或路由不工作？**
A:
1. 检查`vite.config.ts`中的base配置
2. 确认服务器配置支持History模式
3. 检查资源路径是否正确

**Q: 构建体积过大怎么办？**
A:
1. 检查是否有不必要的依赖
2. 分析vite.config.ts中的manualChunks配置
3. 考虑使用动态导入优化路由组件
4. 检查是否正确使用了unplugin的按需导入

**Q: 如何调试自动导入问题？**
A:
1. 检查src/types/auto-imports.d.ts和components.d.ts文件
2. 重启TypeScript服务
3. 确认.eslintrc-auto-import.json配置正确
4. 手动import验证组件是否存在

**Q: 主题切换不生效怎么办？**
A:
1. 确认使用了CSS变量而不是SCSS变量
2. 检查useTheme组合式函数是否正确初始化
3. 验证CSS类名是否正确应用到html元素
4. 检查样式的优先级和作用域

---

# 🚨 CRITICAL CODING STANDARDS - CLAUDE必须遵循

## CSS/SCSS 强制规范 (MANDATORY)
- ❌ **禁止使用!important** - 除非绝对必要，每次使用需说明理由
- ❌ **禁止硬编码颜色** - 必须使用CSS变量，如 var(--color-primary)  
- ❌ **禁止深层嵌套** - CSS选择器嵌套不能超过3层
- ✅ **使用BEM命名** - 严格使用Block__Element--Modifier模式
- ✅ **Scoped样式穿透** - 使用:deep()，不使用/deep/或>>>

## Vue组件强制规范 (MANDATORY)
- ❌ **禁止any类型** - 必须使用严格的TypeScript类型定义
- ❌ **禁止导入不存在的模块** - 每次导入前检查模块是否存在
- ✅ **组件结构顺序** - `<template>` → `<script setup>` → `<style scoped>`
- ✅ **Props接口定义** - 使用interface定义所有Props类型

## 开发流程强制要求 (MANDATORY)
1. **代码前检查** - 参考CODING_STANDARDS.md检查清单
2. **开发中验证** - 实时检查控制台错误和警告
3. **完成后测试** - 运行npm run lint && npm run type-check
4. **提交前确认** - 确保没有!important和硬编码颜色

## 问题排查流程 (MANDATORY)
1. **先分析根因** - 不要直接修改，先分析为什么出现问题
2. **检查规范遵循** - 每个问题都要检查是否违反了编码规范
3. **系统性解决** - 不仅修复当前问题，还要防止同类问题再次发生
4. **文档化改进** - 将解决方案更新到规范文档中

## 当前项目已识别问题类型
- **Scoped CSS问题**: 动态元素样式不生效 → 使用:deep()选择器
- **!important滥用**: App.vue中30处违规 → 重构为CSS变量优先级
- **模块导入错误**: 导出名称不匹配 → 检查所有导入导出
- **嵌套层级过深**: 超过3层嵌套 → 使用BEM平铺命名