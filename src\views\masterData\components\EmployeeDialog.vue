<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      size="default"
      @submit.prevent
    >
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="员工姓名" prop="name">
                <el-input
                  v-model="formData.name"
                  placeholder="请输入员工姓名"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="员工工号" prop="employeeNumber">
                <el-input
                  v-model="formData.employeeNumber"
                  placeholder="请输入员工工号"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="英文姓名" prop="englishName">
                <el-input
                  v-model="formData.englishName"
                  placeholder="请输入英文姓名"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="性别" prop="gender">
                <el-radio-group v-model="formData.gender">
                  <el-radio label="male">男</el-radio>
                  <el-radio label="female">女</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="出生日期" prop="birthDate">
                <el-date-picker
                  v-model="formData.birthDate"
                  type="date"
                  placeholder="请选择出生日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="学历" prop="education">
                <el-select v-model="formData.education" placeholder="请选择学历" style="width: 100%">
                  <el-option label="高中及以下" value="高中及以下" />
                  <el-option label="大专" value="大专" />
                  <el-option label="本科" value="本科" />
                  <el-option label="硕士" value="硕士" />
                  <el-option label="博士" value="博士" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="邮箱地址" prop="email">
                <el-input
                  v-model="formData.email"
                  placeholder="请输入邮箱地址"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号码" prop="phoneNumber">
                <el-input
                  v-model="formData.phoneNumber"
                  placeholder="请输入手机号码"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="工作地点" prop="workLocation">
                <el-input
                  v-model="formData.workLocation"
                  placeholder="请输入工作地点"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="入职日期" prop="hireDate">
                <el-date-picker
                  v-model="formData.hireDate"
                  type="date"
                  placeholder="请选择入职日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 组织信息 -->
        <el-tab-pane label="组织信息" name="organization">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="所属部门" prop="departmentId">
                <el-select
                  v-model="formData.departmentId"
                  placeholder="请选择所属部门"
                  filterable
                  style="width: 100%"
                  @change="handleDepartmentChange"
                >
                  <el-option
                    v-for="dept in departments"
                    :key="dept.id"
                    :label="dept.name"
                    :value="dept.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="担任岗位" prop="positionId">
                <el-select
                  v-model="formData.positionId"
                  placeholder="请选择担任岗位"
                  filterable
                  style="width: 100%"
                >
                  <el-option
                    v-for="pos in availablePositions"
                    :key="pos.id"
                    :label="`${pos.name} (${pos.code})`"
                    :value="pos.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="直接主管" prop="directSupervisorId">
                <el-select
                  v-model="formData.directSupervisorId"
                  placeholder="请选择直接主管"
                  filterable
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="emp in availableSupervisors"
                    :key="emp.id"
                    :label="`${emp.name} (${emp.employeeNumber})`"
                    :value="emp.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 预留字段 -->
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 联系信息 -->
        <el-tab-pane label="联系信息" name="contact">
          <el-form-item label="紧急联系人">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="姓名" prop="emergencyContact.name">
                  <el-input
                    v-model="formData.emergencyContact.name"
                    placeholder="请输入姓名"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="关系" prop="emergencyContact.relationship">
                  <el-select
                    v-model="formData.emergencyContact.relationship"
                    placeholder="请选择关系"
                    style="width: 100%"
                  >
                    <el-option label="父亲" value="父亲" />
                    <el-option label="母亲" value="母亲" />
                    <el-option label="配偶" value="配偶" />
                    <el-option label="子女" value="子女" />
                    <el-option label="兄弟姐妹" value="兄弟姐妹" />
                    <el-option label="朋友" value="朋友" />
                    <el-option label="其他" value="其他" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="电话" prop="emergencyContact.phoneNumber">
                  <el-input
                    v-model="formData.emergencyContact.phoneNumber"
                    placeholder="请输入电话号码"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import type { 
  Employee, 
  Department, 
  Position, 
  EmployeeFormData 
} from '@/types/organization'

// Props
interface Props {
  visible: boolean
  employee?: Employee | null
  departments: Department[]
  positions: Position[]
  employees: Employee[]
}

// Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  employee: null
})

const emit = defineEmits<Emits>()

// 组件引用
const formRef = ref<FormInstance>()

// 状态数据
const loading = ref(false)
const activeTab = ref('basic')
const formData = ref<EmployeeFormData>({
  name: '',
  employeeNumber: '',
  englishName: '',
  departmentId: '',
  positionId: '',
  directSupervisorId: '',
  email: '',
  phoneNumber: '',
  hireDate: new Date(),
  gender: 'male',
  birthDate: undefined,
  education: '',
  workLocation: '',
  emergencyContact: {
    name: '',
    relationship: '',
    phoneNumber: ''
  }
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.employee)

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑员工' : '新增员工'
})

// 可选岗位（根据选择的部门过滤）
const availablePositions = computed(() => {
  if (!formData.value.departmentId) return []
  return props.positions.filter(pos => pos.departmentId === formData.value.departmentId)
})

// 可选主管（同部门的其他员工，排除自己）
const availableSupervisors = computed(() => {
  return props.employees.filter(emp => {
    // 排除自己
    if (props.employee && emp.id === props.employee.id) return false
    // 同部门的员工
    return emp.departmentId === formData.value.departmentId
  })
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入员工姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在2-20个字符', trigger: 'blur' }
  ],
  employeeNumber: [
    { required: true, message: '请输入员工工号', trigger: 'blur' },
    { 
      pattern: /^[A-Z0-9]+$/, 
      message: '工号只能包含大写字母和数字', 
      trigger: 'blur' 
    }
  ],
  departmentId: [
    { required: true, message: '请选择所属部门', trigger: 'change' }
  ],
  positionId: [
    { required: true, message: '请选择担任岗位', trigger: 'change' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phoneNumber: [
    { 
      pattern: /^1[3-9]\d{9}$/, 
      message: '请输入正确的手机号码', 
      trigger: 'blur' 
    }
  ],
  hireDate: [
    { required: true, message: '请选择入职日期', trigger: 'change' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  workLocation: [
    { required: true, message: '请输入工作地点', trigger: 'blur' }
  ]
}

// 事件处理
const handleClose = () => {
  dialogVisible.value = false
}

const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
}

const handleDepartmentChange = () => {
  // 部门变更时清空岗位和主管选择
  formData.value.positionId = ''
  formData.value.directSupervisorId = ''
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 清理空的紧急联系人信息
    if (!formData.value.emergencyContact?.name) {
      formData.value.emergencyContact = undefined
    }
    
    console.log('提交员工数据:', formData.value)
    
    ElMessage.success(isEdit.value ? '员工信息更新成功' : '员工创建成功')
    emit('success')
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    name: '',
    employeeNumber: '',
    englishName: '',
    departmentId: '',
    positionId: '',
    directSupervisorId: '',
    email: '',
    phoneNumber: '',
    hireDate: new Date(),
    gender: 'male',
    birthDate: undefined,
    education: '',
    workLocation: '',
    emergencyContact: {
      name: '',
      relationship: '',
      phoneNumber: ''
    }
  }
  
  activeTab.value = 'basic'
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 初始化表单数据
const initFormData = () => {
  if (props.employee) {
    // 编辑模式
    formData.value = {
      name: props.employee.name,
      employeeNumber: props.employee.employeeNumber,
      englishName: props.employee.englishName || '',
      departmentId: props.employee.departmentId,
      positionId: props.employee.positionId,
      directSupervisorId: props.employee.directSupervisorId || '',
      email: props.employee.email,
      phoneNumber: props.employee.phoneNumber || '',
      hireDate: props.employee.hireDate,
      gender: props.employee.gender,
      birthDate: props.employee.birthDate,
      education: props.employee.education || '',
      workLocation: props.employee.workLocation,
      emergencyContact: props.employee.emergencyContact || {
        name: '',
        relationship: '',
        phoneNumber: ''
      }
    }
  } else {
    // 新增模式
    resetForm()
  }
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible) {
    initFormData()
  }
})

watch(() => props.employee, () => {
  if (props.visible) {
    initFormData()
  }
})
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
}

:deep(.el-dialog) {
  .el-dialog__header {
    padding-bottom: var(--spacing-3);
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .el-dialog__body {
    padding: var(--spacing-4);
  }

  .el-dialog__footer {
    padding-top: var(--spacing-3);
    border-top: 1px solid var(--el-border-color-lighter);
  }
}

:deep(.el-tabs) {
  .el-tabs__header {
    margin-bottom: var(--spacing-4);
  }
  
  .el-tabs__content {
    padding-top: 0;
  }
}

// 紧急联系人表单样式优化
:deep(.el-form-item) {
  &:has(.el-form-item) {
    margin-bottom: 0;
    
    .el-form-item {
      margin-bottom: var(--spacing-3);
    }
  }
}

// 响应式设计
@media (width <= 768px) {
  :deep(.el-dialog) {
    width: 90% !important;
    margin: var(--spacing-2);
  }
  
  :deep(.el-tabs__header) {
    .el-tabs__nav {
      width: 100%;
      
      .el-tabs__item {
        flex: 1;
        text-align: center;
      }
    }
  }
}
</style>