<template>
  <div class="customer-list-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-header__content">
        <div class="page-header__main">
          <h1 class="page-title">客户管理</h1>
          <p class="page-subtitle">IC封装测试客户关系管理系统 - 全生命周期客户服务</p>
        </div>
        <div class="page-header__actions">
          <el-button type="primary" size="large" @click="handleCreateCustomer">
            <svg
              class="button-icon"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
            >
              <path d="M12 5v14m-7-7h14" />
            </svg>
            新建客户
          </el-button>
          <el-button size="large" @click="handleExportCustomers">
            <svg
              class="button-icon"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
            >
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
              <polyline points="7,10 12,15 17,10" />
              <line x1="12" y1="15" x2="12" y2="3" />
            </svg>
            导出数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-cards">
      <div class="stats-grid">
        <div v-for="stat in customerStatsCards" :key="stat.key" class="stat-card">
          <div class="stat-card__header">
            <div class="stat-icon" :class="stat.iconClass">
              <svg viewBox="0 0 24 24"
v-html="stat.icon"
/>
            </div>
            <div class="stat-trend" :class="stat.trendClass">
              <svg
                v-if="stat.trend === 'up'"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M7 14l5-5 5 5" />
              </svg>
              <svg
                v-else-if="stat.trend === 'down'"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M7 10l5 5 5-5" />
              </svg>
              <span>{{ stat.changePercent }}</span>
            </div>
          </div>
          <div class="stat-value">
            {{ stat.value }}
          </div>
          <div class="stat-label">
            {{ stat.label }}
          </div>
          <div class="stat-detail">
            {{ stat.detail }}
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-section">
      <el-card class="filter-card" shadow="never">
        <template #header>
          <div class="filter-header">
            <h3 class="filter-title">高级搜索</h3>
            <el-button
link type="primary"
class="filter-toggle" @click="toggleFilterExpanded"
>
              {{ isFilterExpanded ? '收起' : '展开' }}
              <svg
                class="toggle-icon"
                :class="{ 'toggle-icon--expanded': isFilterExpanded }"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
              >
                <path d="M6 9l6 6 6-6" />
              </svg>
            </el-button>
          </div>
        </template>

        <div class="filter-content" :class="{ 'filter-content--expanded': isFilterExpanded }">
          <el-form :model="searchForm" class="search-form" label-position="top">
            <div class="search-grid">
              <!-- 基础搜索字段 -->
              <el-form-item label="客户名称/编号">
                <el-input
                  v-model="searchForm.keyword"
                  placeholder="请输入客户名称或编号"
                  clearable
                  @keyup.enter="handleSearch"
                >
                  <template #prefix>
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                      <circle cx="11" cy="11" r="8" />
                      <path d="m21 21-4.35-4.35" />
                    </svg>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="客户类型">
                <el-select
                  v-model="searchForm.customerType"
                  placeholder="请选择客户类型"
                  clearable
                  class="w-full"
                >
                  <el-option label="IC设计公司" value="IC_DESIGN" />
                  <el-option label="Foundry代工厂" value="FOUNDRY" />
                  <el-option label="系统集成商" value="SYSTEM_INTEGRATOR" />
                  <el-option label="OEM厂商" value="OEM" />
                  <el-option label="贸易商" value="TRADER" />
                </el-select>
              </el-form-item>

              <el-form-item label="客户等级">
                <el-select
                  v-model="searchForm.customerLevel"
                  placeholder="请选择客户等级"
                  clearable
                  class="w-full"
                >
                  <el-option label="战略客户" value="STRATEGIC" />
                  <el-option label="重要客户" value="IMPORTANT" />
                  <el-option label="普通客户" value="NORMAL" />
                  <el-option label="潜在客户" value="POTENTIAL" />
                </el-select>
              </el-form-item>

              <!-- 展开后的搜索字段 -->
              <template v-if="isFilterExpanded">
                <el-form-item label="行业分类">
                  <el-select
                    v-model="searchForm.industry"
                    placeholder="请选择行业分类"
                    clearable
                    class="w-full"
                  >
                    <el-option label="汽车电子" value="AUTOMOTIVE" />
                    <el-option label="消费电子" value="CONSUMER_ELECTRONICS" />
                    <el-option label="工业控制" value="INDUSTRIAL_CONTROL" />
                    <el-option label="通信设备" value="COMMUNICATION" />
                    <el-option label="计算机及周边" value="COMPUTER" />
                    <el-option label="医疗电子" value="MEDICAL" />
                    <el-option label="航空航天" value="AEROSPACE" />
                    <el-option label="军工" value="MILITARY" />
                  </el-select>
                </el-form-item>

                <el-form-item label="客户状态">
                  <el-select
                    v-model="searchForm.status"
                    placeholder="请选择客户状态"
                    clearable
                    class="w-full"
                  >
                    <el-option label="活跃" value="ACTIVE" />
                    <el-option label="暂停合作" value="SUSPENDED" />
                    <el-option label="潜在客户" value="POTENTIAL" />
                    <el-option label="已停止合作" value="INACTIVE" />
                  </el-select>
                </el-form-item>

                <el-form-item label="创建时间">
                  <el-date-picker
                    v-model="searchForm.createdTimeRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="w-full"
                  />
                </el-form-item>
              </template>
            </div>

            <div class="search-actions">
              <el-button type="primary" @click="handleSearch" :loading="searchLoading">
                <svg
                  class="button-icon"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                >
                  <circle cx="11" cy="11" r="8" />
                  <path d="m21 21-4.35-4.35" />
                </svg>
                搜索
              </el-button>
              <el-button @click="handleResetSearch">
                <svg
                  class="button-icon"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                >
                  <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
                  <path d="M3 3v5h5" />
                </svg>
                重置
              </el-button>
            </div>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 客户列表 -->
    <div class="customer-table-section">
      <el-card class="table-card" shadow="never">
        <template #header>
          <div class="table-header">
            <div class="table-header__left">
              <h3 class="table-title">客户列表</h3>
              <span class="table-subtitle">共 {{ customerStore.total }} 条记录</span>
            </div>
            <div class="table-header__right">
              <el-button
                v-if="selectedCustomerIds.length > 0"
                type="danger"
                :icon="Delete"
                @click="handleBatchDelete"
              >
                批量删除 ({{ selectedCustomerIds.length }})
              </el-button>
              <el-button
:loading="customerStore.loading" @click="handleRefresh"
>
                <svg
                  class="button-icon"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                >
                  <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
                  <path d="M3 3v5h5" />
                </svg>
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          v-loading="customerStore.loading"
          :data="customerStore.customers"
          stripe
          class="customer-table"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
        >
          <el-table-column type="selection" width="50" />

          <el-table-column prop="customerCode" label="客户编号" width="120" fixed="left">
            <template #default="{ row }">
              <el-link type="primary" :underline="false" @click.stop="handleViewCustomer(row)">
                {{ row.customerCode }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column prop="customerName" label="客户名称" min-width="180">
            <template #default="{ row }">
              <div class="customer-name-cell">
                <div class="customer-name">
                  {{ row.customerName }}
                </div>
                <div v-if="row.customerNameEn" class="customer-name-en">
                  {{ row.customerNameEn }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="customerType" label="客户类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getCustomerTypeTagType(row.customerType)" size="small">
                {{ getCustomerTypeText(row.customerType) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="customerLevel" label="客户等级" width="100">
            <template #default="{ row }">
              <el-tag :type="getCustomerLevelTagType(row.customerLevel)" size="small">
                {{ getCustomerLevelText(row.customerLevel) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="industry" label="行业分类" width="120">
            <template #default="{ row }">
              <span class="industry-text">{{ getIndustryText(row.industry) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="contacts" label="主要联系人" width="150">
            <template #default="{ row }">
              <div v-if="row.contacts?.length > 0" class="contact-info">
                <div class="contact-name">
                  {{ getPrimaryContact(row)?.name }}
                </div>
                <div class="contact-phone">
                  {{ getPrimaryContact(row)?.mobile }}
                </div>
              </div>
              <span v-else class="no-contact">暂无联系人</span>
            </template>
          </el-table-column>

          <el-table-column prop="cooperationHistory" label="合作情况" width="150">
            <template #default="{ row }">
              <div class="cooperation-info">
                <div class="cooperation-years">{{ row.cooperationHistory.cooperationYears }}年</div>
                <div class="cooperation-orders">{{ row.cooperationHistory.totalOrders }}个订单</div>
                <div class="cooperation-amount">
                  ¥{{ formatAmount(row.cooperationHistory.totalSalesAmount) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="createdAt" label="创建时间" width="110">
            <template #default="{ row }">
              <span class="created-time">{{ formatDate(row.createdAt) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="160" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button link type="primary" size="small" @click.stop="handleViewCustomer(row)">
                  查看
                </el-button>
                <el-button link type="primary" size="small" @click.stop="handleEditCustomer(row)">
                  编辑
                </el-button>
                <el-button link type="danger" size="small" @click.stop="handleDeleteCustomer(row)">
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="table-pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="customerStore.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 客户详情抽屉 -->
    <CustomerDetailDrawer
v-model="showDetailDrawer" :customer-id="selectedCustomerId"
/>

    <!-- 新建/编辑客户对话框 -->
    <el-dialog
      v-model="showCustomerDialog"
      :title="dialogMode === 'create' ? '新建客户' : '编辑客户'"
      width="1200px"
      :close-on-click-modal="false"
      class="customer-dialog"
    >
      <CustomerForm
        ref="customerFormRef"
        :initial-data="currentCustomerData"
        :loading="submitting"
        @submit="handleSaveCustomer"
        @cancel="showCustomerDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, watch } from 'vue'
  import { Delete } from '@element-plus/icons-vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import type { Customer, CustomerQueryParams } from '@/types/customer'
  import { useCustomerStore } from '@/stores/customer'
  import CustomerForm from '@/components/business/CustomerForm.vue'
  import CustomerDetailDrawer from '@/components/business/CustomerDetailDrawer.vue'

  // Store
  const customerStore = useCustomerStore()

  // 响应式数据
  const isFilterExpanded = ref(false)
  const searchLoading = ref(false)
  const selectedCustomerIds = ref<string[]>([])
  const currentPage = ref(1)
  const pageSize = ref(20)
  const showDetailDrawer = ref(false)
  const selectedCustomerId = ref('')
  const showCustomerDialog = ref(false)
  const dialogMode = ref<'create' | 'edit'>('create')
  const currentCustomerData = ref<Partial<any>>({})
  const submitting = ref(false)
  const customerFormRef = ref()

  // 搜索表单
  const searchForm = reactive<CustomerQueryParams>({
    keyword: '',
    customerType: undefined,
    customerLevel: undefined,
    industry: undefined,
    status: undefined,
    createdTimeRange: undefined
  })

  // 客户统计卡片数据
  const customerStatsCards = computed(() => [
    {
      key: 'total',
      label: '客户总数',
      value: customerStore.stats?.totalCustomers || 0,
      detail: '累计注册客户数量',
      icon: '<path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/><rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>',
      iconClass: 'stat-icon--primary',
      trend: 'up',
      changePercent: '+12%',
      trendClass: 'trend--up'
    },
    {
      key: 'active',
      label: '活跃客户',
      value: customerStore.stats?.activeCustomers || 0,
      detail: '当前合作中的客户',
      icon: '<path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="m22 21-3-3m0 0a5.5 5.5 0 1 0-7.8-7.8 5.5 5.5 0 0 0 7.8 7.8Z"/>',
      iconClass: 'stat-icon--success',
      trend: 'up',
      changePercent: '+8%',
      trendClass: 'trend--up'
    },
    {
      key: 'strategic',
      label: '战略客户',
      value: customerStore.stats?.strategicCustomers || 0,
      detail: '重点战略合作伙伴',
      icon: '<path d="m11 17 2 2a1 1 0 1 0 3-3"/><path d="m14 14 2.5 2.5a1 1 0 1 0 3-3l-3.88-3.88a3 3 0 0 0-4.24 0l-.88.88a1 1 0 1 1-3-3l2.81-2.81a5.79 5.79 0 0 1 7.06-.87l.47.28a2 2 0 0 0 1.42.25L21 4"/>',
      iconClass: 'stat-icon--danger',
      trend: 'up',
      changePercent: '+5%',
      trendClass: 'trend--up'
    },
    {
      key: 'newThisMonth',
      label: '本月新增',
      value: customerStore.stats?.newCustomersThisMonth || 0,
      detail: '本月新注册客户',
      icon: '<path d="M8 2v4"/><path d="M16 2v4"/><rect width="18" height="18" x="3" y="4" rx="2"/><path d="M3 10h18"/><path d="M8 14h.01"/><path d="M12 14h.01"/><path d="M16 14h.01"/><path d="M8 18h.01"/><path d="M12 18h.01"/>',
      iconClass: 'stat-icon--warning',
      trend: 'up',
      changePercent: '+15%',
      trendClass: 'trend--up'
    }
  ])

  // 切换筛选展开状态
  const toggleFilterExpanded = () => {
    isFilterExpanded.value = !isFilterExpanded.value
  }

  // 处理搜索
  const handleSearch = async () => {
    searchLoading.value = true
    try {
      currentPage.value = 1
      await customerStore.searchCustomers({
        ...searchForm,
        page: currentPage.value,
        pageSize: pageSize.value
      })
    } finally {
      searchLoading.value = false
    }
  }

  // 处理重置搜索
  const handleResetSearch = async () => {
    Object.assign(searchForm, {
      keyword: '',
      customerType: undefined,
      customerLevel: undefined,
      industry: undefined,
      status: undefined,
      createdTimeRange: undefined
    })
    currentPage.value = 1
    await customerStore.resetSearch()
  }

  // 处理页码变化
  const handlePageChange = async (page: number) => {
    currentPage.value = page
    await customerStore.fetchCustomers({
      ...searchForm,
      page,
      pageSize: pageSize.value
    })
  }

  // 处理页大小变化
  const handlePageSizeChange = async (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    await customerStore.fetchCustomers({
      ...searchForm,
      page: 1,
      pageSize: size
    })
  }

  // 处理刷新
  const handleRefresh = () => {
    customerStore.fetchCustomers({
      ...searchForm,
      page: currentPage.value,
      pageSize: pageSize.value
    })
  }

  // 处理选择变化
  const handleSelectionChange = (selection: Customer[]) => {
    selectedCustomerIds.value = selection.map(customer => customer.id)
  }

  // 处理行点击
  const handleRowClick = (row: Customer) => {
    handleViewCustomer(row)
  }

  // 处理查看客户
  const handleViewCustomer = (customer: Customer) => {
    selectedCustomerId.value = customer.id
    showDetailDrawer.value = true
  }

  // 处理新建客户
  const handleCreateCustomer = () => {
    dialogMode.value = 'create'
    currentCustomerData.value = {}
    showCustomerDialog.value = true
  }

  // 处理编辑客户
  const handleEditCustomer = (customer: Customer) => {
    dialogMode.value = 'edit'
    currentCustomerData.value = { ...customer }
    showCustomerDialog.value = true
  }

  // 处理保存客户
  const handleSaveCustomer = async (data: any) => {
    submitting.value = true
    try {
      if (dialogMode.value === 'create') {
        await customerStore.createCustomer(data)
        ElMessage.success('客户创建成功')
      } else {
        await customerStore.updateCustomer(currentCustomerData.value.id!, data)
        ElMessage.success('客户更新成功')
      }
      showCustomerDialog.value = false
      handleRefresh()
    } catch (error) {
      ElMessage.error(`客户${dialogMode.value === 'create' ? '创建' : '更新'}失败`)
    } finally {
      submitting.value = false
    }
  }

  // 处理删除客户
  const handleDeleteCustomer = async (customer: Customer) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除客户"${customer.customerName}"吗？此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await customerStore.deleteCustomer(customer.id)
      ElMessage.success('客户删除成功')
      handleRefresh()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('客户删除失败')
      }
    }
  }

  // 处理批量删除
  const handleBatchDelete = async () => {
    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${selectedCustomerIds.value.length} 个客户吗？此操作不可恢复。`,
        '批量删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const result = await customerStore.batchDeleteCustomers(selectedCustomerIds.value)
      ElMessage.success(`成功删除 ${result.success} 个客户`)
      selectedCustomerIds.value = []
      handleRefresh()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('批量删除失败')
      }
    }
  }

  // 处理导出客户
  const handleExportCustomers = async () => {
    try {
      await customerStore.exportCustomers(searchForm)
      ElMessage.success('数据导出成功')
    } catch (error) {
      ElMessage.error('数据导出失败')
    }
  }

  // 工具函数
  const getCustomerTypeText = (type: string) => {
    const map: Record<string, string> = {
      IC_DESIGN: 'IC设计',
      FOUNDRY: 'Foundry',
      SYSTEM_INTEGRATOR: '系统集成',
      OEM: 'OEM',
      TRADER: '贸易商'
    }
    return map[type] || type
  }

  const getCustomerTypeTagType = (type: string) => {
    const map: Record<string, string> = {
      IC_DESIGN: 'primary',
      FOUNDRY: 'success',
      SYSTEM_INTEGRATOR: 'info',
      OEM: 'warning',
      TRADER: 'danger'
    }
    return map[type] || 'info'
  }

  const getCustomerLevelText = (level: string) => {
    const map: Record<string, string> = {
      STRATEGIC: '战略',
      IMPORTANT: '重要',
      NORMAL: '普通',
      POTENTIAL: '潜在'
    }
    return map[level] || level
  }

  const getCustomerLevelTagType = (level: string) => {
    const map: Record<string, string> = {
      STRATEGIC: 'danger',
      IMPORTANT: 'warning',
      NORMAL: 'info',
      POTENTIAL: 'success'
    }
    return map[level] || 'info'
  }

  const getIndustryText = (industry: string) => {
    const map: Record<string, string> = {
      AUTOMOTIVE: '汽车电子',
      CONSUMER_ELECTRONICS: '消费电子',
      COMMUNICATION: '通信设备',
      INDUSTRIAL_CONTROL: '工业控制',
      COMPUTER: '计算机',
      MEDICAL: '医疗电子',
      AEROSPACE: '航空航天',
      MILITARY: '军工'
    }
    return map[industry] || industry
  }

  const getStatusText = (status: string) => {
    const map: Record<string, string> = {
      ACTIVE: '活跃',
      SUSPENDED: '暂停',
      POTENTIAL: '潜在',
      INACTIVE: '停止'
    }
    return map[status] || status
  }

  const getStatusTagType = (status: string) => {
    const map: Record<string, string> = {
      ACTIVE: 'success',
      SUSPENDED: 'warning',
      POTENTIAL: 'info',
      INACTIVE: 'danger'
    }
    return map[status] || 'info'
  }

  const getPrimaryContact = (customer: Customer) => {
    return customer.contacts?.find(contact => contact.isPrimary) || customer.contacts?.[0]
  }

  const formatAmount = (amount: number) => {
    return (amount / 10000).toFixed(1) + '万'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  // 监听页面变化
  watch([currentPage, pageSize], () => {
    // 页面变化时更新数据
  })

  // 初始化
  onMounted(async () => {
    await Promise.all([
      customerStore.fetchCustomers({ page: 1, pageSize: pageSize.value }),
      customerStore.fetchCustomerStats()
    ])
  })
</script>

<style lang="scss" scoped>
  .customer-list-page {
    min-height: calc(100vh - 64px);
    padding: var(--spacing-6);
    background-color: var(--color-bg-secondary);
  }

  .page-header {
    margin-bottom: var(--spacing-6);

    &__content {
      display: flex;
      gap: var(--spacing-4);
      align-items: flex-start;
      justify-content: space-between;
    }

    &__main {
      flex: 1;
    }

    &__actions {
      display: flex;
      gap: var(--spacing-3);
    }
  }

  .page-title {
    margin: 0 0 var(--spacing-2) 0;
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-primary);
  }

  .page-subtitle {
    margin: 0;
    font-size: var(--font-size-base);
    color: var(--color-text-secondary);
  }

  .button-icon {
    width: 18px;
    height: 18px;
    margin-right: var(--spacing-2);
  }

  // 统计卡片样式
  .stats-cards {
    margin-bottom: var(--spacing-6);
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--spacing-4);
  }

  .stat-card {
    padding: var(--spacing-5);
    background: var(--color-bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);

    &:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-2px);
    }

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-3);
    }
  }

  .stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: var(--radius-base);

    svg {
      width: 24px;
      height: 24px;
      stroke-width: 2;
    }

    &--primary {
      color: var(--color-primary);
      background: var(--color-primary-light);
    }

    &--success {
      color: var(--color-success);
      background: var(--color-success-light);
    }

    &--danger {
      color: var(--color-danger);
      background: var(--color-danger-light);
    }

    &--warning {
      color: var(--color-warning);
      background: var(--color-warning-light);
    }
  }

  .stat-trend {
    display: flex;
    gap: var(--spacing-1);
    align-items: center;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);

    svg {
      width: 16px;
      height: 16px;
    }

    &.trend--up {
      color: var(--color-success);
    }

    &.trend--down {
      color: var(--color-danger);
    }
  }

  .stat-value {
    margin-bottom: var(--spacing-1);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-primary);
  }

  .stat-label {
    margin-bottom: var(--spacing-1);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
  }

  .stat-detail {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
  }

  // 筛选区域样式
  .filter-section {
    margin-bottom: var(--spacing-6);
  }

  .filter-card {
    :deep(.el-card__header) {
      padding: var(--spacing-4) var(--spacing-6);
      border-bottom: 1px solid var(--color-border-lighter);
    }

    :deep(.el-card__body) {
      padding: 0;
    }
  }

  .filter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .filter-title {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
  }

  .filter-toggle {
    display: flex;
    gap: var(--spacing-1);
    align-items: center;
  }

  .toggle-icon {
    width: 16px;
    height: 16px;
    transition: transform var(--transition-normal);

    &--expanded {
      transform: rotate(180deg);
    }
  }

  .filter-content {
    padding: var(--spacing-6);
    transition: all var(--transition-normal);

    &:not(.filter-content--expanded) .search-grid > *:nth-child(n + 4) {
      display: none;
    }
  }

  .search-form {
    .search-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-4);
    }

    .search-actions {
      display: flex;
      gap: var(--spacing-3);
      justify-content: center;
    }
  }

  // 表格区域样式
  .customer-table-section {
    .table-card {
      :deep(.el-card__header) {
        padding: var(--spacing-4) var(--spacing-6);
        border-bottom: 1px solid var(--color-border-lighter);
      }

      :deep(.el-card__body) {
        padding: 0;
      }
    }
  }

  .table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &__left {
      display: flex;
      gap: var(--spacing-3);
      align-items: baseline;
    }

    &__right {
      display: flex;
      gap: var(--spacing-3);
      align-items: center;
    }
  }

  .table-title {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
  }

  .table-subtitle {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
  }

  .customer-table {
    :deep(.el-table__body tr) {
      cursor: pointer;

      &:hover {
        background-color: var(--color-bg-hover);
      }
    }

    .customer-name-cell {
      .customer-name {
        margin-bottom: var(--spacing-1);
        font-weight: var(--font-weight-medium);
        color: var(--color-text-primary);
      }

      .customer-name-en {
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }
    }

    .industry-text {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .contact-info {
      .contact-name {
        margin-bottom: var(--spacing-1);
        font-weight: var(--font-weight-medium);
        color: var(--color-text-primary);
      }

      .contact-phone {
        font-family: var(--font-family-mono);
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }
    }

    .no-contact {
      font-size: var(--font-size-sm);
      color: var(--color-text-placeholder);
    }

    .cooperation-info {
      .cooperation-years {
        margin-bottom: var(--spacing-1);
        font-weight: var(--font-weight-medium);
        color: var(--color-text-primary);
      }

      .cooperation-orders,
      .cooperation-amount {
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }

      .cooperation-amount {
        font-weight: var(--font-weight-medium);
        color: var(--color-success);
      }
    }

    .created-time {
      font-family: var(--font-family-mono);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .action-buttons {
      display: flex;
      gap: var(--spacing-2);
    }
  }

  .table-pagination {
    display: flex;
    justify-content: center;
    padding: var(--spacing-4) var(--spacing-6);
    border-top: 1px solid var(--color-border-lighter);
  }

  // 对话框样式
  .customer-dialog {
    :deep(.el-dialog__body) {
      max-height: 70vh;
      padding: 0 var(--spacing-6) var(--spacing-6);
      overflow-y: auto;
    }
  }

  // 响应式设计
  @media (width <= 1200px) {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (width <= 768px) {
    .customer-list-page {
      padding: var(--spacing-4);
    }

    .page-header__content {
      flex-direction: column;
      align-items: stretch;
    }

    .page-header__actions {
      justify-content: stretch;
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }

    .search-grid {
      grid-template-columns: 1fr;
    }

    .table-header {
      flex-direction: column;
      gap: var(--spacing-3);
      align-items: stretch;
    }

    .customer-table {
      :deep(.el-table__body-wrapper) {
        overflow-x: auto;
      }
    }
  }

  // 全局样式
  :deep(.w-full) {
    width: 100%;
  }
</style>
