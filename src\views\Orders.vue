<template>
  <div class="orders-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-header__content">
        <div class="page-header__main">
          <h1 class="page-title">订单管理</h1>
          <p class="page-subtitle">IC封装测试订单全生命周期管理系统</p>
        </div>
        <div class="page-header__actions">
          <el-button type="primary" size="large" @click="handleCreateOrder">
            <svg class="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
              <path d="M12 5v14m-7-7h14"/>
            </svg>
            新建订单
          </el-button>
          <el-button size="large" @click="handleExportOrders">
            <svg class="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
              <polyline points="7,10 12,15 17,10"/>
              <line x1="12" y1="15" x2="12" y2="3"/>
            </svg>
            导出数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-cards">
      <div class="stats-grid">
        <div v-for="stat in orderStats" :key="stat.key" class="stat-card">
          <div class="stat-card__header">
            <div class="stat-icon" :class="stat.iconClass">
              <svg v-html="stat.icon" viewBox="0 0 24 24"></svg>
            </div>
            <div class="stat-trend" :class="stat.trendClass">
              <svg v-if="stat.trend === 'up'" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M7 14l5-5 5 5"/>
              </svg>
              <svg v-else-if="stat.trend === 'down'" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M7 10l5 5 5-5"/>
              </svg>
              <span>{{ stat.changePercent }}</span>
            </div>
          </div>
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-detail">{{ stat.detail }}</div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-section">
      <el-card class="filter-card" shadow="never">
        <template #header>
          <div class="filter-header">
            <h3 class="filter-title">高级搜索</h3>
            <el-button 
              link 
              type="primary" 
              @click="toggleFilterExpanded"
              class="filter-toggle"
            >
              {{ isFilterExpanded ? '收起' : '展开' }}
              <svg class="toggle-icon" :class="{ 'toggle-icon--expanded': isFilterExpanded }" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <path d="M6 9l6 6 6-6"/>
              </svg>
            </el-button>
          </div>
        </template>
        
        <div class="filter-content" :class="{ 'filter-content--expanded': isFilterExpanded }">
          <el-form :model="searchForm" class="search-form" label-position="top">
            <div class="search-grid">
              <!-- 基础搜索字段 -->
              <el-form-item label="订单编号">
                <el-input 
                  v-model="searchForm.orderNumber" 
                  placeholder="请输入订单编号"
                  clearable
                  prefix-icon="Search"
                >
                  <template #prefix>
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                      <circle cx="11" cy="11" r="8"/>
                      <path d="m21 21-4.35-4.35"/>
                    </svg>
                  </template>
                </el-input>
              </el-form-item>
              
              <el-form-item label="客户名称">
                <el-select 
                  v-model="searchForm.customer" 
                  placeholder="请选择客户"
                  clearable
                  filterable
                  class="w-full"
                >
                  <el-option 
                    v-for="customer in customerOptions"
                    :key="customer.value"
                    :label="customer.label"
                    :value="customer.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="订单状态">
                <el-select 
                  v-model="searchForm.status" 
                  placeholder="请选择状态"
                  clearable
                  multiple
                  class="w-full"
                >
                  <el-option 
                    v-for="status in statusOptions"
                    :key="status.value"
                    :label="status.label"
                    :value="status.value"
                  >
                    <div class="status-option">
                      <span class="status-dot" :class="status.colorClass"></span>
                      <span>{{ status.label }}</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>

              <!-- 高级搜索字段 -->
              <template v-if="isFilterExpanded">
                <el-form-item label="封装类型">
                  <el-select 
                    v-model="searchForm.packageType" 
                    placeholder="请选择封装类型"
                    clearable
                    multiple
                    class="w-full"
                  >
                    <el-option 
                      v-for="pkg in packageTypes"
                      :key="pkg.value"
                      :label="pkg.label"
                      :value="pkg.value"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item label="创建日期">
                  <el-date-picker
                    v-model="searchForm.createDate"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="w-full"
                  />
                </el-form-item>

                <el-form-item label="交付日期">
                  <el-date-picker
                    v-model="searchForm.deliveryDate"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="w-full"
                  />
                </el-form-item>
              </template>
            </div>
            
            <div class="search-actions">
              <el-button type="primary" @click="handleSearch">
                <svg class="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                  <circle cx="11" cy="11" r="8"/>
                  <path d="m21 21-4.35-4.35"/>
                </svg>
                搜索
              </el-button>
              <el-button @click="handleResetSearch">
                <svg class="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                  <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
                  <path d="M3 3v5h5"/>
                </svg>
                重置
              </el-button>
              <el-button link type="primary" @click="handleSaveSearch">保存搜索条件</el-button>
            </div>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <el-card class="table-card" shadow="never">
        <template #header>
          <div class="table-header">
            <div class="table-header__left">
              <h3 class="table-title">订单列表 ({{ pagination.total }})</h3>
              <div class="table-tools">
                <el-checkbox v-model="tableConfig.showIndex">显示序号</el-checkbox>
                <el-button link type="primary" @click="handleTableSettings">
                  <svg class="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <circle cx="12" cy="12" r="3"/>
                    <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                  </svg>
                  列设置
                </el-button>
              </div>
            </div>
            <div class="table-header__right">
              <el-button-group>
                <el-button 
                  :type="viewMode === 'table' ? 'primary' : ''" 
                  @click="viewMode = 'table'"
                >
                  <svg class="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <rect x="3" y="4" width="18" height="16" rx="2"/>
                    <path d="M7 8h10"/>
                    <path d="M7 12h4"/>
                  </svg>
                  列表
                </el-button>
                <el-button 
                  :type="viewMode === 'card' ? 'primary' : ''" 
                  @click="viewMode = 'card'"
                >
                  <svg class="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <rect x="3" y="3" width="7" height="7"/>
                    <rect x="14" y="3" width="7" height="7"/>
                    <rect x="14" y="14" width="7" height="7"/>
                    <rect x="3" y="14" width="7" height="7"/>
                  </svg>
                  卡片
                </el-button>
              </el-button-group>
            </div>
          </div>
        </template>

        <!-- 表格视图 -->
        <el-table
          v-if="viewMode === 'table'"
          :data="tableData"
          style="width: 100%"
          v-loading="loading"
          row-key="id"
          :default-sort="{ prop: 'createTime', order: 'descending' }"
          @sort-change="handleSortChange"
          @selection-change="handleSelectionChange"
          stripe
          class="orders-table"
        >
          <el-table-column 
            v-if="tableConfig.showIndex"
            type="index" 
            width="60" 
            align="center"
            label="序号"
          />
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="orderNumber" label="订单编号" width="160" sortable="custom">
            <template #default="{ row }">
              <div class="order-number">
                <el-link type="primary" @click="handleViewOrder(row)">
                  {{ row.orderNumber }}
                </el-link>
                <div class="order-priority" :class="`priority--${row.priority}`">
                  {{ getPriorityText(row.priority) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="customer" label="客户信息" width="200">
            <template #default="{ row }">
              <div class="customer-info">
                <div class="customer-name">{{ row.customer.name }}</div>
                <div class="customer-code">{{ row.customer.code }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="productInfo" label="产品信息" width="250">
            <template #default="{ row }">
              <div class="product-info">
                <div class="product-name">{{ row.productInfo.name }}</div>
                <div class="product-details">
                  <span class="package-type">{{ row.productInfo.packageType }}</span>
                  <span class="quantity">{{ row.productInfo.quantity.toLocaleString() }}K</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="120" sortable="custom">
            <template #default="{ row }">
              <el-tag 
                :type="getStatusTagType(row.status)" 
                effect="light"
                class="status-tag"
              >
                <span class="status-dot" :class="getStatusClass(row.status)"></span>
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="progress" label="进度" width="150">
            <template #default="{ row }">
              <div class="progress-info">
                <el-progress 
                  :percentage="row.progress" 
                  :color="getProgressColor(row.progress)"
                  :stroke-width="6"
                />
                <div class="progress-text">{{ row.progress }}%</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="createTime" label="创建时间" width="180" sortable="custom">
            <template #default="{ row }">
              <div class="time-info">
                <div class="date">{{ formatDate(row.createTime) }}</div>
                <div class="time">{{ formatTime(row.createTime) }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="deliveryDate" label="交付日期" width="120" sortable="custom">
            <template #default="{ row }">
              <div class="delivery-date" :class="getDeliveryClass(row.deliveryDate)">
                {{ formatDate(row.deliveryDate) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button link type="primary" size="small" @click="handleViewOrder(row)">
                  查看
                </el-button>
                <el-button link type="primary" size="small" @click="handleEditOrder(row)">
                  编辑
                </el-button>
                <el-button link type="success" size="small" @click="handleTrackOrder(row)">
                  追踪
                </el-button>
                <el-dropdown @command="(command) => handleDropdownAction(command, row)">
                  <el-button link type="info" size="small">
                    更多
                    <svg class="dropdown-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                      <path d="M6 9l6 6 6-6"/>
                    </svg>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="duplicate">复制订单</el-dropdown-item>
                      <el-dropdown-item command="export">导出详情</el-dropdown-item>
                      <el-dropdown-item command="history">查看历史</el-dropdown-item>
                      <el-dropdown-item divided command="cancel" :disabled="row.status === 'cancelled'">
                        取消订单
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 卡片视图 -->
        <div v-else class="orders-grid">
          <div 
            v-for="order in tableData" 
            :key="order.id" 
            class="order-card"
            @click="handleViewOrder(order)"
          >
            <div class="order-card__header">
              <div class="order-card__title">
                <span class="order-number">{{ order.orderNumber }}</span>
                <div class="order-priority" :class="`priority--${order.priority}`">
                  {{ getPriorityText(order.priority) }}
                </div>
              </div>
              <el-tag 
                :type="getStatusTagType(order.status)" 
                effect="light"
                class="status-tag"
              >
                {{ getStatusText(order.status) }}
              </el-tag>
            </div>
            
            <div class="order-card__content">
              <div class="order-info">
                <div class="info-item">
                  <span class="label">客户：</span>
                  <span class="value">{{ order.customer.name }}</span>
                </div>
                <div class="info-item">
                  <span class="label">产品：</span>
                  <span class="value">{{ order.productInfo.name }}</span>
                </div>
                <div class="info-item">
                  <span class="label">封装：</span>
                  <span class="value">{{ order.productInfo.packageType }}</span>
                </div>
                <div class="info-item">
                  <span class="label">数量：</span>
                  <span class="value">{{ order.productInfo.quantity.toLocaleString() }}K</span>
                </div>
              </div>
              
              <div class="order-progress">
                <div class="progress-header">
                  <span class="progress-label">完成进度</span>
                  <span class="progress-value">{{ order.progress }}%</span>
                </div>
                <el-progress 
                  :percentage="order.progress" 
                  :color="getProgressColor(order.progress)"
                  :stroke-width="4"
                  :show-text="false"
                />
              </div>
            </div>
            
            <div class="order-card__footer">
              <div class="order-dates">
                <div class="date-item">
                  <span class="date-label">创建：</span>
                  <span class="date-value">{{ formatDate(order.createTime) }}</span>
                </div>
                <div class="date-item">
                  <span class="date-label">交付：</span>
                  <span class="date-value" :class="getDeliveryClass(order.deliveryDate)">
                    {{ formatDate(order.deliveryDate) }}
                  </span>
                </div>
              </div>
              <div class="order-actions">
                <el-button size="small" @click.stop="handleEditOrder(order)">编辑</el-button>
                <el-button type="primary" size="small" @click.stop="handleTrackOrder(order)">追踪</el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 批量操作工具栏 -->
        <div v-if="selectedOrders.length > 0" class="batch-toolbar">
          <div class="batch-info">
            已选择 <strong>{{ selectedOrders.length }}</strong> 项
          </div>
          <div class="batch-actions">
            <el-button @click="handleBatchExport">批量导出</el-button>
            <el-button @click="handleBatchUpdate">批量更新</el-button>
            <el-button type="danger" @click="handleBatchCancel">批量取消</el-button>
          </div>
        </div>

        <!-- 分页器 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
          />
        </div>
      </el-card>
    </div>

    <!-- 新建/编辑订单对话框 -->
    <OrderDialog 
      v-model="dialogVisible"
      :order-data="currentOrder"
      :mode="dialogMode"
      @confirm="handleOrderSubmit"
    />

    <!-- 订单详情抽屉 -->
    <OrderDetailDrawer
      v-model="detailDrawerVisible"
      :order-id="selectedOrderId"
    />

    <!-- 订单追踪对话框 -->
    <OrderTrackingDialog
      v-model="trackingDialogVisible"
      :order-data="trackingOrder"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, defineAsyncComponent } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { Order, SearchForm, Pagination } from '@/types/order'

// 模拟组件（实际项目中会是独立文件）
const OrderDialog = defineAsyncComponent(() => import('@/components/orders/OrderDialog.vue'))
const OrderDetailDrawer = defineAsyncComponent(() => import('@/components/orders/OrderDetailDrawer.vue'))
const OrderTrackingDialog = defineAsyncComponent(() => import('@/components/orders/OrderTrackingDialog.vue'))

// 响应式数据
const loading = ref(false)
const isFilterExpanded = ref(false)
const viewMode = ref<'table' | 'card'>('table')
const selectedOrders = ref<Order[]>([])

// 搜索表单
const searchForm = reactive<SearchForm>({
  orderNumber: '',
  customer: '',
  status: [],
  packageType: [],
  createDate: null,
  deliveryDate: null
})

// 分页配置
const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 表格配置
const tableConfig = reactive({
  showIndex: true
})

// 对话框状态
const dialogVisible = ref(false)
const dialogMode = ref<'create' | 'edit'>('create')
const currentOrder = ref<Partial<Order>>({})

const detailDrawerVisible = ref(false)
const selectedOrderId = ref('')

const trackingDialogVisible = ref(false)
const trackingOrder = ref<Order | null>(null)

// 统计数据
const orderStats = ref([
  {
    key: 'total',
    label: '总订单数',
    value: '1,248',
    detail: '较昨日 +12',
    trend: 'up',
    changePercent: '+2.3%',
    trendClass: 'stat-trend--up',
    iconClass: 'stat-icon--primary',
    icon: '<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/>'
  },
  {
    key: 'processing',
    label: '进行中',
    value: '89',
    detail: '待处理 15 项',
    trend: 'stable',
    changePercent: '0%',
    trendClass: 'stat-trend--stable',
    iconClass: 'stat-icon--warning',
    icon: '<circle cx="12" cy="12" r="10"/><polyline points="12,6 12,12 16,14"/>'
  },
  {
    key: 'completed',
    label: '已完成',
    value: '1,156',
    detail: '本月完成率 98.5%',
    trend: 'up',
    changePercent: '+5.2%',
    trendClass: 'stat-trend--up',
    iconClass: 'stat-icon--success',
    icon: '<path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/><polyline points="22,4 12,14.01 9,11.01"/>'
  },
  {
    key: 'urgent',
    label: '紧急订单',
    value: '3',
    detail: '需优先处理',
    trend: 'down',
    changePercent: '-50%',
    trendClass: 'stat-trend--down',
    iconClass: 'stat-icon--danger',
    icon: '<path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/><line x1="12" y1="9" x2="12" y2="13"/><line x1="12" y1="17" x2="12.01" y2="17"/>'
  }
])

// 表格数据
const tableData = ref<Order[]>([])

// 选项数据
const customerOptions = [
  { label: 'Apple Inc.', value: 'apple' },
  { label: 'Samsung Electronics', value: 'samsung' },
  { label: 'TSMC', value: 'tsmc' },
  { label: 'Qualcomm', value: 'qualcomm' },
  { label: 'MediaTek', value: 'mediatek' }
]

const statusOptions = [
  { label: '待确认', value: 'pending', colorClass: 'status-dot--pending' },
  { label: '生产中', value: 'processing', colorClass: 'status-dot--processing' },
  { label: '测试中', value: 'testing', colorClass: 'status-dot--testing' },
  { label: '已完成', value: 'completed', colorClass: 'status-dot--completed' },
  { label: '已取消', value: 'cancelled', colorClass: 'status-dot--cancelled' }
]

const packageTypes = [
  { label: 'QFP (Quad Flat Package)', value: 'QFP' },
  { label: 'BGA (Ball Grid Array)', value: 'BGA' },
  { label: 'CSP (Chip Scale Package)', value: 'CSP' },
  { label: 'FC (Flip Chip)', value: 'FC' },
  { label: 'SOP (Small Outline Package)', value: 'SOP' },
  { label: 'TSSOP (Thin Shrink Small Outline Package)', value: 'TSSOP' }
]

// 方法定义
const toggleFilterExpanded = () => {
  isFilterExpanded.value = !isFilterExpanded.value
}

const handleSearch = async () => {
  loading.value = true
  try {
    await loadTableData()
    ElMessage.success('搜索完成')
  } finally {
    loading.value = false
  }
}

const handleResetSearch = () => {
  Object.assign(searchForm, {
    orderNumber: '',
    customer: '',
    status: [],
    packageType: [],
    createDate: null,
    deliveryDate: null
  })
  handleSearch()
}

const handleSaveSearch = () => {
  ElMessage.success('搜索条件已保存')
}

const handleCreateOrder = () => {
  currentOrder.value = {}
  dialogMode.value = 'create'
  dialogVisible.value = true
}

const handleEditOrder = (order: Order) => {
  currentOrder.value = { ...order }
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

const handleViewOrder = (order: Order) => {
  selectedOrderId.value = order.id
  detailDrawerVisible.value = true
}

const handleTrackOrder = (order: Order) => {
  trackingOrder.value = order
  trackingDialogVisible.value = true
}

const handleDropdownAction = (command: string, order: Order) => {
  switch (command) {
    case 'duplicate':
      handleDuplicateOrder(order)
      break
    case 'export':
      handleExportOrder(order)
      break
    case 'history':
      handleOrderHistory(order)
      break
    case 'cancel':
      handleCancelOrder(order)
      break
  }
}

const handleDuplicateOrder = (order: Order) => {
  const duplicatedOrder = { ...order, id: '', orderNumber: `${order.orderNumber}-COPY` }
  currentOrder.value = duplicatedOrder
  dialogMode.value = 'create'
  dialogVisible.value = true
}

const handleExportOrder = (order: Order) => {
  ElMessage.success(`正在导出订单 ${order.orderNumber} 的详细信息`)
}

const handleOrderHistory = (order: Order) => {
  ElMessage.info(`查看订单 ${order.orderNumber} 的历史记录`)
}

const handleCancelOrder = async (order: Order) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消订单 "${order.orderNumber}" 吗？此操作不可逆。`,
      '取消订单',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // 模拟取消订单API调用
    ElMessage.success(`订单 ${order.orderNumber} 已取消`)
    await loadTableData()
  } catch {
    ElMessage.info('已取消操作')
  }
}

const handleExportOrders = () => {
  ElMessage.success('正在导出订单数据...')
}

const handleTableSettings = () => {
  ElMessage.info('列设置功能开发中')
}

const handleSortChange = ({ column, prop, order }: any) => {
  console.log('排序变化:', { column, prop, order })
  loadTableData()
}

const handleSelectionChange = (selection: Order[]) => {
  selectedOrders.value = selection
}

const handleBatchExport = () => {
  ElMessage.success(`正在导出 ${selectedOrders.value.length} 个订单`)
}

const handleBatchUpdate = () => {
  ElMessage.info('批量更新功能开发中')
}

const handleBatchCancel = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要取消选中的 ${selectedOrders.value.length} 个订单吗？`,
      '批量取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    ElMessage.success('批量取消成功')
    selectedOrders.value = []
    await loadTableData()
  } catch {
    ElMessage.info('已取消操作')
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadTableData()
}

const handleCurrentChange = (current: number) => {
  pagination.currentPage = current
  loadTableData()
}

const handleOrderSubmit = async (orderData: Order) => {
  try {
    if (dialogMode.value === 'create') {
      ElMessage.success('订单创建成功')
    } else {
      ElMessage.success('订单更新成功')
    }
    dialogVisible.value = false
    await loadTableData()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 辅助方法
const getPriorityText = (priority: string) => {
  const priorityMap = {
    'low': '普通',
    'medium': '一般',
    'high': '紧急',
    'urgent': '特急'
  }
  return priorityMap[priority as keyof typeof priorityMap] || priority
}

const getStatusText = (status: string) => {
  const statusMap = {
    'pending': '待确认',
    'processing': '生产中',
    'testing': '测试中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const getStatusTagType = (status: string) => {
  const typeMap = {
    'pending': 'info',
    'processing': 'warning',
    'testing': 'primary',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return typeMap[status as keyof typeof typeMap] || 'info'
}

const getStatusClass = (status: string) => {
  return `status-dot--${status}`
}

const getProgressColor = (progress: number) => {
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

const getDeliveryClass = (deliveryDate: string) => {
  const today = new Date()
  const delivery = new Date(deliveryDate)
  const diffTime = delivery.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'delivery-overdue'
  if (diffDays <= 3) return 'delivery-urgent'
  if (diffDays <= 7) return 'delivery-warning'
  return 'delivery-normal'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟数据
    tableData.value = Array.from({ length: pagination.pageSize }, (_, index) => ({
      id: `order_${pagination.currentPage}_${index + 1}`,
      orderNumber: `CIM${String(pagination.currentPage).padStart(2, '0')}${String(index + 1).padStart(3, '0')}`,
      customer: {
        name: customerOptions[Math.floor(Math.random() * customerOptions.length)].label,
        code: `C${String(index + 1).padStart(3, '0')}`
      },
      productInfo: {
        name: `IC芯片产品-${index + 1}`,
        packageType: packageTypes[Math.floor(Math.random() * packageTypes.length)].value,
        quantity: Math.floor(Math.random() * 1000) + 100
      },
      status: statusOptions[Math.floor(Math.random() * statusOptions.length)].value,
      progress: Math.floor(Math.random() * 100),
      priority: ['low', 'medium', 'high', 'urgent'][Math.floor(Math.random() * 4)],
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      deliveryDate: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
    }))
    
    pagination.total = 1248
  } finally {
    loading.value = false
  }
}

// 组件挂载时初始化
onMounted(() => {
  loadTableData()
  console.log('📋 订单管理页面已加载')
})
</script>

<style lang="scss" scoped>
// IC封测CIM系统订单管理页面样式
// 遵循极简主义设计系统

.orders-page {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
  padding: var(--spacing-6);
  background-color: var(--color-bg-secondary);
  min-height: calc(100vh - var(--header-height));
  
  @media (max-width: 768px) {
    padding: var(--spacing-4);
    gap: var(--spacing-4);
  }
}

// ===== 页面头部 =====
.page-header {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  
  &__content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--spacing-6);
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-4);
    }
  }
}

.page-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-2);
  
  @media (max-width: 768px) {
    font-size: var(--font-size-xl);
  }
}

.page-subtitle {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
  margin: 0;
}

.page-header__actions {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
  
  @media (max-width: 768px) {
    width: 100%;
    justify-content: stretch;
    
    .el-button {
      flex: 1;
    }
  }
}

.button-icon {
  width: 16px;
  height: 16px;
  margin-right: var(--spacing-2);
}

// ===== 统计卡片 =====
.stats-cards {
  margin-bottom: var(--spacing-6);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
  
  @media (max-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
}

.stat-card {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-5);
  transition: all var(--transition-normal);
  
  &:hover {
    box-shadow: var(--shadow-md);
  }
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
  }
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-base);
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    width: 20px;
    height: 20px;
    stroke: currentColor;
    fill: none;
    stroke-width: 1.5;
  }
  
  &--primary {
    background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);
    color: var(--color-primary);
  }
  
  &--warning {
    background-color: color-mix(in srgb, var(--color-warning) 10%, transparent);
    color: var(--color-warning);
  }
  
  &--success {
    background-color: color-mix(in srgb, var(--color-success) 10%, transparent);
    color: var(--color-success);
  }
  
  &--danger {
    background-color: color-mix(in srgb, var(--color-error) 10%, transparent);
    color: var(--color-error);
  }
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  
  svg {
    width: 14px;
    height: 14px;
  }
  
  &--up {
    color: var(--color-success);
  }
  
  &--down {
    color: var(--color-error);
  }
  
  &--stable {
    color: var(--color-text-tertiary);
  }
}

.stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-1);
}

.stat-detail {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

// ===== 搜索筛选区域 =====
.filter-section {
  .filter-card {
    :deep(.el-card__header) {
      padding: var(--spacing-4) var(--spacing-5);
      border-bottom: 1px solid var(--color-border-light);
    }
    
    :deep(.el-card__body) {
      padding: 0;
    }
  }
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0;
}

.filter-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.toggle-icon {
  width: 16px;
  height: 16px;
  transition: transform var(--transition-fast);
  
  &--expanded {
    transform: rotate(180deg);
  }
}

.filter-content {
  max-height: 80px;
  overflow: hidden;
  transition: max-height var(--transition-normal);
  
  &--expanded {
    max-height: 400px;
  }
}

.search-form {
  padding: var(--spacing-5);
}

.search-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.search-actions {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--color-border-light);
}

.status-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  
  &--pending {
    background-color: var(--color-info);
  }
  
  &--processing {
    background-color: var(--color-warning);
  }
  
  &--testing {
    background-color: var(--color-primary);
  }
  
  &--completed {
    background-color: var(--color-success);
  }
  
  &--cancelled {
    background-color: var(--color-error);
  }
}

// ===== 表格区域 =====
.table-section {
  flex: 1;
  
  .table-card {
    :deep(.el-card__header) {
      padding: var(--spacing-4) var(--spacing-5);
      border-bottom: 1px solid var(--color-border-light);
    }
    
    :deep(.el-card__body) {
      padding: 0;
    }
  }
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: stretch;
  }
  
  &__left {
    display: flex;
    align-items: center;
    gap: var(--spacing-6);
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-3);
      align-items: flex-start;
    }
  }
}

.table-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0;
}

.table-tools {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

// ===== 表格样式 =====
.orders-table {
  :deep(.el-table__header) {
    background-color: var(--color-bg-tertiary);
  }
  
  :deep(.el-table__body) {
    tr:hover > td {
      background-color: var(--color-bg-hover) !important;
    }
  }
  
  :deep(.el-table td),
  :deep(.el-table th) {
    border-bottom: 1px solid var(--color-border-light);
  }
  
  .order-number {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
  }
  
  .order-priority {
    font-size: var(--font-size-xs);
    padding: 2px var(--spacing-1);
    border-radius: var(--radius-sm);
    text-align: center;
    
    &.priority--low {
      background-color: var(--color-info);
      color: white;
    }
    
    &.priority--medium {
      background-color: var(--color-primary);
      color: white;
    }
    
    &.priority--high {
      background-color: var(--color-warning);
      color: white;
    }
    
    &.priority--urgent {
      background-color: var(--color-error);
      color: white;
    }
  }
  
  .customer-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    
    .customer-name {
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }
    
    .customer-code {
      font-size: var(--font-size-xs);
      color: var(--color-text-tertiary);
    }
  }
  
  .product-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    
    .product-name {
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }
    
    .product-details {
      display: flex;
      gap: var(--spacing-3);
      
      .package-type {
        font-size: var(--font-size-xs);
        padding: 2px var(--spacing-2);
        background-color: var(--color-bg-tertiary);
        border-radius: var(--radius-sm);
        color: var(--color-text-secondary);
      }
      
      .quantity {
        font-size: var(--font-size-xs);
        color: var(--color-text-tertiary);
      }
    }
  }
  
  .status-tag {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    
    .status-dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
    }
  }
  
  .progress-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    
    .progress-text {
      font-size: var(--font-size-xs);
      color: var(--color-text-secondary);
      text-align: center;
    }
  }
  
  .time-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    
    .date {
      color: var(--color-text-primary);
      font-size: var(--font-size-sm);
    }
    
    .time {
      color: var(--color-text-tertiary);
      font-size: var(--font-size-xs);
    }
  }
  
  .delivery-date {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    
    &.delivery-overdue {
      color: var(--color-error);
    }
    
    &.delivery-urgent {
      color: var(--color-warning);
    }
    
    &.delivery-warning {
      color: var(--color-primary);
    }
    
    &.delivery-normal {
      color: var(--color-text-secondary);
    }
  }
  
  .action-buttons {
    display: flex;
    gap: var(--spacing-2);
    flex-wrap: wrap;
  }
  
  .dropdown-icon {
    width: 14px;
    height: 14px;
    margin-left: var(--spacing-1);
  }
}

// ===== 卡片视图 =====
.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-4);
  padding: var(--spacing-5);
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    padding: var(--spacing-4);
  }
}

.order-card {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-5);
  transition: all var(--transition-normal);
  cursor: pointer;
  
  &:hover {
    box-shadow: var(--shadow-lg);
    border-color: var(--color-primary);
    transform: translateY(-2px);
  }
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-4);
    
    .order-card__title {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-1);
      
      .order-number {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-medium);
        color: var(--color-text-primary);
      }
      
      .order-priority {
        align-self: flex-start;
      }
    }
  }
  
  &__content {
    margin-bottom: var(--spacing-4);
  }
  
  &__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-4);
    border-top: 1px solid var(--color-border-light);
  }
}

.order-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
  
  .info-item {
    display: flex;
    flex-direction: column;
    gap: 2px;
    
    .label {
      font-size: var(--font-size-xs);
      color: var(--color-text-tertiary);
    }
    
    .value {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }
  }
}

.order-progress {
  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-2);
    
    .progress-label {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }
    
    .progress-value {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }
  }
}

.order-dates {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  
  .date-item {
    display: flex;
    gap: var(--spacing-2);
    
    .date-label {
      font-size: var(--font-size-xs);
      color: var(--color-text-tertiary);
      min-width: 36px;
    }
    
    .date-value {
      font-size: var(--font-size-xs);
      color: var(--color-text-secondary);
    }
  }
}

.order-actions {
  display: flex;
  gap: var(--spacing-2);
}

// ===== 批量操作工具栏 =====
.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-5);
  background-color: var(--color-primary);
  color: white;
  margin: 0 -1px -1px -1px;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  
  .batch-info {
    font-weight: var(--font-weight-medium);
  }
  
  .batch-actions {
    display: flex;
    gap: var(--spacing-3);
    
    .el-button {
      background-color: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);
      color: white;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
      
      &.el-button--danger {
        background-color: var(--color-error);
        border-color: var(--color-error);
        
        &:hover {
          background-color: color-mix(in srgb, var(--color-error) 80%, black);
        }
      }
    }
  }
}

// ===== 分页器 =====
.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: var(--spacing-5);
  border-top: 1px solid var(--color-border-light);
}

// ===== 响应式优化 =====
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .search-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .orders-page {
    padding: var(--spacing-4);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .search-grid {
    grid-template-columns: 1fr;
  }
  
  .orders-grid {
    grid-template-columns: 1fr;
  }
  
  .batch-toolbar {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: stretch;
  }
}

// ===== 全局样式 =====
.w-full {
  width: 100%;
}

:deep(.el-input__prefix) {
  svg {
    width: 16px;
    height: 16px;
  }
}

:deep(.el-select .el-input .el-select__caret) {
  color: var(--color-text-tertiary);
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}
</style>