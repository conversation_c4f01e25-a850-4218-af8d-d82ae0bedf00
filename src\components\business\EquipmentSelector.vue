<template>
  <div class="equipment-selector">
    <el-select
      v-model="selectedValue"
      :placeholder="placeholder"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="remote"
      :remote="remote"
      :remote-method="handleRemoteSearch"
      :loading="loading"
      :disabled="disabled"
      class="w-full"
      @change="handleChange"
      @clear="handleClear"
    >
      <template v-if="showCreateOption && !remote" #header>
        <el-button 
          type="primary" 
          size="small" 
          class="w-full mb-2"
          @click="handleCreateNew"
        >
          <Plus class="w-4 h-4 mr-1" />
          新建设备
        </el-button>
      </template>
      
      <el-option
        v-for="equipment in currentOptions"
        :key="equipment.id"
        :label="formatOptionLabel(equipment)"
        :value="returnValue === 'id' ? equipment.id : equipment"
        class="equipment-option"
      >
        <div class="equipment-option-content">
          <div class="equipment-info">
            <span class="equipment-code">{{ equipment.equipmentCode }}</span>
            <span class="equipment-name">{{ equipment.equipmentName }}</span>
            <el-tag 
              :type="getStatusTagType(equipment.status)" 
              size="small"
            >
              {{ getStatusText(equipment.status) }}
            </el-tag>
          </div>
          <div class="equipment-details">
            <el-tag size="small" type="info">{{ getTypeText(equipment.type) }}</el-tag>
            <span class="equipment-model">{{ equipment.model }}</span>
            <span class="equipment-location">
              {{ equipment.location.building }}-{{ equipment.location.room }}
            </span>
          </div>
        </div>
      </el-option>
      
      <template v-if="currentOptions.length === 0 && !loading" #empty>
        <div class="empty-content">
          <div class="empty-icon">🏭</div>
          <div class="empty-text">暂无设备数据</div>
          <el-button 
            v-if="showCreateOption" 
            type="primary" 
            size="small" 
            @click="handleCreateNew"
          >
            新建设备
          </el-button>
        </div>
      </template>
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import type { Equipment, EquipmentStatus, EquipmentType } from '@/types/basicData'
import { searchEquipmentSuggestions } from '@/api/basicData'
import { debounce } from '@/utils'

interface Props {
  /** 当前选中的值 */
  modelValue?: string | string[] | Equipment | Equipment[]
  /** 占位符文本 */
  placeholder?: string
  /** 是否多选 */
  multiple?: boolean
  /** 是否可清空 */
  clearable?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 是否远程搜索 */
  remote?: boolean
  /** 本地选项数据 */
  options?: Equipment[]
  /** 返回值类型 */
  returnValue?: 'id' | 'object'
  /** 是否显示创建选项 */
  showCreateOption?: boolean
  /** 最小搜索长度 */
  minSearchLength?: number
  /** 设备类型筛选 */
  equipmentTypes?: EquipmentType[]
  /** 设备状态筛选 */
  equipmentStatuses?: EquipmentStatus[]
}

interface Emits {
  'update:modelValue': [value: string | string[] | Equipment | Equipment[] | null]
  'change': [equipment: Equipment | Equipment[] | null]
  'select': [equipment: Equipment]
  'create-new': []
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择设备',
  multiple: false,
  clearable: true,
  disabled: false,
  remote: true,
  returnValue: 'object',
  showCreateOption: false,
  minSearchLength: 2
})

const emit = defineEmits<Emits>()

// 状态
const loading = ref(false)
const remoteOptions = ref<Equipment[]>([])
const searchKeyword = ref('')

// 计算属性
const selectedValue = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

const currentOptions = computed(() => {
  let options = props.remote ? remoteOptions.value : (props.options || [])
  
  // 设备类型筛选
  if (props.equipmentTypes && props.equipmentTypes.length > 0) {
    options = options.filter(equipment => props.equipmentTypes!.includes(equipment.type))
  }
  
  // 设备状态筛选
  if (props.equipmentStatuses && props.equipmentStatuses.length > 0) {
    options = options.filter(equipment => props.equipmentStatuses!.includes(equipment.status))
  }
  
  return options
})

// 格式化选项标签
const formatOptionLabel = (equipment: Equipment) => {
  return `${equipment.equipmentCode} - ${equipment.equipmentName}`
}

// 获取设备状态文本
const getStatusText = (status: EquipmentStatus) => {
  const statusMap = {
    running: '运行中',
    idle: '空闲',
    maintenance: '维护中',
    error: '故障',
    setup: '设置中',
    disabled: '停用'
  }
  return statusMap[status] || status
}

// 获取设备状态标签类型
const getStatusTagType = (status: EquipmentStatus) => {
  const typeMap = {
    running: 'success',
    idle: 'info',
    maintenance: 'warning',
    error: 'danger',
    setup: 'primary',
    disabled: 'info'
  }
  return typeMap[status] || 'info'
}

// 获取设备类型文本
const getTypeText = (type: EquipmentType) => {
  const typeMap = {
    prober: '探针台',
    tester: '测试机',
    die_bonder: '贴片机',
    wire_bonder: '金线键合机',
    molding_press: '塑封机',
    deflash_system: '去飞边机',
    laser_marker: '激光打标机',
    trim_form: '切筋成形机',
    handler: '分选机',
    vision_system: '视觉检测系统'
  }
  return typeMap[type] || type
}

// 远程搜索处理
const handleRemoteSearch = debounce(async (query: string) => {
  if (!props.remote) return
  
  searchKeyword.value = query
  if (!query || query.length < props.minSearchLength) {
    remoteOptions.value = []
    return
  }

  loading.value = true
  try {
    const suggestions = await searchEquipmentSuggestions(query)
    // 转换搜索建议为Equipment对象（实际应用中需要完整的Equipment数据）
    remoteOptions.value = suggestions.map(suggestion => ({
      id: suggestion.id,
      equipmentCode: suggestion.code,
      equipmentName: suggestion.name,
      type: 'prober' as EquipmentType, // 临时填充
      model: 'Model',
      manufacturer: 'Manufacturer',
      location: {
        building: 'FAB-1',
        floor: '1F',
        room: 'Room-01',
        position: 'A-01'
      },
      specifications: {
        capacity: 100,
        accuracy: '±1μm',
        repeatability: '±0.5μm',
        workingEnvironment: {
          temperature: '20°C ± 2°C',
          humidity: '45% ± 5%',
          cleanLevel: 'Class 1000'
        }
      },
      status: 'running' as EquipmentStatus,
      operationalInfo: {
        installDate: '',
        warrantyExpiry: '',
        lastMaintenanceDate: '',
        nextMaintenanceDate: '',
        operatingHours: 0,
        cycleCount: 0
      },
      capabilities: {
        supportedPackages: [],
        supportedProcesses: [],
        maxWaferSize: 8,
        parallelSites: 1
      },
      maintenanceInfo: {
        pmInterval: 720,
        pmItems: [],
        sparePartsList: []
      },
      createdAt: '',
      updatedAt: '',
      createdBy: ''
    }))
  } catch (error) {
    console.error('搜索设备失败:', error)
    remoteOptions.value = []
  } finally {
    loading.value = false
  }
}, 300)

// 选择变化处理
const handleChange = (value: any) => {
  if (value === null || value === undefined) {
    emit('change', null)
    return
  }

  if (props.multiple) {
    const equipments = Array.isArray(value) ? value : [value]
    if (props.returnValue === 'id') {
      const selectedEquipments = currentOptions.value.filter(option => 
        equipments.includes(option.id)
      )
      emit('change', selectedEquipments)
    } else {
      emit('change', equipments as Equipment[])
    }
  } else {
    if (props.returnValue === 'id') {
      const selectedEquipment = currentOptions.value.find(option => option.id === value)
      emit('change', selectedEquipment || null)
      if (selectedEquipment) {
        emit('select', selectedEquipment)
      }
    } else {
      emit('change', value as Equipment)
      emit('select', value as Equipment)
    }
  }
}

// 清空处理
const handleClear = () => {
  emit('change', null)
  if (props.remote) {
    remoteOptions.value = []
    searchKeyword.value = ''
  }
}

// 创建新设备
const handleCreateNew = () => {
  emit('create-new')
}

// 初始化
onMounted(() => {
  if (!props.remote && props.options) {
    // 非远程模式，使用传入的options
  }
})
</script>

<style lang="scss" scoped>
.equipment-selector {
  .equipment-option-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;
  }
  
  .equipment-info {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .equipment-code {
      font-weight: 600;
      color: var(--color-text-primary);
      font-size: var(--font-size-sm);
    }
    
    .equipment-name {
      color: var(--color-text-secondary);
      font-size: var(--font-size-sm);
      flex: 1;
    }
  }
  
  .equipment-details {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .equipment-model,
    .equipment-location {
      font-size: var(--font-size-xs);
      color: var(--color-text-tertiary);
    }
  }
  
  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: var(--spacing-4);
    
    .empty-icon {
      font-size: 24px;
      opacity: 0.5;
    }
    
    .empty-text {
      color: var(--color-text-secondary);
      font-size: var(--font-size-sm);
    }
  }
}

:deep(.el-select-dropdown__item) {
  height: auto;
  padding: 8px 12px;
  line-height: 1.4;
}
</style>