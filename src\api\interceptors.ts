/**
 * IC封测CIM系统 - API拦截器
 * API Interceptors for Request/Response Handling
 */

import { ElMessage } from 'element-plus'
import type { ApiResponse, ApiPageResponse } from './config'
import { ApiErrorCode, MockApiHelper } from './config'

// 请求配置接口
export interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  params?: Record<string, any>
  data?: any
  headers?: Record<string, string>
  timeout?: number
  showLoading?: boolean
  showError?: boolean
  showSuccess?: boolean
  successMessage?: string
}

// 响应拦截器配置
export interface InterceptorConfig {
  enableErrorHandler?: boolean
  enableSuccessHandler?: boolean
  enableLoadingHandler?: boolean
  errorMessageDuration?: number
  successMessageDuration?: number
}

// 全局loading实例
let loadingInstance: any = null
let loadingCount = 0

/**
 * API拦截器类
 */
export class ApiInterceptor {
  private config: InterceptorConfig

  constructor(config: InterceptorConfig = {}) {
    this.config = {
      enableErrorHandler: true,
      enableSuccessHandler: false,
      enableLoadingHandler: true,
      errorMessageDuration: 3000,
      successMessageDuration: 2000,
      ...config
    }
  }

  /**
   * 请求拦截器
   */
  onRequest(config: RequestConfig): RequestConfig {
    // 显示加载状态
    if (config.showLoading !== false && this.config.enableLoadingHandler) {
      this.showLoading()
    }

    // 添加默认headers
    const defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...config.headers
    }

    // 添加认证token（实际项目中从store获取）
    const token = localStorage.getItem('auth_token')
    if (token) {
      defaultHeaders.Authorization = `Bearer ${token}`
    }

    return {
      ...config,
      headers: defaultHeaders,
      timeout: config.timeout || 30000
    }
  }

  /**
   * 响应拦截器 - 成功
   */
  onResponseSuccess<T>(
    response: ApiResponse<T> | ApiPageResponse<T>,
    config: RequestConfig
  ): ApiResponse<T> | ApiPageResponse<T> {
    // 隐藏加载状态
    if (config.showLoading !== false && this.config.enableLoadingHandler) {
      this.hideLoading()
    }

    // 检查业务状态码
    if (!response.success) {
      throw new Error(response.message || '请求失败')
    }

    // 显示成功消息
    if (config.showSuccess && this.config.enableSuccessHandler) {
      const message = config.successMessage || response.message
      if (message) {
        ElMessage.success({
          message,
          duration: this.config.successMessageDuration
        })
      }
    }

    return response
  }

  /**
   * 响应拦截器 - 错误
   */
  onResponseError(error: any, config: RequestConfig): Promise<never> {
    // 隐藏加载状态
    if (config.showLoading !== false && this.config.enableLoadingHandler) {
      this.hideLoading()
    }

    // 处理不同类型的错误
    let errorMessage = '请求失败'
    let errorCode = ApiErrorCode.INTERNAL_SERVER_ERROR

    if (error.response) {
      // HTTP错误响应
      const { status, data } = error.response
      errorCode = status
      errorMessage = data?.message || this.getHttpErrorMessage(status)
    } else if (error.request) {
      // 网络错误
      errorMessage = '网络连接失败，请检查网络设置'
      errorCode = ApiErrorCode.SERVICE_UNAVAILABLE
    } else {
      // 其他错误
      errorMessage = error.message || '未知错误'
    }

    // 显示错误消息
    if (config.showError !== false && this.config.enableErrorHandler) {
      this.showErrorMessage(errorMessage, errorCode)
    }

    // 特殊错误处理
    this.handleSpecialErrors(errorCode)

    return Promise.reject({
      code: errorCode,
      message: errorMessage,
      originalError: error
    })
  }

  /**
   * 显示加载状态
   */
  private showLoading(): void {
    loadingCount++
    if (loadingCount === 1) {
      loadingInstance = ElLoading.service({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)',
        spinner: 'el-icon-loading'
      })
    }
  }

  /**
   * 隐藏加载状态
   */
  private hideLoading(): void {
    loadingCount = Math.max(0, loadingCount - 1)
    if (loadingCount === 0 && loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
  }

  /**
   * 显示错误消息
   */
  private showErrorMessage(message: string, code: ApiErrorCode): void {
    const messageType = code >= 500 ? 'error' : 'warning'
    
    ElMessage({
      type: messageType,
      message,
      duration: this.config.errorMessageDuration,
      showClose: true,
      dangerouslyUseHTMLString: false
    })
  }

  /**
   * 获取HTTP错误消息
   */
  private getHttpErrorMessage(status: number): string {
    const messages: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权，请重新登录',
      403: '权限不足，无法访问',
      404: '请求的资源不存在',
      405: '请求方法不被允许',
      408: '请求超时',
      409: '请求冲突',
      422: '数据验证失败',
      429: '请求过于频繁，请稍后重试',
      500: '服务器内部错误',
      501: '服务器不支持此请求',
      502: '网关错误',
      503: '服务暂时不可用',
      504: '网关超时'
    }

    return messages[status] || `HTTP错误 ${status}`
  }

  /**
   * 处理特殊错误
   */
  private handleSpecialErrors(code: ApiErrorCode): void {
    switch (code) {
      case ApiErrorCode.UNAUTHORIZED:
        // 清除认证信息
        localStorage.removeItem('auth_token')
        localStorage.removeItem('user_info')
        
        // 跳转到登录页（实际项目中使用路由跳转）
        if (window.location.pathname !== '/login') {
          window.location.href = '/login'
        }
        break

      case ApiErrorCode.FORBIDDEN:
        // 权限不足处理
        break

      case ApiErrorCode.SERVICE_UNAVAILABLE:
        // 服务不可用处理
        break
    }
  }

  /**
   * 重置加载状态（用于页面卸载时清理）
   */
  resetLoading(): void {
    loadingCount = 0
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
  }
}

/**
 * 模拟HTTP客户端
 */
export class MockHttpClient {
  private interceptor: ApiInterceptor

  constructor(interceptorConfig?: InterceptorConfig) {
    this.interceptor = new ApiInterceptor(interceptorConfig)
  }

  /**
   * 通用请求方法
   */
  async request<T>(config: RequestConfig): Promise<ApiResponse<T> | ApiPageResponse<T>> {
    try {
      // 应用请求拦截器
      const processedConfig = this.interceptor.onRequest(config)

      // 模拟网络延迟
      await MockApiHelper.delay()

      // 模拟网络错误
      MockApiHelper.simulateNetworkError(0.02) // 2%概率网络错误

      // 模拟服务器错误
      const errorResponse = MockApiHelper.simulateServerError(0.01) // 1%概率服务器错误
      if (!errorResponse.success) {
        throw new Error(errorResponse.message)
      }

      // 实际项目中这里是真实的HTTP请求
      // 现在直接返回模拟数据
      const response = await this.simulateApiCall<T>(processedConfig)

      // 应用响应拦截器
      return this.interceptor.onResponseSuccess(response, processedConfig)
    } catch (error) {
      // 应用错误拦截器
      return this.interceptor.onResponseError(error, config)
    }
  }

  /**
   * GET请求
   */
  async get<T>(url: string, params?: Record<string, any>, config?: Partial<RequestConfig>): Promise<ApiResponse<T> | ApiPageResponse<T>> {
    return this.request<T>({
      url,
      method: 'GET',
      params,
      ...config
    })
  }

  /**
   * POST请求
   */
  async post<T>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T> | ApiPageResponse<T>> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      showSuccess: true,
      ...config
    })
  }

  /**
   * PUT请求
   */
  async put<T>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T> | ApiPageResponse<T>> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      showSuccess: true,
      successMessage: '更新成功',
      ...config
    })
  }

  /**
   * DELETE请求
   */
  async delete<T>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T> | ApiPageResponse<T>> {
    return this.request<T>({
      url,
      method: 'DELETE',
      showSuccess: true,
      successMessage: '删除成功',
      ...config
    })
  }

  /**
   * 模拟API调用（实际项目中替换为真实HTTP请求）
   */
  private async simulateApiCall<T>(config: RequestConfig): Promise<ApiResponse<T> | ApiPageResponse<T>> {
    // 这里应该是真实的API调用
    // 现在返回模拟成功响应
    return MockApiHelper.createSuccessResponse(null as T, '操作成功')
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.interceptor.resetLoading()
  }
}

// 创建全局HTTP客户端实例
export const httpClient = new MockHttpClient({
  enableErrorHandler: true,
  enableSuccessHandler: true,
  enableLoadingHandler: true
})

// 导出常用方法
export const { get, post, put, delete: del } = httpClient

export default {
  ApiInterceptor,
  MockHttpClient,
  httpClient,
  get,
  post,
  put,
  del
}