/**
 * IC封测CIM系统 - 客户管理API
 * Customer Management API Services
 */

import type {
  Customer,
  CustomerQueryParams,
  CustomerListResponse,
  CreateCustomerData
} from '@/types/customer'
import type { CustomerStatus, ContactRole, ContactStatus } from '@/types/customer'
import { mockApiServer } from './mockServer'
import { allMockCustomers, customerStats } from '@/utils/mockData/customers'
import type { ApiPageResponse } from './config'

// 延迟函数用于模拟网络请求
const delay = (ms: number = 300): Promise<void> => new Promise(resolve => setTimeout(resolve, ms))

/**
 * 获取客户列表
 */
export async function getCustomers(
  params: CustomerQueryParams = {}
): Promise<CustomerListResponse> {
  const response = (await mockApiServer.handleRequest(
    'GET',
    '/customers',
    params
  )) as ApiPageResponse<Customer>
  return {
    data: response.data,
    total: response.pagination.total,
    page: response.pagination.current,
    pageSize: response.pagination.pageSize,
    totalPages: response.pagination.totalPages
  }
}

/**
 * 根据ID获取客户详情
 */
export async function getCustomerById(id: string): Promise<Customer | null> {
  await delay(200)

  const customer = allMockCustomers.find(c => c.id === id)
  return customer || null
}

/**
 * 创建新客户
 */
export async function createCustomer(data: CreateCustomerData): Promise<Customer> {
  await delay(500)

  // 生成新的客户ID和编号
  const newId = `customer_${Date.now()}`
  const newCode = `C${String(allMockCustomers.length + 1).padStart(3, '0')}`

  const newCustomer: Customer = {
    id: newId,
    ...data,
    code: newCode,
    contact: data.contact ? {
      ...data.contact,
      id: `${newId}_contact_1`,
      customerId: newId
    } : {
      id: `${newId}_contact_1`,
      name: '默认联系人',
      email: '',
      phone: '',
      position: '',
      department: '',
      role: 'BusinessManager' as ContactRole,
      contactType: ['business'],
      status: 'active' as ContactStatus,
      importance: 3,
      trustLevel: 3,
      influenceScore: 5,
      isDecisionMaker: false,
      isPrimaryContact: true,
      customerId: newId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'system'
    },
    cooperationHistory: {
      firstCooperationTime: new Date().toISOString(),
      cooperationYears: 0,
      totalOrders: 0,
      totalSalesAmount: 0,
      ordersLast12Months: 0,
      salesLast12Months: 0,
      averageOrderAmount: 0,
      satisfactionScore: 5.0
    },
    status: 'active' as CustomerStatus,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'current_user'
  }

  // 联系人ID已在创建时生成

  // 模拟保存到数据库
  allMockCustomers.push(newCustomer)

  return newCustomer
}

/**
 * 更新客户信息
 */
export async function updateCustomer(
  id: string,
  data: Partial<CreateCustomerData>
): Promise<Customer> {
  await delay(400)

  const customerIndex = allMockCustomers.findIndex(c => c.id === id)
  if (customerIndex === -1) {
    throw new Error('客户不存在')
  }

  const updatedCustomer: Customer = {
    ...allMockCustomers[customerIndex],
    ...data,
    contact: data.contact ? {
      ...data.contact,
      id: allMockCustomers[customerIndex].contact.id
    } : allMockCustomers[customerIndex].contact,
    updatedAt: new Date().toISOString()
  }

  allMockCustomers[customerIndex] = updatedCustomer

  return updatedCustomer
}

/**
 * 删除客户
 */
export async function deleteCustomer(id: string): Promise<boolean> {
  await delay(300)

  const customerIndex = allMockCustomers.findIndex(c => c.id === id)
  if (customerIndex === -1) {
    return false
  }

  allMockCustomers.splice(customerIndex, 1)
  return true
}

/**
 * 批量删除客户
 */
export async function batchDeleteCustomers(
  ids: string[]
): Promise<{ success: number; failed: number }> {
  await delay(600)

  let success = 0
  let failed = 0

  for (const id of ids) {
    const customerIndex = allMockCustomers.findIndex(c => c.id === id)
    if (customerIndex !== -1) {
      allMockCustomers.splice(customerIndex, 1)
      success++
    } else {
      failed++
    }
  }

  return { success, failed }
}

/**
 * 获取客户统计信息
 */
export async function getCustomerStats() {
  await delay(200)

  return customerStats
}

/**
 * 导出客户数据
 */
export async function exportCustomers(params: CustomerQueryParams = {}): Promise<Blob> {
  await delay(1000) // 模拟导出处理时间

  const { data } = await getCustomers(params)

  // 转换为CSV格式
  const headers = [
    '客户编号',
    '客户名称',
    '客户类型',
    '客户等级',
    '行业分类',
    '联系人',
    '联系电话',
    '邮箱',
    '合作年限',
    '历史订单总数',
    '历史销售额',
    '客户状态'
  ]

  const csvContent = [
    headers.join(','),
    ...data.map(customer =>
      [
        customer.code,
        customer.name,
        customer.type,
        customer.level,
        customer.industryType,
        customer.contact?.name || '',
        customer.contact?.mobile || '',
        customer.contact?.email || '',
        0, // customer.businessHistory?.cooperationYears || 0,
        0, // customer.businessHistory?.totalOrders || 0,
        0, // customer.businessHistory?.totalSalesAmount || 0,
        customer.status
      ].join(',')
    )
  ].join('\n')

  return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
}

/**
 * 搜索客户建议（用于自动完成）
 */
export async function searchCustomerSuggestions(
  keyword: string
): Promise<Array<{ id: string; name: string; code: string }>> {
  await delay(150)

  if (!keyword || keyword.length < 2) {
    return []
  }

  const suggestions = allMockCustomers
    .filter(
      customer =>
        customer.name.toLowerCase().includes(keyword.toLowerCase()) ||
        customer.code.toLowerCase().includes(keyword.toLowerCase())
    )
    .slice(0, 10)
    .map(customer => ({
      id: customer.id,
      name: customer.name,
      code: customer.code
    }))

  return suggestions
}
