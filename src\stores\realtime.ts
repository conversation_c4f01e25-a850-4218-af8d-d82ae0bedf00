/**
 * IC封测CIM系统 - 实时数据状态管理
 * Real-time Data Store for IC Packaging & Testing CIM System
 */

import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
// Element Plus组件通过unplugin-auto-import自动导入
import { useAppStore } from './app'

/**
 * 实时设备状态接口
 */
export interface RealtimeEquipmentStatus {
  equipmentId: string
  equipmentName: string
  status: 'online' | 'offline' | 'error' | 'maintenance' | 'idle' | 'running'
  utilization: number // 0-100
  temperature?: number
  pressure?: number
  vibration?: number
  speed?: number
  alarmCount: number
  lastUpdate: Date
  location?: string
  operator?: string
}

/**
 * 实时生产数据接口
 */
export interface RealtimeProductionData {
  lineId: string
  lineName: string
  productId: string
  productName: string
  targetQuantity: number
  actualQuantity: number
  goodQuantity: number
  defectQuantity: number
  yieldRate: number // 0-100
  efficiency: number // 0-100
  taktTime: number // 秒
  cycleTime: number // 秒
  oeeRate: number // 0-100
  status: 'stopped' | 'running' | 'setup' | 'breakdown'
  shift: string
  startTime: Date
  lastUpdate: Date
}

/**
 * 实时质量数据接口
 */
export interface RealtimeQualityData {
  testerId: string
  testerName: string
  testProgram: string
  testedCount: number
  passCount: number
  failCount: number
  passRate: number // 0-100
  cpk: number
  averageTestTime: number // 秒
  binResults: Array<{
    bin: string
    count: number
    percentage: number
  }>
  lastUpdate: Date
}

/**
 * 实时环境数据接口
 */
export interface RealtimeEnvironmentData {
  areaId: string
  areaName: string
  temperature: number // 摄氏度
  humidity: number // 0-100
  pressure: number // Pa
  airFlow: number // m³/h
  particleCount: number // particles/m³
  cleanroomClass: string
  alarmStatus: 'normal' | 'warning' | 'critical'
  lastUpdate: Date
}

/**
 * 实时库存数据接口
 */
export interface RealtimeInventoryData {
  materialId: string
  materialName: string
  currentStock: number
  safetyStock: number
  unitOfMeasure: string
  location: string
  status: 'normal' | 'low' | 'out_of_stock' | 'expired'
  lastMovement: Date
  lastUpdate: Date
}

/**
 * 实时告警数据接口
 */
export interface RealtimeAlarmData {
  id: string
  source: string
  sourceId: string
  level: 'info' | 'warning' | 'error' | 'critical'
  type: 'equipment' | 'quality' | 'environment' | 'security' | 'system'
  title: string
  message: string
  status: 'active' | 'acknowledged' | 'resolved'
  timestamp: Date
  acknowledgedBy?: string
  acknowledgedAt?: Date
  resolvedAt?: Date
  data?: any
}

/**
 * WebSocket消息接口
 */
export interface WebSocketMessage {
  type: 'equipment' | 'production' | 'quality' | 'environment' | 'inventory' | 'alarm' | 'heartbeat'
  data: any
  timestamp: string
  source?: string
}

/**
 * 数据订阅接口
 */
export interface DataSubscription {
  id: string
  type: string
  filters?: Record<string, any>
  callback: (data: any) => void
  active: boolean
}

/**
 * 连接状态接口
 */
export interface ConnectionStatus {
  connected: boolean
  reconnecting: boolean
  lastConnected?: Date
  lastDisconnected?: Date
  reconnectAttempts: number
  latency: number
}

/**
 * 实时数据状态管理Store
 */
export const useRealtimeStore = defineStore('realtime', () => {
  const appStore = useAppStore()

  // WebSocket连接状态
  const connectionStatus = ref<ConnectionStatus>({
    connected: false,
    reconnecting: false,
    reconnectAttempts: 0,
    latency: 0
  })

  // 实时数据状态
  const equipmentStatuses = ref<Map<string, RealtimeEquipmentStatus>>(new Map())
  const productionData = ref<Map<string, RealtimeProductionData>>(new Map())
  const qualityData = ref<Map<string, RealtimeQualityData>>(new Map())
  const environmentData = ref<Map<string, RealtimeEnvironmentData>>(new Map())
  const inventoryData = ref<Map<string, RealtimeInventoryData>>(new Map())
  const alarmData = ref<Map<string, RealtimeAlarmData>>(new Map())

  // 数据订阅管理
  const subscriptions = ref<Map<string, DataSubscription>>(new Map())
  const dataBuffer = ref<Map<string, any[]>>(new Map())

  // 统计数据
  const statistics = ref({
    totalEquipment: 0,
    onlineEquipment: 0,
    activeAlarms: 0,
    criticalAlarms: 0,
    averageOEE: 0,
    totalProduction: 0,
    qualityYield: 0,
    lastUpdateTime: new Date()
  })

  // WebSocket实例
  let websocket: WebSocket | null = null
  let heartbeatInterval: number | null = null
  let reconnectTimeout: number | null = null

  // 计算属性
  const isConnected = computed(() => connectionStatus.value.connected)
  const equipmentList = computed(() => Array.from(equipmentStatuses.value.values()))
  const productionList = computed(() => Array.from(productionData.value.values()))
  const qualityList = computed(() => Array.from(qualityData.value.values()))
  const environmentList = computed(() => Array.from(environmentData.value.values()))
  const inventoryList = computed(() => Array.from(inventoryData.value.values()))
  const alarmList = computed(() => Array.from(alarmData.value.values()))

  const activeAlarms = computed(() => alarmList.value.filter(alarm => alarm.status === 'active'))

  const criticalAlarms = computed(() =>
    activeAlarms.value.filter(alarm => alarm.level === 'critical')
  )

  const equipmentByStatus = computed(() => {
    const groups: Record<string, RealtimeEquipmentStatus[]> = {}
    equipmentList.value.forEach(equipment => {
      if (!groups[equipment.status]) {
        groups[equipment.status] = []
      }
      groups[equipment.status].push(equipment)
    })
    return groups
  })

  /**
   * 连接WebSocket
   */
  const connect = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      try {
        // 清理现有连接
        disconnect()

        // 创建WebSocket连接
        const wsUrl = getWebSocketUrl()
        websocket = new WebSocket(wsUrl)

        // 连接打开事件
        websocket.onopen = () => {
          connectionStatus.value = {
            connected: true,
            reconnecting: false,
            lastConnected: new Date(),
            reconnectAttempts: 0,
            latency: 0
          }

          console.log('[Realtime] WebSocket connected successfully')
          appStore.showSuccess('实时数据连接已建立')

          // 启动心跳
          startHeartbeat()

          // 重新激活订阅
          reactivateSubscriptions()

          resolve()
        }

        // 消息接收事件
        websocket.onmessage = event => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data)
            handleWebSocketMessage(message)
          } catch (error) {
            console.error('[Realtime] Failed to parse WebSocket message:', error)
          }
        }

        // 连接错误事件
        websocket.onerror = error => {
          console.error('[Realtime] WebSocket error:', error)
          connectionStatus.value.connected = false
          reject(error)
        }

        // 连接关闭事件
        websocket.onclose = event => {
          console.log('[Realtime] WebSocket connection closed:', event.code, event.reason)

          connectionStatus.value.connected = false
          connectionStatus.value.lastDisconnected = new Date()

          // 停止心跳
          stopHeartbeat()

          // 如果不是主动关闭，则尝试重连
          if (event.code !== 1000 && !connectionStatus.value.reconnecting) {
            reconnect()
          }
        }

        // 连接超时处理
        setTimeout(() => {
          if (websocket?.readyState === WebSocket.CONNECTING) {
            websocket.close()
            reject(new Error('WebSocket connection timeout'))
          }
        }, 10000) // 10秒超时
      } catch (error) {
        console.error('[Realtime] Failed to create WebSocket connection:', error)
        reject(error)
      }
    })
  }

  /**
   * 断开WebSocket连接
   */
  const disconnect = (): void => {
    if (websocket) {
      websocket.close(1000, 'Manual disconnect')
      websocket = null
    }

    stopHeartbeat()

    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout)
      reconnectTimeout = null
    }

    connectionStatus.value.connected = false
    connectionStatus.value.reconnecting = false
  }

  /**
   * 重新连接
   */
  const reconnect = (): void => {
    if (connectionStatus.value.reconnecting) return

    connectionStatus.value.reconnecting = true
    connectionStatus.value.reconnectAttempts++

    const delay = Math.min(1000 * Math.pow(2, connectionStatus.value.reconnectAttempts - 1), 30000)

    console.log(
      `[Realtime] Attempting to reconnect (attempt ${connectionStatus.value.reconnectAttempts}) after ${delay}ms`
    )

    reconnectTimeout = window.setTimeout(async () => {
      try {
        await connect()
        connectionStatus.value.reconnecting = false
      } catch (error) {
        console.error('[Realtime] Reconnection failed:', error)

        // 限制重连次数
        if (connectionStatus.value.reconnectAttempts < 10) {
          reconnect()
        } else {
          connectionStatus.value.reconnecting = false
          appStore.showError('实时数据连接失败，请检查网络连接')
        }
      }
    }, delay)
  }

  /**
   * 发送WebSocket消息
   */
  const sendMessage = (message: any): boolean => {
    if (!websocket || websocket.readyState !== WebSocket.OPEN) {
      console.warn('[Realtime] WebSocket not connected, message not sent')
      return false
    }

    try {
      websocket.send(JSON.stringify(message))
      return true
    } catch (error) {
      console.error('[Realtime] Failed to send WebSocket message:', error)
      return false
    }
  }

  /**
   * 处理WebSocket消息
   */
  const handleWebSocketMessage = (message: WebSocketMessage): void => {
    // 更新延迟
    const latency = Date.now() - new Date(message.timestamp).getTime()
    connectionStatus.value.latency = latency

    switch (message.type) {
      case 'equipment':
        handleEquipmentData(message.data)
        break
      case 'production':
        handleProductionData(message.data)
        break
      case 'quality':
        handleQualityData(message.data)
        break
      case 'environment':
        handleEnvironmentData(message.data)
        break
      case 'inventory':
        handleInventoryData(message.data)
        break
      case 'alarm':
        handleAlarmData(message.data)
        break
      case 'heartbeat':
        // 心跳响应，更新连接状态
        break
      default:
        console.warn('[Realtime] Unknown message type:', message.type)
    }

    // 触发数据订阅回调
    triggerSubscriptionCallbacks(message.type, message.data)
  }

  /**
   * 处理设备数据
   */
  const handleEquipmentData = (data: RealtimeEquipmentStatus | RealtimeEquipmentStatus[]): void => {
    const equipmentArray = Array.isArray(data) ? data : [data]

    equipmentArray.forEach(equipment => {
      equipment.lastUpdate = new Date()
      equipmentStatuses.value.set(equipment.equipmentId, equipment)
    })

    updateStatistics()
  }

  /**
   * 处理生产数据
   */
  const handleProductionData = (data: RealtimeProductionData | RealtimeProductionData[]): void => {
    const productionArray = Array.isArray(data) ? data : [data]

    productionArray.forEach(production => {
      production.lastUpdate = new Date()
      productionData.value.set(production.lineId, production)
    })

    updateStatistics()
  }

  /**
   * 处理质量数据
   */
  const handleQualityData = (data: RealtimeQualityData | RealtimeQualityData[]): void => {
    const qualityArray = Array.isArray(data) ? data : [data]

    qualityArray.forEach(quality => {
      quality.lastUpdate = new Date()
      qualityData.value.set(quality.testerId, quality)
    })

    updateStatistics()
  }

  /**
   * 处理环境数据
   */
  const handleEnvironmentData = (
    data: RealtimeEnvironmentData | RealtimeEnvironmentData[]
  ): void => {
    const environmentArray = Array.isArray(data) ? data : [data]

    environmentArray.forEach(env => {
      env.lastUpdate = new Date()
      environmentData.value.set(env.areaId, env)
    })
  }

  /**
   * 处理库存数据
   */
  const handleInventoryData = (data: RealtimeInventoryData | RealtimeInventoryData[]): void => {
    const inventoryArray = Array.isArray(data) ? data : [data]

    inventoryArray.forEach(inventory => {
      inventory.lastUpdate = new Date()
      inventoryData.value.set(inventory.materialId, inventory)
    })
  }

  /**
   * 处理告警数据
   */
  const handleAlarmData = (data: RealtimeAlarmData | RealtimeAlarmData[]): void => {
    const alarmArray = Array.isArray(data) ? data : [data]

    alarmArray.forEach(alarm => {
      alarmData.value.set(alarm.id, alarm)

      // 新告警通知
      if (alarm.status === 'active' && alarm.level === 'critical') {
        appStore.addNotification({
          type: 'error',
          title: `关键告警: ${alarm.title}`,
          message: alarm.message
        })
      }
    })

    updateStatistics()
  }

  /**
   * 订阅数据类型
   */
  const subscribe = (
    type: string,
    callback: (data: any) => void,
    filters?: Record<string, any>
  ): string => {
    const id = `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const subscription: DataSubscription = {
      id,
      type,
      filters,
      callback,
      active: true
    }

    subscriptions.value.set(id, subscription)

    // 发送订阅请求
    sendMessage({
      action: 'subscribe',
      type,
      id,
      filters
    })

    return id
  }

  /**
   * 取消订阅
   */
  const unsubscribe = (id: string): void => {
    const subscription = subscriptions.value.get(id)
    if (subscription) {
      subscription.active = false
      subscriptions.value.delete(id)

      // 发送取消订阅请求
      sendMessage({
        action: 'unsubscribe',
        id
      })
    }
  }

  /**
   * 触发订阅回调
   */
  const triggerSubscriptionCallbacks = (type: string, data: any): void => {
    subscriptions.value.forEach(subscription => {
      if (subscription.active && subscription.type === type) {
        try {
          // 应用过滤器
          if (subscription.filters) {
            const matches = Object.entries(subscription.filters).every(([key, value]) => {
              return data[key] === value
            })
            if (!matches) return
          }

          subscription.callback(data)
        } catch (error) {
          console.error('[Realtime] Subscription callback error:', error)
        }
      }
    })
  }

  /**
   * 重新激活订阅
   */
  const reactivateSubscriptions = (): void => {
    subscriptions.value.forEach(subscription => {
      if (subscription.active) {
        sendMessage({
          action: 'subscribe',
          type: subscription.type,
          id: subscription.id,
          filters: subscription.filters
        })
      }
    })
  }

  /**
   * 启动心跳
   */
  const startHeartbeat = (): void => {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval)
    }

    heartbeatInterval = window.setInterval(() => {
      if (websocket?.readyState === WebSocket.OPEN) {
        sendMessage({
          type: 'heartbeat',
          timestamp: new Date().toISOString()
        })
      }
    }, 30000) // 30秒心跳
  }

  /**
   * 停止心跳
   */
  const stopHeartbeat = (): void => {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval)
      heartbeatInterval = null
    }
  }

  /**
   * 更新统计数据
   */
  const updateStatistics = (): void => {
    const equipment = Array.from(equipmentStatuses.value.values())
    const production = Array.from(productionData.value.values())
    const quality = Array.from(qualityData.value.values())
    const alarms = Array.from(alarmData.value.values())

    statistics.value = {
      totalEquipment: equipment.length,
      onlineEquipment: equipment.filter(e => e.status === 'online' || e.status === 'running')
        .length,
      activeAlarms: alarms.filter(a => a.status === 'active').length,
      criticalAlarms: alarms.filter(a => a.status === 'active' && a.level === 'critical').length,
      averageOEE:
        production.length > 0
          ? production.reduce((sum, p) => sum + p.oeeRate, 0) / production.length
          : 0,
      totalProduction: production.reduce((sum, p) => sum + p.actualQuantity, 0),
      qualityYield:
        quality.length > 0 ? quality.reduce((sum, q) => sum + q.passRate, 0) / quality.length : 0,
      lastUpdateTime: new Date()
    }
  }

  /**
   * 获取WebSocket URL
   */
  const getWebSocketUrl = (): string => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.host
    return `${protocol}//${host}/api/ws/realtime`
  }

  /**
   * 确认告警
   */
  const acknowledgeAlarm = (alarmId: string, acknowledgedBy: string): void => {
    const alarm = alarmData.value.get(alarmId)
    if (alarm && alarm.status === 'active') {
      alarm.status = 'acknowledged'
      alarm.acknowledgedBy = acknowledgedBy
      alarm.acknowledgedAt = new Date()

      sendMessage({
        action: 'acknowledge_alarm',
        alarmId,
        acknowledgedBy
      })
    }
  }

  /**
   * 清除所有数据
   */
  const clearAllData = (): void => {
    equipmentStatuses.value.clear()
    productionData.value.clear()
    qualityData.value.clear()
    environmentData.value.clear()
    inventoryData.value.clear()
    alarmData.value.clear()

    updateStatistics()
  }

  /**
   * 获取历史数据
   */
  const getHistoricalData = async (
    type: string,
    startTime: Date,
    endTime: Date,
    filters?: Record<string, any>
  ): Promise<any[]> => {
    // 实际项目中这里会调用API获取历史数据
    console.log('[Realtime] Fetching historical data:', { type, startTime, endTime, filters })

    // 模拟历史数据
    return []
  }

  // 监听应用在线状态
  watch(
    () => appStore.appState.isOnline,
    isOnline => {
      if (isOnline && !connectionStatus.value.connected && !connectionStatus.value.reconnecting) {
        connect().catch(error => {
          console.error('[Realtime] Failed to reconnect when app came online:', error)
        })
      }
    }
  )

  // 页面可见性变化时的处理
  if (typeof document !== 'undefined') {
    document.addEventListener('visibilitychange', () => {
      if (
        !document.hidden &&
        !connectionStatus.value.connected &&
        !connectionStatus.value.reconnecting
      ) {
        connect().catch(error => {
          console.error('[Realtime] Failed to reconnect when page became visible:', error)
        })
      }
    })
  }

  return {
    // 状态
    connectionStatus,
    equipmentStatuses,
    productionData,
    qualityData,
    environmentData,
    inventoryData,
    alarmData,
    subscriptions,
    statistics,

    // 计算属性
    isConnected,
    equipmentList,
    productionList,
    qualityList,
    environmentList,
    inventoryList,
    alarmList,
    activeAlarms,
    criticalAlarms,
    equipmentByStatus,

    // 方法
    connect,
    disconnect,
    reconnect,
    sendMessage,
    subscribe,
    unsubscribe,
    acknowledgeAlarm,
    clearAllData,
    getHistoricalData,
    updateStatistics
  }
})
