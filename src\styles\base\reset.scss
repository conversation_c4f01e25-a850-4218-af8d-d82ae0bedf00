// IC封测CIM系统 - 极简样式重置
// 基于现代CSS最佳实践的样式重置

// ===== 基础重置 =====
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: var(--line-height-normal);
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1;
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  transition: 
    background-color var(--transition-normal),
    color var(--transition-normal);
  overflow-x: hidden;
}

// ===== 标题重置 =====
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

// ===== 段落和文本重置 =====
p {
  margin: 0;
  color: var(--color-text-primary);
}

// ===== 表单元素重置 =====
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
  border: none;
  outline: none;
  background: transparent;
}

button,
[role="button"] {
  cursor: pointer;
  
  &:disabled {
    cursor: not-allowed;
  }
}

// 焦点样式
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
[tabindex]:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

// 输入框样式
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="search"],
input[type="tel"],
input[type="url"],
textarea,
select {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--color-input-border);
  border-radius: var(--radius-base);
  background-color: var(--color-input-bg);
  color: var(--color-text-primary);
  transition: border-color var(--transition-fast);

  &::placeholder {
    color: var(--color-input-placeholder);
  }

  &:focus {
    border-color: var(--color-input-focus);
  }

  &:disabled {
    background-color: var(--color-bg-disabled);
    color: var(--color-text-disabled);
    cursor: not-allowed;
  }
}

// ===== 链接样式 =====
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
  
  &:hover {
    color: var(--color-primary-hover);
  }

  &:active {
    color: var(--color-primary-active);
  }
}

// ===== 列表重置 =====
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

// ===== 图片和媒体 =====
img {
  max-width: 100%;
  height: auto;
  display: block;
}

svg {
  display: block;
  max-width: 100%;
  height: auto;
}

// ===== 表格重置 =====
table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

th, td {
  text-align: left;
  vertical-align: top;
  padding: var(--table-cell-padding);
  border-bottom: 1px solid var(--color-table-border);
}

th {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  background-color: var(--color-table-header-bg);
}

// ===== 滚动条样式 =====
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-dark);
  border-radius: 3px;
  
  &:hover {
    background: var(--color-text-tertiary);
  }
}

// Firefox滚动条样式
html {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border-dark) var(--color-bg-secondary);
}

// ===== 选择文本样式 =====
::selection {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

::-moz-selection {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

// ===== 工具类 =====
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.clearfix::after {
  content: "";
  display: table;
  clear: both;
}