<template>
  <div class="quality-traceability">
    <div class="quality-traceability__header">
      <div class="header-content">
        <h1>质量追溯系统</h1>
        <p>完整追溯产品从晶圆到成品的质量历程，支持正向和逆向追溯</p>
      </div>
      <div class="header-actions">
        <el-button
type="primary" @click="showTraceMapDialog = true"
>
          <el-icon><DataAnalysis /></el-icon>
          追溯地图
        </el-button>
        <el-button @click="exportTraceReport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 搜索条件 -->
    <div class="search-panel">
      <el-form :model="searchForm" :inline="true" @submit="handleSearch">
        <el-form-item label="批次号">
          <el-input
            v-model="searchForm.lotNumber"
            placeholder="输入批次号"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="晶圆ID">
          <el-input
            v-model="searchForm.waferId"
            placeholder="输入晶圆ID"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="封装ID">
          <el-input
            v-model="searchForm.packageId"
            placeholder="输入封装ID"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="序列号">
          <el-input
            v-model="searchForm.serialNumber"
            placeholder="输入序列号"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="追溯方向">
          <el-select v-model="searchForm.traceDirection" placeholder="选择追溯方向">
            <el-option label="正向追溯" value="forward" />
            <el-option label="逆向追溯" value="backward" />
            <el-option label="双向追溯" value="both" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 追溯结果 -->
    <div v-if="traceabilityData" class="traceability-result">
      <!-- 基本信息卡片 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>产品基本信息</span>
            <div class="header-tags">
              <el-tag :type="getStatusTagType(traceabilityData.currentStatus.overall)">
                {{ getStatusText(traceabilityData.currentStatus.overall) }}
              </el-tag>
              <el-button size="small" type="primary" link @click="showQualityDetails = true">
                查看详情
              </el-button>
            </div>
          </div>
        </template>
        <div class="info-grid">
          <div class="info-item">
            <label>追溯ID:</label>
            <span>{{ traceabilityData.traceId }}</span>
            <el-button
              size="small"
              type="primary"
              link
              @click="copyToClipboard(traceabilityData.traceId)"
            >
              <el-icon><DocumentCopy /></el-icon>
            </el-button>
          </div>
          <div class="info-item">
            <label>批次号:</label>
            <span>{{ traceabilityData.lotNumber }}</span>
            <el-button
              size="small"
              type="primary"
              link
              @click="copyToClipboard(traceabilityData.lotNumber)"
            >
              <el-icon><DocumentCopy /></el-icon>
            </el-button>
          </div>
          <div
v-if="traceabilityData.waferId" class="info-item"
>
            <label>晶圆ID:</label>
            <span>{{ traceabilityData.waferId }}</span>
          </div>
          <div
v-if="traceabilityData.packageId" class="info-item"
>
            <label>封装ID:</label>
            <span>{{ traceabilityData.packageId }}</span>
          </div>
          <div
v-if="traceabilityData.serialNumber" class="info-item"
>
            <label>序列号:</label>
            <span>{{ traceabilityData.serialNumber }}</span>
          </div>
          <div class="info-item">
            <label>客户:</label>
            <span>{{ traceabilityData.customerInfo.customerName }}</span>
          </div>
        </div>
      </el-card>

      <!-- 质量状态卡片 -->
      <el-card class="status-card">
        <template #header>
          <div class="card-header">
            <span>质量检验状态</span>
            <el-progress
              :percentage="getQualityProgress()"
              :color="getProgressColor()"
              :stroke-width="6"
            />
          </div>
        </template>
        <div class="status-grid">
          <div class="status-item">
            <div class="status-icon">
              <el-icon :class="getStatusIconClass(traceabilityData.currentStatus.iqc.status)">
                <CircleCheck v-if="traceabilityData.currentStatus.iqc.status === 'PASS'" />
                <CircleClose v-else-if="traceabilityData.currentStatus.iqc.status === 'FAIL'" />
                <Clock v-else />
              </el-icon>
            </div>
            <div class="status-content">
              <div class="status-label">来料检验(IQC)</div>
              <el-tag :type="getStatusTagType(traceabilityData.currentStatus.iqc.status)">
                {{ getStatusText(traceabilityData.currentStatus.iqc.status) }}
              </el-tag>
              <div class="status-info">
                {{ formatTime(traceabilityData.currentStatus.iqc.timestamp) }}
              </div>
              <div class="status-inspector">
                {{ traceabilityData.currentStatus.iqc.inspector }}
              </div>
            </div>
          </div>
          <div class="status-item">
            <div class="status-icon">
              <el-icon :class="getStatusIconClass(traceabilityData.currentStatus.ipqc.status)">
                <CircleCheck v-if="traceabilityData.currentStatus.ipqc.status === 'PASS'" />
                <CircleClose v-else-if="traceabilityData.currentStatus.ipqc.status === 'FAIL'" />
                <Clock v-else />
              </el-icon>
            </div>
            <div class="status-content">
              <div class="status-label">过程检验(IPQC)</div>
              <el-tag :type="getStatusTagType(traceabilityData.currentStatus.ipqc.status)">
                {{ getStatusText(traceabilityData.currentStatus.ipqc.status) }}
              </el-tag>
              <div class="status-info">
                {{ formatTime(traceabilityData.currentStatus.ipqc.timestamp) }}
              </div>
              <div class="status-inspector">
                {{ traceabilityData.currentStatus.ipqc.inspector }}
              </div>
            </div>
          </div>
          <div class="status-item">
            <div class="status-icon">
              <el-icon :class="getStatusIconClass(traceabilityData.currentStatus.fqc.status)">
                <CircleCheck v-if="traceabilityData.currentStatus.fqc.status === 'PASS'" />
                <CircleClose v-else-if="traceabilityData.currentStatus.fqc.status === 'FAIL'" />
                <Clock v-else />
              </el-icon>
            </div>
            <div class="status-content">
              <div class="status-label">成品检验(FQC)</div>
              <el-tag :type="getStatusTagType(traceabilityData.currentStatus.fqc.status)">
                {{ getStatusText(traceabilityData.currentStatus.fqc.status) }}
              </el-tag>
              <div class="status-info">
                {{ formatTime(traceabilityData.currentStatus.fqc.timestamp) }}
              </div>
              <div class="status-inspector">
                {{ traceabilityData.currentStatus.fqc.inspector }}
              </div>
            </div>
          </div>
          <div class="status-item">
            <div class="status-icon">
              <el-icon
                :class="getStatusIconClass(traceabilityData.currentStatus.reliability.status)"
              >
                <CircleCheck v-if="traceabilityData.currentStatus.reliability.status === 'PASS'" />
                <CircleClose
                  v-else-if="traceabilityData.currentStatus.reliability.status === 'FAIL'"
                />
                <Clock v-else />
              </el-icon>
            </div>
            <div class="status-content">
              <div class="status-label">可靠性测试</div>
              <el-tag :type="getStatusTagType(traceabilityData.currentStatus.reliability.status)">
                {{ getStatusText(traceabilityData.currentStatus.reliability.status) }}
              </el-tag>
              <div class="status-inspector">
                {{ traceabilityData.currentStatus.reliability.inspector }}
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 追溯链 -->
      <el-card class="trace-chain-card">
        <template #header>
          <div class="card-header">
            <span>工艺追溯链</span>
            <div class="chain-actions">
              <el-radio-group v-model="viewMode" size="small">
                <el-radio-button value="timeline">时间线</el-radio-button>
                <el-radio-button value="flow">流程图</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>

        <!-- 时间线视图 -->
        <div v-if="viewMode === 'timeline'" class="trace-timeline">
          <el-timeline>
            <el-timeline-item
              v-for="(node, index) in traceabilityData.traceabilityChain"
              :key="node.id"
              :timestamp="formatTime(node.timestamp)"
              placement="top"
              :type="getProcessTimelineType(node.processStep)"
              :size="
                index === 0 || index === traceabilityData.traceabilityChain.length - 1
                  ? 'large'
                  : 'normal'
              "
            >
              <el-card class="timeline-node-card" shadow="hover">
                <template #header>
                  <div class="node-header">
                    <span class="node-title">{{ node.processStep }}</span>
                    <el-tag size="small" type="info">
                      {{ node.location }}
                    </el-tag>
                  </div>
                </template>
                <div class="node-content">
                  <div class="node-basic-info">
                    <div class="info-row">
                      <span class="label">操作员:</span>
                      <span class="value">{{ node.operator }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">设备:</span>
                      <span class="value">{{ node.equipment }}</span>
                    </div>
                  </div>

                  <div
v-if="Object.keys(node.parameters).length > 0" class="node-parameters"
>
                    <h5>工艺参数</h5>
                    <div class="parameter-grid">
                      <div
                        v-for="(value, key) in node.parameters"
                        :key="key"
                        class="parameter-item"
                      >
                        <span class="param-key">{{ key }}</span>
                        <span class="param-value">{{ value }}</span>
                      </div>
                    </div>
                  </div>

                  <div
v-if="node.qualityData.length > 0" class="node-quality"
>
                    <h5>质量数据</h5>
                    <div class="quality-grid">
                      <div
v-for="qd in node.qualityData" :key="qd.id"
class="quality-item"
>
                        <div class="quality-param">
                          {{ qd.specification.parameter }}
                        </div>
                        <div class="quality-value">
{{ qd.value }}{{ qd.specification.unit }}
</div>
                        <div class="quality-spec">
                          {{ qd.specification.lsl }}-{{ qd.specification.usl }}
                        </div>
                        <el-tag :type="getResultTagType(qd.result)" size="small">
                          {{ qd.result === 'PASS' ? '合格' : '不合格' }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>

        <!-- 流程图视图 -->
        <div v-else class="trace-flow">
          <div class="flow-container">
            <div
              v-for="(node, index) in traceabilityData.traceabilityChain"
              :key="node.id"
              class="flow-node"
            >
              <div
v-if="index > 0" class="flow-connector"
>
                <el-icon><Right /></el-icon>
              </div>
              <div class="flow-card" @click="showNodeDetails(node)">
                <div class="flow-icon">
                  <el-icon>{{ getProcessIcon(node.processStep) }}</el-icon>
                </div>
                <div class="flow-title">
                  {{ node.processStep }}
                </div>
                <div class="flow-time">
                  {{ formatTime(node.timestamp) }}
                </div>
                <div class="flow-location">
                  {{ node.location }}
                </div>
                <div
v-if="hasQualityIssues(node)" class="flow-quality"
>
                  <el-icon class="warning-icon">
                    <Warning />
                  </el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 质量事件历史 -->
      <el-card class="quality-history-card">
        <template #header>
          <div class="card-header">
            <span>质量事件历史</span>
            <el-select v-model="eventFilter" size="small" style="width: 120px">
              <el-option label="全部" value="all" />
              <el-option label="检验" value="INSPECTION" />
              <el-option label="缺陷" value="DEFECT" />
              <el-option label="返工" value="REWORK" />
              <el-option label="报废" value="SCRAP" />
              <el-option label="放行" value="RELEASE" />
            </el-select>
          </div>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="event in filteredQualityHistory"
            :key="event.id"
            :timestamp="formatTime(event.timestamp)"
            :type="getEventTimelineType(event.eventType)"
            :color="getEventColor(event.severity)"
            :size="event.eventType === 'RELEASE' ? 'large' : 'normal'"
          >
            <div class="timeline-content">
              <div class="event-header">
                <span class="event-type">{{ getEventTypeText(event.eventType) }}</span>
                <div class="event-tags">
                  <el-tag :type="getSeverityTagType(event.severity)" size="small">
                    {{ event.severity }}
                  </el-tag>
                  <el-tag :type="getEventStatusTagType(event.status)" size="small">
                    {{ getEventStatusText(event.status) }}
                  </el-tag>
                </div>
              </div>
              <div class="event-description">
                {{ event.description }}
              </div>
              <div class="event-responsible">负责人: {{ event.responsible }}</div>
              <div v-if="event.rootCause" class="event-details">
                <div class="detail-item">
                  <strong>根本原因:</strong>
                  {{ event.rootCause }}
                </div>
              </div>
              <div v-if="event.correctiveAction" class="event-details">
                <div class="detail-item">
                  <strong>纠正措施:</strong>
                  {{ event.correctiveAction }}
                </div>
              </div>
              <div v-if="event.preventiveAction" class="event-details">
                <div class="detail-item">
                  <strong>预防措施:</strong>
                  {{ event.preventiveAction }}
                </div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!loading && !searchPerformed" class="empty-state">
      <el-empty description="请输入查询条件进行追溯查询">
        <el-button type="primary"
@click="loadSampleData"
>
加载示例数据
</el-button>
      </el-empty>
    </div>

    <div v-else-if="!loading && searchPerformed" class="empty-state">
      <el-empty description="未找到匹配的追溯记录">
        <el-button @click="handleReset">
重新查询
</el-button>
      </el-empty>
    </div>

    <!-- 追溯地图对话框 -->
    <el-dialog
      v-model="showTraceMapDialog"
      title="追溯关系图"
      width="80%"
      :before-close="handleCloseTraceMap"
    >
      <div class="trace-map-container">
        <div
ref="traceMapRef" class="trace-map" />
      </div>
      <template #footer>
        <el-button @click="showTraceMapDialog = false">关闭</el-button>
        <el-button
type="primary" @click="exportTraceMap">导出图像</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import {
    Search,
    Refresh,
    DataAnalysis,
    Download,
    DocumentCopy,
    Right,
    Warning,
    CircleCheck,
    CircleClose,
    Clock
  } from '@element-plus/icons-vue'
  import type { QualityTraceability, TraceabilityNode, QualityEvent } from '@/types/quality'
  import { qualityTraceabilityData } from '@/utils/mockData/quality'

  // 响应式数据
  const loading = ref(false)
  const searchPerformed = ref(false)
  const traceabilityData = ref<QualityTraceability | null>(null)
  const showTraceMapDialog = ref(false)
  const showQualityDetails = ref(false)
  const viewMode = ref<'timeline' | 'flow'>('timeline')
  const eventFilter = ref<string>('all')
  const traceMapRef = ref<HTMLDivElement>()

  // 搜索表单
  const searchForm = reactive({
    lotNumber: '',
    waferId: '',
    packageId: '',
    serialNumber: '',
    traceDirection: 'both' as 'forward' | 'backward' | 'both'
  })

  // 计算属性
  const filteredQualityHistory = computed(() => {
    if (!traceabilityData.value) return []

    if (eventFilter.value === 'all') {
      return traceabilityData.value.qualityHistory
    }

    return traceabilityData.value.qualityHistory.filter(
      event => event.eventType === eventFilter.value
    )
  })

  // 方法
  const formatTime = (date?: Date): string => {
    if (!date) return ''
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  const getStatusTagType = (status: string): string => {
    switch (status) {
      case 'PASS':
        return 'success'
      case 'FAIL':
        return 'danger'
      case 'HOLD':
        return 'warning'
      case 'SCRAP':
        return 'danger'
      case 'PENDING':
        return 'info'
      default:
        return 'info'
    }
  }

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'PASS':
        return '合格'
      case 'FAIL':
        return '不合格'
      case 'HOLD':
        return '暂停'
      case 'SCRAP':
        return '报废'
      case 'PENDING':
        return '待检'
      case 'SKIP':
        return '跳过'
      default:
        return '未知'
    }
  }

  const getResultTagType = (result: string): string => {
    return result === 'PASS' ? 'success' : 'danger'
  }

  const getEventTimelineType = (eventType: string): string => {
    switch (eventType) {
      case 'INSPECTION':
        return 'primary'
      case 'DEFECT':
        return 'warning'
      case 'REWORK':
        return 'info'
      case 'SCRAP':
        return 'danger'
      case 'RELEASE':
        return 'success'
      default:
        return 'primary'
    }
  }

  const getEventColor = (severity: string): string => {
    switch (severity) {
      case 'LOW':
        return '#909399'
      case 'MEDIUM':
        return '#E6A23C'
      case 'HIGH':
        return '#F56C6C'
      case 'CRITICAL':
        return '#F56C6C'
      default:
        return '#409EFF'
    }
  }

  const getEventTypeText = (eventType: string): string => {
    switch (eventType) {
      case 'INSPECTION':
        return '检验'
      case 'DEFECT':
        return '缺陷'
      case 'REWORK':
        return '返工'
      case 'SCRAP':
        return '报废'
      case 'RELEASE':
        return '放行'
      default:
        return eventType
    }
  }

  const getSeverityTagType = (severity: string): string => {
    switch (severity) {
      case 'LOW':
        return 'info'
      case 'MEDIUM':
        return 'warning'
      case 'HIGH':
        return 'danger'
      case 'CRITICAL':
        return 'danger'
      default:
        return 'info'
    }
  }

  const getEventStatusTagType = (status: string): string => {
    switch (status) {
      case 'OPEN':
        return 'warning'
      case 'IN_PROGRESS':
        return 'primary'
      case 'CLOSED':
        return 'success'
      default:
        return 'info'
    }
  }

  const getEventStatusText = (status: string): string => {
    switch (status) {
      case 'OPEN':
        return '开放'
      case 'IN_PROGRESS':
        return '进行中'
      case 'CLOSED':
        return '已关闭'
      default:
        return status
    }
  }

  const getQualityProgress = (): number => {
    if (!traceabilityData.value) return 0

    const statuses = [
      traceabilityData.value.currentStatus.iqc.status,
      traceabilityData.value.currentStatus.ipqc.status,
      traceabilityData.value.currentStatus.fqc.status,
      traceabilityData.value.currentStatus.reliability.status
    ]

    const passCount = statuses.filter(status => status === 'PASS').length
    return (passCount / statuses.length) * 100
  }

  const getProgressColor = (): string => {
    const progress = getQualityProgress()
    if (progress === 100) return '#67C23A'
    if (progress >= 75) return '#E6A23C'
    return '#F56C6C'
  }

  const getStatusIconClass = (status: string): string => {
    switch (status) {
      case 'PASS':
        return 'status-pass'
      case 'FAIL':
        return 'status-fail'
      default:
        return 'status-pending'
    }
  }

  const getProcessTimelineType = (processStep: string): string => {
    switch (processStep) {
      case '晶圆制造':
        return 'primary'
      case 'CP测试':
        return 'success'
      case '贴片工艺':
        return 'warning'
      case '线键合':
        return 'info'
      case '塑封工艺':
        return 'warning'
      case 'FT测试':
        return 'success'
      case '最终检验':
        return 'primary'
      default:
        return 'primary'
    }
  }

  const getProcessIcon = (processStep: string): string => {
    // 这里应该返回相应的图标名称，简化处理
    return 'Operation'
  }

  const hasQualityIssues = (node: TraceabilityNode): boolean => {
    return node.qualityData.some(qd => qd.result !== 'PASS')
  }

  const handleSearch = async () => {
    if (
      !searchForm.lotNumber &&
      !searchForm.waferId &&
      !searchForm.packageId &&
      !searchForm.serialNumber
    ) {
      ElMessage.warning('请至少输入一个查询条件')
      return
    }

    loading.value = true
    searchPerformed.value = true

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 根据查询条件匹配数据
      const matchedData = qualityTraceabilityData.find(
        data =>
          (searchForm.lotNumber && data.lotNumber.includes(searchForm.lotNumber)) ||
          (searchForm.waferId && data.waferId?.includes(searchForm.waferId)) ||
          (searchForm.packageId && data.packageId?.includes(searchForm.packageId)) ||
          (searchForm.serialNumber && data.serialNumber?.includes(searchForm.serialNumber))
      )

      if (matchedData) {
        traceabilityData.value = matchedData
        ElMessage.success('追溯数据查询成功')
      } else {
        traceabilityData.value = null
        ElMessage.info('未找到匹配的追溯记录')
      }
    } catch (error) {
      ElMessage.error('查询失败，请重试')
      console.error('追溯查询错误:', error)
    } finally {
      loading.value = false
    }
  }

  const handleReset = () => {
    // 重置搜索表单
    searchForm.lotNumber = ''
    searchForm.waferId = ''
    searchForm.packageId = ''
    searchForm.serialNumber = ''
    searchForm.traceDirection = 'both'
    traceabilityData.value = null
    searchPerformed.value = false
  }

  const loadSampleData = () => {
    searchForm.lotNumber = 'LOT24120001'
    handleSearch()
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      ElMessage.success('已复制到剪贴板')
    } catch (error) {
      ElMessage.error('复制失败')
    }
  }

  const showNodeDetails = (node: TraceabilityNode) => {
    ElMessage.info(`查看节点详情: ${node.processStep}`)
  }

  const exportTraceReport = () => {
    if (!traceabilityData.value) {
      ElMessage.warning('请先查询追溯数据')
      return
    }

    ElMessage.success('追溯报告导出成功')
  }

  const handleCloseTraceMap = () => {
    showTraceMapDialog.value = false
  }

  const exportTraceMap = () => {
    ElMessage.success('追溯地图导出成功')
  }

  // 生命周期
  onMounted(() => {
    // 初始化组件
  })
</script>

<style lang="scss" scoped>
  .quality-traceability {
    padding: var(--spacing-6);

    &__header {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      padding-bottom: var(--spacing-4);
      margin-bottom: var(--spacing-6);
      border-bottom: 2px solid var(--color-border-light);

      .header-content {
        h1 {
          margin: 0 0 var(--spacing-2) 0;
          font-size: 28px;
          font-weight: 700;
          color: var(--color-text-primary);
        }

        p {
          margin: 0;
          font-size: 16px;
          color: var(--color-text-secondary);
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-2);
      }
    }

    .search-panel {
      padding: var(--spacing-5);
      margin-bottom: var(--spacing-6);
      background: var(--color-bg-secondary);
      border-radius: var(--radius-base);

      .el-form {
        .el-form-item {
          margin-bottom: var(--spacing-3);
        }
      }
    }

    .traceability-result {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-6);

      .info-card,
      .status-card,
      .trace-chain-card,
      .quality-history-card {
        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;

          .header-tags {
            display: flex;
            gap: var(--spacing-2);
            align-items: center;
          }
        }
      }

      .info-card {
        .info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: var(--spacing-4);

          .info-item {
            display: flex;
            gap: var(--spacing-2);
            align-items: center;

            label {
              min-width: 80px;
              font-weight: 500;
              color: var(--color-text-secondary);
            }

            span {
              font-family: var(--font-mono);
              color: var(--color-text-primary);
            }
          }
        }
      }

      .status-card {
        .status-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: var(--spacing-4);

          .status-item {
            display: flex;
            gap: var(--spacing-3);
            padding: var(--spacing-4);
            background: var(--color-bg-secondary);
            border-radius: var(--radius-base);

            .status-icon {
              flex-shrink: 0;
              font-size: 32px;

              .status-pass {
                color: var(--color-success);
              }

              .status-fail {
                color: var(--color-danger);
              }

              .status-pending {
                color: var(--color-warning);
              }
            }

            .status-content {
              flex: 1;

              .status-label {
                margin-bottom: var(--spacing-2);
                font-weight: 600;
                color: var(--color-text-primary);
              }

              .status-info {
                margin-top: var(--spacing-2);
                font-size: 14px;
                color: var(--color-text-secondary);
              }

              .status-inspector {
                margin-top: var(--spacing-1);
                font-size: 12px;
                color: var(--color-text-tertiary);
              }
            }
          }
        }
      }

      .trace-chain-card {
        .chain-actions {
          display: flex;
          gap: var(--spacing-2);
        }

        .trace-timeline {
          .timeline-node-card {
            margin-bottom: var(--spacing-4);

            .node-header {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .node-title {
                font-weight: 600;
                color: var(--color-text-primary);
              }
            }

            .node-content {
              .node-basic-info {
                margin-bottom: var(--spacing-4);

                .info-row {
                  display: flex;
                  gap: var(--spacing-2);
                  margin-bottom: var(--spacing-2);

                  .label {
                    min-width: 60px;
                    font-weight: 500;
                    color: var(--color-text-secondary);
                  }

                  .value {
                    color: var(--color-text-primary);
                  }
                }
              }

              .node-parameters,
              .node-quality {
                margin-top: var(--spacing-4);

                h5 {
                  margin: 0 0 var(--spacing-3) 0;
                  font-size: 14px;
                  font-weight: 600;
                  color: var(--color-text-primary);
                }

                .parameter-grid,
                .quality-grid {
                  display: grid;
                  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                  gap: var(--spacing-2);

                  .parameter-item,
                  .quality-item {
                    display: flex;
                    flex-direction: column;
                    padding: var(--spacing-3);
                    background: var(--color-bg-tertiary);
                    border-radius: var(--radius-sm);

                    .param-key,
                    .quality-param {
                      margin-bottom: var(--spacing-1);
                      font-size: 12px;
                      color: var(--color-text-secondary);
                    }

                    .param-value,
                    .quality-value {
                      margin-bottom: var(--spacing-1);
                      font-weight: 500;
                      color: var(--color-text-primary);
                    }

                    .quality-spec {
                      margin-bottom: var(--spacing-1);
                      font-size: 12px;
                      color: var(--color-text-tertiary);
                    }
                  }
                }
              }
            }
          }
        }

        .trace-flow {
          .flow-container {
            display: flex;
            gap: var(--spacing-4);
            padding: var(--spacing-4);
            overflow-x: auto;

            .flow-node {
              display: flex;
              gap: var(--spacing-4);
              align-items: center;

              .flow-connector {
                font-size: 24px;
                color: var(--color-primary);
              }

              .flow-card {
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
                min-width: 120px;
                padding: var(--spacing-4);
                cursor: pointer;
                background: var(--color-bg-secondary);
                border-radius: var(--radius-base);
                transition: all 0.3s ease;

                &:hover {
                  background: var(--color-primary-light);
                  transform: translateY(-2px);
                }

                .flow-icon {
                  margin-bottom: var(--spacing-2);
                  font-size: 32px;
                  color: var(--color-primary);
                }

                .flow-title {
                  margin-bottom: var(--spacing-2);
                  font-weight: 600;
                  color: var(--color-text-primary);
                  text-align: center;
                }

                .flow-time {
                  margin-bottom: var(--spacing-1);
                  font-size: 12px;
                  color: var(--color-text-secondary);
                }

                .flow-location {
                  font-size: 12px;
                  color: var(--color-text-tertiary);
                }

                .flow-quality {
                  position: absolute;
                  top: var(--spacing-2);
                  right: var(--spacing-2);

                  .warning-icon {
                    color: var(--color-warning);
                  }
                }
              }
            }
          }
        }
      }

      .quality-history-card {
        .timeline-content {
          .event-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-2);

            .event-type {
              font-weight: 600;
              color: var(--color-text-primary);
            }

            .event-tags {
              display: flex;
              gap: var(--spacing-2);
            }
          }

          .event-description {
            margin-bottom: var(--spacing-2);
            line-height: 1.5;
            color: var(--color-text-primary);
          }

          .event-responsible {
            margin-bottom: var(--spacing-2);
            font-size: 14px;
            color: var(--color-text-secondary);
          }

          .event-details {
            margin-top: var(--spacing-2);

            .detail-item {
              padding: var(--spacing-2);
              margin-bottom: var(--spacing-1);
              font-size: 14px;
              color: var(--color-text-secondary);
              background: var(--color-bg-secondary);
              border-radius: var(--radius-sm);

              strong {
                color: var(--color-text-primary);
              }
            }
          }
        }
      }
    }

    .empty-state {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
    }

    .trace-map-container {
      .trace-map {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 600px;
        color: var(--color-text-secondary);
        background: var(--color-bg-secondary);
        border-radius: var(--radius-base);
      }
    }
  }

  // 响应式设计
  @media (width <= 768px) {
    .quality-traceability {
      padding: var(--spacing-4);

      &__header {
        flex-direction: column;
        gap: var(--spacing-4);
        align-items: stretch;

        .header-actions {
          justify-content: flex-start;
        }
      }

      .search-panel {
        .el-form {
          .el-form-item {
            display: block;
          }
        }
      }

      .traceability-result {
        .info-card .info-grid {
          grid-template-columns: 1fr;
        }

        .status-card .status-grid {
          grid-template-columns: 1fr;
        }

        .trace-chain-card .trace-flow .flow-container {
          flex-direction: column;
          align-items: center;

          .flow-node {
            flex-direction: column;

            .flow-connector {
              transform: rotate(90deg);
            }
          }
        }
      }
    }
  }
</style>
