<template>
  <div class="quality-dashboard">
    <!-- 顶部质量KPI区域 -->
    <div class="dashboard-header">
      <div class="kpi-grid">
        <KPICard
          title="整体良率"
          :value="qualityKPI?.overallYield || 0"
          unit="%"
          :trend="yieldTrend"
          :target="97.5"
          type="success"
          icon="trend"
          size="large"
          :update-time="qualityKPI?.updateTime"
          subtitle="全工序产品良率"
        />

        <KPICard
          title="首次通过率"
          :value="qualityKPI?.firstPassYield || 0"
          unit="%"
          :trend="ftyTrend"
          :target="95"
          type="primary"
          icon="analysis"
          size="large"
          :update-time="qualityKPI?.updateTime"
          subtitle="首次测试通过率"
        />

        <KPICard
          title="CPK指数"
          :value="qualityKPI?.cpkIndex || 0"
          :trend="cpkTrend"
          target="1.33"
          type="warning"
          icon="monitor"
          size="large"
          :update-time="qualityKPI?.updateTime"
          subtitle="过程能力指数"
        />

        <KPICard
          title="客户投诉"
          :value="qualityKPI?.customerComplaints || 0"
          unit="件"
          type="danger"
          icon="warning"
          size="large"
          :update-time="qualityKPI?.updateTime"
          subtitle="当月客户投诉数"
        />
      </div>
    </div>

    <!-- 中间内容区域 -->
    <div class="dashboard-content">
      <!-- 左侧：SPC控制图 -->
      <div class="spc-section">
        <ChartPanel
          title="SPC统计过程控制"
          subtitle="关键参数实时控制图监控"
          :options="spcChartOptions"
          :loading="loading"
          height="400px"
          :show-footer="true"
          :data-count="spcDataPoints"
          @refresh="refreshQualityData"
        />
      </div>

      <!-- 右侧：SPC状态概览 -->
      <div class="spc-status-section">
        <div class="section-card">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon><TrendCharts /></el-icon>
              SPC状态概览
            </h3>
            <div class="header-actions">
              <el-button
                size="small"
                :icon="Refresh"
                :loading="loading"
                @click="refreshQualityData"
              >
                刷新
              </el-button>
            </div>
          </div>

          <div class="spc-status-list">
            <div
v-for="process in spcStatus" :key="process.processId"
class="spc-item"
>
              <div class="spc-header">
                <StatusIndicator
                  :status="getSPCStatus(process.status)"
                  :label="process.processName"
                  :description="process.parameter"
                  size="small"
                />

                <div class="spc-metrics">
                  <div class="metric-item">
                    <span class="metric-label">CPK</span>
                    <span :class="['metric-value', getCPKColor(process.cpk)]">
                      {{ process.cpk.toFixed(2) }}
                    </span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">趋势</span>
                    <el-icon :class="getTrendClass(process.trendDirection)">
                      <ArrowUp v-if="process.trendDirection === 'up'" />
                      <ArrowDown v-if="process.trendDirection === 'down'" />
                      <Minus v-if="process.trendDirection === 'stable'" />
                    </el-icon>
                  </div>
                </div>
              </div>

              <div class="spc-details">
                <div class="control-limits">
                  <div class="limit-item">
                    <span class="limit-label">UCL</span>
                    <span class="limit-value">{{ process.ucl.toFixed(2) }}</span>
                  </div>
                  <div class="limit-item target">
                    <span class="limit-label">目标</span>
                    <span class="limit-value">{{ process.target.toFixed(2) }}</span>
                  </div>
                  <div class="limit-item">
                    <span class="limit-label">LCL</span>
                    <span class="limit-value">{{ process.lcl.toFixed(2) }}</span>
                  </div>
                </div>

                <div class="current-value">
                  <span class="value-label">当前值</span>
                  <span :class="['current-number', getValueStatus(process)]">
                    {{ process.lastValue.toFixed(2) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部图表区域 -->
    <div class="dashboard-footer">
      <!-- 缺陷分析帕累托图 -->
      <div class="chart-section">
        <ChartPanel
          title="缺陷分析帕累托图"
          subtitle="缺陷类型分布和累积影响分析"
          :options="paretoChartOptions"
          :loading="loading"
          height="350px"
          :show-footer="true"
          :data-count="defectAnalysis.length"
        />
      </div>

      <!-- 质量成本趋势 -->
      <div class="chart-section">
        <ChartPanel
          title="质量成本趋势"
          subtitle="预防、评估、内部和外部失败成本"
          :options="qualityCostOptions"
          :loading="loading"
          height="350px"
          :time-ranges="timeRanges"
          @time-range-change="handleTimeRangeChange"
        />
      </div>

      <!-- 客户满意度 -->
      <div class="satisfaction-section">
        <div class="section-card">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon><Star /></el-icon>
              客户满意度
            </h3>
          </div>

          <div class="satisfaction-content">
            <!-- 整体满意度评分 -->
            <div class="overall-score">
              <div class="score-circle">
                <el-progress
                  type="circle"
                  :width="100"
                  :percentage="Math.round(overallSatisfaction)"
                  :color="getSatisfactionColor(overallSatisfaction)"
                  :stroke-width="10"
                >
                  <template #default="{ percentage }">
                    <div class="score-content">
                      <div class="score-value">
                        {{ percentage }}
                      </div>
                      <div class="score-label">分</div>
                    </div>
                  </template>
                </el-progress>
              </div>
              <div class="score-details">
                <div class="detail-item">
                  <span class="label">质量评价</span>
                  <span class="value">{{ averageQualityRating.toFixed(1) }}分</span>
                </div>
                <div class="detail-item">
                  <span class="label">交付评价</span>
                  <span class="value">{{ averageDeliveryRating.toFixed(1) }}分</span>
                </div>
                <div class="detail-item">
                  <span class="label">服务评价</span>
                  <span class="value">{{ averageServiceRating.toFixed(1) }}分</span>
                </div>
              </div>
            </div>

            <!-- 客户评分分布 -->
            <div class="satisfaction-chart">
              <div
                v-for="customer in topCustomers"
                :key="customer.customerId"
                class="customer-rating"
              >
                <div class="customer-info">
                  <div class="customer-name">
                    {{ customer.customerName }}
                  </div>
                  <div class="feedback-count">{{ customer.feedbackCount }} 次反馈</div>
                </div>
                <div class="rating-progress">
                  <el-progress
                    :percentage="customer.satisfactionScore"
                    :color="getSatisfactionColor(customer.satisfactionScore)"
                    :stroke-width="6"
                    :show-text="true"
                    :format="(percentage: number) => `${percentage.toFixed(1)}`"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 质量告警浮动提示 -->
    <div v-if="qualityAlerts.length > 0" class="quality-alerts">
      <div class="alerts-header">
        <el-icon class="alert-icon">
          <Warning />
        </el-icon>
        <span>质量告警</span>
        <el-badge :value="qualityAlerts.length" class="alert-badge" />
      </div>

      <div class="alerts-list">
        <div
          v-for="alert in qualityAlerts.slice(0, 3)"
          :key="alert.id"
          :class="['alert-item', `alert-item--${alert.level}`]"
        >
          <div class="alert-title">
            {{ alert.title }}
          </div>
          <div class="alert-message">
            {{ alert.message }}
          </div>
          <div class="alert-time">
            {{ formatAlertTime(alert.timestamp) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  import {
    TrendCharts,
    Refresh,
    Warning,
    Star,
    ArrowUp,
    ArrowDown,
    Minus
  } from '@element-plus/icons-vue'
  import { KPICard, ChartPanel, StatusIndicator } from '@/components/monitoring'
  import type { StatusType } from '@/components/monitoring'
  import { useMonitoring } from '@/composables/useMonitoring'
  import { monitoringApi } from '@/api/monitoring'
  import type { EChartsOption } from 'echarts'
  import type { SPCStatus, DefectAnalysis, CustomerSatisfaction } from '@/types/monitoring'

  // 使用监控数据管理
  const { loading, qualityKPI, refreshQualityData } = useMonitoring()

  // 响应式数据
  const spcStatus = ref<SPCStatus[]>([])
  const defectAnalysis = ref<DefectAnalysis[]>([])
  const customerSatisfaction = ref<CustomerSatisfaction[]>([])

  // 模拟质量告警数据
  const qualityAlerts = ref([
    {
      id: 'QA001',
      title: 'SPC告警',
      message: '线键合拉力测试连续3点超出控制限',
      level: 'critical',
      timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString()
    },
    {
      id: 'QA002',
      title: 'CPK预警',
      message: 'CP测试电流参数CPK值低于1.33',
      level: 'warning',
      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString()
    }
  ])

  // 时间范围选项
  const timeRanges = [
    { label: '1小时', value: '1h' },
    { label: '4小时', value: '4h' },
    { label: '12小时', value: '12h' },
    { label: '24小时', value: '24h' },
    { label: '7天', value: '7d' }
  ]

  // 计算属性
  const yieldTrend = computed(() => {
    // 模拟良率趋势
    return 0.8
  })

  const ftyTrend = computed(() => {
    // 模拟首次通过率趋势
    return 1.2
  })

  const cpkTrend = computed(() => {
    // 模拟CPK趋势
    return -0.05
  })

  const spcDataPoints = computed(() => {
    return spcStatus.value.length * 50 // 假设每个过程有50个数据点
  })

  const overallSatisfaction = computed(() => {
    if (!customerSatisfaction.value.length) return 0
    const total = customerSatisfaction.value.reduce((sum, c) => sum + c.satisfactionScore, 0)
    return total / customerSatisfaction.value.length
  })

  const averageQualityRating = computed(() => {
    if (!customerSatisfaction.value.length) return 0
    const total = customerSatisfaction.value.reduce((sum, c) => sum + c.qualityRating, 0)
    return total / customerSatisfaction.value.length
  })

  const averageDeliveryRating = computed(() => {
    if (!customerSatisfaction.value.length) return 0
    const total = customerSatisfaction.value.reduce((sum, c) => sum + c.deliveryRating, 0)
    return total / customerSatisfaction.value.length
  })

  const averageServiceRating = computed(() => {
    if (!customerSatisfaction.value.length) return 0
    const total = customerSatisfaction.value.reduce((sum, c) => sum + c.serviceRating, 0)
    return total / customerSatisfaction.value.length
  })

  const topCustomers = computed(() => {
    return customerSatisfaction.value
      .sort((a, b) => b.satisfactionScore - a.satisfactionScore)
      .slice(0, 5)
  })

  // SPC控制图配置
  const spcChartOptions = computed<EChartsOption>(() => {
    // 生成模拟SPC数据
    const dataPoints = 50
    const data: number[] = []
    const centerLine = 90
    const ucl = 100
    const lcl = 80

    for (let i = 0; i < dataPoints; i++) {
      const value = centerLine + (Math.random() - 0.5) * 15
      data.push(value)
    }

    return {
      title: {
        text: '线键合拉力测试控制图',
        left: 'center',
        top: 10,
        textStyle: {
          color: 'var(--color-text-primary)',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const param = params[0]
          return `点号: ${param.dataIndex + 1}<br/>测试值: ${param.value.toFixed(2)}<br/>规格: ${lcl}-${ucl}`
        }
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '15%',
        top: '20%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: Array.from({ length: dataPoints }, (_, i) => i + 1),
        axisLabel: {
          color: 'var(--color-text-secondary)'
        }
      },
      yAxis: {
        type: 'value',
        min: lcl - 10,
        max: ucl + 10,
        axisLabel: {
          color: 'var(--color-text-secondary)'
        },
        splitLine: {
          show: false
        }
      },
      series: [
        // 控制限线
        {
          name: 'UCL',
          type: 'line',
          data: Array(dataPoints).fill(ucl),
          lineStyle: {
            color: '#f5222d',
            type: 'dashed',
            width: 2
          },
          symbol: 'none',
          silent: true
        },
        {
          name: '中心线',
          type: 'line',
          data: Array(dataPoints).fill(centerLine),
          lineStyle: {
            color: '#52c41a',
            type: 'solid',
            width: 2
          },
          symbol: 'none',
          silent: true
        },
        {
          name: 'LCL',
          type: 'line',
          data: Array(dataPoints).fill(lcl),
          lineStyle: {
            color: '#f5222d',
            type: 'dashed',
            width: 2
          },
          symbol: 'none',
          silent: true
        },
        // 数据点
        {
          name: '测试值',
          type: 'line',
          data,
          lineStyle: {
            color: '#1890ff',
            width: 2
          },
          itemStyle: {
            color: (params: any) => {
              const value = params.value
              if (value > ucl || value < lcl) return '#f5222d'
              return '#1890ff'
            }
          },
          symbolSize: 6,
          emphasis: {
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 2
            }
          }
        }
      ]
    }
  })

  // 缺陷帕累托图
  const paretoChartOptions = computed<EChartsOption>(() => {
    const data = defectAnalysis.value.map(d => ({
      name: d.defectType,
      value: d.count
    }))

    const total = data.reduce((sum, item) => sum + item.value, 0)
    let cumulative = 0
    const cumulativeData = data.map(item => {
      cumulative += item.value
      return ((cumulative / total) * 100).toFixed(1)
    })

    return {
      tooltip: [
        {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params: any) => {
            const barParam = params[0]
            const lineParam = params[1]
            return `${barParam.name}<br/>数量: ${barParam.value}<br/>累积比例: ${lineParam.value}%`
          }
        }
      ],
      legend: {
        data: ['缺陷数量', '累积比例'],
        bottom: 10,
        textStyle: {
          color: 'var(--color-text-primary)'
        }
      },
      grid: {
        left: '5%',
        right: '10%',
        bottom: '20%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.map(d => d.name),
        axisLabel: {
          color: 'var(--color-text-secondary)',
          interval: 0,
          rotate: 45
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '缺陷数量',
          axisLabel: {
            color: 'var(--color-text-secondary)'
          }
        },
        {
          type: 'value',
          name: '累积比例(%)',
          max: 100,
          axisLabel: {
            formatter: '{value}%',
            color: 'var(--color-text-secondary)'
          }
        }
      ],
      series: [
        {
          name: '缺陷数量',
          type: 'bar',
          data: data.map(d => d.value),
          itemStyle: {
            color: '#1890ff'
          }
        },
        {
          name: '累积比例',
          type: 'line',
          yAxisIndex: 1,
          data: cumulativeData,
          lineStyle: {
            color: '#f5222d',
            width: 3
          },
          itemStyle: {
            color: '#f5222d'
          },
          symbol: 'circle',
          symbolSize: 6
        }
      ]
    }
  })

  // 质量成本趋势图
  const qualityCostOptions = computed<EChartsOption>(() => {
    const months = ['1月', '2月', '3月', '4月', '5月', '6月']
    const preventionCost = months.map(() => Math.random() * 50 + 30)
    const appraisalCost = months.map(() => Math.random() * 40 + 20)
    const internalFailure = months.map(() => Math.random() * 30 + 10)
    const externalFailure = months.map(() => Math.random() * 20 + 5)

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['预防成本', '评估成本', '内部失败', '外部失败'],
        bottom: 10,
        textStyle: {
          color: 'var(--color-text-primary)'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '20%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: months,
        axisLabel: {
          color: 'var(--color-text-secondary)'
        }
      },
      yAxis: {
        type: 'value',
        name: '成本 (万元)',
        axisLabel: {
          formatter: '{value}万',
          color: 'var(--color-text-secondary)'
        }
      },
      series: [
        {
          name: '预防成本',
          type: 'bar',
          stack: 'cost',
          data: preventionCost,
          itemStyle: { color: '#52c41a' }
        },
        {
          name: '评估成本',
          type: 'bar',
          stack: 'cost',
          data: appraisalCost,
          itemStyle: { color: '#1890ff' }
        },
        {
          name: '内部失败',
          type: 'bar',
          stack: 'cost',
          data: internalFailure,
          itemStyle: { color: '#faad14' }
        },
        {
          name: '外部失败',
          type: 'bar',
          stack: 'cost',
          data: externalFailure,
          itemStyle: { color: '#f5222d' }
        }
      ]
    }
  })

  // 方法
  const getSPCStatus = (status: string): StatusType => {
    const statusMap: Record<string, StatusType> = {
      in_control: 'success',
      out_of_control: 'danger',
      warning: 'warning'
    }
    return statusMap[status] || 'unknown'
  }

  const getCPKColor = (cpk: number): string => {
    if (cpk >= 1.67) return 'text-success'
    if (cpk >= 1.33) return 'text-warning'
    return 'text-danger'
  }

  const getTrendClass = (direction: string): string => {
    switch (direction) {
      case 'up':
        return 'trend-up'
      case 'down':
        return 'trend-down'
      default:
        return 'trend-stable'
    }
  }

  const getValueStatus = (process: SPCStatus): string => {
    const { lastValue, ucl, lcl } = process
    if (lastValue > ucl || lastValue < lcl) return 'out-of-control'
    if (lastValue > ucl * 0.9 || lastValue < lcl * 1.1) return 'warning'
    return 'in-control'
  }

  const getSatisfactionColor = (score: number): string => {
    if (score >= 90) return '#52c41a'
    if (score >= 80) return '#faad14'
    if (score >= 70) return '#fa8c16'
    return '#f5222d'
  }

  const formatAlertTime = (timestamp: string): string => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffMinutes < 1) return '刚刚'
    if (diffMinutes < 60) return `${diffMinutes}分钟前`

    const diffHours = Math.floor(diffMinutes / 60)
    return `${diffHours}小时前`
  }

  const handleTimeRangeChange = (range: string) => {
    // 处理时间范围变化
    console.log('时间范围切换:', range)
  }

  // 数据加载
  const loadQualityData = async () => {
    try {
      const [spcData, defectData, satisfactionData] = await Promise.all([
        monitoringApi.getSPCStatus(),
        monitoringApi.getDefectAnalysis(),
        monitoringApi.getCustomerSatisfaction()
      ])

      spcStatus.value = spcData
      defectAnalysis.value = defectData
      customerSatisfaction.value = satisfactionData
    } catch (error) {
      console.error('加载质量数据失败:', error)
    }
  }

  // 生命周期
  onMounted(() => {
    loadQualityData()
    refreshQualityData()
  })
</script>

<style lang="scss" scoped>
  .quality-dashboard {
    position: relative;
    min-height: 100vh;
    padding: var(--spacing-6);
    background: var(--color-bg-page);

    @media (width >= 1920px) {
      padding: var(--spacing-8);
    }
  }

  .dashboard-header {
    margin-bottom: var(--spacing-6);

    .kpi-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: var(--spacing-4);

      @media (width >= 1920px) {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-6);
      }
    }
  }

  .dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-6);

    @media (width <= 1200px) {
      grid-template-columns: 1fr;
    }
  }

  .dashboard-footer {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--spacing-6);

    @media (width <= 1200px) {
      grid-template-columns: 1fr;
    }
  }

  .section-card {
    overflow: hidden;
    background: var(--color-bg-primary);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-lg);

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-5);
      border-bottom: 1px solid var(--color-border-lighter);

      .card-title {
        display: flex;
        align-items: center;
        margin: 0;
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--color-text-primary);

        .el-icon {
          margin-right: var(--spacing-2);
          color: var(--color-primary);
        }
      }
    }
  }

  .spc-status-list {
    max-height: 400px;
    padding: var(--spacing-4);
    overflow-y: auto;

    .spc-item {
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-3);
      border: 1px solid var(--color-border-lighter);
      border-radius: var(--radius-base);

      &:last-child {
        margin-bottom: 0;
      }

      .spc-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: var(--spacing-3);

        .spc-metrics {
          display: flex;
          gap: var(--spacing-3);

          .metric-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .metric-label {
              margin-bottom: var(--spacing-1);
              font-size: var(--font-size-xs);
              color: var(--color-text-secondary);
            }

            .metric-value {
              font-size: var(--font-size-sm);
              font-weight: var(--font-weight-semibold);

              &.text-success {
                color: var(--color-success);
              }

              &.text-warning {
                color: var(--color-warning);
              }

              &.text-danger {
                color: var(--color-danger);
              }
            }
          }
        }
      }

      .spc-details {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .control-limits {
          display: flex;
          gap: var(--spacing-2);

          .limit-item {
            padding: 2px 6px;
            background: var(--color-bg-secondary);
            border-radius: var(--radius-sm);

            .limit-label {
              font-size: var(--font-size-xs);
              color: var(--color-text-secondary);
            }

            .limit-value {
              margin-left: var(--spacing-1);
              font-size: var(--font-size-xs);
              font-weight: var(--font-weight-medium);
              color: var(--color-text-primary);
            }

            &.target {
              background: var(--color-primary-light);

              .limit-label,
              .limit-value {
                color: var(--color-primary);
              }
            }
          }
        }

        .current-value {
          text-align: right;

          .value-label {
            display: block;
            font-size: var(--font-size-xs);
            color: var(--color-text-secondary);
          }

          .current-number {
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-bold);

            &.in-control {
              color: var(--color-success);
            }

            &.warning {
              color: var(--color-warning);
            }

            &.out-of-control {
              color: var(--color-danger);
            }
          }
        }
      }
    }
  }

  .satisfaction-content {
    padding: var(--spacing-5);

    .overall-score {
      display: flex;
      gap: var(--spacing-4);
      align-items: center;
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-5);
      background: var(--color-bg-secondary);
      border-radius: var(--radius-base);

      .score-content {
        text-align: center;

        .score-value {
          font-size: 1.2rem;
          font-weight: var(--font-weight-bold);
          color: var(--color-text-primary);
        }

        .score-label {
          font-size: var(--font-size-xs);
          color: var(--color-text-secondary);
        }
      }

      .score-details {
        flex: 1;

        .detail-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: var(--spacing-1);

          .label {
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
          }

          .value {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--color-text-primary);
          }
        }
      }
    }

    .satisfaction-chart {
      .customer-rating {
        margin-bottom: var(--spacing-3);

        .customer-info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: var(--spacing-2);

          .customer-name {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--color-text-primary);
          }

          .feedback-count {
            font-size: var(--font-size-xs);
            color: var(--color-text-secondary);
          }
        }
      }
    }
  }

  .quality-alerts {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    width: 320px;
    background: var(--color-bg-primary);
    border: 1px solid var(--color-warning);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);

    .alerts-header {
      display: flex;
      align-items: center;
      padding: var(--spacing-3);
      color: white;
      background: var(--color-warning);
      border-radius: var(--radius-lg) var(--radius-lg) 0 0;

      .alert-icon {
        margin-right: var(--spacing-2);
      }

      .alert-badge {
        margin-left: auto;
      }
    }

    .alerts-list {
      .alert-item {
        padding: var(--spacing-3);
        border-bottom: 1px solid var(--color-border-lighter);

        &:last-child {
          border-bottom: none;
        }

        .alert-title {
          margin-bottom: var(--spacing-1);
          font-weight: var(--font-weight-semibold);
          color: var(--color-text-primary);
        }

        .alert-message {
          margin-bottom: var(--spacing-1);
          font-size: var(--font-size-sm);
          line-height: 1.4;
          color: var(--color-text-secondary);
        }

        .alert-time {
          font-size: var(--font-size-xs);
          color: var(--color-text-tertiary);
        }

        &--critical {
          border-left: 4px solid var(--color-danger);
        }

        &--warning {
          border-left: 4px solid var(--color-warning);
        }
      }
    }
  }

  // 图标颜色样式
  .trend-up {
    color: var(--color-success);
  }

  .trend-down {
    color: var(--color-danger);
  }

  .trend-stable {
    color: var(--color-info);
  }

  // 深色主题适配
  .dark {
    .quality-dashboard {
      background: var(--color-bg-page);
    }

    .section-card {
      background: var(--color-bg-secondary);
      border-color: var(--color-border-dark);
    }

    .quality-alerts {
      background: var(--color-bg-secondary);
      border-color: var(--color-warning);
    }
  }
</style>
