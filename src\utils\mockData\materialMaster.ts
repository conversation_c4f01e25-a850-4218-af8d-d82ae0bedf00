// 物料主数据管理模拟数据 - IC封装测试专业版
// 按照OSAT行业特色创建完整的物料主数据

import type {
  MaterialMaster,
  MaterialUsageStats,
  MaterialRiskWarning,
  SupplierInfo,
  PriceHistory,
  AlternativeRelation,
  Attachment
} from '@/types/materialMaster'

import {
  MaterialMasterCategory,
  MaterialStatus,
  WaferSize,
  PackageType,
  WireType
} from '@/types/materialMaster'

// 物料分类选项
export const MATERIAL_CATEGORY_OPTIONS = [
  { value: MaterialMasterCategory.WAFER, label: '晶圆', color: '#409EFF', icon: 'Cpu' },
  { value: MaterialMasterCategory.SUBSTRATE, label: '基板', color: '#67C23A', icon: 'Grid' },
  { value: MaterialMasterCategory.LEADFRAME, label: '引线框架', color: '#E6A23C', icon: 'Connection' },
  { value: MaterialMasterCategory.WIREBOND, label: '键合材料', color: '#F56C6C', icon: 'Connection' },
  { value: MaterialMasterCategory.MOLDING, label: '塑封材料', color: '#9580FF', icon: 'Box' },
  { value: MaterialMasterCategory.TEST_DEVICE, label: '测试器件', color: '#17A2B8', icon: 'Testing' },
  { value: MaterialMasterCategory.CHEMICAL, label: '化学品', color: '#FFC107', icon: 'Flask' },
  { value: MaterialMasterCategory.CONSUMABLE, label: '易耗品', color: '#6C757D', icon: 'Tools' },
  { value: MaterialMasterCategory.AUXILIARY, label: '辅助材料', color: '#28A745', icon: 'Component' }
]

// 物料状态选项
export const MATERIAL_STATUS_OPTIONS = [
  { value: MaterialStatus.ACTIVE, label: '正常', color: '#67C23A', tagType: 'success' },
  { value: MaterialStatus.DISCONTINUED, label: '停产', color: '#E6A23C', tagType: 'warning' },
  { value: MaterialStatus.RESTRICTED, label: '限制使用', color: '#F56C6C', tagType: 'danger' },
  { value: MaterialStatus.OBSOLETE, label: '淘汰', color: '#909399', tagType: 'info' },
  { value: MaterialStatus.DEVELOPMENT, label: '开发中', color: '#9580FF', tagType: '' }
]

// 封装类型选项
export const PACKAGE_TYPE_OPTIONS = [
  { value: PackageType.QFP, label: 'QFP (四方扁平封装)' },
  { value: PackageType.BGA, label: 'BGA (球栅阵列封装)' },
  { value: PackageType.CSP, label: 'CSP (芯片级封装)' },
  { value: PackageType.SOP, label: 'SOP (小外形封装)' },
  { value: PackageType.TSOP, label: 'TSOP (薄小外形封装)' },
  { value: PackageType.LQFP, label: 'LQFP (低剖面四方扁平封装)' },
  { value: PackageType.FCBGA, label: 'FCBGA (倒装球栅阵列封装)' },
  { value: PackageType.WLCSP, label: 'WLCSP (晶圆级芯片级封装)' }
]

// 晶圆尺寸选项
export const WAFER_SIZE_OPTIONS = [
  { value: WaferSize.INCH_6, label: '6寸 (150mm)' },
  { value: WaferSize.INCH_8, label: '8寸 (200mm)' },
  { value: WaferSize.INCH_12, label: '12寸 (300mm)' }
]

// 键合材料选项
export const WIRE_TYPE_OPTIONS = [
  { value: WireType.GOLD, label: '金线', color: '#FFD700' },
  { value: WireType.SILVER, label: '银线', color: '#C0C0C0' },
  { value: WireType.COPPER, label: '铜线', color: '#B87333' },
  { value: WireType.ALUMINUM, label: '铝线', color: '#848482' }
]

// 价格历史数据
const generatePriceHistory = (basePrice: number, months: number = 12): PriceHistory[] => {
  const history: PriceHistory[] = []
  const startDate = new Date()
  startDate.setMonth(startDate.getMonth() - months)
  
  for (let i = 0; i < months; i++) {
    const date = new Date(startDate)
    date.setMonth(date.getMonth() + i)
    
    const variation = (Math.random() - 0.5) * 0.2 // ±10%价格波动
    const price = basePrice * (1 + variation)
    
    history.push({
      date: date.toISOString().split('T')[0],
      price: parseFloat(price.toFixed(4)),
      supplier: 'Primary Supplier',
      quantity: Math.floor(Math.random() * 10000) + 1000,
      reason: i === 0 ? '基准价格' : ['市场波动', '原料成本调整', '数量折扣', '供应商调价'][Math.floor(Math.random() * 4)]
    })
  }
  
  return history
}

// 模拟物料主数据
export const mockMaterialMasters: MaterialMaster[] = [
  // 晶圆类物料
  {
    id: 'MAT-W001',
    basicInfo: {
      materialCode: 'WAFER-8-SI-001',
      materialName: '8寸硅晶圆',
      englishName: '8 Inch Silicon Wafer',
      specification: '200mm P-type <100> 525±25μm',
      category: MaterialMasterCategory.WAFER,
      manufacturer: 'Shin-Etsu Handotai',
      brand: 'SEH',
      model: 'P-type-8-525',
      status: MaterialStatus.ACTIVE,
      description: '8寸P型硅晶圆，适用于模拟IC制造',
      remarks: '客户指定材料'
    },
    technicalParams: {
      dimensions: {
        diameter: 200000,
        thickness: 525
      },
      material: 'Silicon',
      waferInfo: {
        waferSize: WaferSize.INCH_8,
        thickness: 525,
        orientation: '<100>',
        resistivity: 10,
        dieSize: '5x5mm'
      },
      environmentalRequirements: {
        temperatureMin: -20,
        temperatureMax: 60,
        humidityMax: 45,
        storageConditions: ['ESD防护', '洁净环境', '恒温恒湿']
      }
    },
    suppliers: [
      {
        supplierId: 'SUP-SEH-001',
        supplierName: 'Shin-Etsu Handotai Co., Ltd.',
        contactPerson: 'Yamada Taro',
        contactPhone: '+81-3-3246-5070',
        contactEmail: '<EMAIL>',
        leadTime: 45,
        minOrderQty: 25,
        isPrimary: true,
        qualityRating: 98.5,
        deliveryRating: 96.8,
        certifications: ['ISO9001', 'IATF16949', 'ISO14001']
      },
      {
        supplierId: 'SUP-SUMCO-002',
        supplierName: 'SUMCO Corporation',
        contactPerson: 'Suzuki Hanako',
        contactPhone: '+81-3-3436-1123',
        contactEmail: '<EMAIL>',
        leadTime: 50,
        minOrderQty: 25,
        isPrimary: false,
        qualityRating: 97.2,
        deliveryRating: 95.5,
        certifications: ['ISO9001', 'ISO14001']
      }
    ],
    qualityInfo: {
      standard: 'SEMI M1-0302',
      specification: 'Prime Grade Silicon Wafer',
      inspectionMethod: '表面检查、电阻率测量、平坦度测试',
      acceptanceCriteria: '表面缺陷<0.1/cm², 电阻率10±2 Ω·cm',
      certifications: ['SEMI标准认证', 'JEITA认证'],
      reliability: {
        mtbf: 50000,
        lifeTest: '1000小时老化测试',
        qualificationStatus: '已认证'
      }
    },
    stockInfo: {
      unitOfMeasure: 'PCS',
      safetyStock: 100,
      maxStock: 500,
      reorderPoint: 150,
      leadTime: 45,
      storageRequirements: ['ESD安全', '洁净室Class 100', '温度20-25°C', '湿度40-50%'],
      shelfLife: 365,
      storageLocation: 'ESD-SAFE-A1'
    },
    costInfo: {
      standardCost: 125.50,
      currentPrice: 128.00,
      currency: 'USD',
      priceUnit: 'PCS',
      lastPriceUpdate: '2024-08-01T00:00:00Z',
      priceHistory: generatePriceHistory(125.50),
      costAnalysis: {
        materialCost: 110.00,
        laborCost: 8.00,
        overheadCost: 7.00,
        transportCost: 3.00
      }
    },
    alternatives: [
      {
        alternativeCode: 'WAFER-8-SI-002',
        alternativeName: '8寸硅晶圆(替代型)',
        relationType: 'FULL',
        substitutionRatio: 1.0,
        priority: 1,
        remarks: 'SUMCO供应商提供',
        validFrom: '2024-01-01',
        validTo: '2024-12-31'
      }
    ],
    attachments: [
      {
        id: 'ATT-001',
        fileName: 'wafer_specification.pdf',
        fileType: 'pdf',
        fileSize: 2048576,
        uploadTime: '2024-08-01T10:00:00Z',
        uploadBy: 'Engineer Zhang',
        category: 'SPEC',
        description: '晶圆技术规格书'
      }
    ],
    auditInfo: {
      createdBy: 'System Admin',
      createdAt: '2024-07-01T00:00:00Z',
      updatedBy: 'Engineer Zhang',
      updatedAt: '2024-08-20T15:30:00Z',
      version: 2,
      approvedBy: 'Manager Chen',
      approvedAt: '2024-08-20T16:00:00Z'
    }
  },
  
  // BGA基板
  {
    id: 'MAT-S001',
    basicInfo: {
      materialCode: 'SUB-BGA256-001',
      materialName: 'BGA256基板',
      englishName: 'BGA256 Substrate',
      specification: '256-Ball BGA Substrate 17x17mm',
      category: MaterialMasterCategory.SUBSTRATE,
      manufacturer: 'Unimicron Technology',
      brand: 'UMC',
      model: 'BGA256-1717',
      status: MaterialStatus.ACTIVE,
      description: 'BGA256封装基板，适用于高性能处理器',
      remarks: '高密度互连基板'
    },
    technicalParams: {
      dimensions: {
        length: 17.0,
        width: 17.0,
        thickness: 1.2
      },
      material: 'BT Resin',
      packageInfo: {
        packageType: PackageType.BGA,
        pinCount: 256,
        pitchSize: 1.0,
        bodySize: '17x17mm'
      },
      electricalProperties: {
        resistance: 0.001,
        conductivity: 58000000
      },
      environmentalRequirements: {
        temperatureMin: -40,
        temperatureMax: 125,
        humidityMax: 60,
        storageConditions: ['防潮', '防静电', '平放存储']
      }
    },
    suppliers: [
      {
        supplierId: 'SUP-UMC-001',
        supplierName: 'Unimicron Technology Corp.',
        contactPerson: 'Chen Wei Ming',
        contactPhone: '+886-3-3551234',
        contactEmail: '<EMAIL>',
        leadTime: 28,
        minOrderQty: 1000,
        isPrimary: true,
        qualityRating: 97.8,
        deliveryRating: 96.2,
        certifications: ['ISO9001', 'IATF16949', 'ISO14001', 'RoHS']
      }
    ],
    qualityInfo: {
      standard: 'IPC-2221',
      specification: 'High Density Interconnect Substrate',
      inspectionMethod: '电气测试、尺寸检查、外观检查',
      acceptanceCriteria: '电气连通性100%、尺寸偏差±0.05mm',
      certifications: ['IPC认证', 'RoHS认证']
    },
    stockInfo: {
      unitOfMeasure: 'PCS',
      safetyStock: 5000,
      maxStock: 25000,
      reorderPoint: 8000,
      leadTime: 28,
      storageRequirements: ['防潮包装', 'ESD防护', '水平存放'],
      shelfLife: 180,
      storageLocation: 'CLEAN-B2'
    },
    costInfo: {
      standardCost: 3.25,
      currentPrice: 3.35,
      currency: 'USD',
      priceUnit: 'PCS',
      lastPriceUpdate: '2024-08-15T00:00:00Z',
      priceHistory: generatePriceHistory(3.25),
      costAnalysis: {
        materialCost: 2.80,
        laborCost: 0.25,
        overheadCost: 0.15,
        transportCost: 0.15
      }
    },
    alternatives: [],
    attachments: [
      {
        id: 'ATT-002',
        fileName: 'bga_substrate_drawing.dwg',
        fileType: 'dwg',
        fileSize: 1024000,
        uploadTime: '2024-07-15T14:00:00Z',
        uploadBy: 'Design Team',
        category: 'DRAWING',
        description: 'BGA基板工程图纸'
      }
    ],
    auditInfo: {
      createdBy: 'Engineer Li',
      createdAt: '2024-07-10T00:00:00Z',
      updatedBy: 'Engineer Li',
      updatedAt: '2024-08-15T10:15:00Z',
      version: 1,
      approvedBy: 'Manager Wang',
      approvedAt: '2024-08-15T11:00:00Z'
    }
  },
  
  // QFP引线框架
  {
    id: 'MAT-L001',
    basicInfo: {
      materialCode: 'LF-QFP144-001',
      materialName: 'QFP144引线框架',
      englishName: 'QFP144 Leadframe',
      specification: '144-Pin QFP Leadframe Cu Alloy',
      category: MaterialMasterCategory.LEADFRAME,
      manufacturer: 'SHINKO Electric Industries',
      brand: 'SHINKO',
      model: 'QFP144-Cu',
      status: MaterialStatus.ACTIVE,
      description: 'QFP144铜合金引线框架',
      remarks: '高导电性铜合金材质'
    },
    technicalParams: {
      dimensions: {
        length: 20.0,
        width: 20.0,
        thickness: 0.15
      },
      material: 'Copper Alloy C194',
      packageInfo: {
        packageType: PackageType.QFP,
        pinCount: 144,
        pitchSize: 0.5,
        bodySize: '20x20mm'
      },
      electricalProperties: {
        conductivity: 45000000
      }
    },
    suppliers: [
      {
        supplierId: 'SUP-SHINKO-001',
        supplierName: 'SHINKO Electric Industries Co., Ltd.',
        contactPerson: 'Tanaka Ichiro',
        contactPhone: '+81-3-6359-7777',
        contactEmail: '<EMAIL>',
        leadTime: 21,
        minOrderQty: 2500,
        isPrimary: true,
        qualityRating: 98.2,
        deliveryRating: 97.5,
        certifications: ['ISO9001', 'IATF16949']
      }
    ],
    qualityInfo: {
      standard: 'EIAJ ED-7303',
      specification: 'QFP Type Leadframe',
      inspectionMethod: '尺寸检查、镀层厚度测量、弯曲测试',
      acceptanceCriteria: '引脚平整度±0.05mm、镀层厚度2-8μm',
      certifications: ['EIAJ认证']
    },
    stockInfo: {
      unitOfMeasure: 'PCS',
      safetyStock: 10000,
      maxStock: 50000,
      reorderPoint: 15000,
      leadTime: 21,
      storageRequirements: ['防氧化', 'ESD防护', '分层包装'],
      shelfLife: 365,
      storageLocation: 'CLEAN-C1'
    },
    costInfo: {
      standardCost: 0.85,
      currentPrice: 0.88,
      currency: 'USD',
      priceUnit: 'PCS',
      lastPriceUpdate: '2024-08-10T00:00:00Z',
      priceHistory: generatePriceHistory(0.85)
    },
    alternatives: [],
    attachments: [],
    auditInfo: {
      createdBy: 'Engineer Zhou',
      createdAt: '2024-06-15T00:00:00Z',
      updatedBy: 'Engineer Zhou',
      updatedAt: '2024-08-10T09:20:00Z',
      version: 1
    }
  },
  
  // 金线
  {
    id: 'MAT-W001',
    basicInfo: {
      materialCode: 'WIRE-AU-25',
      materialName: '金线25微米',
      englishName: 'Gold Wire 25μm',
      specification: 'Au Wire 25μm 99.99% Purity',
      category: MaterialMasterCategory.WIREBOND,
      manufacturer: 'Tanaka Holdings',
      brand: 'TANAKA',
      model: 'AU-25-9999',
      status: MaterialStatus.ACTIVE,
      description: '高纯度金线，用于芯片键合',
      remarks: '贵金属材料，需严格库存管控'
    },
    technicalParams: {
      dimensions: {
        diameter: 25
      },
      material: 'Gold',
      purity: 99.99,
      wirebondInfo: {
        wireType: WireType.GOLD,
        diameter: 25,
        tensileStrength: 180,
        elongation: 2.5,
        purity: 99.99
      }
    },
    suppliers: [
      {
        supplierId: 'SUP-TANAKA-001',
        supplierName: 'Tanaka Holdings Co., Ltd.',
        contactPerson: 'Sato Hiroshi',
        contactPhone: '+81-3-6266-5200',
        contactEmail: '<EMAIL>',
        leadTime: 14,
        minOrderQty: 1,
        isPrimary: true,
        qualityRating: 99.5,
        deliveryRating: 98.8,
        certifications: ['ISO9001', 'LBMA认证']
      }
    ],
    qualityInfo: {
      standard: 'ASTM B298',
      specification: 'Gold Wire for Electronic Applications',
      inspectionMethod: '纯度检测、抗拉强度测试、表面检查',
      acceptanceCriteria: '纯度≥99.99%、抗拉强度≥180MPa',
      certifications: ['LBMA认证', 'RoHS认证']
    },
    stockInfo: {
      unitOfMeasure: 'KG',
      safetyStock: 5.0,
      maxStock: 20.0,
      reorderPoint: 8.0,
      leadTime: 14,
      storageRequirements: ['密封包装', '防潮', '防氧化', '贵金属保险库'],
      shelfLife: 9999,
      storageLocation: 'VAULT-A1'
    },
    costInfo: {
      standardCost: 58200.00,
      currentPrice: 59800.00,
      currency: 'USD',
      priceUnit: 'KG',
      lastPriceUpdate: '2024-08-25T00:00:00Z',
      priceHistory: generatePriceHistory(58200, 24), // 2年价格历史
      costAnalysis: {
        materialCost: 58000.00,
        laborCost: 100.00,
        overheadCost: 50.00,
        transportCost: 50.00
      }
    },
    alternatives: [
      {
        alternativeCode: 'WIRE-AU-20',
        alternativeName: '金线20微米',
        relationType: 'PARTIAL',
        substitutionRatio: 0.8,
        priority: 2,
        remarks: '适用于特定应用',
        validFrom: '2024-01-01'
      }
    ],
    attachments: [
      {
        id: 'ATT-003',
        fileName: 'gold_wire_certificate.pdf',
        fileType: 'pdf',
        fileSize: 512000,
        uploadTime: '2024-08-01T16:00:00Z',
        uploadBy: 'QC Manager',
        category: 'CERTIFICATE',
        description: '金线纯度检测证书'
      }
    ],
    auditInfo: {
      createdBy: 'Materials Manager',
      createdAt: '2024-05-01T00:00:00Z',
      updatedBy: 'Materials Manager',
      updatedAt: '2024-08-25T11:30:00Z',
      version: 3,
      approvedBy: 'General Manager',
      approvedAt: '2024-08-25T12:00:00Z'
    }
  },
  
  // 塑封料
  {
    id: 'MAT-M001',
    basicInfo: {
      materialCode: 'EMC-A1-BK',
      materialName: 'A1级黑色塑封料',
      englishName: 'EMC Grade A1 Black',
      specification: 'Epoxy Molding Compound A1 Grade Black',
      category: MaterialMasterCategory.MOLDING,
      manufacturer: 'Sumitomo Bakelite',
      brand: 'SUMITOMO',
      model: 'EME-G770H',
      status: MaterialStatus.ACTIVE,
      description: 'A1级黑色环氧塑封料，适用于高可靠性产品',
      remarks: '低应力、高阻燃等级'
    },
    technicalParams: {
      material: 'Epoxy Resin',
      environmentalRequirements: {
        temperatureMin: -55,
        temperatureMax: 175,
        storageConditions: ['干燥环境', '避光保存', '密封包装']
      }
    },
    suppliers: [
      {
        supplierId: 'SUP-SUMITOMO-001',
        supplierName: 'Sumitomo Bakelite Co., Ltd.',
        contactPerson: 'Nakamura Kenji',
        contactPhone: '+81-3-5405-5000',
        contactEmail: '<EMAIL>',
        leadTime: 35,
        minOrderQty: 500,
        isPrimary: true,
        qualityRating: 96.8,
        deliveryRating: 95.2,
        certifications: ['ISO9001', 'ISO14001', 'UL认证']
      }
    ],
    qualityInfo: {
      standard: 'IEC 60249-2-13',
      specification: 'Thermosetting Molding Compound',
      inspectionMethod: '流动性测试、固化特性测试、热稳定性测试',
      acceptanceCriteria: '流动长度≥30cm、固化时间60-120s',
      certifications: ['UL认证', 'RoHS认证', '阻燃V-0等级']
    },
    stockInfo: {
      unitOfMeasure: 'KG',
      safetyStock: 2000,
      maxStock: 10000,
      reorderPoint: 3500,
      leadTime: 35,
      storageRequirements: ['干燥环境', '温度≤25°C', '湿度≤60%', '避光保存'],
      shelfLife: 180,
      storageLocation: 'CHEM-DRY-A1'
    },
    costInfo: {
      standardCost: 12.50,
      currentPrice: 12.80,
      currency: 'USD',
      priceUnit: 'KG',
      lastPriceUpdate: '2024-08-20T00:00:00Z',
      priceHistory: generatePriceHistory(12.50)
    },
    alternatives: [],
    attachments: [],
    auditInfo: {
      createdBy: 'Process Engineer',
      createdAt: '2024-07-01T00:00:00Z',
      updatedBy: 'Process Engineer',
      updatedAt: '2024-08-20T14:45:00Z',
      version: 1
    }
  },
  
  // 测试板
  {
    id: 'MAT-T001',
    basicInfo: {
      materialCode: 'TESTBD-BGA256',
      materialName: 'BGA256测试板',
      englishName: 'BGA256 Test Board',
      specification: 'Load Board for BGA256 Testing',
      category: MaterialMasterCategory.TEST_DEVICE,
      manufacturer: 'Advantest Corporation',
      brand: 'ADVANTEST',
      model: 'T5375-BGA256',
      status: MaterialStatus.ACTIVE,
      description: 'BGA256封装专用测试板',
      remarks: '高频测试专用'
    },
    technicalParams: {
      dimensions: {
        length: 150.0,
        width: 100.0,
        thickness: 2.4
      },
      material: 'FR4 + Rogers',
      packageInfo: {
        packageType: PackageType.BGA,
        pinCount: 256
      },
      electricalProperties: {
        voltage: 5.0,
        current: 2.0
      }
    },
    suppliers: [
      {
        supplierId: 'SUP-ADV-001',
        supplierName: 'Advantest Corporation',
        contactPerson: 'Yoshida Takeshi',
        contactPhone: '+81-3-3214-7500',
        contactEmail: '<EMAIL>',
        leadTime: 42,
        minOrderQty: 5,
        isPrimary: true,
        qualityRating: 98.9,
        deliveryRating: 97.1,
        certifications: ['ISO9001', 'ISO14001']
      }
    ],
    qualityInfo: {
      standard: 'IPC-2221',
      specification: 'Test Board for IC Testing',
      inspectionMethod: '电气连续性测试、高频特性测试',
      acceptanceCriteria: '电气连通性100%、插拔寿命≥10000次',
      certifications: ['IPC认证']
    },
    stockInfo: {
      unitOfMeasure: 'PCS',
      safetyStock: 20,
      maxStock: 100,
      reorderPoint: 35,
      leadTime: 42,
      storageRequirements: ['防静电包装', '水平存放', '避免碰撞'],
      shelfLife: 9999,
      storageLocation: 'TEST-EQ-A1'
    },
    costInfo: {
      standardCost: 1250.00,
      currentPrice: 1280.00,
      currency: 'USD',
      priceUnit: 'PCS',
      lastPriceUpdate: '2024-07-01T00:00:00Z',
      priceHistory: generatePriceHistory(1250.00, 6)
    },
    alternatives: [],
    attachments: [],
    auditInfo: {
      createdBy: 'Test Engineer',
      createdAt: '2024-06-01T00:00:00Z',
      updatedBy: 'Test Engineer',
      updatedAt: '2024-07-01T16:20:00Z',
      version: 1
    }
  }
]

// 物料使用统计数据
export const mockMaterialUsageStats: MaterialUsageStats[] = [
  {
    materialCode: 'WIRE-AU-25',
    materialName: '金线25微米',
    category: MaterialMasterCategory.WIREBOND,
    usageQty: 125.8,
    usageValue: 7525400.00,
    frequency: 156,
    lastUsedDate: '2024-08-25',
    trend: 'UP'
  },
  {
    materialCode: 'SUB-BGA256-001',
    materialName: 'BGA256基板',
    category: MaterialMasterCategory.SUBSTRATE,
    usageQty: 45600,
    usageValue: 152760.00,
    frequency: 89,
    lastUsedDate: '2024-08-26',
    trend: 'STABLE'
  },
  {
    materialCode: 'LF-QFP144-001',
    materialName: 'QFP144引线框架',
    category: MaterialMasterCategory.LEADFRAME,
    usageQty: 78900,
    usageValue: 69432.00,
    frequency: 234,
    lastUsedDate: '2024-08-25',
    trend: 'DOWN'
  }
]

// 缺料风险预警数据
export const mockMaterialRiskWarnings: MaterialRiskWarning[] = [
  {
    materialCode: 'WIRE-AU-25',
    materialName: '金线25微米',
    riskType: 'PRICE_INCREASE',
    riskLevel: 'HIGH',
    description: '黄金价格持续上涨，预计未来3个月金线成本将上升15-20%',
    impact: '直接影响产品毛利率，月影响金额约200万USD',
    recommendation: '考虑提前采购3个月用量或寻找替代方案',
    dueDate: '2024-09-30',
    responsible: 'Materials Manager',
    status: 'OPEN'
  },
  {
    materialCode: 'EMC-A1-BK',
    materialName: 'A1级黑色塑封料',
    riskType: 'SHORTAGE',
    riskLevel: 'MEDIUM',
    description: '供应商产能限制，Q4季度供货可能紧张',
    impact: '可能影响50%生产计划，需要寻找备用供应商',
    recommendation: '启动备用供应商认证程序，增加安全库存',
    dueDate: '2024-10-15',
    responsible: 'Supply Chain Manager',
    status: 'IN_PROGRESS'
  },
  {
    materialCode: 'TESTBD-BGA256',
    materialName: 'BGA256测试板',
    riskType: 'DISCONTINUATION',
    riskLevel: 'LOW',
    description: '供应商通知该型号将于2025年停产',
    impact: '影响新产品开发和现有产品维护',
    recommendation: '寻找替代产品或设计升级方案',
    dueDate: '2025-06-30',
    responsible: 'Test Engineer',
    status: 'OPEN'
  }
]

// 生成新的物料编码
export const generateNewMaterialCode = (category: MaterialMasterCategory): string => {
  const prefixMap = {
    [MaterialMasterCategory.WAFER]: 'WAFER',
    [MaterialMasterCategory.SUBSTRATE]: 'SUB',
    [MaterialMasterCategory.LEADFRAME]: 'LF',
    [MaterialMasterCategory.WIREBOND]: 'WIRE',
    [MaterialMasterCategory.MOLDING]: 'EMC',
    [MaterialMasterCategory.TEST_DEVICE]: 'TEST',
    [MaterialMasterCategory.CHEMICAL]: 'CHEM',
    [MaterialMasterCategory.CONSUMABLE]: 'CONS',
    [MaterialMasterCategory.AUXILIARY]: 'AUX'
  }
  
  const prefix = prefixMap[category]
  const timestamp = Date.now().toString().slice(-6)
  return `${prefix}-${timestamp}`
}

// 获取物料分类统计
export const getMaterialCategoryStats = () => {
  const stats: Record<MaterialMasterCategory, number> = {} as any
  
  MATERIAL_CATEGORY_OPTIONS.forEach(option => {
    stats[option.value] = mockMaterialMasters.filter(
      material => material.basicInfo.category === option.value
    ).length
  })
  
  return stats
}

// 获取供应商绩效统计
export const getSupplierPerformanceStats = () => {
  const supplierStats = new Map()
  
  mockMaterialMasters.forEach(material => {
    material.suppliers.forEach(supplier => {
      if (!supplierStats.has(supplier.supplierId)) {
        supplierStats.set(supplier.supplierId, {
          supplierId: supplier.supplierId,
          supplierName: supplier.supplierName,
          materialCount: 0,
          avgQualityRating: 0,
          avgDeliveryRating: 0,
          avgLeadTime: 0
        })
      }
      
      const stats = supplierStats.get(supplier.supplierId)
      stats.materialCount++
      stats.avgQualityRating += supplier.qualityRating
      stats.avgDeliveryRating += supplier.deliveryRating
      stats.avgLeadTime += supplier.leadTime
    })
  })
  
  // 计算平均值
  supplierStats.forEach(stats => {
    stats.avgQualityRating = parseFloat((stats.avgQualityRating / stats.materialCount).toFixed(1))
    stats.avgDeliveryRating = parseFloat((stats.avgDeliveryRating / stats.materialCount).toFixed(1))
    stats.avgLeadTime = parseFloat((stats.avgLeadTime / stats.materialCount).toFixed(1))
  })
  
  return Array.from(supplierStats.values())
}

// 获取成本分析数据
export const getMaterialCostAnalysis = () => {
  return mockMaterialMasters.map(material => ({
    materialCode: material.basicInfo.materialCode,
    materialName: material.basicInfo.materialName,
    category: material.basicInfo.category,
    currentPrice: material.costInfo.currentPrice,
    standardCost: material.costInfo.standardCost,
    variance: material.costInfo.currentPrice - material.costInfo.standardCost,
    variancePercent: ((material.costInfo.currentPrice - material.costInfo.standardCost) / material.costInfo.standardCost * 100).toFixed(2),
    totalValue: material.stockInfo ? material.costInfo.currentPrice * (material.stockInfo.maxStock || 0) : 0,
    trend: Math.random() > 0.5 ? 'UP' : 'DOWN'
  }))
}