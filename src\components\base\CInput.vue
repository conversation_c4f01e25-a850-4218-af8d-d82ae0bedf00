<template>
  <div :class="wrapperClasses">
    <label v-if="label" :for="inputId" class="c-input__label">
      {{ label }}
      <span v-if="required" class="c-input__required">*</span>
    </label>
    
    <div class="c-input__container">
      <span v-if="$slots.prefix" class="c-input__prefix">
        <slot name="prefix" />
      </span>
      
      <component
        :is="inputComponent"
        :id="inputId"
        ref="inputRef"
        v-model="inputValue"
        :class="inputClasses"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        :rows="rows"
        :type="inputType"
        :autocomplete="autocomplete"
        v-bind="$attrs"
        @input="handleInput"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
      />
      
      <span v-if="showClear" class="c-input__clear" @click="handleClear">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
        </svg>
      </span>
      
      <span v-if="$slots.suffix" class="c-input__suffix">
        <slot name="suffix" />
      </span>
    </div>
    
    <div v-if="showMessage" class="c-input__message">
      <span v-if="errorMessage" class="c-input__error">{{ errorMessage }}</span>
      <span v-else-if="helpText" class="c-input__help">{{ helpText }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, nextTick, useAttrs, useSlots } from 'vue'
import { generateId } from '@/utils/common'

export interface CInputProps {
  /** 输入框值 */
  modelValue?: string | number
  /** 输入框类型 */
  type?: 'text' | 'password' | 'email' | 'number' | 'tel' | 'url' | 'search' | 'textarea'
  /** 输入框尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 标签文本 */
  label?: string
  /** 占位符文本 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 是否必填 */
  required?: boolean
  /** 最大字符数 */
  maxlength?: number
  /** 文本域行数 */
  rows?: number
  /** 是否显示清除按钮 */
  clearable?: boolean
  /** 错误信息 */
  errorMessage?: string
  /** 帮助文本 */
  helpText?: string
  /** 自动完成 */
  autocomplete?: string
}

const props = withDefaults(defineProps<CInputProps>(), {
  type: 'text',
  size: 'medium',
  disabled: false,
  readonly: false,
  required: false,
  clearable: false,
  rows: 3,
  autocomplete: 'off'
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  input: [value: string | number, event: Event]
  change: [value: string | number, event: Event]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  clear: []
  keydown: [event: KeyboardEvent]
}>()

const attrs = useAttrs()
const slots = useSlots()

// 生成唯一ID
const inputId = generateId('c-input')

// 输入框引用
const inputRef = ref<HTMLInputElement | HTMLTextAreaElement>()

// 内部状态
const isFocused = ref(false)

// 计算输入组件类型
const inputComponent = computed(() => {
  return props.type === 'textarea' ? 'textarea' : 'input'
})

// 计算输入框类型
const inputType = computed(() => {
  return props.type === 'textarea' ? undefined : props.type
})

// 双向绑定值
const inputValue = computed({
  get: () => props.modelValue ?? '',
  set: (value) => emit('update:modelValue', value)
})

// 计算样式类名
const wrapperClasses = computed(() => [
  'c-input',
  `c-input--${props.size}`,
  {
    'c-input--disabled': props.disabled,
    'c-input--readonly': props.readonly,
    'c-input--focused': isFocused.value,
    'c-input--error': !!props.errorMessage,
    'c-input--with-label': !!props.label,
    'c-input--with-prefix': !!slots.prefix,
    'c-input--with-suffix': !!slots.suffix || showClear.value
  }
])

const inputClasses = computed(() => [
  'c-input__control',
  {
    'c-input__control--textarea': props.type === 'textarea'
  }
])

// 计算是否显示清除按钮
const showClear = computed(() => {
  return props.clearable && !props.disabled && !props.readonly && inputValue.value
})

// 计算是否显示消息
const showMessage = computed(() => {
  return !!(props.errorMessage || props.helpText)
})

// 事件处理
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement | HTMLTextAreaElement
  const value = target.value
  emit('input', value, event)
}

const handleChange = (event: Event) => {
  const target = event.target as HTMLInputElement | HTMLTextAreaElement
  const value = target.value
  emit('change', value, event)
}

const handleFocus = (event: FocusEvent) => {
  isFocused.value = true
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  isFocused.value = false
  emit('blur', event)
}

const handleClear = () => {
  inputValue.value = ''
  emit('clear')
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event)
}

// 暴露方法
const focus = () => {
  inputRef.value?.focus()
}

const blur = () => {
  inputRef.value?.blur()
}

const select = () => {
  inputRef.value?.select()
}

defineExpose({
  focus,
  blur,
  select,
  inputRef
})
</script>

<style lang="scss">
.c-input {
  display: flex;
  flex-direction: column;
  gap: var(--form-label-margin);
  
  &__label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    line-height: var(--line-height-base);
  }
  
  &__required {
    color: var(--color-error);
    margin-left: 2px;
  }
  
  &__container {
    position: relative;
    display: flex;
    align-items: center;
    border: 1px solid var(--color-border-base);
    border-radius: var(--radius-base);
    background: var(--color-bg-primary);
    transition: border-color var(--transition-fast), 
                box-shadow var(--transition-fast);
    
    &:hover:not(.c-input--disabled) {
      border-color: var(--color-border-dark);
    }
  }
  
  &__control {
    @include input-base;
    border: none;
    flex: 1;
    min-width: 0;
    
    &--textarea {
      resize: vertical;
      font-family: var(--font-family-base);
    }
    
    &:focus {
      border: none;
      box-shadow: none;
    }
  }
  
  &__prefix,
  &__suffix {
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-2);
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    flex-shrink: 0;
  }
  
  &__prefix {
    border-right: 1px solid var(--color-border-light);
    margin-right: var(--spacing-1);
  }
  
  &__suffix {
    border-left: 1px solid var(--color-border-light);
    margin-left: var(--spacing-1);
  }
  
  &__clear {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-2);
    color: var(--color-text-tertiary);
    cursor: pointer;
    transition: color var(--transition-fast);
    
    svg {
      width: 12px;
      height: 12px;
    }
    
    &:hover {
      color: var(--color-text-secondary);
    }
  }
  
  &__message {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-base);
    margin-top: 2px;
  }
  
  &__error {
    color: var(--color-error);
  }
  
  &__help {
    color: var(--color-text-secondary);
  }
  
  // 尺寸样式
  &--small {
    .c-input__container {
      min-height: 28px;
    }
    
    .c-input__control {
      padding: 4px 8px;
      font-size: var(--font-size-xs);
    }
  }
  
  &--medium {
    .c-input__container {
      min-height: 36px;
    }
    
    .c-input__control {
      padding: var(--spacing-2) var(--spacing-3);
      font-size: var(--font-size-sm);
    }
  }
  
  &--large {
    .c-input__container {
      min-height: 44px;
    }
    
    .c-input__control {
      padding: 10px var(--spacing-4);
      font-size: var(--font-size-base);
    }
  }
  
  // 状态样式
  &--focused {
    .c-input__container {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
  }
  
  &--error {
    .c-input__container {
      border-color: var(--color-error);
      
      &:hover {
        border-color: var(--color-error);
      }
    }
    
    &.c-input--focused {
      .c-input__container {
        border-color: var(--color-error);
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
      }
    }
  }
  
  &--disabled {
    .c-input__container {
      background: var(--color-bg-disabled);
      cursor: not-allowed;
    }
    
    .c-input__control {
      color: var(--color-text-disabled);
      cursor: not-allowed;
    }
    
    .c-input__label {
      color: var(--color-text-disabled);
    }
  }
  
  &--readonly {
    .c-input__container {
      background: var(--color-bg-secondary);
    }
    
    .c-input__control {
      cursor: default;
    }
  }
}</style>