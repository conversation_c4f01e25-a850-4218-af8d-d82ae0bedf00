/**
 * IC封测CIM系统 - 工作流引擎类型定义
 * Workflow Engine Type Definitions for IC Packaging & Testing CIM System
 */

// ===== 工作流基础类型 =====

// 工作流状态
export enum WorkflowStatus {
  DRAFT = 'draft', // 草稿
  ACTIVE = 'active', // 激活
  PAUSED = 'paused', // 暂停
  COMPLETED = 'completed', // 完成
  CANCELLED = 'cancelled', // 取消
  ERROR = 'error' // 错误
}

// 节点类型
export enum NodeType {
  START = 'start', // 开始节点
  END = 'end', // 结束节点
  TASK = 'task', // 任务节点
  DECISION = 'decision', // 决策节点
  PARALLEL = 'parallel', // 并行节点
  MERGE = 'merge', // 合并节点
  SUBPROCESS = 'subprocess', // 子流程节点
  TIMER = 'timer', // 定时器节点
  EVENT = 'event' // 事件节点
}

// 任务类型
export enum TaskType {
  MANUAL = 'manual', // 人工任务
  AUTOMATIC = 'automatic', // 自动任务
  APPROVAL = 'approval', // 审批任务
  NOTIFICATION = 'notification', // 通知任务
  SCRIPT = 'script', // 脚本任务
  SERVICE = 'service', // 服务任务
  USER = 'user' // 用户任务
}

// 任务状态
export enum TaskStatus {
  PENDING = 'pending', // 待执行
  RUNNING = 'running', // 执行中
  COMPLETED = 'completed', // 已完成
  FAILED = 'failed', // 失败
  CANCELLED = 'cancelled', // 已取消
  SKIPPED = 'skipped' // 跳过
}

// 工作流实例状态
export enum InstanceStatus {
  CREATED = 'created', // 已创建
  RUNNING = 'running', // 运行中
  SUSPENDED = 'suspended', // 暂停
  COMPLETED = 'completed', // 完成
  CANCELLED = 'cancelled', // 取消
  ERROR = 'error' // 错误
}

// ===== 工作流定义 =====

// 工作流定义
export interface WorkflowDefinition {
  id: string
  name: string
  description: string
  version: string
  category: string
  status: WorkflowStatus
  nodes: WorkflowNode[]
  edges: WorkflowEdge[]
  variables: WorkflowVariable[]
  triggers: WorkflowTrigger[]
  metadata: {
    createdBy: string
    createdAt: string
    updatedBy: string
    updatedAt: string
    tags: string[]
  }
}

// 工作流节点
export interface WorkflowNode {
  id: string
  name: string
  description?: string
  type: NodeType
  taskType?: TaskType
  position: {
    x: number
    y: number
  }
  properties: NodeProperties
  conditions?: NodeCondition[]
  timeout?: number
  retryCount?: number
}

// 节点属性（根据不同节点类型有不同属性）
export interface NodeProperties {
  // 任务节点属性
  assignee?: string // 任务分配给谁
  candidateGroups?: string[] // 候选组
  formKey?: string // 表单Key
  dueDate?: string // 到期时间
  priority?: number // 优先级

  // 决策节点属性
  decisionTable?: DecisionRule[] // 决策表
  defaultOutcome?: string // 默认结果

  // 脚本任务属性
  scriptType?: 'javascript' | 'groovy' | 'python' // 脚本类型
  scriptContent?: string // 脚本内容
  inputParams?: string[] // 输入参数
  outputParams?: string[] // 输出参数

  // 服务任务属性
  serviceUrl?: string // 服务URL
  serviceMethod?: 'GET' | 'POST' | 'PUT' | 'DELETE' // 服务方法
  requestBody?: any // 请求体
  headers?: Record<string, string> // 请求头

  // 定时器属性
  timerType?: 'duration' | 'date' | 'cycle' // 定时器类型
  timerExpression?: string // 定时器表达式

  // 自定义属性
  [key: string]: any
}

// 节点条件
export interface NodeCondition {
  expression: string // 条件表达式
  outcome: string // 条件结果
  description?: string // 条件描述
}

// 决策规则
export interface DecisionRule {
  condition: string // 条件
  outcome: string // 结果
  description?: string // 描述
  priority: number // 优先级
}

// 工作流边（连线）
export interface WorkflowEdge {
  id: string
  sourceNodeId: string // 源节点ID
  targetNodeId: string // 目标节点ID
  condition?: string // 流转条件
  name?: string // 连线名称
  properties?: {
    sourceHandle?: string // 源节点连接点
    targetHandle?: string // 目标节点连接点
    style?: any // 样式
  }
}

// 工作流变量
export interface WorkflowVariable {
  name: string // 变量名
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' // 变量类型
  defaultValue?: any // 默认值
  description?: string // 描述
  required?: boolean // 是否必填
  scope: 'global' | 'local' // 作用域
}

// 工作流触发器
export interface WorkflowTrigger {
  id: string
  name: string
  type: 'manual' | 'timer' | 'event' | 'api' // 触发类型
  config: TriggerConfig
  enabled: boolean
}

// 触发器配置
export interface TriggerConfig {
  // 定时器触发器
  cron?: string // Cron表达式
  startTime?: string // 开始时间
  endTime?: string // 结束时间

  // 事件触发器
  eventType?: string // 事件类型
  eventSource?: string // 事件源
  conditions?: string[] // 事件条件

  // API触发器
  endpoint?: string // API端点
  method?: string // HTTP方法
  authentication?: any // 认证配置

  [key: string]: any
}

// ===== 工作流实例 =====

// 工作流实例
export interface WorkflowInstance {
  id: string
  workflowDefinitionId: string
  name: string
  status: InstanceStatus
  businessKey?: string // 业务键（如订单号）
  startTime: string
  endTime?: string
  duration?: number // 执行时长（毫秒）
  currentNodes: string[] // 当前活动节点
  variables: Record<string, any> // 实例变量
  context: Record<string, any> // 上下文数据
  metadata: {
    initiator: string // 发起人
    priority: number // 优先级
    tags: string[] // 标签
    parentInstanceId?: string // 父实例ID
  }
  tasks: TaskInstance[] // 任务实例列表
  history: WorkflowHistory[] // 执行历史
  createdAt: string
  updatedAt: string
}

// 任务实例
export interface TaskInstance {
  id: string
  workflowInstanceId: string
  nodeId: string
  name: string
  type: TaskType
  status: TaskStatus
  assignee?: string // 当前处理人
  candidateUsers?: string[] // 候选用户
  candidateGroups?: string[] // 候选组
  formKey?: string // 表单键
  formData?: Record<string, any> // 表单数据
  startTime?: string // 开始时间
  endTime?: string // 结束时间
  dueDate?: string // 到期时间
  priority: number // 优先级
  variables: Record<string, any> // 任务变量
  comments?: TaskComment[] // 任务评论
  attachments?: TaskAttachment[] // 附件
  executionHistory: TaskExecution[] // 执行历史
  createdAt: string
  updatedAt: string
}

// 任务评论
export interface TaskComment {
  id: string
  userId: string
  userName: string
  content: string
  createdAt: string
}

// 任务附件
export interface TaskAttachment {
  id: string
  name: string
  url: string
  size: number
  type: string
  uploadedBy: string
  uploadedAt: string
}

// 任务执行记录
export interface TaskExecution {
  id: string
  action: 'claim' | 'complete' | 'cancel' | 'delegate' | 'assign' // 操作类型
  userId: string
  userName: string
  comment?: string
  variables?: Record<string, any>
  result?: any
  timestamp: string
}

// 工作流历史
export interface WorkflowHistory {
  id: string
  nodeId: string
  nodeName: string
  nodeType: NodeType
  action: 'enter' | 'leave' | 'skip' | 'error' // 动作类型
  status: TaskStatus
  userId?: string
  userName?: string
  variables?: Record<string, any>
  duration?: number // 节点执行时长
  timestamp: string
  details?: string // 详细信息
}

// ===== 工作流查询和管理 =====

// 工作流定义查询参数
export interface WorkflowDefinitionQueryParams {
  page?: number
  pageSize?: number
  keyword?: string
  category?: string
  status?: WorkflowStatus
  createdBy?: string
  tags?: string[]
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 工作流实例查询参数
export interface WorkflowInstanceQueryParams {
  page?: number
  pageSize?: number
  workflowDefinitionId?: string
  businessKey?: string
  status?: InstanceStatus[]
  initiator?: string
  startTimeRange?: [string, string]
  endTimeRange?: [string, string]
  currentNode?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 任务查询参数
export interface TaskQueryParams {
  page?: number
  pageSize?: number
  assignee?: string
  candidateUser?: string
  candidateGroup?: string
  status?: TaskStatus[]
  type?: TaskType[]
  priority?: number[]
  dueDateRange?: [string, string]
  workflowDefinitionId?: string
  businessKey?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// ===== IC封测专用工作流类型 =====

// IC封测工作流类别
export enum ICWorkflowCategory {
  ORDER_PROCESS = 'order_process', // 订单流程
  PRODUCTION = 'production', // 生产流程
  QUALITY_CONTROL = 'quality_control', // 质量控制
  EQUIPMENT_MAINTENANCE = 'equipment_maintenance', // 设备维护
  MATERIAL_MANAGEMENT = 'material_management', // 物料管理
  CUSTOMER_SERVICE = 'customer_service', // 客户服务
  ENGINEERING_CHANGE = 'engineering_change', // 工程变更
  SUPPLIER_MANAGEMENT = 'supplier_management' // 供应商管理
}

// 订单驱动的工作流上下文
export interface OrderWorkflowContext {
  orderId: string
  orderNumber: string
  customerId: string
  customerName: string
  productCode: string
  productName: string
  packageType: string
  quantity: number
  priority: string
  deliveryDate: string
  processRequirements: {
    waferSize: number
    testProgram: string
    qualityLevel: string
    specialRequirements?: string[]
  }
  currentStage: 'cp_test' | 'assembly' | 'ft_test' | 'packaging' | 'shipping'
  stageProgress: Record<string, number>
}

// 生产工作流上下文
export interface ProductionWorkflowContext {
  lotNumber: string
  waferLot: string
  productCode: string
  quantity: number
  equipmentIds: string[]
  processParameters: Record<string, any>
  qualityCheckpoints: string[]
  currentStation: string
  nextStation: string
  yieldData?: {
    inputQty: number
    outputQty: number
    defectQty: number
    yieldRate: number
  }
}

// 工作流统计信息
export interface WorkflowStats {
  totalDefinitions: number
  activeDefinitions: number
  totalInstances: number
  runningInstances: number
  completedInstances: number
  failedInstances: number
  pendingTasks: number
  overdueTasks: number
  averageExecutionTime: number
  instancesByStatus: Record<InstanceStatus, number>
  tasksByType: Record<TaskType, number>
  performanceMetrics: {
    throughput: number // 吞吐量（实例/小时）
    efficiency: number // 效率（完成率）
    averageCycleTime: number // 平均周期时间
  }
}

export default {
  WorkflowStatus,
  NodeType,
  TaskType,
  TaskStatus,
  InstanceStatus,
  ICWorkflowCategory
}
