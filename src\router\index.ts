import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 导入页面组件 - 专业版IC封测CIM系统 (菜单切换性能优化版)
// 核心高频页面：立即加载，确保菜单切换无延迟
import Home from '@/views/Home.vue'
import ComponentDemo from '@/views/ComponentDemo.vue'
import CustomerList from '@/views/CustomerList.vue'
import OrderManagement from '@/views/Orders.vue'

// 中频访问页面：使用webpackPrefetch预加载
const ProductionPlan = () => import(/* webpackPrefetch: true */ '@/views/ProductionPlan.vue')

// 低频访问页面：普通lazy loading
const Test = () => import('@/views/Test.vue')
const QualityModuleTest = () => import('@/views/QualityModuleTest.vue')

// 客户关系管理 - Customer Relationship Management
const ContactManagement = () =>
  import(/* webpackPrefetch: true */ '@/views/customer/ContactManagement.vue')
const CommunicationRecords = () =>
  import(/* webpackPrefetch: true */ '@/views/customer/CommunicationRecords.vue')

// 制造执行管理 - Manufacturing Execution System (立即加载核心模块)
import CPTesting from '@/views/manufacturing/CPTesting.vue'
import Assembly from '@/views/manufacturing/Assembly.vue'
import FinalTest from '@/views/manufacturing/FinalTest.vue'

// 设备管理系统 - Equipment Management System (设备状态高频访问)
import EquipmentStatus from '@/views/equipment/EquipmentStatus.vue'
const SecsGemManager = () =>
  import(/* webpackPrefetch: true */ '@/views/equipment/SecsGemManager.vue')
const MaintenanceManager = () => import('@/views/equipment/MaintenanceManager.vue')
const EquipmentConfig = () => import('@/views/equipment/EquipmentConfig.vue')

// 质量管理系统 - Quality Management System
const SPCControl = () => import('@/views/quality/SPCControl.vue')
const QualityTraceability = () => import('@/views/quality/QualityTraceability.vue')
const ComplianceManager = () => import('@/views/quality/ComplianceManager.vue')
const QualityInspection = () => import('@/views/quality/QualityInspection.vue')
const QualityAnalytics = () => import('@/views/quality/QualityAnalytics.vue')

// 物料与库存管理 - Material & Inventory Management
const InventoryManagement = () => import('@/views/material/InventoryManagement.vue')

// 基础数据管理 - Master Data Management (第一阶段核心功能)
const CustomerMasterData = () => 
  import(/* webpackPrefetch: true */ '@/views/masterData/CustomerMasterData.vue')
const ProductMasterData = () =>
  import(/* webpackPrefetch: true */ '@/views/masterData/ProductMasterData.vue')
const MaterialMasterData = () =>
  import(/* webpackPrefetch: true */ '@/views/masterData/MaterialMasterData.vue')
const OrganizationStructure = () =>
  import(/* webpackPrefetch: true */ '@/views/masterData/OrganizationStructure.vue')

// 询价管理系统 - Inquiry Management System (第一阶段核心功能)
const InquiryList = () => import(/* webpackPrefetch: true */ '@/views/inquiry/InquiryList.vue')

// 报价管理系统 - Quotation Management System (第一阶段核心功能)
const QuotationForm = () =>
  import(/* webpackPrefetch: true */ '@/views/quotation/QuotationForm.vue')

// 订单跟踪系统 - Order Tracking System (第一阶段核心功能)
const OrderTracking = () => import(/* webpackPrefetch: true */ '@/views/order/OrderTracking.vue')

// 订单评审系统 - Order Review System (第一阶段核心功能)
const OrderReview = () => import(/* webpackPrefetch: true */ '@/views/order/OrderReview.vue')

// 合同管理系统 - Contract Management System (第一阶段核心功能)
const ContractManagement = () =>
  import(/* webpackPrefetch: true */ '@/views/contract/ContractManagement.vue')

// 实时监控中心 - Real-time Monitoring Center
const MonitoringCenter = () => import('@/views/monitoring/MonitoringCenter.vue')
const ProductionDashboard = () => import('@/views/monitoring/ProductionDashboard.vue')
const EquipmentDashboard = () => import('@/views/monitoring/EquipmentDashboard.vue')
const QualityDashboard = () => import('@/views/monitoring/QualityDashboard.vue')
const MonitoringTest = () => import('@/views/MonitoringTest.vue')

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/demo',
    name: 'ComponentDemo',
    component: ComponentDemo,
    meta: {
      title: '组件演示'
    }
  },
  {
    path: '/customers',
    name: 'CustomerList',
    component: CustomerList,
    meta: {
      title: '客户管理'
    }
  },
  {
    path: '/customer/contacts',
    name: 'ContactManagement',
    component: ContactManagement,
    meta: {
      title: '客户联系人管理',
      icon: 'user',
      description: 'Customer Contact Management - IC设计客户关键联系人全生命周期管理系统'
    }
  },
  {
    path: '/customer/communications',
    name: 'CommunicationRecords',
    component: CommunicationRecords,
    meta: {
      title: '客户沟通记录',
      icon: 'chat-dot-round',
      description: 'Communication Records Management - 客户沟通历史跟踪与业务进展管理'
    }
  },
  {
    path: '/orders',
    name: 'OrderManagement',
    component: OrderManagement,
    meta: {
      title: '订单管理'
    }
  },
  {
    path: '/production',
    name: 'ProductionPlan',
    component: ProductionPlan,
    meta: {
      title: '生产计划'
    }
  },
  {
    path: '/test',
    name: 'Test',
    component: Test,
    meta: {
      title: '开发完成验证'
    }
  },
  {
    path: '/quality-test',
    name: 'QualityModuleTest',
    component: QualityModuleTest,
    meta: {
      title: '质量管理模块测试'
    }
  },
  {
    path: '/monitoring-test',
    name: 'MonitoringTest',
    component: MonitoringTest,
    meta: {
      title: '监控系统功能测试'
    }
  },
  // 第一阶段：基础数据管理 - 客户主数据管理
  {
    path: '/master-data/customers',
    name: 'CustomerMasterData',
    component: CustomerMasterData,
    meta: {
      title: '客户主数据管理',
      icon: 'user',
      description: 'Customer Master Data Management - IC设计公司客户标准化主数据维护，确保数据一致性'
    }
  },
  // 第一阶段：基础数据管理 - 产品主数据管理
  {
    path: '/master-data/products',
    name: 'ProductMasterData',
    component: ProductMasterData,
    meta: {
      title: '产品主数据管理',
      icon: 'cpu',
      description: 'Product Master Data Management - IC产品规格库标准化管理，封测服务产品技术参数维护'
    }
  },
  // 第一阶段：基础数据管理 - 物料主数据管理
  {
    path: '/master-data/materials',
    name: 'MaterialMasterData',
    component: MaterialMasterData,
    meta: {
      title: '物料主数据管理',
      icon: 'box',
      description: 'Material Master Data Management - OSAT封装测试过程中所有物料的标准化管理，支持BOM管理和采购计划'
    }
  },
  // 第一阶段：基础数据管理 - 组织架构管理
  {
    path: '/master-data/organization',
    name: 'OrganizationStructure',
    component: OrganizationStructure,
    meta: {
      title: '组织架构管理',
      icon: 'office-building',
      description: 'Organization Structure Management - IC封测工厂内部组织架构维护管理，支持权限分配和工作流配置'
    }
  },
  // 第一阶段：订单全流程核心功能 - 询价管理路由
  {
    path: '/inquiry/list',
    name: 'InquiryList',
    component: InquiryList,
    meta: {
      title: '客户询价管理',
      icon: 'document',
      description: 'Customer Inquiry Management - IC封装测试询价全流程管理，订单驱动的数字化基础'
    }
  },
  // 第一阶段：报价单生成管理 - 智能报价系统
  {
    path: '/quotation/form/:id?',
    name: 'QuotationForm',
    component: QuotationForm,
    meta: {
      title: '智能报价单生成',
      icon: 'coin',
      description: 'Intelligent Quotation System - 基于询价信息自动计算成本并生成专业报价单'
    }
  },
  // 第一阶段：订单跟踪管理 - 生产全流程可视化跟踪
  {
    path: '/order/tracking',
    name: 'OrderTracking',
    component: OrderTracking,
    meta: {
      title: '订单跟踪',
      icon: 'view',
      description: 'Order Tracking System - 生产全流程可视化跟踪，OSAT专业生产进度监控'
    }
  },
  // 第一阶段：订单评审系统 - 多部门协同评审决策
  {
    path: '/order/review/:orderId',
    name: 'OrderReview',
    component: OrderReview,
    meta: {
      title: '订单评审',
      icon: 'check',
      description: 'Order Review System - 技术、产能、质量、供应链、财务五维度评审决策系统'
    }
  },
  // 第一阶段：合同管理系统 - 合同全生命周期管理
  {
    path: '/contract/management',
    name: 'ContractManagement',
    component: ContractManagement,
    meta: {
      title: '合同管理',
      icon: 'document',
      description: 'Contract Management System - 合同全生命周期管理，从合同生成到执行监控'
    }
  },
  // 物料与库存管理路由
  {
    path: '/inventory/management',
    name: 'InventoryManagement',
    component: InventoryManagement,
    meta: {
      title: '物料与库存管理',
      icon: 'box',
      description: 'Material & Inventory Management - 半导体专用仓库管理系统'
    }
  },
  // 制造执行管理路由
  {
    path: '/manufacturing/cp-testing',
    name: 'CPTesting',
    component: CPTesting,
    meta: {
      title: 'CP晶圆测试',
      icon: 'cpu',
      description: 'Crystal Probe Testing - 晶圆电测管理'
    }
  },
  {
    path: '/manufacturing/assembly',
    name: 'Assembly',
    component: Assembly,
    meta: {
      title: '封装工艺控制',
      icon: 'component',
      description: 'Assembly Process - 贴片线键合塑封管理'
    }
  },
  {
    path: '/manufacturing/final-test',
    name: 'FinalTest',
    component: FinalTest,
    meta: {
      title: '最终测试',
      icon: 'test',
      description: 'Final Test - 成品测试分选管理'
    }
  },
  // 设备管理系统路由
  {
    path: '/equipment/status',
    name: 'EquipmentStatus',
    component: EquipmentStatus,
    meta: {
      title: '设备状态监控',
      icon: 'monitor',
      description: '实时监控设备运行状态、OEE指标和告警信息'
    }
  },
  {
    path: '/equipment/secs-gem',
    name: 'SecsGemManager',
    component: SecsGemManager,
    meta: {
      title: 'SECS/GEM通信管理',
      icon: 'connection',
      description: 'SECS-II消息监控、GEM状态管理和Recipe配置'
    }
  },
  {
    path: '/equipment/maintenance',
    name: 'MaintenanceManager',
    component: MaintenanceManager,
    meta: {
      title: '预测性维护管理',
      icon: 'tools',
      description: '设备维护计划、任务执行跟踪和备件库存管理'
    }
  },
  {
    path: '/equipment/config',
    name: 'EquipmentConfig',
    component: EquipmentConfig,
    meta: {
      title: '设备配置管理',
      icon: 'setting',
      description: '设备参数配置、Recipe版本管理和性能基准设置'
    }
  },
  // 质量管理系统路由
  {
    path: '/quality/spc-control',
    name: 'SPCControl',
    component: SPCControl,
    meta: {
      title: 'SPC统计过程控制',
      icon: 'trend-charts',
      description: 'Statistical Process Control - 实时SPC监控和过程能力分析'
    }
  },
  {
    path: '/quality/traceability',
    name: 'QualityTraceability',
    component: QualityTraceability,
    meta: {
      title: '质量追溯系统',
      icon: 'link',
      description: 'Quality Traceability - 完整的质量追溯链和根因分析'
    }
  },
  {
    path: '/quality/compliance',
    name: 'ComplianceManager',
    component: ComplianceManager,
    meta: {
      title: 'IATF16949合规管理',
      icon: 'document-checked',
      description: 'Compliance Management - IATF16949文档控制和审核管理'
    }
  },
  {
    path: '/quality/inspection',
    name: 'QualityInspection',
    component: QualityInspection,
    meta: {
      title: '质量检验管理',
      icon: 'search',
      description: 'Quality Inspection - IQC/IPQC/FQC/OQC检验管理'
    }
  },
  {
    path: '/quality/analytics',
    name: 'QualityAnalytics',
    component: QualityAnalytics,
    meta: {
      title: '质量分析报告',
      icon: 'pie-chart',
      description: 'Quality Analytics - KPI仪表盘和质量成本分析'
    }
  },
  // 实时监控中心路由
  {
    path: '/monitoring/center',
    name: 'MonitoringCenter',
    component: MonitoringCenter,
    meta: {
      title: '综合监控中心',
      icon: 'data-analysis',
      description: 'Real-time Monitoring Center - 生产、设备、质量综合监控大屏'
    }
  },
  {
    path: '/monitoring/production',
    name: 'ProductionMonitoring',
    component: ProductionDashboard,
    meta: {
      title: '生产监控大屏',
      icon: 'trend-charts',
      description: 'Production Monitoring - 生产KPI、产线状态、趋势分析大屏'
    }
  },
  {
    path: '/monitoring/equipment',
    name: 'EquipmentMonitoring',
    component: EquipmentDashboard,
    meta: {
      title: '设备监控大屏',
      icon: 'monitor',
      description: 'Equipment Monitoring - 设备健康度、OEE、告警管理大屏'
    }
  },
  {
    path: '/monitoring/quality',
    name: 'QualityMonitoring',
    component: QualityDashboard,
    meta: {
      title: '质量监控大屏',
      icon: 'medal',
      description: 'Quality Monitoring - SPC控制图、缺陷分析、客户满意度大屏'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫 - 添加性能优化
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - IC封测CIM系统`
  } else {
    document.title = 'IC封测CIM系统'
  }

  // 菜单切换性能优化：预先移除页面focus
  if (document.activeElement && 'blur' in document.activeElement) {
    ;(document.activeElement as HTMLElement).blur()
  }

  next()
})

// 路由切换完成后的优化
router.afterEach((to, from) => {
  // 确保页面滚动到顶部
  if (typeof window !== 'undefined') {
    // 使用 requestAnimationFrame 确保 DOM 更新后再滚动
    requestAnimationFrame(() => {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'instant' // 即时滚动，不使用平滑滚动避免额外性能开销
      })
    })
  }
})

export default router
