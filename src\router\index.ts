import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 导入页面组件 - 专业版IC封测CIM系统
const Home = () => import('@/views/Home.vue')
const ComponentDemo = () => import('@/views/ComponentDemo.vue')
const CustomerList = () => import('@/views/CustomerList.vue')
const OrderManagement = () => import('@/views/Orders.vue')
const ProductionPlan = () => import('@/views/ProductionPlan.vue')
const Test = () => import('@/views/Test.vue')

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/demo',
    name: 'ComponentDemo',
    component: ComponentDemo,
    meta: {
      title: '组件演示'
    }
  },
  {
    path: '/customers',
    name: 'CustomerList',
    component: CustomerList,
    meta: {
      title: '客户管理'
    }
  },
  {
    path: '/orders',
    name: 'OrderManagement',
    component: OrderManagement,
    meta: {
      title: '订单管理'
    }
  },
  {
    path: '/production',
    name: 'ProductionPlan',
    component: ProductionPlan,
    meta: {
      title: '生产计划'
    }
  },
  {
    path: '/test',
    name: 'Test',
    component: Test,
    meta: {
      title: '开发完成验证'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - IC封测CIM系统`
  } else {
    document.title = 'IC封测CIM系统'
  }
  
  next()
})

export default router