// IC封测CIM系统 - 工具类样式
// 基于极简主义设计的实用工具类

// ===== 边框 =====
.border { border: 1px solid var(--color-border-base); }
.border-0 { border: 0; }
.border-t { border-top: 1px solid var(--color-border-base); }
.border-r { border-right: 1px solid var(--color-border-base); }
.border-b { border-bottom: 1px solid var(--color-border-base); }
.border-l { border-left: 1px solid var(--color-border-base); }

.border-light { border-color: var(--color-border-light); }
.border-base { border-color: var(--color-border-base); }
.border-dark { border-color: var(--color-border-dark); }
.border-primary { border-color: var(--color-primary); }

// ===== 圆角 =====
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-base); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-full { border-radius: var(--radius-full); }

.rounded-t-none { border-top-left-radius: 0; border-top-right-radius: 0; }
.rounded-r-none { border-top-right-radius: 0; border-bottom-right-radius: 0; }
.rounded-b-none { border-bottom-right-radius: 0; border-bottom-left-radius: 0; }
.rounded-l-none { border-top-left-radius: 0; border-bottom-left-radius: 0; }

.rounded-t { border-top-left-radius: var(--radius-base); border-top-right-radius: var(--radius-base); }
.rounded-r { border-top-right-radius: var(--radius-base); border-bottom-right-radius: var(--radius-base); }
.rounded-b { border-bottom-right-radius: var(--radius-base); border-bottom-left-radius: var(--radius-base); }
.rounded-l { border-top-left-radius: var(--radius-base); border-bottom-left-radius: var(--radius-base); }

// ===== 阴影 =====
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-base); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

// ===== 背景色 =====
.bg-transparent { background-color: transparent; }
.bg-primary { background-color: var(--color-bg-primary); }
.bg-secondary { background-color: var(--color-bg-secondary); }
.bg-tertiary { background-color: var(--color-bg-tertiary); }
.bg-hover { background-color: var(--color-bg-hover); }
.bg-active { background-color: var(--color-bg-active); }

.bg-brand-primary { background-color: var(--color-primary); }
.bg-success { background-color: var(--color-success); }
.bg-warning { background-color: var(--color-warning); }
.bg-error { background-color: var(--color-error); }
.bg-info { background-color: var(--color-info); }

// ===== 过渡动画 =====
.transition-none { transition: none; }
.transition-all { transition: all var(--transition-normal); }
.transition-colors { transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast); }
.transition-opacity { transition: opacity var(--transition-normal); }
.transition-transform { transition: transform var(--transition-normal); }

.duration-fast { transition-duration: var(--transition-fast); }
.duration-normal { transition-duration: var(--transition-normal); }
.duration-slow { transition-duration: var(--transition-slow); }

.ease-linear { transition-timing-function: linear; }
.ease-in { transition-timing-function: ease-in; }
.ease-out { transition-timing-function: ease-out; }
.ease-in-out { transition-timing-function: ease-in-out; }

// ===== 变换 =====
.transform { transform: var(--tw-transform); }
.transform-gpu { transform: translate3d(var(--tw-translate-x, 0), var(--tw-translate-y, 0), 0) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.transform-none { transform: none; }

.scale-0 { transform: scale(0); }
.scale-50 { transform: scale(0.5); }
.scale-75 { transform: scale(0.75); }
.scale-90 { transform: scale(0.9); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }
.scale-125 { transform: scale(1.25); }
.scale-150 { transform: scale(1.5); }

.rotate-0 { transform: rotate(0deg); }
.rotate-45 { transform: rotate(45deg); }
.rotate-90 { transform: rotate(90deg); }
.rotate-180 { transform: rotate(180deg); }
.rotate-270 { transform: rotate(270deg); }

// ===== 透明度 =====
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

// ===== 可见性 =====
.visible { visibility: visible; }
.invisible { visibility: hidden; }

// ===== 指针事件 =====
.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

// ===== 用户选择 =====
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

// ===== 光标 =====
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-help { cursor: help; }
.cursor-not-allowed { cursor: not-allowed; }

// ===== Z轴层级 =====
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-dropdown { z-index: var(--z-dropdown); }
.z-sticky { z-index: var(--z-sticky); }
.z-fixed { z-index: var(--z-fixed); }
.z-modal { z-index: var(--z-modal); }
.z-tooltip { z-index: var(--z-tooltip); }

// ===== 特殊工具类 =====
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

// ===== 交互状态 =====
.hover\:opacity-75:hover { opacity: 0.75; }
.hover\:opacity-100:hover { opacity: 1; }
.hover\:scale-105:hover { transform: scale(1.05); }
.hover\:scale-110:hover { transform: scale(1.1); }

.focus\:outline-none:focus { outline: none; }
.focus\:ring:focus { 
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.active\:scale-95:active { transform: scale(0.95); }
.active\:opacity-75:active { opacity: 0.75; }

.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }
.disabled\:pointer-events-none:disabled { pointer-events: none; }

// ===== IC封测专业样式类 =====
.ic-wafer {
  background-color: var(--color-wafer);
  color: var(--color-text-primary);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.ic-die-pass {
  background-color: var(--color-die-pass);
  color: var(--color-success);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.ic-die-fail {
  background-color: var(--color-die-fail);
  color: var(--color-error);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.ic-stage-cp {
  color: var(--color-stage-cp);
  font-weight: var(--font-weight-medium);
}

.ic-stage-assembly {
  color: var(--color-stage-assembly);
  font-weight: var(--font-weight-medium);
}

.ic-stage-ft {
  color: var(--color-stage-ft);
  font-weight: var(--font-weight-medium);
}

.ic-stage-delivery {
  color: var(--color-stage-delivery);
  font-weight: var(--font-weight-medium);
}