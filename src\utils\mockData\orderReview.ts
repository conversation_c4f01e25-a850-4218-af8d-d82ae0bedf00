// 订单评审模拟数据 - Order Review Mock Data
// IC封测CIM系统 - 多部门协同评审决策系统

import type {
  OrderReview,
  OrderReviewTask,
  OrderReviewStatus,
  OrderReviewDepartment,
  ReviewResult,
  RiskLevel,
  RiskItem,
  ReviewDecision,
  TechnicalReviewDetail,
  CapacityReviewDetail,
  QualityReviewDetail,
  SupplyChainReviewDetail,
  FinanceReviewDetail,
  Order,
  OrderPriority,
  PackageType,
  ReviewStatistics
} from '@/types/order'

// 模拟订单评审数据生成器
export class OrderReviewMockData {
  
  /**
   * 生成订单评审完整信息
   */
  static generateOrderReview(orderId: string): OrderReview {
    const mockOrder = this.generateMockOrder(orderId)
    const reviewTasks = this.generateReviewTasks(orderId)
    const riskItems = this.generateRiskItems()
    
    return {
      id: `review-${orderId}`,
      orderId: orderId,
      order: mockOrder,
      reviewStatus: OrderReviewStatus.IN_PROGRESS,
      initiatedBy: 'manager001',
      initiatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2天前
      
      reviewTasks: reviewTasks,
      
      riskAssessment: {
        overallRisk: RiskLevel.MEDIUM,
        riskItems: riskItems,
        riskMatrix: [
          { department: OrderReviewDepartment.TECHNICAL, riskLevel: RiskLevel.MEDIUM, score: 6 },
          { department: OrderReviewDepartment.CAPACITY, riskLevel: RiskLevel.HIGH, score: 8 },
          { department: OrderReviewDepartment.QUALITY, riskLevel: RiskLevel.LOW, score: 3 },
          { department: OrderReviewDepartment.SUPPLY_CHAIN, riskLevel: RiskLevel.MEDIUM, score: 5 },
          { department: OrderReviewDepartment.FINANCE, riskLevel: RiskLevel.LOW, score: 2 }
        ]
      },
      
      progress: {
        completedTasks: 2,
        totalTasks: 5,
        percentage: 40,
        averageScore: 7.2,
        criticalIssues: 2
      },
      
      timeline: [
        {
          event: '订单评审启动',
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          actor: 'manager001',
          description: '订单确认后自动启动评审流程，分配给各部门经理',
          category: 'milestone'
        },
        {
          event: '技术评审完成',
          timestamp: new Date(Date.now() - 1.5 * 24 * 60 * 60 * 1000).toISOString(),
          actor: '李技术经理',
          description: '技术评审通过，工艺可行性确认',
          category: 'task'
        },
        {
          event: '质量评审完成',
          timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          actor: '赵质量经理',
          description: '质量评审通过，IATF16949合规性确认',
          category: 'task'
        },
        {
          event: '产能评审风险识别',
          timestamp: new Date(Date.now() - 0.5 * 24 * 60 * 60 * 1000).toISOString(),
          actor: '王产能经理',
          description: '识别CP测试产线产能瓶颈风险',
          category: 'issue'
        }
      ],
      
      updatedAt: new Date().toISOString()
    }
  }
  
  /**
   * 生成评审任务列表
   */
  static generateReviewTasks(orderId: string): OrderReviewTask[] {
    const baseTime = Date.now() - 2 * 24 * 60 * 60 * 1000 // 2天前开始
    
    return [
      {
        id: `task-technical-${orderId}`,
        orderId: orderId,
        department: OrderReviewDepartment.TECHNICAL,
        departmentName: '技术部',
        assignedTo: '李技术经理',
        status: 'completed',
        priority: 'high' as OrderPriority,
        dueDate: new Date(baseTime + 24 * 60 * 60 * 1000).toISOString(), // 1天后截止
        startedAt: new Date(baseTime + 2 * 60 * 60 * 1000).toISOString(), // 2小时后开始
        completedAt: new Date(baseTime + 1.5 * 24 * 60 * 60 * 1000).toISOString(), // 1.5天后完成
        result: ReviewResult.PASS,
        overallComments: '工艺技术评审通过，现有设备和工艺能力可以满足产品需求。建议优化测试程序以提高效率。',
        technicalDetail: {
          processCompatibility: ReviewResult.PASS,
          processNotes: '现有QFP封装工艺完全兼容，无需额外开发',
          equipmentCapability: ReviewResult.PASS,
          equipmentNotes: '现有设备能力充足，CP和FT测试设备均可支持',
          dftRequirement: ReviewResult.PASS,
          dftNotes: '客户DFT设计规范符合我司标准',
          testSolution: ReviewResult.CONDITIONAL_PASS,
          testNotes: '测试方案可行，但建议优化测试时间以提高产能',
          yieldFeasibility: ReviewResult.PASS,
          yieldNotes: '基于历史数据，预期良率可达99.5%以上',
          riskLevel: RiskLevel.LOW,
          estimatedDevelopmentTime: 5,
          requiredCapitalInvestment: 0
        }
      },
      {
        id: `task-capacity-${orderId}`,
        orderId: orderId,
        department: OrderReviewDepartment.CAPACITY,
        departmentName: '产能部',
        assignedTo: '王产能经理',
        status: 'in_progress',
        priority: 'high' as OrderPriority,
        dueDate: new Date(baseTime + 24 * 60 * 60 * 1000).toISOString(),
        startedAt: new Date(baseTime + 4 * 60 * 60 * 1000).toISOString(),
        overallComments: '产能评审进行中，CP测试线存在一定产能压力，需要优化排程。',
        capacityDetail: {
          cpLineCapacity: ReviewResult.CONDITIONAL_PASS,
          cpNotes: 'CP测试线当前利用率85%，需要增加夜班以满足交期',
          cpUtilization: 85,
          assemblyCapacity: ReviewResult.PASS,
          assemblyNotes: '封装线产能充足',
          assemblyUtilization: 65,
          ftLineCapacity: ReviewResult.PASS,
          ftNotes: 'FT测试线产能正常',
          ftUtilization: 70,
          equipmentScheduling: ReviewResult.CONDITIONAL_PASS,
          schedulingNotes: '需要协调设备排程，避免与其他高优先级订单冲突',
          capacityRisk: RiskLevel.MEDIUM,
          recommendedProductionWindow: '2024年2月-3月，避开春节假期影响'
        }
      },
      {
        id: `task-quality-${orderId}`,
        orderId: orderId,
        department: OrderReviewDepartment.QUALITY,
        departmentName: '质量部',
        assignedTo: '赵质量经理',
        status: 'completed',
        priority: 'high' as OrderPriority,
        dueDate: new Date(baseTime + 24 * 60 * 60 * 1000).toISOString(),
        startedAt: new Date(baseTime + 1 * 60 * 60 * 1000).toISOString(),
        completedAt: new Date(baseTime + 1 * 24 * 60 * 60 * 1000).toISOString(),
        result: ReviewResult.PASS,
        overallComments: '质量评审通过，客户标准和IATF16949要求完全满足。',
        qualityDetail: {
          customerStandards: ReviewResult.PASS,
          standardsNotes: '客户质量标准完全符合我司能力',
          iatf16949Compliance: ReviewResult.PASS,
          iatfNotes: 'IATF16949汽车行业标准完全合规',
          reliabilityRequirement: ReviewResult.PASS,
          reliabilityNotes: '可靠性测试要求在我司能力范围内',
          testCapability: ReviewResult.PASS,
          testCapabilityNotes: '现有测试设备和方法完全满足要求',
          qualificationRequirement: true,
          qualificationTime: 15,
          qualityRisk: RiskLevel.LOW,
          proposedQualityPlan: '严格按照IATF16949执行，增加关键工序监控点'
        }
      },
      {
        id: `task-supply-chain-${orderId}`,
        orderId: orderId,
        department: OrderReviewDepartment.SUPPLY_CHAIN,
        departmentName: '供应链部',
        assignedTo: '钱供应链经理',
        status: 'pending',
        priority: 'high' as OrderPriority,
        dueDate: new Date(baseTime + 24 * 60 * 60 * 1000).toISOString(),
        overallComments: ''
      },
      {
        id: `task-finance-${orderId}`,
        orderId: orderId,
        department: OrderReviewDepartment.FINANCE,
        departmentName: '财务部',
        assignedTo: '孙财务经理',
        status: 'pending',
        priority: 'high' as OrderPriority,
        dueDate: new Date(baseTime + 24 * 60 * 60 * 1000).toISOString(),
        overallComments: ''
      }
    ]
  }
  
  /**
   * 生成风险识别项
   */
  static generateRiskItems(): RiskItem[] {
    return [
      {
        id: 'risk-001',
        category: OrderReviewDepartment.TECHNICAL,
        riskType: '工艺风险',
        description: '新客户首次合作，工艺参数需要优化验证',
        probability: 30,
        impact: RiskLevel.MEDIUM,
        mitigation: '提前进行工艺验证，小批试产确认参数',
        responsible: '李技术经理',
        status: 'identified',
        targetDate: '2024-01-15'
      },
      {
        id: 'risk-002',
        category: OrderReviewDepartment.CAPACITY,
        riskType: '产能风险',
        description: 'CP测试设备利用率过高，可能影响交期',
        probability: 60,
        impact: RiskLevel.HIGH,
        mitigation: '安排夜班生产，考虑外包部分测试工序',
        responsible: '王产能经理',
        status: 'mitigating',
        targetDate: '2024-01-20'
      },
      {
        id: 'risk-003',
        category: OrderReviewDepartment.SUPPLY_CHAIN,
        riskType: '供应风险',
        description: '晶圆供应商交期存在不确定性',
        probability: 40,
        impact: RiskLevel.MEDIUM,
        mitigation: '提前下单，寻找备用供应商',
        responsible: '钱供应链经理',
        status: 'identified',
        targetDate: '2024-01-18'
      },
      {
        id: 'risk-004',
        category: OrderReviewDepartment.FINANCE,
        riskType: '财务风险',
        description: '客户信用记录有限，需要加强风控',
        probability: 25,
        impact: RiskLevel.LOW,
        mitigation: '要求客户提供银行保函或预付款',
        responsible: '孙财务经理',
        status: 'identified',
        targetDate: '2024-01-12'
      }
    ]
  }
  
  /**
   * 生成模拟订单信息
   */
  static generateMockOrder(orderId: string): Order {
    return {
      id: orderId,
      orderNumber: `ORD-${orderId}`,
      customerId: 'CUST001',
      customer: {
        id: 'CUST001',
        name: '某汽车电子公司',
        code: 'AUTO_ELEC_001',
        contact: {
          name: '张工程师',
          phone: '13800138000',
          email: '<EMAIL>'
        }
      },
      productInfo: {
        productName: 'MCU主控芯片',
        productCode: 'MCU-ARM-001',
        packageType: PackageType.QFP,
        quantity: 100, // 100K pcs
        waferSize: 8,
        dieSize: '5.2x5.2mm',
        leadCount: 144,
        specifications: 'ARM Cortex-M4 32位微控制器，工作频率180MHz，1MB Flash，256KB RAM，汽车级温度范围'
      },
      pricing: {
        unitPrice: 25.60,
        totalAmount: 2560000,
        currency: 'CNY',
        paymentTerms: '30天付款'
      },
      schedule: {
        orderDate: '2024-01-01',
        deliveryDate: '2024-03-15',
        confirmedDate: '2024-01-05'
      },
      status: 'confirmed',
      priority: 'high',
      progress: {
        overall: 0,
        cpTesting: 0,
        assembly: 0,
        ftTesting: 0,
        packaging: 0
      },
      qualityInfo: {
        yieldRequirement: 99.5,
        qualityLevel: 'A'
      },
      createdAt: '2024-01-01T08:00:00Z',
      updatedAt: '2024-01-05T14:30:00Z',
      createdBy: 'sales001',
      notes: '汽车电子客户重要订单，需严格按照IATF16949标准执行'
    }
  }
  
  /**
   * 生成评审统计数据
   */
  static generateReviewStatistics(): ReviewStatistics {
    return {
      totalReviews: 156,
      pendingReviews: 12,
      approvedReviews: 128,
      rejectedReviews: 16,
      averageReviewTime: 2.8,
      approvalRate: 82.1,
      
      departmentStats: [
        {
          department: OrderReviewDepartment.TECHNICAL,
          averageTime: 1.2,
          approvalRate: 94.5,
          workload: 8
        },
        {
          department: OrderReviewDepartment.CAPACITY,
          averageTime: 1.8,
          approvalRate: 78.3,
          workload: 12
        },
        {
          department: OrderReviewDepartment.QUALITY,
          averageTime: 1.5,
          approvalRate: 96.8,
          workload: 6
        },
        {
          department: OrderReviewDepartment.SUPPLY_CHAIN,
          averageTime: 2.2,
          approvalRate: 85.7,
          workload: 10
        },
        {
          department: OrderReviewDepartment.FINANCE,
          averageTime: 0.8,
          approvalRate: 91.2,
          workload: 4
        }
      ],
      
      riskDistribution: [
        { riskLevel: RiskLevel.LOW, count: 45, percentage: 28.8 },
        { riskLevel: RiskLevel.MEDIUM, count: 78, percentage: 50.0 },
        { riskLevel: RiskLevel.HIGH, count: 28, percentage: 17.9 },
        { riskLevel: RiskLevel.CRITICAL, count: 5, percentage: 3.2 }
      ],
      
      monthlyTrend: [
        { month: '2024-01', submitted: 28, approved: 24, rejected: 4, averageTime: 3.1 },
        { month: '2024-02', submitted: 32, approved: 26, rejected: 6, averageTime: 2.9 },
        { month: '2024-03', submitted: 35, approved: 29, rejected: 6, averageTime: 2.7 },
        { month: '2024-04', submitted: 31, approved: 25, rejected: 6, averageTime: 2.5 },
        { month: '2024-05', submitted: 30, approved: 24, rejected: 6, averageTime: 2.8 }
      ]
    }
  }
}

// 导出默认数据实例
export default OrderReviewMockData