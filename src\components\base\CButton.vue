<template>
  <component
    :is="tag"
    :class="buttonClasses"
    :disabled="disabled || loading"
    :type="htmlType"
    v-bind="linkProps"
    @click="handleClick"
  >
    <span v-if="loading" class="c-button__loading">
      <svg class="c-button__loading-icon" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416" stroke-linecap="round" />
      </svg>
    </span>
    
    <span v-if="$slots.icon && !loading" class="c-button__icon">
      <slot name="icon" />
    </span>
    
    <span v-if="$slots.default" class="c-button__content">
      <slot />
    </span>
    
    <span v-if="$slots.suffix" class="c-button__suffix">
      <slot name="suffix" />
    </span>
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 定义组件名称
defineOptions({
  name: 'CButton'
})

export interface CButtonProps {
  /** 按钮类型 */
  type?: 'primary' | 'secondary' | 'text' | 'link'
  /** 按钮尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 是否禁用 */
  disabled?: boolean
  /** 是否加载中 */
  loading?: boolean
  /** 是否块级按钮 */
  block?: boolean
  /** 是否圆角按钮 */
  round?: boolean
  /** HTML 标签类型 */
  tag?: string
  /** HTML button type */
  htmlType?: 'button' | 'submit' | 'reset'
  /** 链接地址（当 tag 为 'a' 时使用） */
  href?: string
  /** 链接目标 */
  target?: string
}

const props = withDefaults(defineProps<CButtonProps>(), {
  type: 'secondary',
  size: 'medium',
  disabled: false,
  loading: false,
  block: false,
  round: false,
  tag: 'button',
  htmlType: 'button'
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

// 计算按钮样式类名
const buttonClasses = computed(() => [
  'c-button',
  `c-button--${props.type}`,
  `c-button--${props.size}`,
  {
    'c-button--disabled': props.disabled,
    'c-button--loading': props.loading,
    'c-button--block': props.block,
    'c-button--round': props.round
  }
])

// 链接属性
const linkProps = computed(() => {
  if (props.tag === 'a') {
    return {
      href: props.href,
      target: props.target
    }
  }
  return {}
})

// 处理点击事件
const handleClick = (event: MouseEvent) => {
  if (props.disabled || props.loading) {
    event.preventDefault()
    event.stopPropagation()
    return
  }
  
  emit('click', event)
}
</script>

<style lang="scss">
.c-button {
  @include button-base;
  position: relative;
  gap: var(--button-gap);
  
  // 按钮类型样式
  &--primary {
    @include button-primary;
  }
  
  &--secondary {
    @include button-secondary;
  }
  
  &--text {
    @include button-text;
  }
  
  &--link {
    @include button-text;
    color: var(--color-primary);
    
    &:hover:not(.c-button--disabled) {
      color: var(--color-primary-hover);
      text-decoration: underline;
    }
  }
  
  // 按钮尺寸
  &--small {
    padding: 6px 12px;
    font-size: var(--font-size-xs);
    min-height: 28px;
  }
  
  &--medium {
    padding: var(--button-padding-y) var(--button-padding-x);
    font-size: var(--font-size-sm);
    min-height: 36px;
  }
  
  &--large {
    padding: 12px 24px;
    font-size: var(--font-size-base);
    min-height: 44px;
  }
  
  // 按钮状态
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:hover,
    &:active {
      transform: none;
    }
  }
  
  &--loading {
    cursor: not-allowed;
    
    .c-button__content {
      opacity: 0.6;
    }
  }
  
  &--block {
    width: 100%;
  }
  
  &--round {
    border-radius: var(--radius-full);
  }
  
  // 按钮内容区域
  &__loading {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  
  &__loading-icon {
    width: 1em;
    height: 1em;
    animation: button-loading 1s linear infinite;
  }
  
  &__icon,
  &__suffix {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  &__content {
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
  }
  
  // 焦点样式
  &:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
  
  // 激活效果
  &:active:not(.c-button--disabled):not(.c-button--loading) {
    transform: translateY(1px);
  }
}

// 加载动画
@keyframes button-loading {
  0% {
    stroke-dashoffset: 31.416;
    transform: rotate(0deg);
  }
  50% {
    stroke-dashoffset: 15.708;
    transform: rotate(180deg);
  }
  100% {
    stroke-dashoffset: 31.416;
    transform: rotate(360deg);
  }
}

// 按钮组合
.c-button-group {
  display: inline-flex;
  
  .c-button {
    &:not(:first-child) {
      margin-left: -1px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
    
    &:not(:last-child) {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    
    &:hover {
      z-index: 1;
    }
    
    &:focus {
      z-index: 2;
    }
  }
}
</style>