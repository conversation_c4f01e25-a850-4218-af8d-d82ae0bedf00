<template>
  <div class="equipment-status-card">
    <el-card
:class="['status-card', `status-${equipment.status.toLowerCase()}`]" shadow="hover"
>
      <div class="card-header">
        <div class="equipment-info">
          <h3 class="equipment-name">
            {{ equipment.name }}
          </h3>
          <p class="equipment-code">
            {{ equipment.code }}
          </p>
          <p class="equipment-location">
            {{ equipment.location }}
          </p>
        </div>
        <div class="status-indicator">
          <div
            :class="['status-dot', `status-${equipment.status.toLowerCase()}`]"
            :title="getStatusText(equipment.status)"
          />
          <span class="status-text">{{ getStatusText(equipment.status) }}</span>
        </div>
      </div>

      <div class="status-details">
        <div class="detail-row">
          <span class="label">连接状态:</span>
          <el-tag
:type="equipment.isConnected ? 'success' : 'danger'" size="small"
>
            {{ equipment.isConnected ? '已连接' : '未连接' }}
          </el-tag>
        </div>

        <div
v-if="statusDetail" class="detail-row"
>
          <span class="label">运行时间:</span>
          <span class="value">{{ formatDuration(statusDetail.uptime) }}</span>
        </div>

        <div
v-if="statusDetail && statusDetail.currentLot" class="detail-row"
>
          <span class="label">当前批次:</span>
          <span class="value">{{ statusDetail.currentLot }}</span>
        </div>

        <div
v-if="statusDetail && statusDetail.currentRecipe" class="detail-row"
>
          <span class="label">当前Recipe:</span>
          <span class="value">{{ statusDetail.currentRecipe }}</span>
        </div>
      </div>

      <!-- OEE指标 -->
      <div
v-if="statusDetail?.oeeData" class="oee-section"
>
        <h4 class="section-title">OEE指标</h4>
        <div class="oee-metrics">
          <div class="metric">
            <span class="metric-label">可用率</span>
            <el-progress
              :percentage="Math.round(statusDetail.oeeData.availability)"
              :color="getProgressColor(statusDetail.oeeData.availability)"
              :show-text="false"
              :stroke-width="6"
            />
            <span class="metric-value">{{ statusDetail.oeeData.availability.toFixed(1) }}%</span>
          </div>
          <div class="metric">
            <span class="metric-label">性能率</span>
            <el-progress
              :percentage="Math.round(statusDetail.oeeData.performance)"
              :color="getProgressColor(statusDetail.oeeData.performance)"
              :show-text="false"
              :stroke-width="6"
            />
            <span class="metric-value">{{ statusDetail.oeeData.performance.toFixed(1) }}%</span>
          </div>
          <div class="metric">
            <span class="metric-label">质量率</span>
            <el-progress
              :percentage="Math.round(statusDetail.oeeData.quality)"
              :color="getProgressColor(statusDetail.oeeData.quality)"
              :show-text="false"
              :stroke-width="6"
            />
            <span class="metric-value">{{ statusDetail.oeeData.quality.toFixed(1) }}%</span>
          </div>
          <div class="metric oee-total">
            <span class="metric-label">OEE</span>
            <el-progress
              :percentage="Math.round(statusDetail.oeeData.oee)"
              :color="getProgressColor(statusDetail.oeeData.oee)"
              :show-text="false"
              :stroke-width="8"
            />
            <span class="metric-value oee-value">{{ statusDetail.oeeData.oee.toFixed(1) }}%</span>
          </div>
        </div>
      </div>

      <!-- 告警信息 -->
      <div
v-if="activeAlarms.length > 0" class="alarm-section"
>
        <h4 class="section-title">
          <el-icon><Warning /></el-icon>
          活动告警 ({{ activeAlarms.length }})
        </h4>
        <div class="alarm-list">
          <div
            v-for="alarm in activeAlarms.slice(0, 3)"
            :key="alarm.id"
            :class="['alarm-item', `severity-${alarm.severity.toLowerCase()}`]"
          >
            <el-icon class="alarm-icon">
              <Warning v-if="alarm.severity === 'CRITICAL'" />
              <InfoFilled v-else />
            </el-icon>
            <span class="alarm-text">{{ alarm.alarmText }}</span>
            <el-tag
:type="getSeverityType(alarm.severity)" size="small"
class="alarm-severity"
>
              {{ getSeverityText(alarm.severity) }}
            </el-tag>
          </div>
          <div v-if="activeAlarms.length > 3" class="more-alarms">
            还有 {{ activeAlarms.length - 3 }} 个告警...
          </div>
        </div>
      </div>

      <!-- 控制按钮 -->
      <div
v-if="showControls" class="control-actions"
>
        <el-button
          v-if="equipment.status === 'IDLE'"
          type="success"
          size="small"
          :disabled="!equipment.isConnected"
          @click="$emit('start')"
        >
          <el-icon><VideoPlay /></el-icon>
          启动
        </el-button>

        <el-button
          v-if="equipment.status === 'RUN'"
          type="warning"
          size="small"
          :disabled="!equipment.isConnected"
          @click="$emit('stop')"
        >
          <el-icon><VideoPause /></el-icon>
          停止
        </el-button>

        <el-button
size="small" @click="$emit('detail')"
>
          <el-icon><View /></el-icon>
          详情
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { Warning, InfoFilled, VideoPlay, VideoPause, View } from '@element-plus/icons-vue'
  import type { Equipment, EquipmentStatusDetail, EquipmentAlarm } from '@/types/equipment'
  import { EquipmentStatus } from '@/types/equipment'

  interface Props {
    equipment: Equipment
    statusDetail?: EquipmentStatusDetail
    alarms?: EquipmentAlarm[]
    showControls?: boolean
  }

  interface Emits {
    (e: 'start'): void
    (e: 'stop'): void
    (e: 'detail'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    showControls: true,
    alarms: () => []
  })

  const emit = defineEmits<Emits>()

  const activeAlarms = computed(() =>
    props.alarms.filter(alarm => alarm.equipmentId === props.equipment.id && alarm.isActive)
  )

  const getStatusText = (status: EquipmentStatus): string => {
    const textMap: Record<EquipmentStatus, string> = {
      [EquipmentStatus.RUN]: '运行',
      [EquipmentStatus.IDLE]: '空闲',
      [EquipmentStatus.DOWN]: '故障',
      [EquipmentStatus.PM]: '保养',
      [EquipmentStatus.SETUP]: '调机',
      [EquipmentStatus.ALARM]: '告警'
    }
    return textMap[status] || '未知'
  }

  const getSeverityText = (severity: string): string => {
    const textMap: Record<string, string> = {
      CRITICAL: '严重',
      MAJOR: '重要',
      MINOR: '次要',
      WARNING: '警告'
    }
    return textMap[severity] || severity
  }

  const getSeverityType = (severity: string): string => {
    const typeMap: Record<string, string> = {
      CRITICAL: 'danger',
      MAJOR: 'warning',
      MINOR: 'info',
      WARNING: 'info'
    }
    return typeMap[severity] || 'info'
  }

  const getProgressColor = (value: number): string => {
    if (value >= 85) return '#67C23A'
    if (value >= 70) return '#E6A23C'
    return '#F56C6C'
  }

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}h ${minutes}m`
  }
</script>

<style lang="scss" scoped>
  .equipment-status-card {
    .status-card {
      border-left: 4px solid var(--color-border-light);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: var(--shadow-large);
        transform: translateY(-2px);
      }

      &.status-run {
        border-left-color: var(--color-success);
      }

      &.status-idle {
        border-left-color: var(--color-warning);
      }

      &.status-down {
        border-left-color: var(--color-danger);
      }

      &.status-pm {
        border-left-color: var(--color-info);
      }

      &.status-alarm {
        border-left-color: var(--color-danger);
        animation: pulse-alarm 2s infinite;
      }
    }

    .card-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: var(--spacing-4);
    }

    .equipment-info {
      flex: 1;

      .equipment-name {
        margin: 0 0 var(--spacing-1);
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--color-text-primary);
      }

      .equipment-code,
      .equipment-location {
        margin: var(--spacing-1) 0;
        font-size: 0.875rem;
        color: var(--color-text-secondary);
      }
    }

    .status-indicator {
      display: flex;
      gap: var(--spacing-2);
      align-items: center;

      .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;

        &.status-run {
          background-color: var(--color-success);
          animation: pulse-run 2s infinite;
        }

        &.status-idle {
          background-color: var(--color-warning);
        }

        &.status-down {
          background-color: var(--color-danger);
        }

        &.status-pm {
          background-color: var(--color-info);
        }

        &.status-alarm {
          background-color: var(--color-danger);
          animation: pulse-alarm 1s infinite;
        }
      }

      .status-text {
        font-size: 0.875rem;
        font-weight: 500;
      }
    }

    .status-details {
      margin-bottom: var(--spacing-4);

      .detail-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-2);

        .label {
          font-size: 0.875rem;
          color: var(--color-text-secondary);
        }

        .value {
          font-size: 0.875rem;
          font-weight: 500;
          color: var(--color-text-primary);
        }
      }
    }

    .oee-section,
    .alarm-section {
      padding: var(--spacing-3);
      margin-bottom: var(--spacing-4);
      background-color: var(--color-bg-soft);
      border-radius: var(--radius-base);

      .section-title {
        display: flex;
        gap: var(--spacing-1);
        align-items: center;
        margin: 0 0 var(--spacing-3);
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--color-text-primary);
      }
    }

    .oee-metrics {
      .metric {
        display: flex;
        gap: var(--spacing-2);
        align-items: center;
        margin-bottom: var(--spacing-2);

        .metric-label {
          flex-shrink: 0;
          width: 48px;
          font-size: 0.75rem;
          color: var(--color-text-secondary);
        }

        :deep(.el-progress) {
          flex: 1;
        }

        .metric-value {
          flex-shrink: 0;
          width: 40px;
          font-size: 0.75rem;
          font-weight: 600;
          text-align: right;
        }

        &.oee-total {
          padding-top: var(--spacing-2);
          margin-top: var(--spacing-2);
          border-top: 1px solid var(--color-border-light);

          .metric-label {
            font-weight: 600;
            color: var(--color-text-primary);
          }

          .oee-value {
            font-size: 0.875rem;
            color: var(--color-text-primary);
          }
        }
      }
    }

    .alarm-list {
      .alarm-item {
        display: flex;
        gap: var(--spacing-2);
        align-items: center;
        padding: var(--spacing-2);
        margin-bottom: var(--spacing-2);
        border-radius: var(--radius-small);

        &.severity-critical {
          background-color: rgb(245 108 108 / 10%);
        }

        &.severity-major {
          background-color: rgb(230 162 60 / 10%);
        }

        .alarm-icon {
          flex-shrink: 0;
          font-size: 14px;
          color: var(--color-danger);
        }

        .alarm-text {
          flex: 1;
          font-size: 0.75rem;
          color: var(--color-text-primary);
        }

        .alarm-severity {
          flex-shrink: 0;
        }
      }

      .more-alarms {
        margin-top: var(--spacing-2);
        font-size: 0.75rem;
        color: var(--color-text-secondary);
        text-align: center;
      }
    }

    .control-actions {
      display: flex;
      gap: var(--spacing-2);
      justify-content: flex-end;
      padding-top: var(--spacing-3);
      border-top: 1px solid var(--color-border-light);
    }
  }

  @keyframes pulse-run {
    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.6;
    }
  }

  @keyframes pulse-alarm {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }

    50% {
      opacity: 0.8;
      transform: scale(1.1);
    }
  }
</style>
