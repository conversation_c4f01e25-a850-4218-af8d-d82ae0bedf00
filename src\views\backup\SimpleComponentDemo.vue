<template>
  <div class="component-demo">
    <div class="page-header">
      <h1>🎨 组件演示中心</h1>
      <p>IC封测CIM系统 - 基础UI组件库展示</p>
    </div>

    <!-- 组件导航 -->
    <div class="component-nav">
      <button
        v-for="component in components"
        :key="component.key"
        :class="['nav-btn', { active: activeComponent === component.key }]"
        @click="activeComponent = component.key"
      >
        {{ component.icon }} {{ component.name }}
      </button>
    </div>

    <!-- 按钮组件演示 -->
    <div v-if="activeComponent === 'buttons'" class="demo-section">
      <h2>🔘 按钮组件 (Buttons)</h2>

      <div class="demo-group">
        <h3>按钮类型</h3>
        <div class="demo-grid">
          <button class="btn btn-primary">主要按钮</button>
          <button class="btn btn-secondary">次要按钮</button>
          <button class="btn btn-success">成功按钮</button>
          <button class="btn btn-warning">警告按钮</button>
          <button class="btn btn-danger">危险按钮</button>
          <button class="btn btn-info">信息按钮</button>
        </div>
      </div>

      <div class="demo-group">
        <h3>按钮尺寸</h3>
        <div class="demo-grid">
          <button class="btn btn-primary btn-large">大号按钮</button>
          <button class="btn btn-primary">标准按钮</button>
          <button class="btn btn-primary btn-small">小号按钮</button>
        </div>
      </div>

      <div class="demo-group">
        <h3>按钮状态</h3>
        <div class="demo-grid">
          <button class="btn btn-primary">正常状态</button>
          <button
class="btn btn-primary" disabled>禁用状态</button>
          <button class="btn btn-primary loading">
            <span class="loading-spinner" />
            加载中...
          </button>
        </div>
      </div>
    </div>

    <!-- 输入框组件演示 -->
    <div v-if="activeComponent === 'inputs'" class="demo-section">
      <h2>📝 输入框组件 (Inputs)</h2>

      <div class="demo-group">
        <h3>基础输入框</h3>
        <div class="demo-grid">
          <div class="input-group">
            <label>文本输入</label>
            <input type="text" class="input-field" placeholder="请输入文本..." />
          </div>
          <div class="input-group">
            <label>密码输入</label>
            <input type="password" class="input-field" placeholder="请输入密码..." />
          </div>
          <div class="input-group">
            <label>数字输入</label>
            <input type="number" class="input-field" placeholder="请输入数字..." />
          </div>
        </div>
      </div>

      <div class="demo-group">
        <h3>选择器</h3>
        <div class="demo-grid">
          <div class="input-group">
            <label>下拉选择</label>
            <select class="input-field">
              <option value="">请选择...</option>
              <option value="qfp">QFP封装</option>
              <option value="bga">BGA封装</option>
              <option value="csp">CSP封装</option>
            </select>
          </div>
          <div class="input-group">
            <label>日期选择</label>
            <input type="date" class="input-field" />
          </div>
        </div>
      </div>

      <div class="demo-group">
        <h3>多行文本</h3>
        <div class="input-group">
          <label>备注信息</label>
          <textarea
class="input-field" rows="4" placeholder="请输入备注信息..." />
        </div>
      </div>
    </div>

    <!-- 卡片组件演示 -->
    <div v-if="activeComponent === 'cards'" class="demo-section">
      <h2>🗂️ 卡片组件 (Cards)</h2>

      <div class="demo-group">
        <h3>基础卡片</h3>
        <div class="cards-grid">
          <div class="card">
            <div class="card-header">
              <h4>基础卡片</h4>
            </div>
            <div class="card-body">
              <p>这是一个基础的卡片组件，用于展示内容和信息。</p>
            </div>
          </div>

          <div class="card card-hoverable">
            <div class="card-header">
              <h4>可悬停卡片</h4>
            </div>
            <div class="card-body">
              <p>鼠标悬停时会有交互效果的卡片。</p>
            </div>
          </div>
        </div>
      </div>

      <div class="demo-group">
        <h3>统计卡片</h3>
        <div class="cards-grid">
          <div class="stat-card">
            <div class="stat-icon">📊</div>
            <div class="stat-content">
              <h3>1,234</h3>
              <p>总订单数</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">⚡</div>
            <div class="stat-content">
              <h3>89%</h3>
              <p>完成率</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">🎯</div>
            <div class="stat-content">
              <h3>98.5%</h3>
              <p>良品率</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格组件演示 -->
    <div v-if="activeComponent === 'tables'" class="demo-section">
      <h2>📋 表格组件 (Tables)</h2>

      <div class="demo-group">
        <h3>数据表格</h3>
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>订单号</th>
                <th>客户名称</th>
                <th>封装类型</th>
                <th>数量</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="order in sampleOrders" :key="order.id">
                <td class="order-no">
                  {{ order.orderNo }}
                </td>
                <td>{{ order.customerName }}</td>
                <td>
                  <span class="package-badge" :class="order.packageType.toLowerCase()">
                    {{ order.packageType }}
                  </span>
                </td>
                <td class="number">
                  {{ order.quantity.toLocaleString() }}
                </td>
                <td>
                  <span class="status-badge" :class="order.status">
                    {{ getStatusText(order.status) }}
                  </span>
                </td>
                <td>
                  <button class="btn-small btn-info">查看</button>
                  <button class="btn-small btn-warning">编辑</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 弹窗组件演示 -->
    <div v-if="activeComponent === 'modals'" class="demo-section">
      <h2>🔲 弹窗组件 (Modals)</h2>

      <div class="demo-group">
        <h3>弹窗类型</h3>
        <div class="demo-grid">
          <button
class="btn btn-info" @click="showInfoModal = true">信息弹窗</button>
          <button
class="btn btn-warning" @click="showConfirmModal = true">确认弹窗</button>
          <button
class="btn btn-primary" @click="showFormModal = true">表单弹窗</button>
        </div>
      </div>
    </div>

    <!-- 通知组件演示 -->
    <div v-if="activeComponent === 'notifications'" class="demo-section">
      <h2>🔔 通知组件 (Notifications)</h2>

      <div class="demo-group">
        <h3>通知类型</h3>
        <div class="demo-grid">
          <button
class="btn btn-success" @click="showNotification('success')">成功通知</button>
          <button
class="btn btn-warning" @click="showNotification('warning')">警告通知</button>
          <button
class="btn btn-danger" @click="showNotification('error')">错误通知</button>
          <button
class="btn btn-info" @click="showNotification('info')">信息通知</button>
        </div>
      </div>

      <!-- 通知显示区域 -->
      <div class="notifications-container">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          :class="['notification', notification.type]"
        >
          <span class="notification-icon">{{ getNotificationIcon(notification.type) }}</span>
          <span class="notification-text">{{ notification.message }}</span>
          <button
class="notification-close" @click="removeNotification(notification.id)">×</button>
        </div>
      </div>
    </div>

    <!-- 信息弹窗 -->
    <div v-if="showInfoModal" class="modal-backdrop" @click="showInfoModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>📋 信息提示</h3>
          <button
class="modal-close" @click="showInfoModal = false">✕</button>
        </div>
        <div class="modal-body">
          <p>这是一个信息提示弹窗，用于向用户展示重要信息。</p>
        </div>
        <div class="modal-footer">
          <button
class="btn btn-primary" @click="showInfoModal = false">确定</button>
        </div>
      </div>
    </div>

    <!-- 确认弹窗 -->
    <div v-if="showConfirmModal" class="modal-backdrop" @click="showConfirmModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>⚠️ 确认操作</h3>
          <button
class="modal-close" @click="showConfirmModal = false">✕</button>
        </div>
        <div class="modal-body">
          <p>您确定要执行此操作吗？此操作可能无法撤销。</p>
        </div>
        <div class="modal-footer">
          <button
class="btn btn-secondary" @click="showConfirmModal = false">取消</button>
          <button
class="btn btn-danger" @click="showConfirmModal = false">确认</button>
        </div>
      </div>
    </div>

    <!-- 表单弹窗 -->
    <div v-if="showFormModal" class="modal-backdrop" @click="showFormModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>📝 表单填写</h3>
          <button
class="modal-close" @click="showFormModal = false">✕</button>
        </div>
        <div class="modal-body">
          <div class="form-grid">
            <div class="input-group">
              <label>订单号</label>
              <input type="text" class="input-field" placeholder="输入订单号" />
            </div>
            <div class="input-group">
              <label>客户名称</label>
              <input type="text" class="input-field" placeholder="输入客户名称" />
            </div>
            <div class="input-group">
              <label>封装类型</label>
              <select class="input-field">
                <option value="">请选择</option>
                <option value="QFP">QFP</option>
                <option value="BGA">BGA</option>
                <option value="CSP">CSP</option>
              </select>
            </div>
            <div class="input-group">
              <label>数量</label>
              <input type="number" class="input-field" placeholder="输入数量" />
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button
class="btn btn-secondary" @click="showFormModal = false">取消</button>
          <button
class="btn btn-primary" @click="showFormModal = false">提交</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'

  // 组件导航
  const components = ref([
    { key: 'buttons', name: '按钮', icon: '🔘' },
    { key: 'inputs', name: '输入框', icon: '📝' },
    { key: 'cards', name: '卡片', icon: '🗂️' },
    { key: 'tables', name: '表格', icon: '📋' },
    { key: 'modals', name: '弹窗', icon: '🔲' },
    { key: 'notifications', name: '通知', icon: '🔔' }
  ])

  const activeComponent = ref('buttons')

  // 弹窗状态
  const showInfoModal = ref(false)
  const showConfirmModal = ref(false)
  const showFormModal = ref(false)

  // 通知系统
  const notifications = ref([])
  let notificationId = 0

  // 示例数据
  const sampleOrders = ref([
    {
      id: 1,
      orderNo: 'IC2024001',
      customerName: 'ABC半导体',
      packageType: 'QFP',
      quantity: 10000,
      status: 'processing'
    },
    {
      id: 2,
      orderNo: 'IC2024002',
      customerName: 'XYZ电子',
      packageType: 'BGA',
      quantity: 5000,
      status: 'completed'
    },
    {
      id: 3,
      orderNo: 'IC2024003',
      customerName: 'DEF芯片',
      packageType: 'CSP',
      quantity: 15000,
      status: 'pending'
    }
  ])

  // 方法
  const getStatusText = (status: string) => {
    const statusMap = {
      pending: '待处理',
      processing: '进行中',
      completed: '已完成'
    }
    return statusMap[status] || status
  }

  const showNotification = (type: string) => {
    const messages = {
      success: '操作成功完成！',
      warning: '请注意相关风险！',
      error: '操作失败，请重试！',
      info: '这是一条信息通知。'
    }

    const notification = {
      id: ++notificationId,
      type,
      message: messages[type] || '通知消息'
    }

    notifications.value.push(notification)

    // 3秒后自动移除
    setTimeout(() => {
      removeNotification(notification.id)
    }, 3000)
  }

  const getNotificationIcon = (type: string) => {
    const icons = {
      success: '✅',
      warning: '⚠️',
      error: '❌',
      info: 'ℹ️'
    }
    return icons[type] || 'ℹ️'
  }

  const removeNotification = (id: number) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  onMounted(() => {
    console.log('组件演示页面加载完成')
  })
</script>

<style scoped>
  .component-demo {
    min-height: calc(100vh - 100px);
    padding: 1.5rem;
    background: #f9fafb;
  }

  .page-header {
    padding: 2rem;
    margin-bottom: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  }

  .page-header h1 {
    margin: 0 0 0.5rem;
    font-size: 2rem;
    color: #1f2937;
  }

  .page-header p {
    margin: 0;
    color: #6b7280;
  }

  .component-nav {
    display: flex;
    gap: 0.5rem;
    padding: 0.5rem;
    margin-bottom: 1.5rem;
    overflow-x: auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  }

  .nav-btn {
    flex: 1;
    min-width: 120px;
    padding: 1rem;
    font-weight: 500;
    color: #6b7280;
    white-space: nowrap;
    cursor: pointer;
    background: transparent;
    border: none;
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .nav-btn:hover {
    color: #374151;
    background: #f3f4f6;
  }

  .nav-btn.active {
    color: white;
    background: #2563eb;
  }

  .demo-section {
    padding: 2rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  }

  .demo-section h2 {
    margin: 0 0 1.5rem;
    font-size: 1.5rem;
    color: #1f2937;
  }

  .demo-group {
    margin-bottom: 2rem;
  }

  .demo-group:last-child {
    margin-bottom: 0;
  }

  .demo-group h3 {
    margin: 0 0 1rem;
    font-size: 1.125rem;
    color: #374151;
  }

  .demo-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: flex-end;
  }

  .cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  /* 按钮样式 */
  .btn,
  .btn-small {
    display: inline-flex;
    gap: 0.5rem;
    align-items: center;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    border: none;
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .btn-small {
    padding: 0.5rem 1rem;
    margin-right: 0.5rem;
    font-size: 0.75rem;
  }

  .btn-large {
    padding: 1rem 2rem;
    font-size: 1rem;
  }

  .btn-primary {
    color: white;
    background: #2563eb;
  }

  .btn-primary:hover {
    background: #1d4ed8;
  }

  .btn-secondary {
    color: #374151;
    background: #e5e7eb;
  }

  .btn-secondary:hover {
    background: #d1d5db;
  }

  .btn-success {
    color: white;
    background: #10b981;
  }

  .btn-success:hover {
    background: #059669;
  }

  .btn-warning {
    color: white;
    background: #f59e0b;
  }

  .btn-warning:hover {
    background: #d97706;
  }

  .btn-danger {
    color: white;
    background: #dc2626;
  }

  .btn-danger:hover {
    background: #b91c1c;
  }

  .btn-info {
    color: white;
    background: #06b6d4;
  }

  .btn-info:hover {
    background: #0891b2;
  }

  .btn:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .btn.loading {
    position: relative;
  }

  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgb(255 255 255 / 30%);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  /* 输入框样式 */
  .input-group {
    display: flex;
    flex-direction: column;
    min-width: 200px;
  }

  .input-group label {
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
  }

  .input-field {
    padding: 0.75rem;
    font-size: 0.875rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    transition: border-color 0.2s ease;
  }

  .input-field:focus {
    border-color: #2563eb;
    outline: none;
    box-shadow: 0 0 0 3px rgb(37 99 235 / 10%);
  }

  /* 卡片样式 */
  .card {
    overflow: hidden;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
  }

  .card-hoverable:hover {
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
    transition: all 0.2s ease;
    transform: translateY(-2px);
  }

  .card-header {
    padding: 1rem;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
  }

  .card-header h4 {
    margin: 0;
    color: #1f2937;
  }

  .card-body {
    padding: 1rem;
  }

  .card-body p {
    margin: 0;
    line-height: 1.5;
    color: #6b7280;
  }

  .stat-card {
    display: flex;
    gap: 1rem;
    align-items: center;
    padding: 1.5rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
  }

  .stat-icon {
    font-size: 2rem;
    opacity: 0.8;
  }

  .stat-content h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
  }

  .stat-content p {
    margin: 0.25rem 0 0;
    font-size: 0.875rem;
    color: #6b7280;
  }

  /* 表格样式 */
  .table-container {
    overflow-x: auto;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
  }

  .data-table {
    width: 100%;
    border-collapse: collapse;
  }

  .data-table th,
  .data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }

  .data-table th {
    font-weight: 600;
    color: #374151;
    background: #f9fafb;
  }

  .data-table tbody tr:hover {
    background: #f9fafb;
  }

  .order-no {
    font-family: monospace;
    font-weight: 600;
    color: #2563eb;
  }

  .number {
    text-align: right;
  }

  .package-badge,
  .status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
  }

  .package-badge.qfp {
    color: #1d4ed8;
    background: #dbeafe;
  }

  .package-badge.bga {
    color: #166534;
    background: #dcfce7;
  }

  .package-badge.csp {
    color: #92400e;
    background: #fef3c7;
  }

  .status-badge.pending {
    color: #92400e;
    background: #fef3c7;
  }

  .status-badge.processing {
    color: #1d4ed8;
    background: #dbeafe;
  }

  .status-badge.completed {
    color: #166534;
    background: #dcfce7;
  }

  /* 通知样式 */
  .notifications-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .notification {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    min-width: 300px;
    padding: 1rem;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
    animation: slideInRight 0.3s ease;
  }

  .notification.success {
    color: #166534;
    background: #dcfce7;
  }

  .notification.warning {
    color: #92400e;
    background: #fef3c7;
  }

  .notification.error {
    color: #dc2626;
    background: #fee2e2;
  }

  .notification.info {
    color: #1d4ed8;
    background: #dbeafe;
  }

  .notification-icon {
    font-size: 1.25rem;
  }

  .notification-text {
    flex: 1;
  }

  .notification-close {
    font-size: 1.25rem;
    cursor: pointer;
    background: none;
    border: none;
    opacity: 0.7;
  }

  .notification-close:hover {
    opacity: 1;
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(100%);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* 弹窗样式 */
  .modal-backdrop {
    position: fixed;
    inset: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgb(0 0 0 / 50%);
  }

  .modal-content {
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    background: white;
    border-radius: 8px;
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .modal-header h3 {
    margin: 0;
    color: #1f2937;
  }

  .modal-close {
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    background: none;
    border: none;
  }

  .modal-body {
    padding: 1.5rem;
  }

  .modal-footer {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  @media (width <= 768px) {
    .component-demo {
      padding: 1rem;
    }

    .component-nav {
      overflow-x: auto;
    }

    .demo-grid {
      flex-direction: column;
      align-items: stretch;
    }

    .cards-grid {
      grid-template-columns: 1fr;
    }

    .modal-content {
      width: 95%;
      margin: 1rem;
    }

    .form-grid {
      grid-template-columns: 1fr;
    }

    .notifications-container {
      right: 1rem;
      left: 1rem;
    }

    .notification {
      min-width: auto;
    }
  }
</style>
