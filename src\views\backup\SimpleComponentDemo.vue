<template>
  <div class="component-demo">
    <div class="page-header">
      <h1>🎨 组件演示中心</h1>
      <p>IC封测CIM系统 - 基础UI组件库展示</p>
    </div>

    <!-- 组件导航 -->
    <div class="component-nav">
      <button 
        v-for="component in components" 
        :key="component.key"
        @click="activeComponent = component.key"
        :class="['nav-btn', { active: activeComponent === component.key }]"
      >
        {{ component.icon }} {{ component.name }}
      </button>
    </div>

    <!-- 按钮组件演示 -->
    <div v-if="activeComponent === 'buttons'" class="demo-section">
      <h2>🔘 按钮组件 (Buttons)</h2>
      
      <div class="demo-group">
        <h3>按钮类型</h3>
        <div class="demo-grid">
          <button class="btn btn-primary">主要按钮</button>
          <button class="btn btn-secondary">次要按钮</button>
          <button class="btn btn-success">成功按钮</button>
          <button class="btn btn-warning">警告按钮</button>
          <button class="btn btn-danger">危险按钮</button>
          <button class="btn btn-info">信息按钮</button>
        </div>
      </div>

      <div class="demo-group">
        <h3>按钮尺寸</h3>
        <div class="demo-grid">
          <button class="btn btn-primary btn-large">大号按钮</button>
          <button class="btn btn-primary">标准按钮</button>
          <button class="btn btn-primary btn-small">小号按钮</button>
        </div>
      </div>

      <div class="demo-group">
        <h3>按钮状态</h3>
        <div class="demo-grid">
          <button class="btn btn-primary">正常状态</button>
          <button class="btn btn-primary" disabled>禁用状态</button>
          <button class="btn btn-primary loading">
            <span class="loading-spinner"></span>
            加载中...
          </button>
        </div>
      </div>
    </div>

    <!-- 输入框组件演示 -->
    <div v-if="activeComponent === 'inputs'" class="demo-section">
      <h2>📝 输入框组件 (Inputs)</h2>
      
      <div class="demo-group">
        <h3>基础输入框</h3>
        <div class="demo-grid">
          <div class="input-group">
            <label>文本输入</label>
            <input type="text" class="input-field" placeholder="请输入文本..." />
          </div>
          <div class="input-group">
            <label>密码输入</label>
            <input type="password" class="input-field" placeholder="请输入密码..." />
          </div>
          <div class="input-group">
            <label>数字输入</label>
            <input type="number" class="input-field" placeholder="请输入数字..." />
          </div>
        </div>
      </div>

      <div class="demo-group">
        <h3>选择器</h3>
        <div class="demo-grid">
          <div class="input-group">
            <label>下拉选择</label>
            <select class="input-field">
              <option value="">请选择...</option>
              <option value="qfp">QFP封装</option>
              <option value="bga">BGA封装</option>
              <option value="csp">CSP封装</option>
            </select>
          </div>
          <div class="input-group">
            <label>日期选择</label>
            <input type="date" class="input-field" />
          </div>
        </div>
      </div>

      <div class="demo-group">
        <h3>多行文本</h3>
        <div class="input-group">
          <label>备注信息</label>
          <textarea class="input-field" rows="4" placeholder="请输入备注信息..."></textarea>
        </div>
      </div>
    </div>

    <!-- 卡片组件演示 -->
    <div v-if="activeComponent === 'cards'" class="demo-section">
      <h2>🗂️ 卡片组件 (Cards)</h2>
      
      <div class="demo-group">
        <h3>基础卡片</h3>
        <div class="cards-grid">
          <div class="card">
            <div class="card-header">
              <h4>基础卡片</h4>
            </div>
            <div class="card-body">
              <p>这是一个基础的卡片组件，用于展示内容和信息。</p>
            </div>
          </div>
          
          <div class="card card-hoverable">
            <div class="card-header">
              <h4>可悬停卡片</h4>
            </div>
            <div class="card-body">
              <p>鼠标悬停时会有交互效果的卡片。</p>
            </div>
          </div>
        </div>
      </div>

      <div class="demo-group">
        <h3>统计卡片</h3>
        <div class="cards-grid">
          <div class="stat-card">
            <div class="stat-icon">📊</div>
            <div class="stat-content">
              <h3>1,234</h3>
              <p>总订单数</p>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">⚡</div>
            <div class="stat-content">
              <h3>89%</h3>
              <p>完成率</p>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">🎯</div>
            <div class="stat-content">
              <h3>98.5%</h3>
              <p>良品率</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格组件演示 -->
    <div v-if="activeComponent === 'tables'" class="demo-section">
      <h2>📋 表格组件 (Tables)</h2>
      
      <div class="demo-group">
        <h3>数据表格</h3>
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>订单号</th>
                <th>客户名称</th>
                <th>封装类型</th>
                <th>数量</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="order in sampleOrders" :key="order.id">
                <td class="order-no">{{ order.orderNo }}</td>
                <td>{{ order.customerName }}</td>
                <td>
                  <span class="package-badge" :class="order.packageType.toLowerCase()">
                    {{ order.packageType }}
                  </span>
                </td>
                <td class="number">{{ order.quantity.toLocaleString() }}</td>
                <td>
                  <span class="status-badge" :class="order.status">
                    {{ getStatusText(order.status) }}
                  </span>
                </td>
                <td>
                  <button class="btn-small btn-info">查看</button>
                  <button class="btn-small btn-warning">编辑</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 弹窗组件演示 -->
    <div v-if="activeComponent === 'modals'" class="demo-section">
      <h2>🔲 弹窗组件 (Modals)</h2>
      
      <div class="demo-group">
        <h3>弹窗类型</h3>
        <div class="demo-grid">
          <button @click="showInfoModal = true" class="btn btn-info">信息弹窗</button>
          <button @click="showConfirmModal = true" class="btn btn-warning">确认弹窗</button>
          <button @click="showFormModal = true" class="btn btn-primary">表单弹窗</button>
        </div>
      </div>
    </div>

    <!-- 通知组件演示 -->
    <div v-if="activeComponent === 'notifications'" class="demo-section">
      <h2>🔔 通知组件 (Notifications)</h2>
      
      <div class="demo-group">
        <h3>通知类型</h3>
        <div class="demo-grid">
          <button @click="showNotification('success')" class="btn btn-success">成功通知</button>
          <button @click="showNotification('warning')" class="btn btn-warning">警告通知</button>
          <button @click="showNotification('error')" class="btn btn-danger">错误通知</button>
          <button @click="showNotification('info')" class="btn btn-info">信息通知</button>
        </div>
      </div>

      <!-- 通知显示区域 -->
      <div class="notifications-container">
        <div 
          v-for="notification in notifications" 
          :key="notification.id"
          :class="['notification', notification.type]"
        >
          <span class="notification-icon">{{ getNotificationIcon(notification.type) }}</span>
          <span class="notification-text">{{ notification.message }}</span>
          <button @click="removeNotification(notification.id)" class="notification-close">×</button>
        </div>
      </div>
    </div>

    <!-- 信息弹窗 -->
    <div v-if="showInfoModal" class="modal-backdrop" @click="showInfoModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>📋 信息提示</h3>
          <button @click="showInfoModal = false" class="modal-close">✕</button>
        </div>
        <div class="modal-body">
          <p>这是一个信息提示弹窗，用于向用户展示重要信息。</p>
        </div>
        <div class="modal-footer">
          <button @click="showInfoModal = false" class="btn btn-primary">确定</button>
        </div>
      </div>
    </div>

    <!-- 确认弹窗 -->
    <div v-if="showConfirmModal" class="modal-backdrop" @click="showConfirmModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>⚠️ 确认操作</h3>
          <button @click="showConfirmModal = false" class="modal-close">✕</button>
        </div>
        <div class="modal-body">
          <p>您确定要执行此操作吗？此操作可能无法撤销。</p>
        </div>
        <div class="modal-footer">
          <button @click="showConfirmModal = false" class="btn btn-secondary">取消</button>
          <button @click="showConfirmModal = false" class="btn btn-danger">确认</button>
        </div>
      </div>
    </div>

    <!-- 表单弹窗 -->
    <div v-if="showFormModal" class="modal-backdrop" @click="showFormModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>📝 表单填写</h3>
          <button @click="showFormModal = false" class="modal-close">✕</button>
        </div>
        <div class="modal-body">
          <div class="form-grid">
            <div class="input-group">
              <label>订单号</label>
              <input type="text" class="input-field" placeholder="输入订单号" />
            </div>
            <div class="input-group">
              <label>客户名称</label>
              <input type="text" class="input-field" placeholder="输入客户名称" />
            </div>
            <div class="input-group">
              <label>封装类型</label>
              <select class="input-field">
                <option value="">请选择</option>
                <option value="QFP">QFP</option>
                <option value="BGA">BGA</option>
                <option value="CSP">CSP</option>
              </select>
            </div>
            <div class="input-group">
              <label>数量</label>
              <input type="number" class="input-field" placeholder="输入数量" />
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="showFormModal = false" class="btn btn-secondary">取消</button>
          <button @click="showFormModal = false" class="btn btn-primary">提交</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 组件导航
const components = ref([
  { key: 'buttons', name: '按钮', icon: '🔘' },
  { key: 'inputs', name: '输入框', icon: '📝' },
  { key: 'cards', name: '卡片', icon: '🗂️' },
  { key: 'tables', name: '表格', icon: '📋' },
  { key: 'modals', name: '弹窗', icon: '🔲' },
  { key: 'notifications', name: '通知', icon: '🔔' }
])

const activeComponent = ref('buttons')

// 弹窗状态
const showInfoModal = ref(false)
const showConfirmModal = ref(false)
const showFormModal = ref(false)

// 通知系统
const notifications = ref([])
let notificationId = 0

// 示例数据
const sampleOrders = ref([
  {
    id: 1,
    orderNo: 'IC2024001',
    customerName: 'ABC半导体',
    packageType: 'QFP',
    quantity: 10000,
    status: 'processing'
  },
  {
    id: 2,
    orderNo: 'IC2024002',
    customerName: 'XYZ电子',
    packageType: 'BGA',
    quantity: 5000,
    status: 'completed'
  },
  {
    id: 3,
    orderNo: 'IC2024003',
    customerName: 'DEF芯片',
    packageType: 'CSP',
    quantity: 15000,
    status: 'pending'
  }
])

// 方法
const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待处理',
    processing: '进行中',
    completed: '已完成'
  }
  return statusMap[status] || status
}

const showNotification = (type: string) => {
  const messages = {
    success: '操作成功完成！',
    warning: '请注意相关风险！',
    error: '操作失败，请重试！',
    info: '这是一条信息通知。'
  }
  
  const notification = {
    id: ++notificationId,
    type,
    message: messages[type] || '通知消息'
  }
  
  notifications.value.push(notification)
  
  // 3秒后自动移除
  setTimeout(() => {
    removeNotification(notification.id)
  }, 3000)
}

const getNotificationIcon = (type: string) => {
  const icons = {
    success: '✅',
    warning: '⚠️',
    error: '❌',
    info: 'ℹ️'
  }
  return icons[type] || 'ℹ️'
}

const removeNotification = (id: number) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

onMounted(() => {
  console.log('组件演示页面加载完成')
})
</script>

<style scoped>
.component-demo {
  padding: 1.5rem;
  background: #f9fafb;
  min-height: calc(100vh - 100px);
}

.page-header {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.page-header h1 {
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
}

.page-header p {
  color: #6b7280;
  margin: 0;
}

.component-nav {
  display: flex;
  background: white;
  border-radius: 8px;
  padding: 0.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  gap: 0.5rem;
  overflow-x: auto;
}

.nav-btn {
  flex: 1;
  padding: 1rem;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  white-space: nowrap;
  min-width: 120px;
}

.nav-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.nav-btn.active {
  background: #2563eb;
  color: white;
}

.demo-section {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.demo-section h2 {
  color: #1f2937;
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
}

.demo-group {
  margin-bottom: 2rem;
}

.demo-group:last-child {
  margin-bottom: 0;
}

.demo-group h3 {
  color: #374151;
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
}

.demo-grid {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: flex-end;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

/* 按钮样式 */
.btn, .btn-small {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
  margin-right: 0.5rem;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1rem;
}

.btn-primary { background: #2563eb; color: white; }
.btn-primary:hover { background: #1d4ed8; }

.btn-secondary { background: #e5e7eb; color: #374151; }
.btn-secondary:hover { background: #d1d5db; }

.btn-success { background: #10b981; color: white; }
.btn-success:hover { background: #059669; }

.btn-warning { background: #f59e0b; color: white; }
.btn-warning:hover { background: #d97706; }

.btn-danger { background: #dc2626; color: white; }
.btn-danger:hover { background: #b91c1c; }

.btn-info { background: #06b6d4; color: white; }
.btn-info:hover { background: #0891b2; }

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn.loading {
  position: relative;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 输入框样式 */
.input-group {
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

.input-group label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.input-field {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.input-field:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* 卡片样式 */
.card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  overflow: hidden;
}

.card-hoverable:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transform: translateY(-2px);
  transition: all 0.2s ease;
}

.card-header {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.card-header h4 {
  margin: 0;
  color: #1f2937;
}

.card-body {
  padding: 1rem;
}

.card-body p {
  margin: 0;
  color: #6b7280;
  line-height: 1.5;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
}

.stat-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.stat-content h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.stat-content p {
  margin: 0.25rem 0 0 0;
  color: #6b7280;
  font-size: 0.875rem;
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.data-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.data-table tbody tr:hover {
  background: #f9fafb;
}

.order-no {
  font-family: monospace;
  font-weight: 600;
  color: #2563eb;
}

.number {
  text-align: right;
}

.package-badge, .status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.package-badge.qfp { background: #dbeafe; color: #1d4ed8; }
.package-badge.bga { background: #dcfce7; color: #166534; }
.package-badge.csp { background: #fef3c7; color: #92400e; }

.status-badge.pending { background: #fef3c7; color: #92400e; }
.status-badge.processing { background: #dbeafe; color: #1d4ed8; }
.status-badge.completed { background: #dcfce7; color: #166534; }

/* 通知样式 */
.notifications-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.notification {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  min-width: 300px;
  animation: slideInRight 0.3s ease;
}

.notification.success { background: #dcfce7; color: #166534; }
.notification.warning { background: #fef3c7; color: #92400e; }
.notification.error { background: #fee2e2; color: #dc2626; }
.notification.info { background: #dbeafe; color: #1d4ed8; }

.notification-icon {
  font-size: 1.25rem;
}

.notification-text {
  flex: 1;
}

.notification-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  opacity: 0.7;
}

.notification-close:hover {
  opacity: 1;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 弹窗样式 */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  color: #1f2937;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

@media (max-width: 768px) {
  .component-demo {
    padding: 1rem;
  }
  
  .component-nav {
    overflow-x: auto;
  }
  
  .demo-grid {
    flex-direction: column;
    align-items: stretch;
  }
  
  .cards-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    width: 95%;
    margin: 1rem;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .notifications-container {
    left: 1rem;
    right: 1rem;
  }
  
  .notification {
    min-width: auto;
  }
}
</style>