{"name": "ic-cim-frontend", "version": "1.0.0", "description": "IC封测CIM系统前端 - 极简主义设计", "type": "module", "scripts": {"dev": "vite --host", "build": "vue-tsc && vite build", "build:dev": "vue-tsc && vite build --mode development", "build:staging": "vue-tsc && vite build --mode staging", "preview": "vite preview", "type-check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "lint:style": "stylelint src/**/*.{css,scss,vue} --fix", "format": "prettier --write src/", "test:unit": "vitest", "test:e2e": "cypress run", "test:e2e:dev": "cypress open"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vant/icons": "^3.0.1", "@vueuse/core": "^10.11.1", "axios": "^1.6.7", "d3": "^7.8.5", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-plus": "2.5.6", "lodash-es": "^4.17.21", "pinia": "2.1.7", "vant": "^4.8.7", "vue": "3.4.15", "vue-router": "4.2.5"}, "devDependencies": {"@types/d3": "^7.4.3", "@types/lodash-es": "^4.17.12", "@types/node": "^20.11.16", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.4", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.17", "cypress": "^13.6.6", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.21.1", "jsdom": "^24.0.0", "npm-run-all2": "^6.2.3", "postcss": "^8.4.35", "prettier": "^3.2.5", "sass": "^1.71.0", "stylelint": "^16.2.1", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-standard-scss": "^13.0.0", "stylelint-config-standard-vue": "^1.0.0", "typescript": "~5.3.0", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.1.4", "vitest": "^1.2.2", "vue-tsc": "^1.8.27"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["vue3", "typescript", "vite", "element-plus", "ic-cim", "极简主义", "双主题"], "author": "IC CIM Frontend Team", "license": "MIT"}