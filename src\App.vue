<template>
  <div id="app" class="app-layout">
    <!-- 专业级顶部导航 -->
    <header class="app-header">
      <div class="app-header__content">
        <div class="app-header__left">
          <div class="app-logo">
            <div class="app-logo__icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
              </svg>
            </div>
            <div class="app-logo__text">
              <div class="app-logo__title">IC封测CIM系统</div>
              <div class="app-logo__subtitle">专业制造执行系统</div>
            </div>
          </div>
        </div>
        
        <nav class="app-nav">
          <router-link 
            v-for="navItem in navigation" 
            :key="navItem.path"
            :to="navItem.path"
            class="app-nav__item"
          >
            <div class="app-nav__icon">
              <!-- 简化的图标，直接使用SVG -->
              <svg v-if="navItem.icon === 'home'" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <path d="M3 9.5L12 3L21 9.5"/>
                <path d="M5 21V10.5"/>
                <path d="M19 21V10.5"/>
              </svg>
              <svg v-else-if="navItem.icon === 'grid'" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <rect x="3" y="3" width="7" height="7"/>
                <rect x="14" y="3" width="7" height="7"/>
                <rect x="14" y="14" width="7" height="7"/>
                <rect x="3" y="14" width="7" height="7"/>
              </svg>
              <svg v-else-if="navItem.icon === 'users'" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <path d="m22 21-3-3m0 0a5.5 5.5 0 1 0-7.8-7.8 5.5 5.5 0 0 0 7.8 7.8Z"/>
              </svg>
              <svg v-else-if="navItem.icon === 'clipboard'" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                <rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>
              </svg>
              <svg v-else-if="navItem.icon === 'chart'" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <rect x="3" y="4" width="18" height="16" rx="2"/>
                <path d="M7 8h10"/>
                <path d="M7 12h7"/>
              </svg>
            </div>
            <span class="app-nav__text">{{ navItem.name }}</span>
          </router-link>
        </nav>
        
        <div class="app-header__right">
          <!-- 主题切换器 -->
          <ThemeToggle />
          
          <!-- 用户信息 -->
          <div class="user-info">
            <div class="user-avatar">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
              </svg>
            </div>
            <div class="user-details">
              <div class="user-name">管理员</div>
              <div class="user-role">系统管理员</div>
            </div>
          </div>
        </div>
      </div>
    </header>
    
    <!-- 主内容区域 -->
    <main class="app-main">
      <div class="app-content">
        <router-view v-slot="{ Component, route }">
          <transition name="page" mode="out-in">
            <component :is="Component" :key="route.path" />
          </transition>
        </router-view>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import ThemeToggle from '@/components/layout/ThemeToggle.vue'
import { useTheme } from '@/composables/useTheme'

// 初始化主题系统
useTheme()

// 导航配置
const navigation = [
  {
    name: '首页',
    path: '/',
    icon: 'home'
  },
  {
    name: '组件演示', 
    path: '/demo',
    icon: 'grid'
  },
  {
    name: '客户管理',
    path: '/customers', 
    icon: 'users'
  },
  {
    name: '订单管理',
    path: '/orders', 
    icon: 'clipboard'
  },
  {
    name: '生产计划',
    path: '/production',
    icon: 'chart'
  }
]

console.log('🚀 IC封测CIM系统已启动')
</script>

<style lang="scss" scoped>
// IC封测CIM系统 - 主应用样式
// 严格遵循极简主义设计系统

.app-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--color-bg-primary);
  transition: background-color var(--transition-normal);
}

// ===== 应用头部 =====
.app-header {
  flex-shrink: 0;
  height: var(--header-height);
  background-color: var(--color-nav-bg);
  border-bottom: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  transition: all var(--transition-normal);

  &__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
    
    @media (max-width: 768px) {
      padding: 0 var(--spacing-4);
    }
  }

  &__left {
    display: flex;
    align-items: center;
  }

  &__right {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
  }
}

// ===== 应用Logo =====
.app-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);

  &__icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-primary);
    color: var(--color-text-inverse);
    border-radius: var(--radius-md);
    flex-shrink: 0;
    
    svg {
      width: 20px;
      height: 20px;
    }
  }

  &__text {
    display: flex;
    flex-direction: column;
    gap: 2px;
    
    @media (max-width: 640px) {
      display: none;
    }
  }

  &__title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-primary);
    line-height: var(--line-height-tight);
  }

  &__subtitle {
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    line-height: var(--line-height-tight);
  }
}

// ===== 应用导航 =====
.app-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  
  @media (max-width: 768px) {
    gap: var(--spacing-1);
  }

  &__item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    color: var(--color-nav-text);
    text-decoration: none;
    border-radius: var(--radius-base);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
    
    &:hover {
      background-color: var(--color-nav-item-hover);
      color: var(--color-nav-text-active);
    }

    &.router-link-active {
      background-color: var(--color-nav-item-active);
      color: var(--color-nav-text-active);
    }
    
    @media (max-width: 640px) {
      padding: var(--spacing-2);
    }
  }

  &__icon {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
  }

  &__text {
    @media (max-width: 640px) {
      display: none;
    }
  }
}

// ===== 用户信息 =====
.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-base);
  transition: background-color var(--transition-fast);
  cursor: pointer;
  
  &:hover {
    background-color: var(--color-bg-hover);
  }
  
  @media (max-width: 768px) {
    display: none;
  }
}

.user-avatar {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-bg-tertiary);
  color: var(--color-text-secondary);
  border-radius: var(--radius-full);
  flex-shrink: 0;
  
  svg {
    width: 18px;
    height: 18px;
  }
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
}

.user-role {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  line-height: var(--line-height-tight);
}

// ===== 主内容区域 =====
.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg-secondary);
  transition: background-color var(--transition-normal);
}

.app-content {
  flex: 1;
  overflow: hidden;
  padding: var(--spacing-6);
  
  @media (max-width: 768px) {
    padding: var(--spacing-4);
  }
}

// ===== 页面过渡动画 =====
.page-enter-active,
.page-leave-active {
  transition: all var(--transition-normal);
}

.page-enter-from {
  opacity: 0;
  transform: translateY(16px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-16px);
}

// ===== 响应式优化 =====
@media (max-width: 1024px) {
  .app-header__content {
    gap: var(--spacing-4);
  }
  
  .app-nav {
    gap: var(--spacing-1);
  }
}

@media (max-width: 768px) {
  .app-header {
    height: 56px;
  }
  
  .app-logo__icon {
    width: 28px;
    height: 28px;
    
    svg {
      width: 16px;
      height: 16px;
    }
  }
  
  .app-nav__item {
    padding: var(--spacing-2);
    gap: 0;
  }
  
  .app-content {
    padding: var(--spacing-4) var(--spacing-3);
  }
}

// ===== 主题切换动画 =====
.theme-transition {
  transition: 
    background-color var(--transition-normal),
    color var(--transition-normal),
    border-color var(--transition-normal),
    box-shadow var(--transition-normal);
}
</style>