<template>
  <div class="production-plan">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">生产计划</h1>
      <div class="header-actions">
        <c-button @click="handleRefresh">刷新</c-button>
        <c-button type="primary" @click="showPlanModal = true">
          新建计划
        </c-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <c-card v-for="stat in stats" :key="stat.key" class="stat-card">
        <div class="stat-content">
          <div class="stat-icon" :class="stat.iconClass">
            {{ stat.icon }}
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </c-card>
    </div>

    <!-- 筛选条件 -->
    <c-card class="filter-card">
      <div class="filter-form">
        <div class="filter-item">
          <label>时间范围</label>
          <c-select v-model="filterForm.timeRange" :options="timeRangeOptions" />
        </div>
        <div class="filter-item">
          <label>生产线</label>
          <c-select v-model="filterForm.productionLine" :options="productionLineOptions" />
        </div>
        <div class="filter-item">
          <label>状态</label>
          <c-select v-model="filterForm.status" :options="planStatusOptions" />
        </div>
        <div class="filter-actions">
          <c-button type="primary" @click="loadPlans">查询</c-button>
        </div>
      </div>
    </c-card>

    <!-- 甘特图视图 -->
    <c-card class="gantt-card">
      <div class="gantt-header">
        <h3>生产计划甘特图</h3>
        <div class="view-controls">
          <c-button 
            v-for="view in viewOptions" 
            :key="view.value"
            :type="currentView === view.value ? 'primary' : 'secondary'"
            size="small"
            @click="currentView = view.value"
          >
            {{ view.label }}
          </c-button>
        </div>
      </div>
      <div class="gantt-container" ref="ganttContainer">
        <div class="gantt-timeline">
          <div class="timeline-header">
            <div class="timeline-scale">
              <div 
                v-for="date in timelineData" 
                :key="date.date"
                class="scale-item"
                :style="{ width: scaleItemWidth + 'px' }"
              >
                {{ formatDate(date.date) }}
              </div>
            </div>
          </div>
          <div class="timeline-body">
            <div 
              v-for="plan in filteredPlans" 
              :key="plan.id"
              class="timeline-row"
            >
              <div class="row-label">
                <div class="plan-info">
                  <div class="plan-name">{{ plan.orderNo }}</div>
                  <div class="plan-product">{{ plan.productName }}</div>
                </div>
              </div>
              <div class="row-content" :style="{ width: timelineWidth + 'px' }">
                <div 
                  v-for="stage in plan.stages" 
                  :key="stage.id"
                  :class="getStageClass(stage)"
                  :style="getStageStyle(stage)"
                  @click="handleStageClick(plan, stage)"
                >
                  <div class="stage-content">
                    <span class="stage-name">{{ stage.name }}</span>
                    <span class="stage-progress">{{ stage.progress }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </c-card>

    <!-- 计划列表 -->
    <c-card class="list-card">
      <c-table
        :dataSource="plans"
        :columns="planColumns"
        :loading="tableLoading"
        :showPagination="true"
        v-model:current="pagination.current"
        v-model:pageSize="pagination.pageSize"
        :total="pagination.total"
        @refresh="loadPlans"
        @row-click="handleRowClick"
        class="plan-table"
      >
        <!-- 订单号列 -->
        <template #orderNo="{ record }">
          <span class="order-no">{{ record.orderNo }}</span>
        </template>

        <!-- 进度列 -->
        <template #progress="{ record }">
          <div class="progress-bar">
            <div class="progress-track">
              <div 
                class="progress-fill" 
                :style="{ width: record.progress + '%' }"
              ></div>
            </div>
            <span class="progress-text">{{ record.progress }}%</span>
          </div>
        </template>

        <!-- 状态列 -->
        <template #status="{ record }">
          <span :class="getPlanStatusClass(record.status)">
            {{ getPlanStatusText(record.status) }}
          </span>
        </template>

        <!-- 操作列 -->
        <template #actions="{ record }">
          <div class="table-actions">
            <c-button type="text" size="small" @click="handleViewDetails(record)">详情</c-button>
            <c-button type="text" size="small" @click="handleEditPlan(record)">编辑</c-button>
          </div>
        </template>
      </c-table>
    </c-card>

    <!-- 新建计划模态框 -->
    <c-modal
      v-model:visible="showPlanModal"
      title="新建生产计划"
      width="700px"
      :confirmLoading="submitLoading"
      @confirm="handleSubmitPlan"
      @cancel="handleCancelPlan"
    >
      <div class="plan-form">
        <div class="form-grid">
          <div class="form-item">
            <label class="required">关联订单</label>
            <c-select 
              v-model="planForm.orderId"
              :options="orderOptions"
              placeholder="请选择订单"
              filterable
            />
          </div>
          <div class="form-item">
            <label class="required">生产线</label>
            <c-select 
              v-model="planForm.productionLine"
              :options="productionLineOptions"
              placeholder="请选择生产线"
            />
          </div>
          <div class="form-item">
            <label class="required">计划开始时间</label>
            <c-input 
              v-model="planForm.startDate"
              type="datetime-local"
              placeholder="请选择开始时间"
            />
          </div>
          <div class="form-item">
            <label class="required">计划结束时间</label>
            <c-input 
              v-model="planForm.endDate"
              type="datetime-local"
              placeholder="请选择结束时间"
            />
          </div>
          <div class="form-item full-width">
            <label>备注</label>
            <c-input 
              v-model="planForm.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </div>
        </div>
      </div>
    </c-modal>

    <!-- 阶段详情模态框 -->
    <c-modal
      v-model:visible="showStageModal"
      :title="currentStage ? `${currentStage.name} - 详情` : '阶段详情'"
      width="600px"
      :showFooter="false"
    >
      <div v-if="currentStage" class="stage-detail">
        <div class="detail-grid">
          <div class="detail-item">
            <span class="label">阶段名称</span>
            <span class="value">{{ currentStage.name }}</span>
          </div>
          <div class="detail-item">
            <span class="label">进度</span>
            <span class="value">{{ currentStage.progress }}%</span>
          </div>
          <div class="detail-item">
            <span class="label">开始时间</span>
            <span class="value">{{ currentStage.startDate }}</span>
          </div>
          <div class="detail-item">
            <span class="label">结束时间</span>
            <span class="value">{{ currentStage.endDate }}</span>
          </div>
          <div class="detail-item">
            <span class="label">负责人</span>
            <span class="value">{{ currentStage.assignee }}</span>
          </div>
          <div class="detail-item">
            <span class="label">状态</span>
            <span class="value" :class="getStageStatusClass(currentStage.status)">
              {{ currentStage.status }}
            </span>
          </div>
        </div>
        
        <div class="stage-equipment" v-if="currentStage.equipment">
          <h4>使用设备</h4>
          <div class="equipment-list">
            <div 
              v-for="equip in currentStage.equipment" 
              :key="equip.id"
              class="equipment-item"
            >
              <span class="equipment-name">{{ equip.name }}</span>
              <span class="equipment-status" :class="equip.status">{{ equip.statusText }}</span>
            </div>
          </div>
        </div>
      </div>
    </c-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { formatNumber } from '@/utils/common'
import type { CTableColumn } from '@/components/base'

// 接口定义
interface ProductionPlan {
  id: string
  orderNo: string
  productName: string
  customerName: string
  quantity: number
  progress: number
  status: string
  startDate: string
  endDate: string
  stages: ProductionStage[]
}

interface ProductionStage {
  id: string
  name: string
  type: 'cp' | 'assembly' | 'ft' | 'delivery'
  startDate: string
  endDate: string
  progress: number
  status: string
  assignee: string
  equipment?: Array<{ id: string; name: string; status: string; statusText: string }>
}

// 响应式数据
const plans = ref<ProductionPlan[]>([])
const tableLoading = ref(false)
const submitLoading = ref(false)
const showPlanModal = ref(false)
const showStageModal = ref(false)
const currentView = ref('week')
const currentStage = ref<ProductionStage | null>(null)
const ganttContainer = ref<HTMLElement>()

// 统计数据
const stats = ref([
  { key: 'total', label: '总计划数', value: '28', icon: '📋', iconClass: 'stat-icon--primary' },
  { key: 'running', label: '进行中', value: '12', icon: '⚡', iconClass: 'stat-icon--warning' },
  { key: 'completed', label: '已完成', value: '15', icon: '✅', iconClass: 'stat-icon--success' },
  { key: 'delayed', label: '延期', value: '1', icon: '⚠️', iconClass: 'stat-icon--error' }
])

// 筛选表单
const filterForm = reactive({
  timeRange: 'week',
  productionLine: '',
  status: ''
})

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 计划表单
const planForm = reactive({
  orderId: '',
  productionLine: '',
  startDate: '',
  endDate: '',
  remarks: ''
})

// 选项数据
const timeRangeOptions = [
  { label: '本周', value: 'week' },
  { label: '本月', value: 'month' },
  { label: '本季度', value: 'quarter' }
]

const productionLineOptions = [
  { label: 'CP测试线A', value: 'cp-a' },
  { label: 'CP测试线B', value: 'cp-b' },
  { label: '封装线1', value: 'assembly-1' },
  { label: '封装线2', value: 'assembly-2' },
  { label: 'FT测试线A', value: 'ft-a' },
  { label: 'FT测试线B', value: 'ft-b' }
]

const planStatusOptions = [
  { label: '计划中', value: 'planned' },
  { label: '进行中', value: 'running' },
  { label: '暂停', value: 'paused' },
  { label: '完成', value: 'completed' },
  { label: '延期', value: 'delayed' }
]

const viewOptions = [
  { label: '周视图', value: 'week' },
  { label: '月视图', value: 'month' }
]

const orderOptions = [
  { label: 'ORD-2024-001 - 华为技术', value: '1' },
  { label: 'ORD-2024-002 - 小米科技', value: '2' },
  { label: 'ORD-2024-003 - OPPO', value: '3' }
]

// 表格列配置
const planColumns: CTableColumn[] = [
  {
    key: 'orderNo',
    title: '订单号',
    dataIndex: 'orderNo',
    width: 150
  },
  {
    key: 'productName',
    title: '产品名称',
    dataIndex: 'productName',
    width: 180
  },
  {
    key: 'customerName',
    title: '客户',
    dataIndex: 'customerName',
    width: 150
  },
  {
    key: 'quantity',
    title: '数量',
    dataIndex: 'quantity',
    width: 100,
    align: 'right'
  },
  {
    key: 'progress',
    title: '进度',
    width: 150
  },
  {
    key: 'status',
    title: '状态',
    dataIndex: 'status',
    width: 100
  },
  {
    key: 'startDate',
    title: '开始时间',
    dataIndex: 'startDate',
    width: 120
  },
  {
    key: 'endDate',
    title: '结束时间',
    dataIndex: 'endDate',
    width: 120
  },
  {
    key: 'actions',
    title: '操作',
    width: 120,
    fixed: 'right'
  }
]

// 计算属性
const filteredPlans = computed(() => {
  return plans.value.filter(plan => {
    if (filterForm.status && plan.status !== filterForm.status) return false
    return true
  })
})

const timelineData = computed(() => {
  const dates = []
  const today = new Date()
  const daysToShow = currentView.value === 'week' ? 7 : 30
  
  for (let i = 0; i < daysToShow; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() + i)
    dates.push({ date: date.toISOString().split('T')[0] })
  }
  
  return dates
})

const scaleItemWidth = computed(() => {
  return currentView.value === 'week' ? 120 : 40
})

const timelineWidth = computed(() => {
  return timelineData.value.length * scaleItemWidth.value
})

// 方法
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  if (currentView.value === 'week') {
    return `${date.getMonth() + 1}/${date.getDate()}`
  } else {
    return `${date.getDate()}`
  }
}

const getStageClass = (stage: ProductionStage) => [
  'timeline-stage',
  `timeline-stage--${stage.type}`,
  {
    'timeline-stage--completed': stage.progress === 100,
    'timeline-stage--running': stage.progress > 0 && stage.progress < 100,
    'timeline-stage--pending': stage.progress === 0
  }
]

const getStageStyle = (stage: ProductionStage) => {
  const startDate = new Date(stage.startDate)
  const endDate = new Date(stage.endDate)
  const today = new Date(timelineData.value[0].date)
  
  const startOffset = Math.floor((startDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
  const duration = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  
  return {
    left: Math.max(0, startOffset * scaleItemWidth.value) + 'px',
    width: Math.max(scaleItemWidth.value, duration * scaleItemWidth.value) + 'px'
  }
}

const getPlanStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    'planned': 'status-planned',
    'running': 'status-running',
    'paused': 'status-paused',
    'completed': 'status-completed',
    'delayed': 'status-delayed'
  }
  return classMap[status] || ''
}

const getPlanStatusText = (status: string) => {
  const option = planStatusOptions.find(opt => opt.value === status)
  return option?.label || status
}

const getStageStatusClass = (status: string) => {
  return `stage-status--${status.toLowerCase()}`
}

const loadPlans = async () => {
  tableLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    const mockPlans: ProductionPlan[] = [
      {
        id: '1',
        orderNo: 'ORD-2024-001',
        productName: 'HiSilicon Kirin 9000',
        customerName: '华为技术',
        quantity: 10000,
        progress: 65,
        status: 'running',
        startDate: '2024-02-01',
        endDate: '2024-03-15',
        stages: [
          {
            id: '1-1',
            name: 'CP测试',
            type: 'cp',
            startDate: '2024-02-01',
            endDate: '2024-02-10',
            progress: 100,
            status: 'completed',
            assignee: '张工程师',
            equipment: [
              { id: 'cp-001', name: 'CP测试台A01', status: 'idle', statusText: '空闲' }
            ]
          },
          {
            id: '1-2',
            name: '封装',
            type: 'assembly',
            startDate: '2024-02-11',
            endDate: '2024-02-28',
            progress: 70,
            status: 'running',
            assignee: '李工程师',
            equipment: [
              { id: 'asm-001', name: '封装线1', status: 'busy', statusText: '运行中' }
            ]
          },
          {
            id: '1-3',
            name: 'FT测试',
            type: 'ft',
            startDate: '2024-03-01',
            endDate: '2024-03-10',
            progress: 0,
            status: 'pending',
            assignee: '王工程师',
            equipment: [
              { id: 'ft-001', name: 'FT测试台B01', status: 'maintenance', statusText: '维护中' }
            ]
          }
        ]
      }
    ]
    
    plans.value = mockPlans
    pagination.total = mockPlans.length
  } finally {
    tableLoading.value = false
  }
}

const handleRefresh = () => {
  loadPlans()
}

const handleRowClick = (record: ProductionPlan) => {
  console.log('点击计划:', record)
}

const handleViewDetails = (record: ProductionPlan) => {
  console.log('查看详情:', record)
}

const handleEditPlan = (record: ProductionPlan) => {
  console.log('编辑计划:', record)
}

const handleStageClick = (plan: ProductionPlan, stage: ProductionStage) => {
  currentStage.value = stage
  showStageModal.value = true
}

const handleSubmitPlan = async () => {
  submitLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    console.log('提交计划:', planForm)
    showPlanModal.value = false
    loadPlans()
  } finally {
    submitLoading.value = false
  }
}

const handleCancelPlan = () => {
  Object.keys(planForm).forEach(key => {
    planForm[key] = ''
  })
}

// 生命周期
onMounted(() => {
  loadPlans()
})
</script>

<style lang="scss">
.production-plan {
  padding: var(--spacing-6);
}

.page-header {
  @include flex-between;
  margin-bottom: var(--spacing-6);
}

.page-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--spacing-3);
}

// 统计卡片
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.stat-card {
  .stat-content {
    @include flex-center;
    gap: var(--spacing-4);
    padding: var(--spacing-2);
  }
  
  .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    @include flex-center;
    font-size: 20px;
    
    &--primary { background: var(--color-primary-light); }
    &--success { background: var(--color-success); opacity: 0.1; }
    &--warning { background: var(--color-warning); opacity: 0.1; }
    &--error { background: var(--color-error); opacity: 0.1; }
  }
  
  .stat-info {
    flex: 1;
    
    .stat-value {
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-text-primary);
      margin-bottom: var(--spacing-1);
    }
    
    .stat-label {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }
  }
}

// 筛选卡片
.filter-card {
  margin-bottom: var(--spacing-6);
  
  .filter-form {
    @include flex-between;
    gap: var(--spacing-4);
    
    .filter-item {
      label {
        display: block;
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
        margin-bottom: var(--spacing-1);
      }
    }
    
    .filter-actions {
      margin-top: 20px;
    }
  }
}

// 甘特图
.gantt-card {
  margin-bottom: var(--spacing-6);
  
  .gantt-header {
    @include flex-between;
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-3);
    border-bottom: 1px solid var(--color-border-light);
    
    h3 {
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-bold);
      color: var(--color-text-primary);
      margin: 0;
    }
    
    .view-controls {
      display: flex;
      gap: var(--spacing-2);
    }
  }
  
  .gantt-container {
    overflow-x: auto;
    @include scrollbar;
  }
  
  .gantt-timeline {
    min-width: 800px;
    
    .timeline-header {
      border-bottom: 1px solid var(--color-border-base);
      
      .timeline-scale {
        display: flex;
        height: 40px;
        margin-left: 200px;
        
        .scale-item {
          @include flex-center;
          border-right: 1px solid var(--color-border-light);
          font-size: var(--font-size-xs);
          color: var(--color-text-secondary);
        }
      }
    }
    
    .timeline-body {
      .timeline-row {
        display: flex;
        min-height: 60px;
        border-bottom: 1px solid var(--color-border-light);
        
        &:hover {
          background: var(--color-bg-hover);
        }
        
        .row-label {
          width: 200px;
          padding: var(--spacing-3);
          border-right: 1px solid var(--color-border-light);
          
          .plan-info {
            .plan-name {
              font-weight: var(--font-weight-medium);
              color: var(--color-text-primary);
              margin-bottom: var(--spacing-1);
            }
            
            .plan-product {
              font-size: var(--font-size-xs);
              color: var(--color-text-secondary);
            }
          }
        }
        
        .row-content {
          position: relative;
          padding: var(--spacing-2) 0;
        }
      }
    }
  }
  
  .timeline-stage {
    position: absolute;
    height: 36px;
    border-radius: var(--radius-base);
    cursor: pointer;
    transition: all var(--transition-fast);
    
    &--cp {
      background: var(--color-cp-test);
      border-left: 4px solid var(--color-info);
    }
    
    &--assembly {
      background: var(--color-assembly);
      border-left: 4px solid #8b5cf6;
    }
    
    &--ft {
      background: var(--color-ft-test);
      border-left: 4px solid var(--color-success);
    }
    
    &--completed {
      opacity: 0.8;
    }
    
    &--running {
      box-shadow: 0 0 0 2px var(--color-primary);
    }
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }
    
    .stage-content {
      @include flex-between;
      height: 100%;
      padding: 0 var(--spacing-2);
      align-items: center;
      
      .stage-name {
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-medium);
      }
      
      .stage-progress {
        font-size: var(--font-size-xs);
        opacity: 0.8;
      }
    }
  }
}

// 进度条
.progress-bar {
  @include flex-center;
  gap: var(--spacing-2);
  
  .progress-track {
    flex: 1;
    height: 8px;
    background: var(--color-bg-tertiary);
    border-radius: var(--radius-full);
    overflow: hidden;
    
    .progress-fill {
      height: 100%;
      background: var(--color-success);
      transition: width var(--transition-normal);
    }
  }
  
  .progress-text {
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    min-width: 35px;
  }
}

// 状态样式
.status-planned { color: var(--color-info); }
.status-running { color: var(--color-primary); }
.status-paused { color: var(--color-warning); }
.status-completed { color: var(--color-success); }
.status-delayed { color: var(--color-error); }

// 表单样式
.plan-form {
  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-4);
    
    .full-width {
      grid-column: 1 / -1;
    }
  }
  
  .form-item {
    label {
      display: block;
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
      margin-bottom: var(--spacing-1);
      
      &.required::after {
        content: ' *';
        color: var(--color-error);
      }
    }
  }
}

// 阶段详情
.stage-detail {
  .detail-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
  }
  
  .detail-item {
    .label {
      display: block;
      font-size: var(--font-size-xs);
      color: var(--color-text-secondary);
      margin-bottom: var(--spacing-1);
    }
    
    .value {
      font-size: var(--font-size-sm);
      color: var(--color-text-primary);
      font-weight: var(--font-weight-medium);
    }
  }
  
  .stage-equipment {
    h4 {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-bold);
      color: var(--color-text-primary);
      margin-bottom: var(--spacing-3);
    }
    
    .equipment-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }
    
    .equipment-item {
      @include flex-between;
      padding: var(--spacing-2) var(--spacing-3);
      background: var(--color-bg-secondary);
      border-radius: var(--radius-base);
      
      .equipment-name {
        font-size: var(--font-size-sm);
        color: var(--color-text-primary);
      }
      
      .equipment-status {
        font-size: var(--font-size-xs);
        padding: 2px 6px;
        border-radius: var(--radius-sm);
        
        &.idle {
          background: var(--color-success);
          color: white;
          opacity: 0.8;
        }
        
        &.busy {
          background: var(--color-warning);
          color: white;
          opacity: 0.8;
        }
        
        &.maintenance {
          background: var(--color-error);
          color: white;
          opacity: 0.8;
        }
      }
    }
  }
}

// 响应式
@media (max-width: 768px) {
  .production-plan {
    padding: var(--spacing-4);
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .filter-form {
    flex-direction: column;
    gap: var(--spacing-3) !important;
  }
  
  .gantt-container {
    font-size: var(--font-size-xs);
    
    .timeline-row .row-label {
      width: 150px;
    }
  }
  
  .form-grid {
    grid-template-columns: 1fr !important;
  }
  
  .detail-grid {
    grid-template-columns: 1fr !important;
  }
}
</style>