// IC封测CIM系统 - 生产计划页面样式

.production-page {
  padding: var(--spacing-6);
  
  &__header {
    margin-bottom: var(--spacing-6);
  }
  
  &__stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
  }
  
  &__gantt {
    background-color: var(--color-card-bg);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin-bottom: var(--spacing-6);
  }
  
  &__stages {
    background-color: var(--color-card-bg);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-5);
  }
}

.production-stage {
  display: flex;
  align-items: center;
  padding: var(--spacing-3);
  border-radius: var(--radius-base);
  margin-bottom: var(--spacing-2);
  transition: background-color var(--transition-fast);
  
  &:hover {
    background-color: var(--color-bg-hover);
  }
  
  &__icon {
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-3);
    flex-shrink: 0;
  }
  
  &__info {
    flex: 1;
  }
  
  &__name {
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    margin-bottom: 2px;
  }
  
  &__progress {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
  }
  
  &__status {
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
  }
  
  &--cp {
    .production-stage__icon {
      color: var(--color-stage-cp);
    }
  }
  
  &--assembly {
    .production-stage__icon {
      color: var(--color-stage-assembly);
    }
  }
  
  &--ft {
    .production-stage__icon {
      color: var(--color-stage-ft);
    }
  }
  
  &--delivery {
    .production-stage__icon {
      color: var(--color-stage-delivery);
    }
  }
}