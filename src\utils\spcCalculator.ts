// SPC实例计算器类
import type { SPCPoint, ControlLimits, SPCStatistics } from '@/types/quality'

export class SPCCalculator {
  // 计算控制限
  calculateControlLimits(sampleData: SPCPoint[]): ControlLimits {
    if (sampleData.length === 0) {
      return {
        xbar: { ucl: 0, cl: 0, lcl: 0 },
        r: { ucl: 0, cl: 0, lcl: 0 },
        sigma: { ucl: 3, cl: 0, lcl: -3 }
      }
    }

    const means = sampleData.map(point => point.mean)
    const ranges = sampleData.map(point => point.range)

    const xBarBar = means.reduce((sum, mean) => sum + mean, 0) / means.length
    const rBar = ranges.reduce((sum, range) => sum + range, 0) / ranges.length

    // 使用标准子组大小5的控制图常数
    const A2 = 0.577
    const D3 = 0.0
    const D4 = 2.115

    return {
      xbar: {
        ucl: xBarBar + A2 * rBar,
        cl: xBarBar,
        lcl: xBarBar - A2 * rBar
      },
      r: {
        ucl: D4 * rBar,
        cl: rBar,
        lcl: D3 * rBar
      },
      sigma: {
        ucl: 3.0,
        cl: 0,
        lcl: -3.0
      }
    }
  }

  // 计算统计量
  calculateStatistics(
    sampleData: SPCPoint[],
    usl: number,
    lsl: number,
    target: number
  ): SPCStatistics {
    if (sampleData.length === 0) {
      return {
        cpk: 0,
        ppk: 0,
        cp: 0,
        pp: 0,
        ca: 0,
        mean: 0,
        standardDeviation: 0,
        yield: 0
      }
    }

    const allValues = sampleData.flatMap(point => point.values)
    const mean = allValues.reduce((sum, val) => sum + val, 0) / allValues.length
    const variance =
      allValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (allValues.length - 1)
    const standardDeviation = Math.sqrt(variance)

    // 过程能力计算
    const cp = (usl - lsl) / (6 * standardDeviation)
    const cpk = Math.min(
      (usl - mean) / (3 * standardDeviation),
      (mean - lsl) / (3 * standardDeviation)
    )
    const pp = cp // 简化处理，实际应使用过程性能标准差
    const ppk = cpk // 简化处理
    const ca = Math.abs(mean - target) / ((usl - lsl) / 2)

    // 良率计算
    const passCount = allValues.filter(val => val >= lsl && val <= usl).length
    const yieldValue = passCount / allValues.length

    return {
      cpk,
      ppk,
      cp,
      pp,
      ca,
      mean,
      standardDeviation,
      yield: yieldValue
    }
  }

  // 计算sigma水平
  static calculateSigmaLevel(defectRate: number): number {
    if (defectRate <= 0) return 6.0
    if (defectRate >= 1) return 0.0

    // 使用正态分布逆函数近似计算
    const z = this.normalInverse(1 - defectRate / 2)
    return Math.max(0, Math.min(6, z))
  }

  // 计算DPMO（Defects Per Million Opportunities）
  static calculateDPMO(defects: number, opportunities: number, units: number): number {
    if (opportunities === 0 || units === 0) return 0
    return (defects / (opportunities * units)) * 1000000
  }

  // 正态分布逆函数近似
  static normalInverse(p: number): number {
    // 使用Beasley-Springer-Moro算法近似
    const a = [
      -3.969683028665376e1, 2.209460984245205e2, -2.759285104469687e2, 1.38357751867269e2,
      -3.066479806614716e1, 2.506628277459239
    ]
    const b = [
      -5.447609879822406e1, 1.615858368580409e2, -1.556989798598866e2, 6.680131188771972e1,
      -1.328068155288572e1
    ]
    const c = [
      -7.784894002430293e-3, -3.223964580411365e-1, -2.400758277161838, -2.549732539343734,
      4.374664141464968, 2.938163982698783
    ]
    const d = [7.784695709041462e-3, 3.224671290700398e-1, 2.445134137142996, 3.754408661907416]

    const pLow = 0.02425
    const pHigh = 1 - pLow

    if (p < pLow) {
      const q = Math.sqrt(-2 * Math.log(p))
      return (
        (((((c[0] * q + c[1]) * q + c[2]) * q + c[3]) * q + c[4]) * q + c[5]) /
        ((((d[0] * q + d[1]) * q + d[2]) * q + d[3]) * q + 1)
      )
    }

    if (p <= pHigh) {
      const q = p - 0.5
      const r = q * q
      return (
        ((((((a[0] * r + a[1]) * r + a[2]) * r + a[3]) * r + a[4]) * r + a[5]) * q) /
        (((((b[0] * r + b[1]) * r + b[2]) * r + b[3]) * r + b[4]) * r + 1)
      )
    }

    const q = Math.sqrt(-2 * Math.log(1 - p))
    return (
      -(((((c[0] * q + c[1]) * q + c[2]) * q + c[3]) * q + c[4]) * q + c[5]) /
      ((((d[0] * q + d[1]) * q + d[2]) * q + d[3]) * q + 1)
    )
  }
}
