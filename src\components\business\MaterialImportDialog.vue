<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量导入物料"
    width="800px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="import-container">
      <!-- 导入步骤 -->
      <el-steps :active="currentStep" align-center>
        <el-step title="选择文件" description="上传Excel文件" />
        <el-step title="数据校验" description="检查数据格式" />
        <el-step title="确认导入" description="导入到系统" />
      </el-steps>

      <!-- 步骤1: 文件上传 -->
      <div v-show="currentStep === 0" class="step-content">
        <div class="upload-section">
          <el-alert
            title="导入说明"
            type="info"
            :closable="false"
            show-icon
            style="margin-bottom: 20px"
          >
            <template #default>
              <div class="import-instructions">
                <p>1. 请按照标准模板格式准备Excel文件</p>
                <p>2. 支持的文件格式：.xlsx、.xls</p>
                <p>3. 建议单次导入数量不超过1000条</p>
                <p>4. 物料编码必须唯一，重复编码将被跳过</p>
              </div>
            </template>
          </el-alert>

          <div class="upload-actions">
            <el-button type="info" @click="downloadTemplate">
              <el-icon><Download /></el-icon>
              下载模板
            </el-button>
            <el-button type="success" @click="downloadSample">
              <el-icon><DocumentCopy /></el-icon>
              下载示例数据
            </el-button>
          </div>

          <el-upload
            ref="uploadRef"
            class="material-upload"
            drag
            :auto-upload="false"
            :limit="1"
            accept=".xlsx,.xls"
            :on-change="handleFileChange"
            :on-exceed="handleExceed"
            :before-upload="beforeUpload"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将Excel文件拖拽到此处，或
              <em>点击选择文件</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持.xlsx/.xls格式，文件大小不超过10MB
              </div>
            </template>
          </el-upload>

          <div v-if="selectedFile" class="file-info">
            <el-card shadow="hover">
              <div class="file-details">
                <div class="file-icon">
                  <el-icon size="32" color="var(--color-success)"><Document /></el-icon>
                </div>
                <div class="file-meta">
                  <div class="file-name">{{ selectedFile.name }}</div>
                  <div class="file-size">{{ formatFileSize(selectedFile.size) }}</div>
                  <div class="file-time">{{ formatTime(selectedFile.lastModified) }}</div>
                </div>
                <div class="file-actions">
                  <el-button size="small" @click="clearFile">
                    <el-icon><Delete /></el-icon>
                    移除
                  </el-button>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>

      <!-- 步骤2: 数据校验 -->
      <div v-show="currentStep === 1" class="step-content">
        <div class="validation-section">
          <div v-if="validating" class="validation-loading">
            <el-skeleton :rows="5" animated />
            <div class="loading-text">正在解析和校验数据...</div>
          </div>

          <div v-else-if="validationResult" class="validation-result">
            <!-- 校验统计 -->
            <div class="validation-stats">
              <el-row :gutter="20">
                <el-col :span="6">
                  <div class="stat-item total">
                    <div class="stat-value">{{ validationResult.totalCount }}</div>
                    <div class="stat-label">总记录数</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="stat-item valid">
                    <div class="stat-value">{{ validationResult.validCount }}</div>
                    <div class="stat-label">有效记录</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="stat-item error">
                    <div class="stat-value">{{ validationResult.errorCount }}</div>
                    <div class="stat-label">错误记录</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="stat-item warning">
                    <div class="stat-value">{{ validationResult.warningCount }}</div>
                    <div class="stat-label">警告记录</div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 数据预览 -->
            <el-tabs v-model="previewTab" class="validation-tabs">
              <el-tab-pane label="有效数据预览" name="valid">
                <el-table
                  :data="validationResult.validData.slice(0, 10)"
                  border
                  stripe
                  max-height="300"
                  size="small"
                >
                  <el-table-column prop="materialCode" label="物料编码" width="120" />
                  <el-table-column prop="materialName" label="物料名称" width="150" />
                  <el-table-column prop="specification" label="规格型号" width="180" />
                  <el-table-column prop="category" label="分类" width="100" />
                  <el-table-column prop="manufacturer" label="制造商" width="120" />
                  <el-table-column prop="status" label="状态" width="80">
                    <template #default="{ row }">
                      <el-tag :type="row.status === 'active' ? 'success' : 'info'" size="small">
                        {{ row.status === 'active' ? '正常' : '其他' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>
                <div v-if="validationResult.validData.length > 10" class="preview-tip">
                  仅显示前10条有效数据，导入时将处理全部{{ validationResult.validData.length }}条有效记录
                </div>
              </el-tab-pane>

              <el-tab-pane label="错误数据" name="errors" :disabled="validationResult.errorCount === 0">
                <el-table
                  :data="validationResult.errorData"
                  border
                  stripe
                  max-height="300"
                  size="small"
                >
                  <el-table-column type="index" label="#" width="50" />
                  <el-table-column prop="rowIndex" label="行号" width="60" />
                  <el-table-column prop="materialCode" label="物料编码" width="120" />
                  <el-table-column prop="materialName" label="物料名称" width="150" />
                  <el-table-column prop="errors" label="错误信息" min-width="200">
                    <template #default="{ row }">
                      <div class="error-messages">
                        <el-tag
                          v-for="error in row.errors"
                          :key="error"
                          type="danger"
                          size="small"
                          style="margin: 2px"
                        >
                          {{ error }}
                        </el-tag>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>

              <el-tab-pane label="警告数据" name="warnings" :disabled="validationResult.warningCount === 0">
                <el-table
                  :data="validationResult.warningData"
                  border
                  stripe
                  max-height="300"
                  size="small"
                >
                  <el-table-column type="index" label="#" width="50" />
                  <el-table-column prop="rowIndex" label="行号" width="60" />
                  <el-table-column prop="materialCode" label="物料编码" width="120" />
                  <el-table-column prop="materialName" label="物料名称" width="150" />
                  <el-table-column prop="warnings" label="警告信息" min-width="200">
                    <template #default="{ row }">
                      <div class="warning-messages">
                        <el-tag
                          v-for="warning in row.warnings"
                          :key="warning"
                          type="warning"
                          size="small"
                          style="margin: 2px"
                        >
                          {{ warning }}
                        </el-tag>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>

      <!-- 步骤3: 确认导入 -->
      <div v-show="currentStep === 2" class="step-content">
        <div class="import-section">
          <div v-if="importing" class="import-progress">
            <div class="progress-header">
              <h3>正在导入物料数据...</h3>
              <div class="progress-stats">
                已处理 {{ importProgress.current }} / {{ importProgress.total }} 条记录
              </div>
            </div>
            <el-progress
              :percentage="importProgress.percentage"
              :stroke-width="12"
              status="success"
            />
            <div class="progress-details">
              <div class="detail-item">
                <span class="label">成功:</span>
                <span class="value success">{{ importProgress.success }}</span>
              </div>
              <div class="detail-item">
                <span class="label">失败:</span>
                <span class="value error">{{ importProgress.failed }}</span>
              </div>
              <div class="detail-item">
                <span class="label">跳过:</span>
                <span class="value warning">{{ importProgress.skipped }}</span>
              </div>
            </div>
          </div>

          <div v-else-if="importResult" class="import-result">
            <el-result
              :icon="importResult.success ? 'success' : 'warning'"
              :title="importResult.success ? '导入完成' : '导入完成(部分失败)'"
              :sub-title="importResult.message"
            >
              <template #extra>
                <div class="result-details">
                  <div class="result-stats">
                    <div class="stat-group">
                      <div class="stat-item">
                        <div class="stat-value success">{{ importResult.successCount }}</div>
                        <div class="stat-label">成功导入</div>
                      </div>
                      <div class="stat-item">
                        <div class="stat-value error">{{ importResult.failedCount }}</div>
                        <div class="stat-label">导入失败</div>
                      </div>
                      <div class="stat-item">
                        <div class="stat-value warning">{{ importResult.skippedCount }}</div>
                        <div class="stat-label">重复跳过</div>
                      </div>
                    </div>
                  </div>

                  <div v-if="importResult.failedItems.length > 0" class="failed-items">
                    <h4>失败记录详情</h4>
                    <el-table
                      :data="importResult.failedItems"
                      border
                      size="small"
                      max-height="200"
                    >
                      <el-table-column prop="materialCode" label="物料编码" width="120" />
                      <el-table-column prop="materialName" label="物料名称" width="150" />
                      <el-table-column prop="reason" label="失败原因" min-width="200" />
                    </el-table>
                  </div>
                </div>
              </template>
            </el-result>
          </div>

          <div v-else class="import-confirm">
            <el-alert
              title="导入确认"
              type="success"
              :closable="false"
              show-icon
            >
              <template #default>
                <div class="confirm-info">
                  <p>即将导入 <strong>{{ validationResult?.validCount || 0 }}</strong> 条有效的物料数据到系统中</p>
                  <p v-if="validationResult && validationResult.errorCount > 0" class="error-notice">
                    <el-icon><WarningFilled /></el-icon>
                    {{ validationResult.errorCount }} 条错误数据将被跳过
                  </p>
                  <p v-if="validationResult && validationResult.warningCount > 0" class="warning-notice">
                    <el-icon><InfoFilled /></el-icon>
                    {{ validationResult.warningCount }} 条数据存在警告，但仍会导入
                  </p>
                </div>
              </template>
            </el-alert>

            <div class="import-options">
              <h4>导入选项</h4>
              <el-checkbox v-model="importOptions.skipDuplicates">跳过重复的物料编码</el-checkbox>
              <el-checkbox v-model="importOptions.updateExisting">更新已存在的物料信息</el-checkbox>
              <el-checkbox v-model="importOptions.createBackup">导入前创建数据备份</el-checkbox>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button v-if="currentStep > 0" @click="previousStep">上一步</el-button>
        <el-button
          v-if="currentStep < 2"
          type="primary"
          :disabled="!canProceed"
          @click="nextStep"
        >
          下一步
        </el-button>
        <el-button
          v-if="currentStep === 2 && !importResult"
          type="success"
          :loading="importing"
          @click="startImport"
        >
          开始导入
        </el-button>
        <el-button
          v-if="importResult"
          type="primary"
          @click="handleComplete"
        >
          完成
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import type { UploadFile, UploadFiles, UploadInstance } from 'element-plus'

interface Props {
  visible: boolean
}

interface Emits {
  'update:visible': [visible: boolean]
  'success': [count: number]
}

// 验证结果接口
interface ValidationResult {
  totalCount: number
  validCount: number
  errorCount: number
  warningCount: number
  validData: any[]
  errorData: any[]
  warningData: any[]
}

// 导入结果接口
interface ImportResult {
  success: boolean
  message: string
  successCount: number
  failedCount: number
  skippedCount: number
  failedItems: any[]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 组件引用
const uploadRef = ref<UploadInstance>()

// 页面状态
const currentStep = ref(0)
const selectedFile = ref<File | null>(null)
const validating = ref(false)
const importing = ref(false)
const previewTab = ref('valid')

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 验证结果
const validationResult = ref<ValidationResult | null>(null)

// 导入结果
const importResult = ref<ImportResult | null>(null)

// 导入选项
const importOptions = reactive({
  skipDuplicates: true,
  updateExisting: false,
  createBackup: true
})

// 导入进度
const importProgress = reactive({
  current: 0,
  total: 0,
  percentage: 0,
  success: 0,
  failed: 0,
  skipped: 0
})

// 是否可以进行下一步
const canProceed = computed(() => {
  if (currentStep.value === 0) {
    return selectedFile.value !== null
  }
  if (currentStep.value === 1) {
    return validationResult.value !== null && validationResult.value.validCount > 0
  }
  return true
})

// 监听对话框打开，重置状态
watch(() => props.visible, (visible) => {
  if (visible) {
    resetDialog()
  }
})

// 重置对话框状态
const resetDialog = () => {
  currentStep.value = 0
  selectedFile.value = null
  validationResult.value = null
  importResult.value = null
  validating.value = false
  importing.value = false
  previewTab.value = 'valid'
  
  // 重置导入进度
  Object.assign(importProgress, {
    current: 0,
    total: 0,
    percentage: 0,
    success: 0,
    failed: 0,
    skipped: 0
  })
  
  // 清空上传组件
  uploadRef.value?.clearFiles()
}

// 处理文件选择
const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  if (file.raw) {
    selectedFile.value = file.raw
  }
}

// 处理文件数量超限
const handleExceed = () => {
  ElMessage.warning('只能选择一个文件')
}

// 文件上传前检查
const beforeUpload = (file: File) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel'
  
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB')
    return false
  }
  
  return false // 阻止自动上传
}

// 清空文件
const clearFile = () => {
  selectedFile.value = null
  uploadRef.value?.clearFiles()
}

// 下载模板
const downloadTemplate = () => {
  ElMessage.info('模板下载功能开发中...')
}

// 下载示例数据
const downloadSample = () => {
  ElMessage.info('示例数据下载功能开发中...')
}

// 下一步
const nextStep = async () => {
  if (currentStep.value === 0) {
    // 进入数据校验
    await validateData()
    currentStep.value = 1
  } else if (currentStep.value === 1) {
    // 进入确认导入
    currentStep.value = 2
  }
}

// 上一步
const previousStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 数据校验
const validateData = async () => {
  if (!selectedFile.value) return
  
  validating.value = true
  
  try {
    // 模拟数据校验过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟验证结果
    const mockResult: ValidationResult = {
      totalCount: 150,
      validCount: 140,
      errorCount: 8,
      warningCount: 2,
      validData: generateMockData(10, 'valid'),
      errorData: generateMockData(8, 'error'),
      warningData: generateMockData(2, 'warning')
    }
    
    validationResult.value = mockResult
    
    ElMessage.success('数据校验完成')
    
  } catch (error) {
    console.error('数据校验失败:', error)
    ElMessage.error('数据校验失败，请检查文件格式')
  } finally {
    validating.value = false
  }
}

// 生成模拟数据
const generateMockData = (count: number, type: 'valid' | 'error' | 'warning') => {
  const data = []
  
  for (let i = 0; i < count; i++) {
    const baseData = {
      rowIndex: i + 2,
      materialCode: `MAT-${type.toUpperCase()}-${String(i + 1).padStart(3, '0')}`,
      materialName: `${type === 'valid' ? '正常' : type === 'error' ? '错误' : '警告'}物料${i + 1}`,
      specification: `${type}规格型号${i + 1}`,
      category: 'substrate',
      manufacturer: `${type}制造商${i + 1}`,
      status: 'active'
    }
    
    if (type === 'error') {
      data.push({
        ...baseData,
        errors: ['物料编码重复', '制造商信息缺失']
      })
    } else if (type === 'warning') {
      data.push({
        ...baseData,
        warnings: ['价格信息缺失', '供应商信息不完整']
      })
    } else {
      data.push(baseData)
    }
  }
  
  return data
}

// 开始导入
const startImport = async () => {
  if (!validationResult.value) return
  
  importing.value = true
  
  // 初始化进度
  importProgress.total = validationResult.value.validCount
  importProgress.current = 0
  importProgress.percentage = 0
  importProgress.success = 0
  importProgress.failed = 0
  importProgress.skipped = 0
  
  try {
    // 模拟导入过程
    for (let i = 0; i < importProgress.total; i++) {
      await new Promise(resolve => setTimeout(resolve, 50))
      
      importProgress.current = i + 1
      importProgress.percentage = Math.round((i + 1) / importProgress.total * 100)
      
      // 模拟导入结果
      const random = Math.random()
      if (random < 0.9) {
        importProgress.success++
      } else if (random < 0.95) {
        importProgress.skipped++
      } else {
        importProgress.failed++
      }
    }
    
    // 设置导入结果
    importResult.value = {
      success: importProgress.failed === 0,
      message: importProgress.failed === 0 
        ? `成功导入${importProgress.success}条物料数据` 
        : `导入完成，${importProgress.success}条成功，${importProgress.failed}条失败`,
      successCount: importProgress.success,
      failedCount: importProgress.failed,
      skippedCount: importProgress.skipped,
      failedItems: importProgress.failed > 0 ? [
        {
          materialCode: 'MAT-FAIL-001',
          materialName: '失败物料1',
          reason: '物料编码重复'
        },
        {
          materialCode: 'MAT-FAIL-002',
          materialName: '失败物料2',
          reason: '数据格式错误'
        }
      ] : []
    }
    
    // 显示通知
    if (importResult.value.success) {
      ElNotification({
        title: '导入成功',
        message: `成功导入${importResult.value.successCount}条物料数据`,
        type: 'success',
        duration: 5000
      })
    } else {
      ElNotification({
        title: '导入完成',
        message: `部分数据导入失败，请检查失败记录`,
        type: 'warning',
        duration: 5000
      })
    }
    
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入过程中发生错误')
    
    importResult.value = {
      success: false,
      message: '导入失败，请重试',
      successCount: 0,
      failedCount: importProgress.total,
      skippedCount: 0,
      failedItems: []
    }
  } finally {
    importing.value = false
  }
}

// 完成导入
const handleComplete = () => {
  const successCount = importResult.value?.successCount || 0
  emit('success', successCount)
  dialogVisible.value = false
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) return `${size}B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
  return `${(size / (1024 * 1024)).toFixed(1)}MB`
}

// 格式化时间
const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString('zh-CN')
}
</script>

<style lang="scss" scoped>
.import-container {
  .step-content {
    min-height: 400px;
    margin-top: 30px;
  }
  
  .import-instructions {
    line-height: 1.6;
    
    p {
      margin: 4px 0;
    }
  }
  
  .upload-actions {
    margin: 20px 0;
    text-align: center;
    
    .el-button {
      margin: 0 10px;
    }
  }
  
  .material-upload {
    margin: 20px 0;
    
    :deep(.el-upload-dragger) {
      width: 100%;
      height: 180px;
    }
  }
  
  .file-info {
    margin-top: 20px;
    
    .file-details {
      display: flex;
      gap: 16px;
      align-items: center;
      
      .file-meta {
        flex: 1;
        
        .file-name {
          margin-bottom: 4px;
          font-weight: 500;
          color: var(--color-text-primary);
        }
        
        .file-size, .file-time {
          font-size: 12px;
          color: var(--color-text-secondary);
        }
      }
    }
  }
  
  .validation-loading {
    padding: 40px 0;
    text-align: center;
    
    .loading-text {
      margin-top: 16px;
      color: var(--color-text-secondary);
    }
  }
  
  .validation-stats {
    margin-bottom: 20px;
    
    .stat-item {
      padding: 20px;
      text-align: center;
      border-radius: 8px;
      
      .stat-value {
        margin-bottom: 8px;
        font-size: 28px;
        font-weight: 600;
      }
      
      .stat-label {
        font-size: 14px;
        color: var(--color-text-secondary);
      }
      
      &.total {
        background: var(--color-primary-light);

        .stat-value { color: var(--color-primary); }
      }
      
      &.valid {
        background: var(--color-success-light);

        .stat-value { color: var(--color-success); }
      }
      
      &.error {
        background: var(--color-danger-light);

        .stat-value { color: var(--color-danger); }
      }
      
      &.warning {
        background: var(--color-warning-light);

        .stat-value { color: var(--color-warning); }
      }
    }
  }
  
  .validation-tabs {
    :deep(.el-tabs__content) {
      padding-top: 16px;
    }
  }
  
  .preview-tip {
    margin-top: 8px;
    font-size: 12px;
    color: var(--color-text-secondary);
    text-align: center;
  }
  
  .error-messages, .warning-messages {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .import-progress {
    padding: 40px 20px;
    text-align: center;
    
    .progress-header {
      margin-bottom: 24px;
      
      h3 {
        margin: 0 0 8px;
        color: var(--color-text-primary);
      }
      
      .progress-stats {
        color: var(--color-text-secondary);
      }
    }
    
    .progress-details {
      display: flex;
      gap: 40px;
      justify-content: center;
      margin-top: 20px;
      
      .detail-item {
        .label {
          margin-right: 8px;
          color: var(--color-text-secondary);
        }
        
        .value {
          font-weight: 600;
          
          &.success { color: var(--color-success); }

          &.error { color: var(--color-danger); }

          &.warning { color: var(--color-warning); }
        }
      }
    }
  }
  
  .import-result {
    .result-details {
      .result-stats {
        margin: 20px 0;
        
        .stat-group {
          display: flex;
          gap: 40px;
          justify-content: center;
          
          .stat-item {
            text-align: center;
            
            .stat-value {
              font-size: 24px;
              font-weight: 600;
              
              &.success { color: var(--color-success); }

              &.error { color: var(--color-danger); }

              &.warning { color: var(--color-warning); }
            }
            
            .stat-label {
              margin-top: 4px;
              font-size: 14px;
              color: var(--color-text-secondary);
            }
          }
        }
      }
      
      .failed-items {
        margin-top: 20px;
        
        h4 {
          margin: 0 0 12px;
          color: var(--color-text-primary);
        }
      }
    }
  }
  
  .import-confirm {
    .confirm-info {
      line-height: 1.6;
      
      .error-notice {
        display: flex;
        gap: 6px;
        align-items: center;
        color: var(--color-danger);
      }
      
      .warning-notice {
        display: flex;
        gap: 6px;
        align-items: center;
        color: var(--color-warning);
      }
    }
    
    .import-options {
      padding: 16px;
      margin-top: 24px;
      background: var(--color-bg-light);
      border-radius: 6px;
      
      h4 {
        margin: 0 0 12px;
        color: var(--color-text-primary);
      }
      
      .el-checkbox {
        display: block;
        margin: 8px 0;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
  
  .el-button {
    margin-left: 10px;
  }
}

// 响应式设计
@media (width <= 900px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 20px auto !important;
  }
  
  .import-container {
    .validation-stats {
      .el-col {
        margin-bottom: 16px;
      }
    }
    
    .progress-details {
      flex-direction: column;
      gap: 12px !important;
    }
    
    .stat-group {
      flex-direction: column !important;
      gap: 20px !important;
    }
  }
}
</style>