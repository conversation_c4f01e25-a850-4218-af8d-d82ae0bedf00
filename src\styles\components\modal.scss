// IC封测CIM系统 - 模态框组件样式

.modal-mask {
  position: fixed;
  inset: 0;
  background-color: var(--color-bg-mask);
  z-index: var(--z-modal-backdrop);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
}

.modal {
  background-color: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  z-index: var(--z-modal);
  
  &__header {
    padding: var(--spacing-5);
    border-bottom: 1px solid var(--color-border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    &-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }
    
    &-close {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      background: transparent;
      color: var(--color-text-tertiary);
      cursor: pointer;
      border-radius: var(--radius-base);
      transition: all var(--transition-fast);
      
      &:hover {
        background-color: var(--color-bg-hover);
        color: var(--color-text-primary);
      }
    }
  }
  
  &__body {
    padding: var(--spacing-5);
    overflow-y: auto;
  }
  
  &__footer {
    padding: var(--spacing-5);
    border-top: 1px solid var(--color-border-light);
    background-color: var(--color-bg-secondary);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-3);
  }
}