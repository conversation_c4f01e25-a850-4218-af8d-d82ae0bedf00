<!--
  合同管理页面
  Contract Management Page - 合同全生命周期管理
  功能：合同生成、签署、执行、变更的全生命周期管理
-->
<template>
  <div class="contract-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon><Document /></el-icon>
          合同管理
        </h1>
        <p class="page-subtitle">Contract Management - 合同全生命周期管理</p>
      </div>
      <div class="header-actions">
        <el-button type="primary"
@click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建合同
        </el-button>
        <el-button @click="showTemplateDialog = true">
          <el-icon><Collection /></el-icon>
          模板管理
        </el-button>
        <el-button @click="exportContracts">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-number">
            {{ statistics.totalContracts }}
          </div>
          <div class="stat-label">合同总数</div>
        </div>
        <el-icon class="stat-icon total">
          <Document />
        </el-icon>
      </div>
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-number">
            {{ statistics.statusDistribution.executing }}
          </div>
          <div class="stat-label">执行中</div>
        </div>
        <el-icon class="stat-icon executing">
          <CircleCheck />
        </el-icon>
      </div>
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-number">
            {{ statistics.expiringContracts }}
          </div>
          <div class="stat-label">即将到期</div>
        </div>
        <el-icon class="stat-icon warning">
          <Warning />
        </el-icon>
      </div>
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ (statistics.totalValue / 100000000).toFixed(1) }}亿</div>
          <div class="stat-label">合同总价值</div>
        </div>
        <el-icon class="stat-icon value">
          <Money />
        </el-icon>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="queryParams.keyword"
              placeholder="搜索合同编号、客户名称..."
              prefix-icon="Search"
              @input="handleSearch"
            />
          </el-col>
          <el-col :span="4">
            <el-select v-model="queryParams.status"
placeholder="合同状态" @change="handleSearch">
              <el-option label="全部状态"
value="" />
              <el-option label="草稿"
:value="ContractStatus.DRAFT" />
              <el-option label="待审核"
:value="ContractStatus.PENDING_REVIEW" />
              <el-option label="待签署"
:value="ContractStatus.PENDING_SIGN" />
              <el-option label="执行中"
:value="ContractStatus.EXECUTING" />
              <el-option label="已完成"
:value="ContractStatus.COMPLETED" />
              <el-option label="已终止"
:value="ContractStatus.TERMINATED" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="queryParams.type"
placeholder="合同类型" @change="handleSearch">
              <el-option label="全部类型"
value="" />
              <el-option label="量产合同"
:value="ContractType.MASS_PRODUCTION" />
              <el-option label="NPI开发"
:value="ContractType.NPI_DEVELOPMENT" />
              <el-option label="框架合同"
:value="ContractType.FRAMEWORK" />
              <el-option label="紧急订单"
:value="ContractType.URGENT_ORDER" />
              <el-option label="定制服务"
:value="ContractType.CUSTOM_SERVICE" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              @change="handleSearch"
            />
          </el-col>
          <el-col :span="4">
            <el-button type="primary"
@click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 合同列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>合同列表</span>
          <div class="header-tools">
            <el-button-group size="small">
              <el-button :type="viewMode === 'table' ? 'primary' : ''"
@click="viewMode = 'table'">
                <el-icon><Grid /></el-icon>
              </el-button>
              <el-button :type="viewMode === 'card' ? 'primary' : ''"
@click="viewMode = 'card'">
                <el-icon><Menu /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <el-table
        v-if="viewMode === 'table'"
        v-loading="loading"
        :data="contracts"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection"
width="55" />

        <el-table-column prop="contractNumber"
label="合同编号" width="140">
          <template #default="{ row }">
            <el-link type="primary"
@click="viewContract(row)">
              {{ row.contractNumber }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="title"
label="合同标题" min-width="200" show-overflow-tooltip />

        <el-table-column prop="customerName"
label="客户" width="120" />

        <el-table-column prop="type"
label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getContractTypeColor(row.type)"
size="small">
              {{ formatContractType(row.type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status"
label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)"
size="small">
              {{ formatStatus(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="totalValue"
label="合同金额" width="120" align="right">
          <template #default="{ row }">
            <span class="amount">
              {{ formatAmount(row.totalValue, row.currency) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="effectiveDate"
label="生效日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.effectiveDate) }}
          </template>
        </el-table-column>

        <el-table-column prop="expiryDate"
label="到期日期" width="120">
          <template #default="{ row }">
            <span
              :class="{
                'text-warning': isExpiringSoon(row.expiryDate),
                'text-danger': isExpired(row.expiryDate)
              }"
            >
              {{ formatDate(row.expiryDate) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="signatureStatus"
label="签署状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getSignatureStatusColor(row.signatureStatus)"
size="small">
              {{ formatSignatureStatus(row.signatureStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="执行进度"
width="150" v-if="showExecutionProgress">
          <template #default="{ row }">
            <div v-if="row.executionMonitoring">
              <el-progress
                :percentage="row.executionMonitoring.deliveryProgress.actual"
                :stroke-width="8"
                :status="getProgressStatus(row.executionMonitoring.deliveryProgress.onTimeRate)"
              />
              <div class="progress-text">
                {{ row.executionMonitoring.deliveryProgress.actual }}% 完成
              </div>
            </div>
            <span v-else
class="text-muted">--</span>
          </template>
        </el-table-column>

        <el-table-column label="操作"
width="180" fixed="right">
          <template #default="{ row }">
            <el-button-group size="small">
              <el-button type="primary"
link @click="viewContract(row)">
                <el-icon><View /></el-icon>
              </el-button>
              <el-button type="primary" link v-if="canEdit(row)" @click="editContract(row)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-dropdown @command="command => handleAction(command, row)">
                <el-button type="primary"
link>
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="canSign(row)" command="sign">
                      <el-icon><EditPen /></el-icon>
                      签署
                    </el-dropdown-item>
                    <el-dropdown-item v-if="canChange(row)" command="change">
                      <el-icon><Switch /></el-icon>
                      变更
                    </el-dropdown-item>
                    <el-dropdown-item v-if="canMonitor(row)" command="monitor">
                      <el-icon><Monitor /></el-icon>
                      监控
                    </el-dropdown-item>
                    <el-dropdown-item v-if="canTerminate(row)" command="terminate">
                      <el-icon><CircleClose /></el-icon>
                      终止
                    </el-dropdown-item>
                    <el-dropdown-item command="download">
                      <el-icon><Download /></el-icon>
                      下载
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 卡片视图 -->
      <div v-else
class="contract-cards">
        <div v-for="contract in contracts"
:key="contract.id" class="contract-card">
          <div class="card-header">
            <div class="contract-info">
              <h3 class="contract-title">
                {{ contract.title }}
              </h3>
              <p class="contract-number">
                {{ contract.contractNumber }}
              </p>
            </div>
            <div class="contract-status">
              <el-tag :type="getStatusColor(contract.status)">
                {{ formatStatus(contract.status) }}
              </el-tag>
            </div>
          </div>

          <div class="card-content">
            <div class="info-row">
              <span class="label">客户：</span>
              <span class="value">{{ contract.customerName }}</span>
            </div>
            <div class="info-row">
              <span class="label">类型：</span>
              <el-tag :type="getContractTypeColor(contract.type)"
size="small">
                {{ formatContractType(contract.type) }}
              </el-tag>
            </div>
            <div class="info-row">
              <span class="label">金额：</span>
              <span class="amount">{{ formatAmount(contract.totalValue, contract.currency) }}</span>
            </div>
            <div class="info-row">
              <span class="label">有效期：</span>
              <span>
                {{ formatDate(contract.effectiveDate) }} ~ {{ formatDate(contract.expiryDate) }}
              </span>
            </div>
          </div>

          <div class="card-footer">
            <div class="signature-info">
              <el-tag :type="getSignatureStatusColor(contract.signatureStatus)"
size="small">
                {{ formatSignatureStatus(contract.signatureStatus) }}
              </el-tag>
            </div>
            <div class="card-actions">
              <el-button
size="small" @click="viewContract(contract)">查看</el-button>
              <el-button
                v-if="canEdit(contract)"
                size="small"
                type="primary"
                @click="editContract(contract)"
              >
                编辑
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSearch"
          @current-change="handleSearch"
        />
      </div>
    </el-card>

    <!-- 创建合同对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建合同"
      width="1200px"
      :before-close="handleCreateDialogClose"
    >
      <div class="dialog-placeholder">
        <el-result icon="info"
title="功能开发中" sub-title="合同创建功能正在开发中，敬请期待">
          <template #extra>
            <el-button
type="primary" @click="showCreateDialog = false">确定</el-button>
          </template>
        </el-result>
      </div>
    </el-dialog>

    <!-- 合同详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="合同详情"
      width="1400px"
      :before-close="handleDetailDialogClose"
    >
      <div v-if="currentContract" class="dialog-placeholder">
        <el-descriptions title="合同基本信息"
:column="2" border>
          <el-descriptions-item label="合同编号">
            {{ currentContract.contractNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="合同标题">
            {{ currentContract.title }}
          </el-descriptions-item>
          <el-descriptions-item label="客户名称">
            {{ currentContract.customerName }}
          </el-descriptions-item>
          <el-descriptions-item label="合同状态">
            <el-tag :type="getStatusColor(currentContract.status)">
              {{ formatStatus(currentContract.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="合同类型">
            <el-tag :type="getContractTypeColor(currentContract.type)">
              {{ formatContractType(currentContract.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="合同金额">
            {{ formatAmount(currentContract.totalValue, currentContract.currency) }}
          </el-descriptions-item>
          <el-descriptions-item label="生效日期">
            {{ formatDate(currentContract.effectiveDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="到期日期">
            {{ formatDate(currentContract.expiryDate) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 签署对话框 -->
    <el-dialog v-model="showSignDialog" title="合同签署" width="800px">
      <div class="dialog-placeholder">
        <el-result icon="info"
title="功能开发中" sub-title="合同签署功能正在开发中，敬请期待">
          <template #extra>
            <el-button
type="primary" @click="showSignDialog = false">确定</el-button>
          </template>
        </el-result>
      </div>
    </el-dialog>

    <!-- 变更管理对话框 -->
    <el-dialog v-model="showChangeDialog" title="合同变更" width="1000px">
      <div class="dialog-placeholder">
        <el-result icon="info"
title="功能开发中" sub-title="合同变更功能正在开发中，敬请期待">
          <template #extra>
            <el-button
type="primary" @click="showChangeDialog = false">确定</el-button>
          </template>
        </el-result>
      </div>
    </el-dialog>

    <!-- 执行监控对话框 -->
    <el-dialog v-model="showMonitorDialog" title="合同执行监控" width="1200px">
      <div v-if="currentContract && currentContract.executionMonitoring" class="dialog-placeholder">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card title="交付进度">
              <el-progress
                :percentage="currentContract.executionMonitoring.deliveryProgress.actual"
                :status="
                  getProgressStatus(currentContract.executionMonitoring.deliveryProgress.onTimeRate)
                "
              />
              <p>计划进度: {{ currentContract.executionMonitoring.deliveryProgress.planned }}%</p>
              <p>实际进度: {{ currentContract.executionMonitoring.deliveryProgress.actual }}%</p>
              <p>准时率: {{ currentContract.executionMonitoring.deliveryProgress.onTimeRate }}%</p>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card title="付款进度">
              <p>
                合同总金额:
                {{
                  formatAmount(
                    currentContract.executionMonitoring.paymentProgress.totalAmount,
                    currentContract.currency
                  )
                }}
              </p>
              <p>
                已付金额:
                {{
                  formatAmount(
                    currentContract.executionMonitoring.paymentProgress.paidAmount,
                    currentContract.currency
                  )
                }}
              </p>
              <p>
                待付金额:
                {{
                  formatAmount(
                    currentContract.executionMonitoring.paymentProgress.pendingAmount,
                    currentContract.currency
                  )
                }}
              </p>
              <p>
                逾期金额:
                {{
                  formatAmount(
                    currentContract.executionMonitoring.paymentProgress.overdueAmount,
                    currentContract.currency
                  )
                }}
              </p>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card title="质量指标">
              <p>良率: {{ currentContract.executionMonitoring.qualityMetrics.yieldRate }}%</p>
              <p>缺陷率: {{ currentContract.executionMonitoring.qualityMetrics.defectRate }}%</p>
              <p>
                客户投诉:
                {{ currentContract.executionMonitoring.qualityMetrics.customerComplaints }}次
              </p>
              <p>退货率: {{ currentContract.executionMonitoring.qualityMetrics.returnRate }}%</p>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 模板管理对话框 -->
    <el-dialog v-model="showTemplateDialog" title="合同模板管理" width="1000px">
      <div class="dialog-placeholder">
        <el-result icon="info"
title="功能开发中" sub-title="合同模板管理功能正在开发中，敬请期待">
          <template #extra>
            <el-button
type="primary" @click="showTemplateDialog = false">确定</el-button>
          </template>
        </el-result>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import {
    Document,
    Plus,
    Collection,
    Download,
    Search,
    Grid,
    Menu,
    CircleCheck,
    Warning,
    Money,
    View,
    Edit,
    MoreFilled,
    EditPen,
    Switch,
    Monitor,
    CircleClose
  } from '@element-plus/icons-vue'

  import {
    Contract,
    ContractStatus,
    ContractType,
    SignatureStatus,
    ContractQueryParams,
    ContractStatistics
  } from '@/types/contract'

  // 导入模拟数据
  import { mockContracts, mockContractStatistics } from '@/utils/mockData/contract'

  // 组件将在后续开发阶段逐步实现

  // 响应式数据
  const loading = ref(false)
  const viewMode = ref<'table' | 'card'>('table')
  const showExecutionProgress = ref(true)

  // 对话框状态
  const showCreateDialog = ref(false)
  const showDetailDialog = ref(false)
  const showSignDialog = ref(false)
  const showChangeDialog = ref(false)
  const showMonitorDialog = ref(false)
  const showTemplateDialog = ref(false)

  // 当前操作的合同
  const currentContract = ref<Contract | null>(null)

  // 列表数据
  const contracts = ref<Contract[]>([])
  const total = ref(0)
  const statistics = ref<ContractStatistics>(mockContractStatistics)
  const selectedContracts = ref<Contract[]>([])

  // 搜索参数
  const queryParams = reactive<ContractQueryParams>({
    keyword: '',
    status: undefined,
    type: undefined,
    page: 1,
    pageSize: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })

  const dateRange = ref<[Date, Date] | null>(null)

  // 计算属性
  const hasSelection = computed(() => selectedContracts.value.length > 0)

  // 状态格式化函数
  const formatStatus = (status: ContractStatus): string => {
    const statusMap = {
      [ContractStatus.DRAFT]: '草稿',
      [ContractStatus.PENDING_REVIEW]: '待审核',
      [ContractStatus.PENDING_SIGN]: '待签署',
      [ContractStatus.EXECUTING]: '执行中',
      [ContractStatus.COMPLETED]: '已完成',
      [ContractStatus.TERMINATED]: '已终止',
      [ContractStatus.EXPIRED]: '已过期'
    }
    return statusMap[status] || status
  }

  const formatContractType = (type: ContractType): string => {
    const typeMap = {
      [ContractType.MASS_PRODUCTION]: '量产合同',
      [ContractType.NPI_DEVELOPMENT]: 'NPI开发',
      [ContractType.FRAMEWORK]: '框架合同',
      [ContractType.URGENT_ORDER]: '紧急订单',
      [ContractType.CUSTOM_SERVICE]: '定制服务'
    }
    return typeMap[type] || type
  }

  const formatSignatureStatus = (status: SignatureStatus): string => {
    const statusMap = {
      [SignatureStatus.UNSIGNED]: '未签署',
      [SignatureStatus.PARTIAL]: '部分签署',
      [SignatureStatus.COMPLETED]: '已签署',
      [SignatureStatus.REJECTED]: '签署拒绝'
    }
    return statusMap[status] || status
  }

  // 颜色函数
  const getStatusColor = (status: ContractStatus): string => {
    const colorMap = {
      [ContractStatus.DRAFT]: 'info',
      [ContractStatus.PENDING_REVIEW]: 'warning',
      [ContractStatus.PENDING_SIGN]: 'primary',
      [ContractStatus.EXECUTING]: 'success',
      [ContractStatus.COMPLETED]: '',
      [ContractStatus.TERMINATED]: 'danger',
      [ContractStatus.EXPIRED]: 'danger'
    }
    return colorMap[status] || ''
  }

  const getContractTypeColor = (type: ContractType): string => {
    const colorMap = {
      [ContractType.MASS_PRODUCTION]: 'success',
      [ContractType.NPI_DEVELOPMENT]: 'primary',
      [ContractType.FRAMEWORK]: 'warning',
      [ContractType.URGENT_ORDER]: 'danger',
      [ContractType.CUSTOM_SERVICE]: 'info'
    }
    return colorMap[type] || ''
  }

  const getSignatureStatusColor = (status: SignatureStatus): string => {
    const colorMap = {
      [SignatureStatus.UNSIGNED]: 'info',
      [SignatureStatus.PARTIAL]: 'warning',
      [SignatureStatus.COMPLETED]: 'success',
      [SignatureStatus.REJECTED]: 'danger'
    }
    return colorMap[status] || ''
  }

  const getProgressStatus = (onTimeRate: number): string => {
    if (onTimeRate >= 95) return 'success'
    if (onTimeRate >= 80) return 'warning'
    return 'exception'
  }

  // 格式化函数
  const formatDate = (date: Date | string): string => {
    if (!date) return '--'
    const d = new Date(date)
    return d.toLocaleDateString('zh-CN')
  }

  const formatAmount = (amount: number, currency: string): string => {
    if (currency === 'CNY') {
      return `¥${(amount / 10000).toFixed(1)}万`
    } else if (currency === 'USD') {
      return `$${(amount / 10000).toFixed(1)}万`
    }
    return `${amount.toLocaleString()} ${currency}`
  }

  // 日期判断函数
  const isExpiringSoon = (date: Date | string): boolean => {
    const expiryDate = new Date(date)
    const now = new Date()
    const diffTime = expiryDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays <= 30 && diffDays > 0
  }

  const isExpired = (date: Date | string): boolean => {
    const expiryDate = new Date(date)
    const now = new Date()
    return expiryDate < now
  }

  // 权限判断函数
  const canEdit = (contract: Contract): boolean => {
    return [ContractStatus.DRAFT, ContractStatus.PENDING_REVIEW].includes(contract.status)
  }

  const canSign = (contract: Contract): boolean => {
    return contract.status === ContractStatus.PENDING_SIGN
  }

  const canChange = (contract: Contract): boolean => {
    return contract.status === ContractStatus.EXECUTING
  }

  const canMonitor = (contract: Contract): boolean => {
    return contract.status === ContractStatus.EXECUTING
  }

  const canTerminate = (contract: Contract): boolean => {
    return [ContractStatus.EXECUTING, ContractStatus.PENDING_SIGN].includes(contract.status)
  }

  // 数据加载
  const loadContracts = async () => {
    loading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))

      let filteredContracts = [...mockContracts]

      // 应用搜索过滤
      if (queryParams.keyword) {
        const keyword = queryParams.keyword.toLowerCase()
        filteredContracts = filteredContracts.filter(
          contract =>
            contract.contractNumber.toLowerCase().includes(keyword) ||
            contract.title.toLowerCase().includes(keyword) ||
            contract.customerName.toLowerCase().includes(keyword)
        )
      }

      if (queryParams.status) {
        filteredContracts = filteredContracts.filter(
          contract => contract.status === queryParams.status
        )
      }

      if (queryParams.type) {
        filteredContracts = filteredContracts.filter(contract => contract.type === queryParams.type)
      }

      if (dateRange.value) {
        const [startDate, endDate] = dateRange.value
        filteredContracts = filteredContracts.filter(contract => {
          const effectiveDate = new Date(contract.effectiveDate)
          return effectiveDate >= startDate && effectiveDate <= endDate
        })
      }

      total.value = filteredContracts.length

      // 分页
      const start = (queryParams.page - 1) * queryParams.pageSize
      const end = start + queryParams.pageSize
      contracts.value = filteredContracts.slice(start, end)
    } catch (error) {
      console.error('加载合同数据失败:', error)
      ElMessage.error('加载合同数据失败')
    } finally {
      loading.value = false
    }
  }

  // 事件处理函数
  const handleSearch = () => {
    queryParams.page = 1
    loadContracts()
  }

  const handleSelectionChange = (selection: Contract[]) => {
    selectedContracts.value = selection
  }

  const handleCreateDialogClose = () => {
    showCreateDialog.value = false
  }

  const handleCreateSuccess = () => {
    showCreateDialog.value = false
    loadContracts()
    ElMessage.success('合同创建成功')
  }

  const handleDetailDialogClose = () => {
    showDetailDialog.value = false
    currentContract.value = null
  }

  const handleContractUpdate = () => {
    loadContracts()
    ElMessage.success('合同更新成功')
  }

  const handleSignSuccess = () => {
    showSignDialog.value = false
    loadContracts()
    ElMessage.success('合同签署成功')
  }

  const handleChangeSuccess = () => {
    showChangeDialog.value = false
    loadContracts()
    ElMessage.success('合同变更成功')
  }

  // 操作函数
  const viewContract = (contract: Contract) => {
    currentContract.value = contract
    showDetailDialog.value = true
  }

  const editContract = (contract: Contract) => {
    if (!canEdit(contract)) {
      ElMessage.warning('当前合同状态不允许编辑')
      return
    }
    currentContract.value = contract
    showCreateDialog.value = true
  }

  const handleAction = (command: string, contract: Contract) => {
    currentContract.value = contract

    switch (command) {
      case 'sign':
        if (canSign(contract)) {
          showSignDialog.value = true
        } else {
          ElMessage.warning('当前合同状态不允许签署')
        }
        break

      case 'change':
        if (canChange(contract)) {
          showChangeDialog.value = true
        } else {
          ElMessage.warning('当前合同状态不允许变更')
        }
        break

      case 'monitor':
        if (canMonitor(contract)) {
          showMonitorDialog.value = true
        } else {
          ElMessage.warning('当前合同状态无法监控')
        }
        break

      case 'terminate':
        handleTerminateContract(contract)
        break

      case 'download':
        handleDownloadContract(contract)
        break
    }
  }

  const handleTerminateContract = async (contract: Contract) => {
    try {
      await ElMessageBox.confirm(
        `确定要终止合同 "${contract.contractNumber}" 吗？此操作不可撤销。`,
        '终止合同',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      ElMessage.success('合同终止成功')
      loadContracts()
    } catch (error) {
      // 用户取消操作
    }
  }

  const handleDownloadContract = (contract: Contract) => {
    // 模拟下载
    ElMessage.success(`正在下载合同 ${contract.contractNumber}`)
  }

  const exportContracts = () => {
    if (selectedContracts.value.length === 0) {
      ElMessage.warning('请先选择要导出的合同')
      return
    }

    // 模拟导出
    ElMessage.success(`正在导出 ${selectedContracts.value.length} 个合同`)
  }

  // 生命周期
  onMounted(() => {
    loadContracts()
  })

  // 导出常量供模板使用
  defineExpose({
    ContractStatus,
    ContractType,
    SignatureStatus
  })
</script>

<style lang="scss" scoped>
  .contract-management {
    min-height: calc(100vh - var(--navbar-height));
    padding: var(--spacing-6);
    background-color: var(--color-bg-secondary);

    .page-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: var(--spacing-6);

      .header-left {
        .page-title {
          display: flex;
          gap: var(--spacing-2);
          align-items: center;
          margin: 0;
          font-size: var(--font-size-xl);
          color: var(--color-text-primary);

          .el-icon {
            font-size: var(--font-size-lg);
            color: var(--color-primary);
          }
        }

        .page-subtitle {
          margin: var(--spacing-1) 0 0;
          font-size: var(--font-size-sm);
          color: var(--color-text-secondary);
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-3);
      }
    }

    // 统计卡片样式
    .statistics-cards {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-6);

      .stat-card {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: var(--spacing-5);
        background: var(--color-bg-primary);
        border: 1px solid var(--color-border-light);
        border-radius: var(--radius-lg);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: var(--shadow-md);
          transform: translateY(-2px);
        }

        .stat-content {
          .stat-number {
            margin-bottom: var(--spacing-1);
            font-size: var(--font-size-xxl);
            font-weight: 600;
            color: var(--color-text-primary);
          }

          .stat-label {
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
          }
        }

        .stat-icon {
          font-size: var(--font-size-xxl);
          opacity: 0.6;

          &.total {
            color: var(--color-primary);
          }

          &.executing {
            color: var(--color-success);
          }

          &.warning {
            color: var(--color-warning);
          }

          &.value {
            color: var(--color-info);
          }
        }
      }
    }

    // 搜索卡片样式
    .search-card {
      margin-bottom: var(--spacing-6);

      .search-form {
        padding: var(--spacing-4) 0;
      }
    }

    // 表格卡片样式
    .table-card {
      .table-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .header-tools {
          display: flex;
          gap: var(--spacing-3);
          align-items: center;
        }
      }

      :deep(.el-table) {
        .amount {
          font-weight: 500;
          color: var(--color-primary);
        }

        .text-warning {
          color: var(--color-warning);
        }

        .text-danger {
          color: var(--color-danger);
        }

        .text-muted {
          color: var(--color-text-placeholder);
        }

        .progress-text {
          margin-top: var(--spacing-1);
          font-size: var(--font-size-xs);
          color: var(--color-text-secondary);
          text-align: center;
        }
      }
    }

    // 卡片视图样式
    .contract-cards {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: var(--spacing-4);

      .contract-card {
        padding: var(--spacing-5);
        background: var(--color-bg-primary);
        border: 1px solid var(--color-border-light);
        border-radius: var(--radius-lg);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: var(--shadow-md);
          transform: translateY(-2px);
        }

        .card-header {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          margin-bottom: var(--spacing-4);

          .contract-info {
            .contract-title {
              margin: 0 0 var(--spacing-1);
              font-size: var(--font-size-lg);
              font-weight: 500;
              color: var(--color-text-primary);
            }

            .contract-number {
              margin: 0;
              font-size: var(--font-size-sm);
              color: var(--color-text-secondary);
            }
          }
        }

        .card-content {
          .info-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-3);

            .label {
              min-width: 60px;
              font-size: var(--font-size-sm);
              color: var(--color-text-secondary);
            }

            .value {
              flex: 1;
              color: var(--color-text-primary);
              text-align: right;
            }

            .amount {
              font-weight: 500;
              color: var(--color-primary);
            }
          }
        }

        .card-footer {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-top: var(--spacing-4);
          margin-top: var(--spacing-4);
          border-top: 1px solid var(--color-border-lighter);

          .card-actions {
            display: flex;
            gap: var(--spacing-2);
          }
        }
      }
    }

    // 分页样式
    .pagination-wrapper {
      display: flex;
      justify-content: center;
      padding-top: var(--spacing-4);
      margin-top: var(--spacing-6);
      border-top: 1px solid var(--color-border-lighter);
    }

    // 对话框样式
    .dialog-placeholder {
      padding: var(--spacing-4);

      :deep(.el-descriptions) {
        .el-descriptions__label {
          font-weight: 500;
          color: var(--color-text-secondary);
        }

        .el-descriptions__content {
          color: var(--color-text-primary);
        }
      }

      :deep(.el-card) {
        .el-card__header {
          font-weight: 500;
          color: var(--color-text-primary);
        }

        .el-card__body {
          p {
            margin: var(--spacing-2) 0;
            color: var(--color-text-secondary);

            &:first-child {
              margin-top: 0;
            }

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    // 响应式样式
    @media (width <= 768px) {
      padding: var(--spacing-4);

      .statistics-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-3);
      }

      .page-header {
        flex-direction: column;
        gap: var(--spacing-4);

        .header-actions {
          justify-content: flex-start;
          width: 100%;
        }
      }

      .contract-cards {
        grid-template-columns: 1fr;
      }
    }
  }
</style>
