<template>
  <div class="quality-analytics">
    <div class="quality-analytics__header">
      <div class="header-left">
        <h2>质量分析报告</h2>
        <p>Quality Analytics Dashboard</p>
      </div>
      <div class="header-right">
        <el-date-picker
          v-model="dateRange"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          @change="handleDateRangeChange"
        />
        <el-button type="primary" @click="refreshAnalytics" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="exportReport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- KPI 仪表盘 -->
    <div class="analytics-section">
      <h3>质量KPI仪表盘</h3>
      <div class="kpi-dashboard">
        <div class="kpi-cards">
          <div
v-for="kpi in kpiData" class="kpi-card"
:key="kpi.title"
>
            <div class="kpi-icon" :class="kpi.color">
              <el-icon><component :is="kpi.icon" /></el-icon>
            </div>
            <div class="kpi-content">
              <div class="kpi-title">
                {{ kpi.title }}
              </div>
              <div class="kpi-value">
                {{ kpi.value }}
              </div>
              <div class="kpi-change" :class="kpi.trend">
                <el-icon>
                  <component
                    :is="
                      kpi.trend === 'UP' ? 'ArrowUp' : kpi.trend === 'DOWN' ? 'ArrowDown' : 'Minus'
                    "
                  />
                </el-icon>
                {{ kpi.change }}
              </div>
            </div>
          </div>
        </div>

        <!-- KPI 趋势图表 -->
        <div class="kpi-charts">
          <div class="chart-container">
            <h4>整体良率趋势</h4>
            <div
ref="yieldTrendChart" class="chart" />
          </div>
          <div class="chart-container">
            <h4>过程能力指数(Cpk)分布</h4>
            <div
ref="cpkDistChart" class="chart" />
          </div>
        </div>
      </div>
    </div>

    <!-- 良率分析 -->
    <div class="analytics-section">
      <h3>良率趋势分析</h3>
      <div class="yield-analysis">
        <div class="analysis-controls">
          <el-tabs v-model="yieldAnalysisTab" @tab-change="handleYieldTabChange">
            <el-tab-pane
label="工艺良率" name="process" />
            <el-tab-pane
label="产品良率" name="product" />
            <el-tab-pane
label="设备良率" name="equipment" />
            <el-tab-pane
label="良率对比" name="comparison" />
          </el-tabs>

          <div class="filter-controls">
            <el-select v-model="yieldFilter.process" placeholder="选择工艺" clearable>
              <el-option
                v-for="process in processOptions"
                :key="process.value"
                :label="process.label"
                :value="process.value"
              />
            </el-select>
            <el-select v-model="yieldFilter.product" placeholder="选择产品" clearable>
              <el-option
                v-for="product in productOptions"
                :key="product.value"
                :label="product.label"
                :value="product.value"
              />
            </el-select>
          </div>
        </div>

        <div class="yield-charts">
          <div class="main-chart">
            <div
ref="yieldAnalysisChart" class="chart" />
          </div>
          <div class="yield-summary">
            <div class="summary-card">
              <div class="summary-title">当前良率</div>
              <div class="summary-value">{{ yieldSummary.current }}%</div>
            </div>
            <div class="summary-card">
              <div class="summary-title">月平均良率</div>
              <div class="summary-value">{{ yieldSummary.average }}%</div>
            </div>
            <div class="summary-card">
              <div class="summary-title">良率趋势</div>
              <div class="summary-value" :class="yieldSummary.trend">
                {{ yieldSummary.trendValue }}%
              </div>
            </div>
            <div class="summary-card">
              <div class="summary-title">目标良率</div>
              <div class="summary-value">{{ yieldSummary.target }}%</div>
            </div>
          </div>
        </div>

        <!-- 良率详细分析表格 -->
        <div class="yield-details">
          <el-table :data="yieldDetails" style="width: 100%" stripe>
            <el-table-column prop="process" label="工艺" width="120" />
            <el-table-column prop="product" label="产品" width="150" />
            <el-table-column prop="currentYield" label="当前良率(%)" width="120">
              <template #default="scope">
                <span :class="getYieldClass(scope.row.currentYield)">
                  {{ scope.row.currentYield }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="targetYield" label="目标良率(%)" width="120" />
            <el-table-column prop="trend" label="趋势" width="100">
              <template #default="scope">
                <el-tag
                  :type="
                    scope.row.trend === 'up'
                      ? 'success'
                      : scope.row.trend === 'down'
                        ? 'danger'
                        : 'info'
                  "
                >
                  <el-icon>
                    <component
                      :is="
                        scope.row.trend === 'up'
                          ? 'ArrowUp'
                          : scope.row.trend === 'down'
                            ? 'ArrowDown'
                            : 'Minus'
                      "
                    />
                  </el-icon>
                  {{ scope.row.trendValue }}%
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="defectRate" label="缺陷率(%)" width="120" />
            <el-table-column prop="reworkRate" label="返工率(%)" width="120" />
            <el-table-column label="操作" fixed="right">
              <template #default="scope">
                <el-button
type="text" @click="viewYieldDetail(scope.row)">详情</el-button>
                <el-button
type="text" @click="analyzeRootCause(scope.row)">根因分析</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 客户投诉分析 -->
    <div class="analytics-section">
      <h3>客户投诉分析</h3>
      <div class="complaint-analysis">
        <div class="complaint-summary">
          <div class="summary-metrics">
            <div class="metric-item">
              <div class="metric-title">投诉总数</div>
              <div class="metric-value">
                {{ complaintSummary.total }}
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-title">未关闭投诉</div>
              <div class="metric-value warning">
                {{ complaintSummary.open }}
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-title">平均响应时间</div>
              <div class="metric-value">{{ complaintSummary.avgResponse }}h</div>
            </div>
            <div class="metric-item">
              <div class="metric-title">客户满意度</div>
              <div class="metric-value success">{{ complaintSummary.satisfaction }}%</div>
            </div>
          </div>

          <div class="complaint-charts">
            <div class="chart-item">
              <h4>投诉类型分布</h4>
              <div
ref="complaintTypeChart" class="chart-small" />
            </div>
            <div class="chart-item">
              <h4>投诉趋势分析</h4>
              <div
ref="complaintTrendChart" class="chart-small" />
            </div>
            <div class="chart-item">
              <h4>根因分类</h4>
              <div
ref="rootCauseChart" class="chart-small" />
            </div>
          </div>
        </div>

        <!-- 投诉详细列表 -->
        <div class="complaint-list">
          <div class="list-header">
            <h4>投诉处理跟踪</h4>
            <div class="list-filters">
              <el-select v-model="complaintFilter.status" placeholder="状态">
                <el-option label="全部" value="" />
                <el-option label="待处理" value="pending" />
                <el-option label="处理中" value="processing" />
                <el-option label="已关闭" value="closed" />
              </el-select>
              <el-select v-model="complaintFilter.severity" placeholder="严重程度">
                <el-option label="全部" value="" />
                <el-option label="紧急" value="urgent" />
                <el-option label="高" value="high" />
                <el-option label="中" value="medium" />
                <el-option label="低" value="low" />
              </el-select>
            </div>
          </div>

          <el-table :data="filteredComplaints" style="width: 100%" stripe>
            <el-table-column prop="id" label="投诉ID" width="120" />
            <el-table-column prop="customer" label="客户" width="150" />
            <el-table-column prop="product" label="产品" width="150" />
            <el-table-column prop="type" label="投诉类型" width="120">
              <template #default="scope">
                <el-tag :type="getComplaintTypeColor(scope.row.type)">
                  {{ scope.row.type }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="severity" label="严重程度" width="120">
              <template #default="scope">
                <el-tag :type="getSeverityColor(scope.row.severity)">
                  {{ scope.row.severity }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusColor(scope.row.status)">
                  {{ scope.row.statusText }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createDate" label="创建时间" width="120" />
            <el-table-column prop="responseTime" label="响应时间(h)" width="120" />
            <el-table-column label="操作" fixed="right" width="150">
              <template #default="scope">
                <el-button
type="text" @click="viewComplaintDetail(scope.row)">详情</el-button>
                <el-button
type="text" @click="handleComplaint(scope.row)">处理</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 质量成本分析 -->
    <div class="analytics-section">
      <h3>质量成本分析</h3>
      <div class="cost-analysis">
        <div class="cost-overview">
          <div class="cost-categories">
            <div
v-for="category in costCategories" class="cost-category"
:key="category.name"
>
              <div class="category-header">
                <h4>{{ category.name }}</h4>
                <span class="cost-amount">¥{{ formatCost(category.amount) }}</span>
              </div>
              <div class="category-items">
                <div v-for="item in category.items"
class="cost-item" :key="item.name">
                  <span class="item-name">{{ item.name }}</span>
                  <span class="item-amount">¥{{ formatCost(item.amount) }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="cost-charts">
            <div class="chart-container">
              <h4>质量成本构成</h4>
              <div
ref="costStructureChart" class="chart" />
            </div>
            <div class="chart-container">
              <h4>质量成本趋势</h4>
              <div
ref="costTrendChart" class="chart" />
            </div>
          </div>
        </div>

        <!-- 成本绩效指标 -->
        <div class="cost-metrics">
          <h4>质量成本绩效指标</h4>
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-title">总质量成本</div>
              <div class="metric-value">¥{{ formatCost(costMetrics.total) }}</div>
              <div class="metric-desc">占营收比例: {{ costMetrics.revenueRatio }}%</div>
            </div>
            <div class="metric-card">
              <div class="metric-title">预防成本</div>
              <div class="metric-value">¥{{ formatCost(costMetrics.prevention) }}</div>
              <div class="metric-desc">占比: {{ costMetrics.preventionRatio }}%</div>
            </div>
            <div class="metric-card">
              <div class="metric-title">评价成本</div>
              <div class="metric-value">¥{{ formatCost(costMetrics.appraisal) }}</div>
              <div class="metric-desc">占比: {{ costMetrics.appraisalRatio }}%</div>
            </div>
            <div class="metric-card">
              <div class="metric-title">内部失败成本</div>
              <div class="metric-value">¥{{ formatCost(costMetrics.internalFailure) }}</div>
              <div class="metric-desc">占比: {{ costMetrics.internalRatio }}%</div>
            </div>
            <div class="metric-card">
              <div class="metric-title">外部失败成本</div>
              <div class="metric-value">¥{{ formatCost(costMetrics.externalFailure) }}</div>
              <div class="metric-desc">占比: {{ costMetrics.externalRatio }}%</div>
            </div>
            <div class="metric-card">
              <div class="metric-title">质量成本率</div>
              <div class="metric-value">{{ costMetrics.qualityRate }}%</div>
              <div class="metric-desc">目标: &lt; 5%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 投诉详情对话框 -->
    <el-dialog
v-model="complaintDetailVisible" title="投诉详情"
width="800px"
>
      <div
v-if="selectedComplaint" class="complaint-detail"
>
        <div class="detail-section">
          <h4>基本信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="投诉ID">
              {{ selectedComplaint.id }}
            </el-descriptions-item>
            <el-descriptions-item label="客户">
              {{ selectedComplaint.customer }}
            </el-descriptions-item>
            <el-descriptions-item label="产品">
              {{ selectedComplaint.product }}
            </el-descriptions-item>
            <el-descriptions-item label="投诉类型">
              {{ selectedComplaint.type }}
            </el-descriptions-item>
            <el-descriptions-item label="严重程度">
              <el-tag :type="getSeverityColor(selectedComplaint.severity)">
                {{ selectedComplaint.severity }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusColor(selectedComplaint.status)">
                {{ selectedComplaint.statusText }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ selectedComplaint.createDate }}
            </el-descriptions-item>
            <el-descriptions-item label="响应时间">
              {{ selectedComplaint.responseTime }}小时
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <h4>投诉描述</h4>
          <p>{{ selectedComplaint.description }}</p>
        </div>

        <div class="detail-section">
          <h4>处理进展</h4>
          <el-timeline>
            <el-timeline-item
              v-for="step in selectedComplaint.timeline"
              :key="step.id"
              :timestamp="step.timestamp"
              :type="step.type"
            >
              <strong>{{ step.title }}</strong>
              <p>{{ step.description }}</p>
              <p>
                <small>负责人: {{ step.handler }}</small>
              </p>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, nextTick } from 'vue'
  import * as echarts from 'echarts'
  import {
    Refresh,
    Download,
    TrendCharts,
    DataLine,
    PieChart,
    Warning,
    ArrowUp,
    ArrowDown,
    Minus
  } from '@element-plus/icons-vue'
  import { qualityAnalyticsData, qualityKPIs } from '@/utils/mockData/quality'
  import type { QualityKPI, YieldAnalysis, ComplaintRecord, QualityCost } from '@/types/quality'

  // 响应式数据
  const loading = ref(false)
  const dateRange = ref<[Date, Date]>([
    new Date(new Date().getFullYear(), new Date().getMonth() - 5, 1),
    new Date()
  ])

  // KPI数据
  const kpiData = ref<QualityKPI[]>([
    {
      title: '整体良率',
      value: '95.8%',
      change: '+2.3%',
      trend: 'UP',
      icon: 'TrendCharts',
      color: 'success'
    },
    {
      title: 'Cpk指数',
      value: '1.67',
      change: '+0.12',
      trend: 'UP',
      icon: 'DataLine',
      color: 'primary'
    },
    {
      title: '客户投诉',
      value: '3',
      change: '-2',
      trend: 'DOWN',
      icon: 'Warning',
      color: 'warning'
    },
    {
      title: '质量成本率',
      value: '3.2%',
      change: '-0.5%',
      trend: 'DOWN',
      icon: 'PieChart',
      color: 'info'
    }
  ])

  // 良率分析
  const yieldAnalysisTab = ref('process')
  const yieldFilter = reactive({
    process: '',
    product: ''
  })

  const processOptions = [
    { label: 'CP测试', value: 'cp_test' },
    { label: '贴片', value: 'die_attach' },
    { label: '线键合', value: 'wire_bond' },
    { label: '塑封', value: 'molding' },
    { label: 'FT测试', value: 'ft_test' }
  ]

  const productOptions = [
    { label: 'MCU-ARM-001', value: 'MCU-ARM-001' },
    { label: 'MCU-ARM-002', value: 'MCU-ARM-002' },
    { label: 'SENSOR-IMU-001', value: 'SENSOR-IMU-001' },
    { label: 'POWER-MGMT-001', value: 'POWER-MGMT-001' }
  ]

  const yieldSummary = reactive({
    current: 95.8,
    average: 94.2,
    trend: 'up',
    trendValue: 2.3,
    target: 95.0
  })

  const yieldDetails = ref([
    {
      process: 'CP测试',
      product: 'MCU-ARM-001',
      currentYield: 98.2,
      targetYield: 97.0,
      trend: 'UP',
      trendValue: 1.2,
      defectRate: 1.8,
      reworkRate: 0.5
    },
    {
      process: '贴片',
      product: 'MCU-ARM-001',
      currentYield: 99.5,
      targetYield: 99.0,
      trend: 'stable',
      trendValue: 0.1,
      defectRate: 0.5,
      reworkRate: 0.0
    },
    {
      process: '线键合',
      product: 'MCU-ARM-001',
      currentYield: 97.8,
      targetYield: 98.5,
      trend: 'DOWN',
      trendValue: -0.7,
      defectRate: 2.2,
      reworkRate: 1.2
    },
    {
      process: '塑封',
      product: 'MCU-ARM-001',
      currentYield: 95.2,
      targetYield: 96.0,
      trend: 'DOWN',
      trendValue: -0.8,
      defectRate: 4.8,
      reworkRate: 2.1
    },
    {
      process: 'FT测试',
      product: 'MCU-ARM-001',
      currentYield: 94.6,
      targetYield: 95.0,
      trend: 'UP',
      trendValue: 0.4,
      defectRate: 5.4,
      reworkRate: 0.3
    }
  ])

  // 客户投诉分析
  const complaintSummary = reactive({
    total: 15,
    open: 3,
    avgResponse: 4.2,
    satisfaction: 92
  })

  const complaintFilter = reactive({
    status: '',
    severity: ''
  })

  const complaints = ref<ComplaintRecord[]>([
    {
      id: 'COMP-2024-001',
      customer: '华为技术有限公司',
      product: 'MCU-ARM-001',
      type: '功能缺陷',
      severity: 'high',
      status: 'processing',
      statusText: '处理中',
      createDate: '2024-01-15',
      responseTime: 2.5,
      description: '批次号WAF240115001的MCU芯片在高温测试中出现功能异常，影响客户生产计划。',
      timeline: [
        {
          id: 1,
          timestamp: '2024-01-15 09:30',
          title: '收到客户投诉',
          description: '客户反馈产品功能异常，已记录相关信息',
          handler: '张三',
          type: 'primary'
        },
        {
          id: 2,
          timestamp: '2024-01-15 11:45',
          title: '启动调查程序',
          description: '组建调查小组，开始根因分析',
          handler: '李四',
          type: 'success'
        },
        {
          id: 3,
          timestamp: '2024-01-16 14:20',
          title: '初步调查结果',
          description: '发现测试程序参数设置异常',
          handler: '王五',
          type: 'warning'
        }
      ]
    },
    {
      id: 'COMP-2024-002',
      customer: '比亚迪股份有限公司',
      product: 'SENSOR-IMU-001',
      type: '质量问题',
      severity: 'medium',
      status: 'closed',
      statusText: '已关闭',
      createDate: '2024-01-10',
      responseTime: 6.0,
      description: '传感器精度偏差超出规格要求',
      timeline: []
    }
  ])

  const filteredComplaints = computed(() => {
    return complaints.value.filter(complaint => {
      if (complaintFilter.status && complaint.status !== complaintFilter.status) {
        return false
      }
      if (complaintFilter.severity && complaint.severity !== complaintFilter.severity) {
        return false
      }
      return true
    })
  })

  const selectedComplaint = ref<ComplaintRecord | null>(null)
  const complaintDetailVisible = ref(false)

  // 质量成本分析
  const costCategories = ref([
    {
      name: '预防成本',
      amount: 1250000,
      items: [
        { name: '质量培训', amount: 150000 },
        { name: '工艺改进', amount: 300000 },
        { name: '预防维护', amount: 200000 },
        { name: '质量规划', amount: 100000 },
        { name: '供应商评估', amount: 500000 }
      ]
    },
    {
      name: '评价成本',
      amount: 2100000,
      items: [
        { name: '来料检验', amount: 400000 },
        { name: '过程检验', amount: 600000 },
        { name: '最终检验', amount: 500000 },
        { name: '测试设备', amount: 400000 },
        { name: '质量审核', amount: 200000 }
      ]
    },
    {
      name: '内部失败成本',
      amount: 850000,
      items: [
        { name: '返工费用', amount: 300000 },
        { name: '废品损失', amount: 250000 },
        { name: '重新测试', amount: 150000 },
        { name: '故障分析', amount: 100000 },
        { name: '设备停机', amount: 50000 }
      ]
    },
    {
      name: '外部失败成本',
      amount: 420000,
      items: [
        { name: '客户退货', amount: 200000 },
        { name: '保修费用', amount: 120000 },
        { name: '客户投诉处理', amount: 50000 },
        { name: '品牌损失', amount: 50000 }
      ]
    }
  ])

  const costMetrics = reactive({
    total: 4620000,
    prevention: 1250000,
    appraisal: 2100000,
    internalFailure: 850000,
    externalFailure: 420000,
    revenueRatio: 3.2,
    preventionRatio: 27.1,
    appraisalRatio: 45.5,
    internalRatio: 18.4,
    externalRatio: 9.0,
    qualityRate: 3.2
  })

  // 图表引用
  const yieldTrendChart = ref<HTMLElement>()
  const cpkDistChart = ref<HTMLElement>()
  const yieldAnalysisChart = ref<HTMLElement>()
  const complaintTypeChart = ref<HTMLElement>()
  const complaintTrendChart = ref<HTMLElement>()
  const rootCauseChart = ref<HTMLElement>()
  const costStructureChart = ref<HTMLElement>()
  const costTrendChart = ref<HTMLElement>()

  // 方法
  const handleDateRangeChange = () => {
    refreshAnalytics()
  }

  const refreshAnalytics = async () => {
    loading.value = true
    try {
      // 模拟数据刷新
      await new Promise(resolve => setTimeout(resolve, 1000))
      // 重新渲染图表
      await nextTick()
      initCharts()
    } finally {
      loading.value = false
    }
  }

  const exportReport = () => {
    // 导出报告逻辑
    console.log('导出质量分析报告')
  }

  const handleYieldTabChange = (tabName: string) => {
    // 切换良率分析标签页
    console.log('切换到:', tabName)
    updateYieldAnalysisChart()
  }

  const getYieldClass = (yieldValue: number) => {
    if (yieldValue >= 95) return 'yield-excellent'
    if (yieldValue >= 90) return 'yield-good'
    if (yieldValue >= 85) return 'yield-fair'
    return 'yield-poor'
  }

  const viewYieldDetail = (row: any) => {
    console.log('查看良率详情:', row)
  }

  const analyzeRootCause = (row: any) => {
    console.log('根因分析:', row)
  }

  const getComplaintTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      功能缺陷: 'danger',
      质量问题: 'warning',
      外观缺陷: 'info',
      包装问题: 'success'
    }
    return colors[type] || 'info'
  }

  const getSeverityColor = (severity: string) => {
    const colors: Record<string, string> = {
      urgent: 'danger',
      high: 'danger',
      medium: 'warning',
      low: 'success'
    }
    return colors[severity] || 'info'
  }

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      pending: 'warning',
      processing: 'primary',
      closed: 'success'
    }
    return colors[status] || 'info'
  }

  const viewComplaintDetail = (complaint: ComplaintRecord) => {
    selectedComplaint.value = complaint
    complaintDetailVisible.value = true
  }

  const handleComplaint = (complaint: ComplaintRecord) => {
    console.log('处理投诉:', complaint)
  }

  const formatCost = (amount: number) => {
    return (amount / 10000).toFixed(1) + '万'
  }

  // 图表初始化
  const initCharts = () => {
    initYieldTrendChart()
    initCpkDistChart()
    initYieldAnalysisChart()
    initComplaintTypeChart()
    initComplaintTrendChart()
    initRootCauseChart()
    initCostStructureChart()
    initCostTrendChart()
  }

  const initYieldTrendChart = () => {
    if (!yieldTrendChart.value) return

    const chart = echarts.init(yieldTrendChart.value)
    const option = {
      grid: { top: 20, right: 40, bottom: 40, left: 60 },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: {
        type: 'value',
        min: 90,
        max: 100,
        axisLabel: { formatter: '{value}%' }
      },
      series: [
        {
          name: '整体良率',
          type: 'line',
          data: [94.2, 94.8, 95.1, 94.9, 95.3, 95.8],
          smooth: true,
          itemStyle: { color: '#67C23A' },
          areaStyle: { opacity: 0.3 }
        },
        {
          name: '目标良率',
          type: 'line',
          data: [95, 95, 95, 95, 95, 95],
          lineStyle: { type: 'dashed', color: '#E6A23C' },
          itemStyle: { color: '#E6A23C' }
        }
      ],
      legend: { bottom: 0 },
      tooltip: { trigger: 'axis' }
    }
    chart.setOption(option)
  }

  const initCpkDistChart = () => {
    if (!cpkDistChart.value) return

    const chart = echarts.init(cpkDistChart.value)
    const option = {
      grid: { top: 20, right: 40, bottom: 40, left: 60 },
      xAxis: {
        type: 'category',
        data: ['CP测试', '贴片', '线键合', '塑封', 'FT测试']
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 2
      },
      series: [
        {
          name: 'Cpk值',
          type: 'bar',
          data: [1.8, 1.9, 1.5, 1.3, 1.7],
          itemStyle: {
            color: (params: any) => {
              const value = params.value
              if (value >= 1.67) return '#67C23A'
              if (value >= 1.33) return '#E6A23C'
              return '#F56C6C'
            }
          }
        }
      ],
      tooltip: { trigger: 'axis' },
      markLine: {
        data: [
          { yAxis: 1.33, name: '最低要求' },
          { yAxis: 1.67, name: '优秀水平' }
        ]
      }
    }
    chart.setOption(option)
  }

  const initYieldAnalysisChart = () => {
    updateYieldAnalysisChart()
  }

  const updateYieldAnalysisChart = () => {
    if (!yieldAnalysisChart.value) return

    const chart = echarts.init(yieldAnalysisChart.value)
    let option = {}

    switch (yieldAnalysisTab.value) {
      case 'process':
        option = {
          grid: { top: 40, right: 40, bottom: 60, left: 80 },
          xAxis: {
            type: 'category',
            data: ['CP测试', '贴片', '线键合', '塑封', 'FT测试'],
            axisLabel: { rotate: 45 }
          },
          yAxis: {
            type: 'value',
            min: 90,
            max: 100,
            axisLabel: { formatter: '{value}%' }
          },
          series: [
            {
              name: '当前良率',
              type: 'bar',
              data: [98.2, 99.5, 97.8, 95.2, 94.6],
              itemStyle: {
                color: (params: any) => {
                  const value = params.value
                  if (value >= 98) return '#67C23A'
                  if (value >= 95) return '#E6A23C'
                  return '#F56C6C'
                }
              }
            }
          ],
          tooltip: { trigger: 'axis' }
        }
        break
      case 'product':
        option = {
          grid: { top: 40, right: 40, bottom: 60, left: 80 },
          xAxis: {
            type: 'category',
            data: ['MCU-ARM-001', 'MCU-ARM-002', 'SENSOR-IMU-001', 'POWER-MGMT-001']
          },
          yAxis: {
            type: 'value',
            min: 90,
            max: 100,
            axisLabel: { formatter: '{value}%' }
          },
          series: [
            {
              name: '产品良率',
              type: 'line',
              data: [95.8, 94.2, 96.5, 93.8],
              smooth: true,
              itemStyle: { color: '#409EFF' }
            }
          ],
          tooltip: { trigger: 'axis' }
        }
        break
      // 其他标签页类似处理
      default:
        option = {}
    }

    chart.setOption(option)
  }

  const initComplaintTypeChart = () => {
    if (!complaintTypeChart.value) return

    const chart = echarts.init(complaintTypeChart.value)
    const option = {
      series: [
        {
          name: '投诉类型',
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 6, name: '功能缺陷' },
            { value: 4, name: '质量问题' },
            { value: 3, name: '外观缺陷' },
            { value: 2, name: '包装问题' }
          ],
          itemStyle: {
            borderRadius: 4,
            borderColor: '#fff',
            borderWidth: 2
          }
        }
      ],
      tooltip: { trigger: 'item' },
      legend: { bottom: 0, left: 'center' }
    }
    chart.setOption(option)
  }

  const initComplaintTrendChart = () => {
    if (!complaintTrendChart.value) return

    const chart = echarts.init(complaintTrendChart.value)
    const option = {
      grid: { top: 20, right: 40, bottom: 40, left: 40 },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '投诉数量',
          type: 'line',
          data: [8, 6, 4, 5, 3, 3],
          smooth: true,
          itemStyle: { color: '#F56C6C' }
        }
      ],
      tooltip: { trigger: 'axis' }
    }
    chart.setOption(option)
  }

  const initRootCauseChart = () => {
    if (!rootCauseChart.value) return

    const chart = echarts.init(rootCauseChart.value)
    const option = {
      grid: { top: 20, right: 40, bottom: 40, left: 80 },
      xAxis: {
        type: 'value'
      },
      yAxis: {
        type: 'category',
        data: ['工艺问题', '设备故障', '物料缺陷', '操作失误', '设计缺陷']
      },
      series: [
        {
          name: '根因分布',
          type: 'bar',
          data: [5, 3, 4, 2, 1],
          itemStyle: { color: '#909399' }
        }
      ],
      tooltip: { trigger: 'axis' }
    }
    chart.setOption(option)
  }

  const initCostStructureChart = () => {
    if (!costStructureChart.value) return

    const chart = echarts.init(costStructureChart.value)
    const option = {
      series: [
        {
          name: '质量成本构成',
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 1250000, name: '预防成本' },
            { value: 2100000, name: '评价成本' },
            { value: 850000, name: '内部失败成本' },
            { value: 420000, name: '外部失败成本' }
          ],
          itemStyle: {
            borderRadius: 4,
            borderColor: '#fff',
            borderWidth: 2
          }
        }
      ],
      tooltip: {
        trigger: 'item',
        formatter: '{a}<br/>{b}: ¥{c} ({d}%)'
      },
      legend: { bottom: 0, left: 'center' }
    }
    chart.setOption(option)
  }

  const initCostTrendChart = () => {
    if (!costTrendChart.value) return

    const chart = echarts.init(costTrendChart.value)
    const option = {
      grid: { top: 40, right: 40, bottom: 60, left: 80 },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: {
        type: 'value',
        axisLabel: { formatter: '{value}万' }
      },
      series: [
        {
          name: '预防成本',
          type: 'bar',
          stack: 'cost',
          data: [115, 120, 122, 125, 123, 125],
          itemStyle: { color: '#67C23A' }
        },
        {
          name: '评价成本',
          type: 'bar',
          stack: 'cost',
          data: [195, 200, 205, 208, 210, 210],
          itemStyle: { color: '#409EFF' }
        },
        {
          name: '内部失败成本',
          type: 'bar',
          stack: 'cost',
          data: [95, 90, 88, 85, 83, 85],
          itemStyle: { color: '#E6A23C' }
        },
        {
          name: '外部失败成本',
          type: 'bar',
          stack: 'cost',
          data: [50, 45, 43, 42, 40, 42],
          itemStyle: { color: '#F56C6C' }
        }
      ],
      tooltip: { trigger: 'axis' },
      legend: { bottom: 0 }
    }
    chart.setOption(option)
  }

  // 生命周期
  onMounted(() => {
    nextTick(() => {
      initCharts()
    })
  })
</script>

<style lang="scss" scoped>
  .quality-analytics {
    padding: 24px;
    background-color: var(--color-bg-primary);

    &__header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      padding: 20px;
      margin-bottom: 24px;
      background: white;
      border-radius: var(--radius-base);
      box-shadow: var(--shadow-light);

      .header-left {
        h2 {
          margin: 0 0 8px;
          font-size: 24px;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        p {
          margin: 0;
          font-size: 14px;
          color: var(--color-text-secondary);
        }
      }

      .header-right {
        display: flex;
        gap: 12px;
        align-items: center;
      }
    }

    .analytics-section {
      padding: 24px;
      margin-bottom: 24px;
      background: white;
      border-radius: var(--radius-base);
      box-shadow: var(--shadow-light);

      h3 {
        padding-bottom: 8px;
        margin: 0 0 20px;
        font-size: 18px;
        font-weight: 600;
        color: var(--color-text-primary);
        border-bottom: 2px solid var(--color-primary);
      }

      h4 {
        margin: 0 0 16px;
        font-size: 16px;
        font-weight: 500;
        color: var(--color-text-primary);
      }
    }

    // KPI仪表盘样式
    .kpi-dashboard {
      .kpi-cards {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        margin-bottom: 24px;

        .kpi-card {
          display: flex;
          align-items: center;
          padding: 20px;
          background: var(--color-bg-light);
          border-left: 4px solid var(--color-primary);
          border-radius: var(--radius-base);

          .kpi-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            margin-right: 16px;
            font-size: 24px;
            border-radius: 50%;

            &.success {
              color: var(--color-success);
              background: var(--color-success-light);
            }

            &.primary {
              color: var(--color-primary);
              background: var(--color-primary-light);
            }

            &.warning {
              color: var(--color-warning);
              background: var(--color-warning-light);
            }

            &.info {
              color: var(--color-info);
              background: var(--color-info-light);
            }
          }

          .kpi-content {
            .kpi-title {
              margin-bottom: 4px;
              font-size: 14px;
              color: var(--color-text-secondary);
            }

            .kpi-value {
              margin-bottom: 4px;
              font-size: 24px;
              font-weight: 600;
              color: var(--color-text-primary);
            }

            .kpi-change {
              display: flex;
              gap: 4px;
              align-items: center;
              font-size: 12px;

              &.up {
                color: var(--color-success);
              }

              &.down {
                color: var(--color-danger);
              }

              &.stable {
                color: var(--color-info);
              }
            }
          }
        }
      }

      .kpi-charts {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;

        .chart-container {
          .chart {
            height: 300px;
          }
        }
      }
    }

    // 良率分析样式
    .yield-analysis {
      .analysis-controls {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 24px;

        .filter-controls {
          display: flex;
          gap: 12px;
        }
      }

      .yield-charts {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 24px;
        margin-bottom: 24px;

        .main-chart .chart {
          height: 400px;
        }

        .yield-summary {
          display: grid;
          grid-template-columns: 1fr;
          gap: 16px;

          .summary-card {
            padding: 16px;
            text-align: center;
            background: var(--color-bg-light);
            border-radius: var(--radius-base);

            .summary-title {
              margin-bottom: 8px;
              font-size: 14px;
              color: var(--color-text-secondary);
            }

            .summary-value {
              font-size: 20px;
              font-weight: 600;
              color: var(--color-text-primary);

              &.up {
                color: var(--color-success);
              }

              &.down {
                color: var(--color-danger);
              }
            }
          }
        }
      }

      .yield-details {
        .yield-excellent {
          font-weight: 600;
          color: var(--color-success);
        }

        .yield-good {
          font-weight: 600;
          color: var(--color-primary);
        }

        .yield-fair {
          font-weight: 600;
          color: var(--color-warning);
        }

        .yield-poor {
          font-weight: 600;
          color: var(--color-danger);
        }
      }
    }

    // 客户投诉分析样式
    .complaint-analysis {
      .complaint-summary {
        .summary-metrics {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 20px;
          margin-bottom: 24px;

          .metric-item {
            padding: 20px;
            text-align: center;
            background: var(--color-bg-light);
            border-radius: var(--radius-base);

            .metric-title {
              margin-bottom: 8px;
              font-size: 14px;
              color: var(--color-text-secondary);
            }

            .metric-value {
              font-size: 24px;
              font-weight: 600;
              color: var(--color-text-primary);

              &.warning {
                color: var(--color-warning);
              }

              &.success {
                color: var(--color-success);
              }
            }
          }
        }

        .complaint-charts {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 24px;
          margin-bottom: 24px;

          .chart-item {
            .chart-small {
              height: 200px;
            }
          }
        }
      }

      .complaint-list {
        .list-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;

          h4 {
            margin: 0;
          }

          .list-filters {
            display: flex;
            gap: 12px;
          }
        }
      }
    }

    // 质量成本分析样式
    .cost-analysis {
      .cost-overview {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;
        margin-bottom: 24px;

        .cost-categories {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;

          .cost-category {
            padding: 20px;
            background: var(--color-bg-light);
            border-radius: var(--radius-base);

            .category-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding-bottom: 8px;
              margin-bottom: 16px;
              border-bottom: 1px solid var(--color-border-light);

              h4 {
                margin: 0;
                font-size: 16px;
              }

              .cost-amount {
                font-size: 18px;
                font-weight: 600;
                color: var(--color-primary);
              }
            }

            .category-items {
              .cost-item {
                display: flex;
                justify-content: space-between;
                padding: 8px 0;
                font-size: 14px;

                .item-name {
                  color: var(--color-text-secondary);
                }

                .item-amount {
                  font-weight: 500;
                  color: var(--color-text-primary);
                }
              }
            }
          }
        }

        .cost-charts {
          display: grid;
          grid-template-rows: 1fr 1fr;
          gap: 16px;

          .chart-container .chart {
            height: 200px;
          }
        }
      }

      .cost-metrics {
        .metrics-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 20px;

          .metric-card {
            padding: 20px;
            text-align: center;
            background: var(--color-bg-light);
            border-radius: var(--radius-base);

            .metric-title {
              margin-bottom: 8px;
              font-size: 14px;
              color: var(--color-text-secondary);
            }

            .metric-value {
              margin-bottom: 4px;
              font-size: 20px;
              font-weight: 600;
              color: var(--color-text-primary);
            }

            .metric-desc {
              font-size: 12px;
              color: var(--color-text-placeholder);
            }
          }
        }
      }
    }

    // 投诉详情对话框样式
    .complaint-detail {
      .detail-section {
        margin-bottom: 24px;

        h4 {
          margin: 0 0 16px;
          font-size: 16px;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        p {
          margin: 0;
          line-height: 1.6;
          color: var(--color-text-regular);
        }
      }
    }
  }

  // 响应式设计
  @media (width <= 1200px) {
    .quality-analytics {
      .kpi-dashboard .kpi-cards {
        grid-template-columns: repeat(2, 1fr);
      }

      .yield-analysis .yield-charts {
        grid-template-columns: 1fr;
      }

      .cost-analysis .cost-overview {
        grid-template-columns: 1fr;
      }

      .cost-analysis .cost-overview .cost-categories {
        grid-template-columns: 1fr;
      }

      .cost-analysis .cost-metrics .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }

  @media (width <= 768px) {
    .quality-analytics {
      padding: 16px;

      &__header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;

        .header-right {
          flex-wrap: wrap;
          justify-content: flex-start;
        }
      }

      .kpi-dashboard .kpi-cards {
        grid-template-columns: 1fr;
      }

      .complaint-analysis .complaint-summary .summary-metrics {
        grid-template-columns: repeat(2, 1fr);
      }

      .complaint-analysis .complaint-summary .complaint-charts {
        grid-template-columns: 1fr;
      }

      .cost-analysis .cost-metrics .metrics-grid {
        grid-template-columns: 1fr;
      }
    }
  }
</style>
