<template>
  <el-card class="customer-card" shadow="hover" @click="$emit('view', customer)">
    <!-- 卡片头部 -->
    <template #header>
      <div class="card-header">
        <div class="customer-logo">
          <el-avatar 
            :size="50"
            :src="customer.logo"
            :style="{ backgroundColor: getCustomerColor(customer.type) }"
          >
            {{ customer.shortName?.charAt(0) || customer.name.charAt(0) }}
          </el-avatar>
        </div>
        <div class="header-actions">
          <el-button 
            text 
            @click.stop="$emit('favorite', customer)"
            :style="{ color: customer.isFavorite ? '#f56c6c' : '#c0c4cc' }"
          >
            <el-icon>
              <StarFilled v-if="customer.isFavorite" />
              <Star v-else />
            </el-icon>
          </el-button>
          <el-dropdown @command="handleCommand" trigger="click" @click.stop>
            <el-button text>
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-dropdown-item>
                <el-dropdown-item command="duplicate">
                  <el-icon><CopyDocument /></el-icon>
                  复制
                </el-dropdown-item>
                <el-dropdown-item command="delete" divided>
                  <el-icon><Delete /></el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </template>

    <!-- 卡片内容 -->
    <div class="card-content">
      <!-- 客户基本信息 -->
      <div class="customer-basic-info">
        <div class="customer-code">{{ customer.code }}</div>
        <h3 class="customer-name">{{ customer.name }}</h3>
        <div class="customer-english">{{ customer.englishName }}</div>
        <div class="customer-short">{{ customer.shortName }}</div>
      </div>

      <!-- 客户标签 -->
      <div class="customer-tags">
        <el-tag 
          size="small" 
          :type="getCustomerTypeTagType(customer.type)"
        >
          {{ getCustomerTypeLabel(customer.type) }}
        </el-tag>
        <el-tag 
          size="small" 
          :color="getCustomerLevelColor(customer.level)"
          style="color: white;"
        >
          {{ getCustomerLevelLabel(customer.level) }}
        </el-tag>
        <el-tag 
          size="small"
          :type="getStatusTagType(customer.status)"
        >
          {{ getStatusLabel(customer.status) }}
        </el-tag>
      </div>

      <!-- 应用领域 -->
      <div class="application-fields">
        <div class="field-label">主要应用:</div>
        <div class="field-tags">
          <el-tag
            v-for="(field, index) in customer.applicationFields.slice(0, 3)"
            :key="field"
            size="small"
            :type="['success', 'warning', 'info'][index % 3] as any"
          >
            {{ getApplicationFieldLabel(field) }}
          </el-tag>
          <span v-if="customer.applicationFields.length > 3" class="more-fields">
            +{{ customer.applicationFields.length - 3 }}
          </span>
        </div>
      </div>

      <!-- 联系信息 -->
      <div class="contact-section">
        <div class="section-title">
          <el-icon><User /></el-icon>
          主要联系人
        </div>
        <div class="contact-info">
          <div class="contact-name">{{ customer.contact.name }}</div>
          <div class="contact-position">{{ customer.contact.position }}</div>
          <div class="contact-methods">
            <el-button 
              text 
              size="small"
              @click.stop="callContact(customer.contact.phone)"
            >
              <el-icon><Phone /></el-icon>
              {{ customer.contact.phone }}
            </el-button>
            <el-button 
              text 
              size="small"
              @click.stop="emailContact(customer.contact.email)"
            >
              <el-icon><Message /></el-icon>
              {{ customer.contact.email }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 业务指标 -->
      <div class="metrics-section" v-if="customer.metrics">
        <div class="section-title">
          <el-icon><TrendCharts /></el-icon>
          业务指标
        </div>
        <div class="metrics-grid">
          <div class="metric-item">
            <div class="metric-value">{{ customer.metrics.totalOrders }}</div>
            <div class="metric-label">总订单</div>
          </div>
          <div class="metric-item">
            <div class="metric-value">{{ formatRevenue(customer.metrics.totalRevenue) }}</div>
            <div class="metric-label">总营收</div>
          </div>
          <div class="metric-item">
            <div class="metric-value">{{ customer.metrics.onTimeDeliveryRate.toFixed(1) }}%</div>
            <div class="metric-label">准时率</div>
          </div>
          <div class="metric-item">
            <div class="metric-value">{{ customer.metrics.qualityScore.toFixed(1) }}</div>
            <div class="metric-label">质量分</div>
          </div>
        </div>
      </div>

      <!-- 最后联系时间 -->
      <div class="last-contact">
        <el-icon><Clock /></el-icon>
        <span v-if="customer.lastContactDate">
          最后联系: {{ formatDate(customer.lastContactDate) }}
        </span>
        <span v-else class="no-contact">
          暂无联系记录
        </span>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
// Element Plus组件通过unplugin-auto-import自动导入
import type { Customer, CustomerType, CustomerLevel, CustomerStatus } from '@/types/customer'
import { 
  CUSTOMER_TYPE_OPTIONS,
  CUSTOMER_LEVEL_OPTIONS,
  CUSTOMER_STATUS_OPTIONS,
  APPLICATION_FIELD_OPTIONS
} from '@/utils/mockData/customerMaster'

interface Props {
  customer: Customer & { isFavorite?: boolean }
}

interface Emits {
  (e: 'view', customer: Customer): void
  (e: 'edit', customer: Customer): void
  (e: 'delete', customer: Customer): void
  (e: 'favorite', customer: Customer): void
}

const props = defineProps<Props>()
const emits = defineEmits<Emits>()

// 获取客户颜色
const getCustomerColor = (type: CustomerType): string => {
  const colors = {
    fabless: '#409EFF',
    idm: '#67C23A', 
    foundry: '#E6A23C',
    distributor: '#909399',
    broker: '#F56C6C',
    ems: '#9580FF'
  }
  return colors[type]
}

// 获取客户类型标签类型
const getCustomerTypeTagType = (type: CustomerType): string => {
  const types = {
    fabless: 'primary',
    idm: 'success',
    foundry: 'warning', 
    distributor: 'info',
    broker: 'danger',
    ems: ''
  }
  return types[type] || ''
}

// 获取客户类型标签文本
const getCustomerTypeLabel = (type: CustomerType): string => {
  const option = CUSTOMER_TYPE_OPTIONS.find(opt => opt.value === type)
  return option?.label || type
}

// 获取客户等级颜色
const getCustomerLevelColor = (level: CustomerLevel): string => {
  const option = CUSTOMER_LEVEL_OPTIONS.find(opt => opt.value === level)
  return option?.color || '#909399'
}

// 获取客户等级标签文本
const getCustomerLevelLabel = (level: CustomerLevel): string => {
  const option = CUSTOMER_LEVEL_OPTIONS.find(opt => opt.value === level)
  return option?.label || level
}

// 获取状态标签类型
const getStatusTagType = (status: CustomerStatus): string => {
  const types = {
    active: 'success',
    inactive: 'info',
    pending: 'warning',
    suspended: 'danger',
    archived: 'info'
  }
  return types[status] || ''
}

// 获取状态标签文本
const getStatusLabel = (status: CustomerStatus): string => {
  const option = CUSTOMER_STATUS_OPTIONS.find(opt => opt.value === status)
  return option?.label || status
}

// 获取应用领域标签文本
const getApplicationFieldLabel = (field: string): string => {
  const option = APPLICATION_FIELD_OPTIONS.find(opt => opt.value === field)
  return option?.label || field
}

// 格式化日期
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit', 
    day: '2-digit'
  })
}

// 格式化营收
const formatRevenue = (revenue: number): string => {
  if (revenue >= 100000000) {
    return (revenue / 100000000).toFixed(1) + '亿'
  } else if (revenue >= 10000) {
    return (revenue / 10000).toFixed(1) + '万'
  } else {
    return revenue.toString()
  }
}

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  const emits = defineEmits<Emits>()
  
  switch (command) {
    case 'edit':
      emits('edit', props.customer)
      break
    case 'duplicate':
      // 复制功能暂时使用编辑
      emits('edit', props.customer)
      break
    case 'delete':
      emits('delete', props.customer)
      break
  }
}

// 拨打电话
const callContact = (phone: string) => {
  if (phone) {
    window.open(`tel:${phone}`)
  }
}

// 发送邮件
const emailContact = (email: string) => {
  if (email) {
    window.open(`mailto:${email}`)
  }
}
</script>

<style lang="scss" scoped>
.customer-card {
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 25px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    margin: -16px -16px 0;
    border-bottom: 1px solid var(--color-border-light);

    .customer-logo {
      display: flex;
      align-items: center;
    }

    .header-actions {
      display: flex;
      gap: 8px;
      align-items: center;
    }
  }

  .card-content {
    padding-top: 16px;

    .customer-basic-info {
      margin-bottom: 16px;
      text-align: center;

      .customer-code {
        display: inline-block;
        padding: 2px 8px;
        margin-bottom: 8px;
        font-size: 12px;
        color: var(--color-text-placeholder);
        background: var(--color-bg-light);
        border-radius: 4px;
      }

      .customer-name {
        margin: 0 0 4px;
        font-size: 16px;
        font-weight: 600;
        line-height: 1.4;
        color: var(--color-text-primary);
      }

      .customer-english {
        margin-bottom: 2px;
        font-size: 12px;
        color: var(--color-text-regular);
      }

      .customer-short {
        font-size: 12px;
        color: var(--color-text-secondary);
      }
    }

    .customer-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      justify-content: center;
      margin-bottom: 16px;
    }

    .application-fields {
      margin-bottom: 16px;

      .field-label {
        margin-bottom: 6px;
        font-size: 12px;
        color: var(--color-text-regular);
      }

      .field-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        align-items: center;

        .more-fields {
          margin-left: 4px;
          font-size: 12px;
          color: var(--color-text-secondary);
        }
      }
    }

    .contact-section,
    .metrics-section {
      padding: 12px;
      margin-bottom: 16px;
      background: var(--color-bg-light);
      border-radius: 6px;

      .section-title {
        display: flex;
        gap: 6px;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;
        font-weight: 500;
        color: var(--color-text-regular);
      }
    }

    .contact-info {
      .contact-name {
        margin-bottom: 2px;
        font-weight: 500;
        color: var(--color-text-primary);
      }

      .contact-position {
        margin-bottom: 6px;
        font-size: 12px;
        color: var(--color-text-regular);
      }

      .contact-methods {
        display: flex;
        flex-direction: column;
        gap: 2px;

        .el-button {
          justify-content: flex-start;
          height: auto;
          padding: 0;
          font-size: 11px;
        }
      }
    }

    .metrics-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;

      .metric-item {
        text-align: center;

        .metric-value {
          font-size: 14px;
          font-weight: 600;
          line-height: 1;
          color: var(--color-primary);
        }

        .metric-label {
          margin-top: 2px;
          font-size: 11px;
          color: var(--color-text-secondary);
        }
      }
    }

    .last-contact {
      display: flex;
      gap: 6px;
      align-items: center;
      padding-top: 12px;
      font-size: 11px;
      color: var(--color-text-secondary);
      border-top: 1px solid var(--color-border-lighter);

      .no-contact {
        font-style: italic;
        color: var(--color-text-placeholder);
      }
    }
  }
}

// 响应式调整
@media (width <= 768px) {
  .customer-card {
    .metrics-grid {
      grid-template-columns: 1fr;
      gap: 4px;
    }
  }
}
</style>