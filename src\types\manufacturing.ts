// 制造执行管理相关类型定义
// IC封装测试工厂MES系统专用类型

/**
 * 设备状态枚举
 */
export enum EquipmentStatus {
  IDLE = 'idle', // 空闲
  RUNNING = 'running', // 运行中
  ALARM = 'alarm', // 报警
  DOWN = 'down', // 停机
  MAINTENANCE = 'maintenance', // 维护中
  SETUP = 'setup' // 设置中
}

/**
 * 工艺步骤状态
 */
export enum ProcessStatus {
  WAITING = 'waiting', // 等待
  RUNNING = 'running', // 进行中
  COMPLETED = 'completed', // 已完成
  FAILED = 'failed', // 失败
  SKIPPED = 'skipped' // 跳过
}

/**
 * 品质结果枚举
 */
export enum QualityResult {
  PASS = 'pass', // 良品
  FAIL = 'fail', // 不良
  RETEST = 'retest', // 重测
  PENDING = 'pending' // 待判定
}

/**
 * 封装类型枚举
 */
export enum PackageType {
  QFP = 'QFP',
  BGA = 'BGA',
  CSP = 'CSP',
  FC = 'FC',
  SOP = 'SOP',
  TSOP = 'TSOP',
  QFN = 'QFN'
}

/**
 * 基础设备信息
 */
export interface Equipment {
  id: string
  name: string
  model: string
  station: string
  status: EquipmentStatus
  lastUpdated: string
  utilization: number // 稼动率 (%)
  oee: number // 综合设备效率 (%)
}

/**
 * CP测试相关类型
 */
export interface WaferInfo {
  id: string
  waferLot: string
  waferNumber: number
  customerPN: string
  productCode: string
  dieSize: {
    x: number
    y: number
  }
  totalDie: number
  testedDie: number
  passedDie: number
  yield: number // 良率 (%)
  status: ProcessStatus
  startTime?: string
  endTime?: string
}

/**
 * 探针卡信息
 */
export interface ProbeCard {
  id: string
  cardNumber: string
  type: string
  totalTouches: number
  maxTouches: number
  lastCleanTime: string
  nextPMTime: string
  status: 'active' | 'maintenance' | 'retired'
  assignedTester: string
}

/**
 * 晶圆图数据点
 */
export interface WaferMapData {
  x: number
  y: number
  result: QualityResult
  binCode?: string
  testData?: Record<string, number>
}

/**
 * 晶圆图信息
 */
export interface WaferMap {
  waferId: string
  mapData: WaferMapData[]
  dimensions: {
    maxX: number
    maxY: number
  }
  binSummary: Record<string, number>
  yield: number
  generatedTime: string
}

/**
 * 电测参数
 */
export interface ElectricalTest {
  parameterId: string
  parameterName: string
  unit: string
  minLimit: number
  maxLimit: number
  measuredValue: number
  result: QualityResult
  cpk?: number
}

/**
 * CP测试记录
 */
export interface CPTestRecord {
  id: string
  waferInfo: WaferInfo
  probeCard: ProbeCard
  tester: Equipment
  testProgram: string
  temperature: number
  testResults: ElectricalTest[]
  waferMap: WaferMap
  operator: string
  startTime: string
  endTime: string
  duration: number // 测试时长(分钟)
}

/**
 * 封装工艺相关类型
 */
export interface DieAttachProcess {
  temperature: number // 贴片温度 (℃)
  force: number // 贴片压力 (N)
  time: number // 贴片时间 (s)
  epoxy: string // 导电胶型号
  result: QualityResult
}

/**
 * 线键合工艺参数
 */
export interface WireBondProcess {
  wireType: string // 金线规格
  bondForce1: number // 第一点键合力 (gf)
  bondForce2: number // 第二点键合力 (gf)
  ultrasonicPower: number // 超声功率 (%)
  bondTime1: number // 第一点键合时间 (ms)
  bondTime2: number // 第二点键合时间 (ms)
  loopHeight: number // 线弧高度 (μm)
  totalWires: number // 总线数
  passedWires: number // 良品线数
  result: QualityResult
}

/**
 * 塑封工艺参数
 */
export interface MoldingProcess {
  compound: string // 封装料型号
  temperature: number // 塑封温度 (℃)
  pressure: number // 塑封压力 (MPa)
  time: number // 固化时间 (s)
  thickness: number // 厚度 (mm)
  voidRatio: number // 空洞率 (%)
  result: QualityResult
}

/**
 * 封装记录
 */
export interface AssemblyRecord {
  id: string
  lotNumber: string
  packageType: PackageType
  customerPN: string
  quantity: number
  processedQty: number
  dieAttach: DieAttachProcess
  wireBond: WireBondProcess
  molding: MoldingProcess
  operator: string
  equipment: Equipment
  startTime: string
  endTime?: string
  status: ProcessStatus
}

/**
 * 最终测试相关类型
 */
export interface TestProgram {
  id: string
  name: string
  version: string
  customerPN: string
  testItems: string[]
  testTime: number // 测试时间 (s)
  temperatureRange: {
    min: number
    max: number
  }
}

/**
 * 老化测试参数
 */
export interface BurnInProcess {
  temperature: number // 老化温度 (℃)
  voltage: number // 老化电压 (V)
  duration: number // 老化时间 (h)
  socketCount: number // 测试座位数
  failureCount: number // 失效数量
  failureRate: number // 失效率 (%)
}

/**
 * 分选机操作
 */
export interface HandlerOperation {
  handlerId: string
  handlerName: string
  throughput: number // 产能 (UPH)
  jamCount: number // 卡料次数
  efficiency: number // 效率 (%)
  binMapping: Record<string, string> // Bin映射关系
}

/**
 * 最终测试记录
 */
export interface FinalTestRecord {
  id: string
  lotNumber: string
  packageType: PackageType
  customerPN: string
  testProgram: TestProgram
  testedQty: number
  passedQty: number
  yield: number
  burnIn?: BurnInProcess
  handler: HandlerOperation
  testResults: ElectricalTest[]
  binSummary: Record<string, number>
  operator: string
  startTime: string
  endTime: string
  status: ProcessStatus
}

/**
 * 实时监控数据
 */
export interface RealtimeData {
  equipmentId: string
  timestamp: string
  parameters: Record<string, number>
  alarms?: string[]
  messages?: string[]
}

/**
 * 工艺控制参数
 */
export interface ProcessParameter {
  id: string
  name: string
  unit: string
  targetValue: number
  tolerance: number
  currentValue: number
  minLimit: number
  maxLimit: number
  status: 'normal' | 'warning' | 'alarm'
}

/**
 * SPC统计数据
 */
export interface SPCData {
  parameterId: string
  parameterName: string
  samples: number[]
  mean: number
  stdDev: number
  cpk: number
  ucl: number // 上控制线
  lcl: number // 下控制线
  usl: number // 上规格线
  lsl: number // 下规格线
  outOfControl: boolean
}

/**
 * SECS/GEM消息
 */
export interface SECSMessage {
  id: string
  equipmentId: string
  stream: number
  function: number
  direction: 'host_to_equipment' | 'equipment_to_host'
  data: any
  timestamp: string
  status: 'sent' | 'received' | 'timeout' | 'error'
}

/**
 * 设备配方
 */
export interface Recipe {
  id: string
  name: string
  version: string
  equipmentType: string
  parameters: ProcessParameter[]
  createdBy: string
  createdTime: string
  isActive: boolean
}

/**
 * 工艺流程步骤
 */
export interface ProcessStep {
  id: string
  stepName: string
  stepType: 'cp_test' | 'assembly' | 'final_test'
  duration: number
  equipment?: Equipment
  parameters: ProcessParameter[]
  status: ProcessStatus
  startTime?: string
  endTime?: string
  operator?: string
  notes?: string
}

/**
 * 生产工单
 */
export interface WorkOrder {
  id: string
  orderNumber: string
  customerPN: string
  packageType: PackageType
  quantity: number
  completedQty: number
  currentStep: string
  processSteps: ProcessStep[]
  priority: 'low' | 'medium' | 'high' | 'urgent'
  planStartTime: string
  planEndTime: string
  actualStartTime?: string
  actualEndTime?: string
  status: 'planned' | 'released' | 'running' | 'completed' | 'hold'
}

/**
 * 看板数据
 */
export interface DashboardData {
  // 总体KPI
  totalEquipment: number
  runningEquipment: number
  overallOEE: number
  dailyOutput: number

  // CP测试数据
  cpTesting: {
    activeWafers: number
    avgYield: number
    totalTestedDie: number
    equipmentUtilization: number
  }

  // 封装数据
  assembly: {
    activePackages: number
    avgCycleTime: number
    defectRate: number
    throughput: number
  }

  // 最终测试数据
  finalTest: {
    testedUnits: number
    passRate: number
    avgTestTime: number
    handlerEfficiency: number
  }
}
