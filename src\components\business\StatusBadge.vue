<template>
  <div class="status-badge" :class="badgeClass">
    <div v-if="showDot" class="status-dot" :class="dotClass"></div>
    <span class="status-text">{{ displayText }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  /** 状态值 */
  status: string
  /** 状态类型 */
  type?: 'order' | 'equipment' | 'customer' | 'supplier' | 'product' | 'custom'
  /** 自定义状态映射 */
  statusMap?: Record<string, { text: string; color: string; class?: string }>
  /** 是否显示状态点 */
  showDot?: boolean
  /** 尺寸 */
  size?: 'small' | 'medium' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'custom',
  showDot: true,
  size: 'medium'
})

// 预定义的状态映射
const predefinedStatusMaps = {
  order: {
    pending: { text: '待确认', color: '#f59e0b', class: 'warning' },
    confirmed: { text: '已确认', color: '#3b82f6', class: 'primary' },
    processing: { text: '生产中', color: '#8b5cf6', class: 'processing' },
    testing: { text: '测试中', color: '#06b6d4', class: 'info' },
    completed: { text: '已完成', color: '#10b981', class: 'success' },
    cancelled: { text: '已取消', color: '#ef4444', class: 'danger' },
    on_hold: { text: '暂停', color: '#6b7280', class: 'secondary' }
  },
  equipment: {
    running: { text: '运行中', color: '#10b981', class: 'success' },
    idle: { text: '空闲', color: '#3b82f6', class: 'primary' },
    maintenance: { text: '维护中', color: '#f59e0b', class: 'warning' },
    error: { text: '故障', color: '#ef4444', class: 'danger' },
    setup: { text: '设置中', color: '#8b5cf6', class: 'processing' },
    disabled: { text: '停用', color: '#6b7280', class: 'secondary' }
  },
  customer: {
    active: { text: '活跃', color: '#10b981', class: 'success' },
    inactive: { text: '不活跃', color: '#6b7280', class: 'secondary' },
    potential: { text: '潜在客户', color: '#f59e0b', class: 'warning' },
    blacklisted: { text: '黑名单', color: '#ef4444', class: 'danger' }
  },
  supplier: {
    active: { text: '合作中', color: '#10b981', class: 'success' },
    inactive: { text: '暂停合作', color: '#6b7280', class: 'secondary' },
    suspended: { text: '已暂停', color: '#ef4444', class: 'danger' },
    evaluation: { text: '评估中', color: '#f59e0b', class: 'warning' }
  },
  product: {
    active: { text: '在产', color: '#10b981', class: 'success' },
    inactive: { text: '停产', color: '#6b7280', class: 'secondary' },
    discontinued: { text: '已停产', color: '#ef4444', class: 'danger' },
    development: { text: '开发中', color: '#8b5cf6', class: 'processing' }
  }
}

// 计算显示文本和样式
const statusInfo = computed(() => {
  const statusMap = props.statusMap || predefinedStatusMaps[props.type] || {}
  const info = statusMap[props.status]
  
  if (info) {
    return info
  }
  
  // 默认状态
  return {
    text: props.status,
    color: '#6b7280',
    class: 'default'
  }
})

const displayText = computed(() => statusInfo.value.text)

const badgeClass = computed(() => [
  'status-badge',
  `status-badge--${props.size}`,
  `status-badge--${statusInfo.value.class || 'default'}`
])

const dotClass = computed(() => [
  'status-dot',
  `status-dot--${statusInfo.value.class || 'default'}`
])
</script>

<style lang="scss" scoped>
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  white-space: nowrap;
  transition: all var(--transition-fast);
  
  // 尺寸变体
  &--small {
    padding: 2px 6px;
    font-size: 10px;
    gap: 4px;
    
    .status-dot {
      width: 6px;
      height: 6px;
    }
  }
  
  &--medium {
    padding: 4px 8px;
    font-size: var(--font-size-xs);
    gap: 6px;
    
    .status-dot {
      width: 8px;
      height: 8px;
    }
  }
  
  &--large {
    padding: 6px 12px;
    font-size: var(--font-size-sm);
    gap: 8px;
    
    .status-dot {
      width: 10px;
      height: 10px;
    }
  }
  
  // 状态样式变体
  &--success {
    background-color: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.2);
  }
  
  &--primary {
    background-color: rgba(59, 130, 246, 0.1);
    color: #2563eb;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }
  
  &--warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
  }
  
  &--danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
  }
  
  &--info {
    background-color: rgba(6, 182, 212, 0.1);
    color: #0891b2;
    border: 1px solid rgba(6, 182, 212, 0.2);
  }
  
  &--processing {
    background-color: rgba(139, 92, 246, 0.1);
    color: #7c3aed;
    border: 1px solid rgba(139, 92, 246, 0.2);
  }
  
  &--secondary {
    background-color: rgba(107, 114, 128, 0.1);
    color: #4b5563;
    border: 1px solid rgba(107, 114, 128, 0.2);
  }
  
  &--default {
    background-color: rgba(107, 114, 128, 0.1);
    color: #4b5563;
    border: 1px solid rgba(107, 114, 128, 0.2);
  }
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
  
  &--success {
    background-color: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  }
  
  &--primary {
    background-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
  
  &--warning {
    background-color: #f59e0b;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  }
  
  &--danger {
    background-color: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  }
  
  &--info {
    background-color: #06b6d4;
    box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2);
  }
  
  &--processing {
    background-color: #8b5cf6;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
    
    // 添加脉动效果
    animation: pulse 2s infinite;
  }
  
  &--secondary,
  &--default {
    background-color: #6b7280;
    box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.2);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 暗色主题适配
[data-theme="dark"] {
  .status-badge {
    &--success {
      background-color: rgba(16, 185, 129, 0.15);
      color: #34d399;
    }
    
    &--primary {
      background-color: rgba(59, 130, 246, 0.15);
      color: #60a5fa;
    }
    
    &--warning {
      background-color: rgba(245, 158, 11, 0.15);
      color: #fbbf24;
    }
    
    &--danger {
      background-color: rgba(239, 68, 68, 0.15);
      color: #f87171;
    }
    
    &--info {
      background-color: rgba(6, 182, 212, 0.15);
      color: #22d3ee;
    }
    
    &--processing {
      background-color: rgba(139, 92, 246, 0.15);
      color: #a78bfa;
    }
    
    &--secondary,
    &--default {
      background-color: rgba(107, 114, 128, 0.15);
      color: #9ca3af;
    }
  }
}
</style>