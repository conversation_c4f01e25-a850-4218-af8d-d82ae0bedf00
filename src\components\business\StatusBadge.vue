<template>
  <div class="status-badge" :class="badgeClass">
    <div
v-if="showDot" class="status-dot" :class="dotClass" />
    <span class="status-text">{{ displayText }}</span>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'

  interface Props {
    /** 状态值 */
    status: string
    /** 状态类型 */
    type?: 'order' | 'equipment' | 'customer' | 'supplier' | 'product' | 'custom'
    /** 自定义状态映射 */
    statusMap?: Record<string, { text: string; color: string; class?: string }>
    /** 是否显示状态点 */
    showDot?: boolean
    /** 尺寸 */
    size?: 'small' | 'medium' | 'large'
  }

  const props = withDefaults(defineProps<Props>(), {
    type: 'custom',
    showDot: true,
    size: 'medium'
  })

  // 预定义的状态映射
  const predefinedStatusMaps = {
    order: {
      pending: { text: '待确认', color: '#f59e0b', class: 'warning' },
      confirmed: { text: '已确认', color: '#3b82f6', class: 'primary' },
      processing: { text: '生产中', color: '#8b5cf6', class: 'processing' },
      testing: { text: '测试中', color: '#06b6d4', class: 'info' },
      completed: { text: '已完成', color: '#10b981', class: 'success' },
      cancelled: { text: '已取消', color: '#ef4444', class: 'danger' },
      on_hold: { text: '暂停', color: '#6b7280', class: 'secondary' }
    },
    equipment: {
      running: { text: '运行中', color: '#10b981', class: 'success' },
      idle: { text: '空闲', color: '#3b82f6', class: 'primary' },
      maintenance: { text: '维护中', color: '#f59e0b', class: 'warning' },
      error: { text: '故障', color: '#ef4444', class: 'danger' },
      setup: { text: '设置中', color: '#8b5cf6', class: 'processing' },
      disabled: { text: '停用', color: '#6b7280', class: 'secondary' }
    },
    customer: {
      active: { text: '活跃', color: '#10b981', class: 'success' },
      inactive: { text: '不活跃', color: '#6b7280', class: 'secondary' },
      potential: { text: '潜在客户', color: '#f59e0b', class: 'warning' },
      blacklisted: { text: '黑名单', color: '#ef4444', class: 'danger' }
    },
    supplier: {
      active: { text: '合作中', color: '#10b981', class: 'success' },
      inactive: { text: '暂停合作', color: '#6b7280', class: 'secondary' },
      suspended: { text: '已暂停', color: '#ef4444', class: 'danger' },
      evaluation: { text: '评估中', color: '#f59e0b', class: 'warning' }
    },
    product: {
      active: { text: '在产', color: '#10b981', class: 'success' },
      inactive: { text: '停产', color: '#6b7280', class: 'secondary' },
      discontinued: { text: '已停产', color: '#ef4444', class: 'danger' },
      development: { text: '开发中', color: '#8b5cf6', class: 'processing' }
    }
  }

  // 计算显示文本和样式
  const statusInfo = computed(() => {
    const statusMap = props.statusMap || predefinedStatusMaps[props.type] || {}
    const info = statusMap[props.status]

    if (info) {
      return info
    }

    // 默认状态
    return {
      text: props.status,
      color: '#6b7280',
      class: 'default'
    }
  })

  const displayText = computed(() => statusInfo.value.text)

  const badgeClass = computed(() => [
    'status-badge',
    `status-badge--${props.size}`,
    `status-badge--${statusInfo.value.class || 'default'}`
  ])

  const dotClass = computed(() => [
    'status-dot',
    `status-dot--${statusInfo.value.class || 'default'}`
  ])
</script>

<style lang="scss" scoped>
  .status-badge {
    display: inline-flex;
    gap: 6px;
    align-items: center;
    padding: 4px 8px;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    line-height: 1;
    white-space: nowrap;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);

    // 尺寸变体
    &--small {
      gap: 4px;
      padding: 2px 6px;
      font-size: 10px;

      .status-dot {
        width: 6px;
        height: 6px;
      }
    }

    &--medium {
      gap: 6px;
      padding: 4px 8px;
      font-size: var(--font-size-xs);

      .status-dot {
        width: 8px;
        height: 8px;
      }
    }

    &--large {
      gap: 8px;
      padding: 6px 12px;
      font-size: var(--font-size-sm);

      .status-dot {
        width: 10px;
        height: 10px;
      }
    }

    // 状态样式变体
    &--success {
      color: #059669;
      background-color: rgb(16 185 129 / 10%);
      border: 1px solid rgb(16 185 129 / 20%);
    }

    &--primary {
      color: #2563eb;
      background-color: rgb(59 130 246 / 10%);
      border: 1px solid rgb(59 130 246 / 20%);
    }

    &--warning {
      color: #d97706;
      background-color: rgb(245 158 11 / 10%);
      border: 1px solid rgb(245 158 11 / 20%);
    }

    &--danger {
      color: #dc2626;
      background-color: rgb(239 68 68 / 10%);
      border: 1px solid rgb(239 68 68 / 20%);
    }

    &--info {
      color: #0891b2;
      background-color: rgb(6 182 212 / 10%);
      border: 1px solid rgb(6 182 212 / 20%);
    }

    &--processing {
      color: #7c3aed;
      background-color: rgb(139 92 246 / 10%);
      border: 1px solid rgb(139 92 246 / 20%);
    }

    &--secondary {
      color: #4b5563;
      background-color: rgb(107 114 128 / 10%);
      border: 1px solid rgb(107 114 128 / 20%);
    }

    &--default {
      color: #4b5563;
      background-color: rgb(107 114 128 / 10%);
      border: 1px solid rgb(107 114 128 / 20%);
    }
  }

  .status-dot {
    flex-shrink: 0;
    width: 8px;
    height: 8px;
    border-radius: 50%;

    &--success {
      background-color: #10b981;
      box-shadow: 0 0 0 2px rgb(16 185 129 / 20%);
    }

    &--primary {
      background-color: #3b82f6;
      box-shadow: 0 0 0 2px rgb(59 130 246 / 20%);
    }

    &--warning {
      background-color: #f59e0b;
      box-shadow: 0 0 0 2px rgb(245 158 11 / 20%);
    }

    &--danger {
      background-color: #ef4444;
      box-shadow: 0 0 0 2px rgb(239 68 68 / 20%);
    }

    &--info {
      background-color: #06b6d4;
      box-shadow: 0 0 0 2px rgb(6 182 212 / 20%);
    }

    &--processing {
      background-color: #8b5cf6;
      box-shadow: 0 0 0 2px rgb(139 92 246 / 20%);

      // 添加脉动效果
      animation: pulse 2s infinite;
    }

    &--secondary,
    &--default {
      background-color: #6b7280;
      box-shadow: 0 0 0 2px rgb(107 114 128 / 20%);
    }
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.5;
    }
  }

  // 暗色主题适配
  [data-theme='dark'] {
    .status-badge {
      &--success {
        color: #34d399;
        background-color: rgb(16 185 129 / 15%);
      }

      &--primary {
        color: #60a5fa;
        background-color: rgb(59 130 246 / 15%);
      }

      &--warning {
        color: #fbbf24;
        background-color: rgb(245 158 11 / 15%);
      }

      &--danger {
        color: #f87171;
        background-color: rgb(239 68 68 / 15%);
      }

      &--info {
        color: #22d3ee;
        background-color: rgb(6 182 212 / 15%);
      }

      &--processing {
        color: #a78bfa;
        background-color: rgb(139 92 246 / 15%);
      }

      &--secondary,
      &--default {
        color: #9ca3af;
        background-color: rgb(107 114 128 / 15%);
      }
    }
  }
</style>
