<template>
  <div
    class="monitoring-center"
    :class="{ 'fullscreen-mode': config.displayMode === 'fullscreen' }"
  >
    <!-- 顶部控制栏 -->
    <div
v-show="config.displayMode !== 'presentation'" class="control-bar"
>
      <div class="control-left">
        <div class="system-status">
          <StatusIndicator
            :status="systemStatus?.overallHealth || 'unknown'"
            label="系统状态"
            :description="getSystemStatusDescription()"
            size="small"
          />
        </div>

        <div class="connection-status">
          <StatusIndicator
            :status="connected ? 'connected' : 'disconnected'"
            :label="connected ? '已连接' : '未连接'"
            description="实时数据连接"
            size="small"
          />
        </div>

        <div class="data-timestamp">
          <el-icon><Clock /></el-icon>
          <span>{{ currentTime }}</span>
        </div>
      </div>

      <div class="control-center">
        <el-button-group>
          <el-button
            v-for="panel in panels"
            :key="panel.key"
            :type="config.currentPanel === panel.key ? 'primary' : ''"
            size="small"
            @click="switchPanel(panel.key)"
          >
            <el-icon>
              <component :is="panel.icon" />
            </el-icon>
            {{ panel.label }}
          </el-button>
        </el-button-group>
      </div>

      <div class="control-right">
        <div class="refresh-controls">
          <el-select
            v-model="config.refreshInterval"
            size="small"
            style="width: 100px"
            @change="setRefreshInterval"
          >
            <el-option
label="5秒" :value="5"
/>
            <el-option
label="10秒" :value="10"
/>
            <el-option
label="30秒" :value="30"
/>
            <el-option
label="60秒" :value="60"
/>
          </el-select>

          <el-button size="small"
:icon="Refresh" :loading="loading" @click="refreshAllData">
            刷新
          </el-button>
        </div>

        <div class="display-controls">
          <el-tooltip content="全屏模式">
            <el-button
size="small" :icon="FullScreen"
@click="toggleFullscreen"
/>
          </el-tooltip>

          <el-tooltip content="演示模式">
            <el-button
size="small" :icon="Monitor"
@click="togglePresentationMode"
/>
          </el-tooltip>

          <el-tooltip content="导出数据">
            <el-button
size="small" :icon="Download"
@click="exportCurrentData"
/>
          </el-tooltip>

          <el-dropdown @command="handleSettingCommand">
            <el-button
size="small" :icon="Setting"
/>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="sound">
                  <el-icon><Bell /></el-icon>
                  {{ config.soundAlertEnabled ? '关闭' : '开启' }}声音提醒
                </el-dropdown-item>
                <el-dropdown-item command="autoRefresh">
                  <el-icon><Refresh /></el-icon>
                  {{ config.refreshInterval > 0 ? '停止' : '启动' }}自动刷新
                </el-dropdown-item>
                <el-dropdown-item
divided command="reset"
>
                  <el-icon><RefreshLeft /></el-icon>
                  重置布局
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
      <!-- 系统概览面板 -->
      <div
v-if="config.currentPanel === 'overview'" class="overview-panel"
>
        <div class="overview-grid">
          <!-- 系统状态卡片 -->
          <div class="status-cards">
            <div class="card-row">
              <KPICard
                title="生产状态"
                :value="getProductionStatusValue()"
                type="primary"
                icon="trend"
                size="medium"
                :subtitle="getProductionStatusText()"
              />

              <KPICard
                title="设备状态"
                :value="equipmentSummary.running"
                :unit="`/${equipmentSummary.total}`"
                type="success"
                icon="monitor"
                size="medium"
                subtitle="运行中设备数量"
              />

              <KPICard
                title="活跃告警"
                :value="activeAlarmsCount"
                unit="条"
                :type="activeAlarmsCount > 5 ? 'danger' : 'warning'"
                icon="warning"
                size="medium"
                subtitle="需要处理的告警"
              />
            </div>
          </div>

          <!-- 快速数据概览 -->
          <div class="quick-stats">
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-label">日产量</div>
                <div class="stat-value">
                  {{ formatNumber(productionKPI?.dailyOutput || 0) }}
                </div>
                <div class="stat-unit">pcs</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">整体良率</div>
                <div class="stat-value">
                  {{ (qualityKPI?.overallYield || 0).toFixed(1) }}
                </div>
                <div class="stat-unit">%</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">OEE</div>
                <div class="stat-value">
                  {{ (productionKPI?.oeeRate || 0).toFixed(1) }}
                </div>
                <div class="stat-unit">%</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">在制品</div>
                <div class="stat-value">
                  {{ formatNumber(productionKPI?.wipCount || 0) }}
                </div>
                <div class="stat-unit">lots</div>
              </div>
            </div>
          </div>

          <!-- 系统健康度雷达图 -->
          <div class="health-radar">
            <ChartPanel
              title="系统健康度"
              subtitle="各子系统运行状态评估"
              :options="healthRadarOptions"
              :loading="loading"
              height="300px"
              :show-footer="false"
            />
          </div>

          <!-- 实时消息流 -->
          <div class="message-stream">
            <div class="stream-header">
              <h3 class="stream-title">
                <el-icon><ChatLineSquare /></el-icon>
                实时消息
              </h3>
              <el-badge
:value="unreadMessagesCount" class="message-badge"
/>
            </div>

            <div class="message-list">
              <div
                v-for="message in realtimeMessages.slice(0, 8)"
                :key="message.id"
                :class="[
                  'message-item',
                  `message-item--${message.type}`,
                  { unread: !message.acknowledged }
                ]"
              >
                <div class="message-header">
                  <el-icon :class="`icon-${message.type}`">
                    <InfoFilled v-if="message.type === 'info'" />
                    <WarningFilled v-else-if="message.type === 'warning'" />
                    <CircleCloseFilled v-else-if="message.type === 'error'" />
                    <CircleCheckFilled v-else />
                  </el-icon>
                  <span class="message-source">{{ getMessageSourceName(message.source) }}</span>
                  <span class="message-time">{{ formatMessageTime(message.timestamp) }}</span>
                </div>
                <div class="message-content">
                  {{ message.message }}
                </div>
              </div>

              <div
v-if="realtimeMessages.length === 0" class="no-messages"
>
                <el-empty
description="暂无消息" :image-size="60"
/>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 生产监控面板 -->
      <ProductionDashboard v-else-if="config.currentPanel === 'production'" />

      <!-- 设备监控面板 -->
      <EquipmentDashboard v-else-if="config.currentPanel === 'equipment'" />

      <!-- 质量监控面板 -->
      <QualityDashboard v-else-if="config.currentPanel === 'quality'" />
    </div>

    <!-- 底部状态栏 -->
    <div
v-show="config.displayMode !== 'presentation'" class="status-bar"
>
      <div class="status-left">
        <span class="status-item">
          <el-icon><DataAnalysis /></el-icon>
          数据源: MES({{ systemStatus?.dataSourceStatus.mes || 'unknown' }}) | 设备({{
            systemStatus?.dataSourceStatus.equipment || 'unknown'
          }}) | 质量({{ systemStatus?.dataSourceStatus.quality || 'unknown' }})
        </span>
      </div>

      <div class="status-right">
        <span class="status-item">刷新间隔: {{ config.refreshInterval }}秒</span>
        <span class="status-item">最后更新: {{ lastUpdateTime }}</span>
      </div>
    </div>

    <!-- 全屏时的退出按钮 -->
    <div
v-if="config.displayMode === 'fullscreen'" class="fullscreen-exit"
>
      <el-button
type="primary" @click="exitFullscreen"
>
        <el-icon><CloseBold /></el-icon>
        退出全屏
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted } from 'vue'
  import {
    Clock,
    Refresh,
    FullScreen,
    Monitor,
    Download,
    Setting,
    Bell,
    RefreshLeft,
    CloseBold,
    DataAnalysis,
    ChatLineSquare,
    InfoFilled,
    WarningFilled,
    CircleCloseFilled,
    CircleCheckFilled
  } from '@element-plus/icons-vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import { KPICard, ChartPanel, StatusIndicator } from '@/components/monitoring'
  import ProductionDashboard from './ProductionDashboard.vue'
  import EquipmentDashboard from './EquipmentDashboard.vue'
  import QualityDashboard from './QualityDashboard.vue'
  import { useMonitoring } from '@/composables/useMonitoring'
  import type { EChartsOption } from 'echarts'
  import { formatNumber } from '@/utils'

  // 使用监控数据管理
  const {
    loading,
    connected,
    productionKPI,
    qualityKPI,
    systemStatus,
    realtimeMessages,
    equipmentSummary,
    activeAlarmsCount,
    unreadMessagesCount,
    config,
    switchPanel,
    setRefreshInterval,
    refreshAllData,
    exportData
  } = useMonitoring()

  // 响应式数据
  const currentTime = ref('')
  const lastUpdateTime = ref('')

  // 面板配置
  const panels = [
    { key: 'overview', label: '总览', icon: 'DataAnalysis' },
    { key: 'production', label: '生产', icon: 'TrendCharts' },
    { key: 'equipment', label: '设备', icon: 'Monitor' },
    { key: 'quality', label: '质量', icon: 'Medal' }
  ]

  // 计算属性
  const healthRadarOptions = computed<EChartsOption>(() => ({
    radar: {
      indicator: [
        { name: '生产效率', max: 100 },
        { name: '设备健康', max: 100 },
        { name: '质量水平', max: 100 },
        { name: '数据质量', max: 100 },
        { name: '系统稳定', max: 100 }
      ],
      radius: '60%',
      axisName: {
        color: 'var(--color-text-secondary)'
      },
      splitLine: {
        lineStyle: {
          color: 'var(--color-border-light)'
        }
      },
      splitArea: {
        areaStyle: {
          color: ['transparent', 'var(--color-bg-secondary)']
        }
      }
    },
    series: [
      {
        name: '系统健康度',
        type: 'radar',
        data: [
          {
            value: [
              Math.min(productionKPI.value?.oeeRate || 80, 100),
              Math.min(
                (equipmentSummary.value.running / equipmentSummary.value.total) * 100 || 80,
                100
              ),
              Math.min(qualityKPI.value?.overallYield || 95, 100),
              95, // 数据质量
              connected.value ? 95 : 60 // 系统稳定性
            ],
            name: '当前状态',
            itemStyle: {
              color: '#1890ff'
            },
            areaStyle: {
              color: 'rgba(24, 144, 255, 0.2)'
            }
          }
        ]
      }
    ]
  }))

  // 方法
  const updateTime = () => {
    currentTime.value = new Date().toLocaleTimeString()
    lastUpdateTime.value = new Date().toLocaleString()
  }

  const getSystemStatusDescription = (): string => {
    const status = systemStatus.value?.overallHealth
    const descriptions = {
      healthy: '系统运行正常',
      warning: '存在轻微问题',
      critical: '存在严重问题'
    }
    return descriptions[status as keyof typeof descriptions] || '状态未知'
  }

  const getProductionStatusValue = (): number => {
    const status = systemStatus.value?.productionStatus
    const values = {
      normal: 100,
      ahead_schedule: 110,
      behind_schedule: 85,
      stopped: 0
    }
    return values[status as keyof typeof values] || 50
  }

  const getProductionStatusText = (): string => {
    const status = systemStatus.value?.productionStatus
    const texts = {
      normal: '正常生产',
      ahead_schedule: '超前完成',
      behind_schedule: '进度滞后',
      stopped: '生产停止'
    }
    return texts[status as keyof typeof texts] || '状态未知'
  }

  const getMessageSourceName = (source: string): string => {
    const names = {
      production: '生产',
      equipment: '设备',
      quality: '质量',
      system: '系统'
    }
    return names[source as keyof typeof names] || source
  }

  const formatMessageTime = (timestamp: string): string => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffMinutes < 1) return '刚刚'
    if (diffMinutes < 60) return `${diffMinutes}分钟前`

    const diffHours = Math.floor(diffMinutes / 60)
    if (diffHours < 24) return `${diffHours}小时前`

    return date.toLocaleDateString()
  }

  const toggleFullscreen = () => {
    if (config.displayMode === 'fullscreen') {
      exitFullscreen()
    } else {
      config.displayMode = 'fullscreen'
      document.documentElement.requestFullscreen?.()
    }
  }

  const exitFullscreen = () => {
    config.displayMode = 'normal'
    document.exitFullscreen?.()
  }

  const togglePresentationMode = () => {
    if (config.displayMode === 'presentation') {
      config.displayMode = 'normal'
    } else {
      config.displayMode = 'presentation'
    }
  }

  const exportCurrentData = () => {
    const panelMap = {
      overview: 'all',
      production: 'production',
      equipment: 'equipment',
      quality: 'quality'
    }
    const dataType = panelMap[config.currentPanel as keyof typeof panelMap] || 'all'
    exportData(dataType as any)
    ElMessage.success('数据导出成功')
  }

  const handleSettingCommand = async (command: string) => {
    switch (command) {
      case 'sound':
        config.soundAlertEnabled = !config.soundAlertEnabled
        ElMessage.success(`声音提醒已${config.soundAlertEnabled ? '开启' : '关闭'}`)
        break
      case 'autoRefresh':
        if (config.refreshInterval > 0) {
          setRefreshInterval(0 as any)
          ElMessage.success('自动刷新已停止')
        } else {
          setRefreshInterval(10)
          ElMessage.success('自动刷新已启动')
        }
        break
      case 'reset':
        try {
          await ElMessageBox.confirm('确定要重置布局吗？', '确认操作', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })

          // 重置配置到默认值
          config.currentPanel = 'overview'
          config.displayMode = 'normal'
          config.refreshInterval = 10
          config.timeRange = '24h'

          ElMessage.success('布局已重置')
        } catch {
          // 用户取消操作
        }
        break
    }
  }

  // 键盘快捷键处理
  const handleKeydown = (event: KeyboardEvent) => {
    // ESC键退出全屏或演示模式
    if (event.key === 'Escape') {
      if (config.displayMode === 'fullscreen') {
        exitFullscreen()
      } else if (config.displayMode === 'presentation') {
        config.displayMode = 'normal'
      }
    }

    // 数字键切换面板
    if (event.key >= '1' && event.key <= '4') {
      const panelIndex = parseInt(event.key) - 1
      if (panels[panelIndex]) {
        switchPanel(panels[panelIndex].key as any)
      }
    }

    // F11全屏
    if (event.key === 'F11') {
      event.preventDefault()
      toggleFullscreen()
    }

    // F5刷新
    if (event.key === 'F5') {
      event.preventDefault()
      refreshAllData()
    }
  }

  // 生命周期
  let timeInterval: number

  onMounted(() => {
    // 初始化时间显示
    updateTime()
    timeInterval = window.setInterval(updateTime, 1000)

    // 绑定键盘事件
    document.addEventListener('keydown', handleKeydown)

    // 监听全屏变化
    document.addEventListener('fullscreenchange', () => {
      if (!document.fullscreenElement) {
        config.displayMode = 'normal'
      }
    })
  })

  onUnmounted(() => {
    if (timeInterval) {
      clearInterval(timeInterval)
    }
    document.removeEventListener('keydown', handleKeydown)
  })
</script>

<style lang="scss" scoped>
  .monitoring-center {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    background: var(--color-bg-page);

    &.fullscreen-mode {
      position: fixed;
      top: 0;
      left: 0;
      z-index: 9999;
      width: 100vw;
      height: 100vh;
    }
  }

  .control-bar {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-3) var(--spacing-4);
    background: var(--color-bg-primary);
    border-bottom: 1px solid var(--color-border-light);

    .control-left,
    .control-right {
      display: flex;
      gap: var(--spacing-3);
      align-items: center;
    }

    .control-center {
      display: flex;
      flex: 1;
      justify-content: center;
    }

    .system-status,
    .connection-status {
      padding: 0 var(--spacing-2);
    }

    .data-timestamp {
      display: flex;
      gap: var(--spacing-1);
      align-items: center;
      font-family: Monaco, Menlo, monospace;
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .refresh-controls,
    .display-controls {
      display: flex;
      gap: var(--spacing-2);
      align-items: center;
    }
  }

  .content-area {
    flex: 1;
    padding: var(--spacing-4);
    overflow: auto;

    .fullscreen-mode & {
      padding: var(--spacing-6);
    }
  }

  .overview-panel {
    height: 100%;

    .overview-grid {
      display: grid;
      grid-template-rows: auto 1fr;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-4);
      height: 100%;

      @media (width <= 1400px) {
        grid-template-rows: auto auto auto auto;
        grid-template-columns: 1fr;
      }

      .status-cards {
        grid-column: 1 / -1;

        .card-row {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: var(--spacing-4);
        }
      }

      .quick-stats {
        padding: var(--spacing-5);
        background: var(--color-bg-primary);
        border: 1px solid var(--color-border-light);
        border-radius: var(--radius-lg);

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: var(--spacing-4);

          .stat-item {
            padding: var(--spacing-3);
            text-align: center;
            background: var(--color-bg-secondary);
            border-radius: var(--radius-base);

            .stat-label {
              margin-bottom: var(--spacing-2);
              font-size: var(--font-size-sm);
              color: var(--color-text-secondary);
            }

            .stat-value {
              font-size: 1.8rem;
              font-weight: var(--font-weight-bold);
              line-height: 1.2;
              color: var(--color-primary);
            }

            .stat-unit {
              margin-top: var(--spacing-1);
              font-size: var(--font-size-sm);
              color: var(--color-text-secondary);
            }
          }
        }
      }

      .health-radar,
      .message-stream {
        overflow: hidden;
        background: var(--color-bg-primary);
        border: 1px solid var(--color-border-light);
        border-radius: var(--radius-lg);
      }

      .message-stream {
        .stream-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: var(--spacing-4);
          border-bottom: 1px solid var(--color-border-lighter);

          .stream-title {
            display: flex;
            align-items: center;
            margin: 0;
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--color-text-primary);

            .el-icon {
              margin-right: var(--spacing-2);
              color: var(--color-primary);
            }
          }
        }

        .message-list {
          max-height: 400px;
          overflow-y: auto;

          .message-item {
            padding: var(--spacing-3);
            border-bottom: 1px solid var(--color-border-lighter);
            transition: all 0.3s ease;

            &.unread {
              background: var(--color-primary-light);
              border-left: 4px solid var(--color-primary);
            }

            &:hover {
              background: var(--color-bg-secondary);
            }

            .message-header {
              display: flex;
              align-items: center;
              margin-bottom: var(--spacing-1);

              .el-icon {
                margin-right: var(--spacing-2);

                &.icon-info {
                  color: var(--color-info);
                }

                &.icon-warning {
                  color: var(--color-warning);
                }

                &.icon-error {
                  color: var(--color-danger);
                }

                &.icon-success {
                  color: var(--color-success);
                }
              }

              .message-source {
                font-size: var(--font-size-sm);
                font-weight: var(--font-weight-medium);
                color: var(--color-text-primary);
              }

              .message-time {
                margin-left: auto;
                font-size: var(--font-size-xs);
                color: var(--color-text-tertiary);
              }
            }

            .message-content {
              padding-left: var(--spacing-6);
              font-size: var(--font-size-sm);
              line-height: 1.4;
              color: var(--color-text-secondary);
            }
          }

          .no-messages {
            padding: var(--spacing-8) var(--spacing-4);
            text-align: center;
          }
        }
      }
    }
  }

  .status-bar {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    background: var(--color-bg-secondary);
    border-top: 1px solid var(--color-border-light);

    .status-left,
    .status-right {
      display: flex;
      gap: var(--spacing-4);
      align-items: center;
    }

    .status-item {
      display: flex;
      gap: var(--spacing-1);
      align-items: center;
    }
  }

  .fullscreen-exit {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
  }

  // 深色主题适配
  .dark {
    .control-bar {
      background: var(--color-bg-secondary);
      border-bottom-color: var(--color-border-dark);
    }

    .status-bar {
      background: var(--color-bg-tertiary);
      border-top-color: var(--color-border-dark);
    }

    .overview-grid {
      .quick-stats,
      .health-radar,
      .message-stream {
        background: var(--color-bg-secondary);
        border-color: var(--color-border-dark);
      }

      .quick-stats .stat-item {
        background: var(--color-bg-tertiary);
      }

      .message-stream .stream-header {
        border-bottom-color: var(--color-border-dark);
      }
    }
  }

  // 演示模式样式
  .monitoring-center {
    &:has(.content-area) {
      .content-area {
        padding: 0;
      }
    }
  }

  // 响应式设计
  @media (width <= 768px) {
    .control-bar {
      flex-direction: column;
      gap: var(--spacing-2);
      align-items: stretch;

      .control-left,
      .control-center,
      .control-right {
        justify-content: center;
      }
    }

    .overview-grid {
      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
  }
</style>
