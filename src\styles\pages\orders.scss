// IC封测CIM系统 - 订单管理页面样式

.orders-page {
  padding: var(--spacing-6);
  
  &__header {
    margin-bottom: var(--spacing-6);
  }
  
  &__filters {
    background-color: var(--color-card-bg);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-5);
    margin-bottom: var(--spacing-6);
    
    .filter-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-4);
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  &__table {
    background-color: var(--color-card-bg);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    overflow: hidden;
  }
}

.order-status {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  
  &--pending {
    background-color: var(--color-warning);
    color: var(--color-text-inverse);
  }
  
  &--processing {
    background-color: var(--color-primary);
    color: var(--color-text-inverse);
  }
  
  &--completed {
    background-color: var(--color-success);
    color: var(--color-text-inverse);
  }
  
  &--cancelled {
    background-color: var(--color-error);
    color: var(--color-text-inverse);
  }
}