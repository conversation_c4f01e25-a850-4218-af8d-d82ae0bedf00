// 物料与库存管理类型定义 - OSAT专业版
// 严格按照第一阶段规划：半导体专用仓库管理 (ESD-safe, temperature-controlled)

export enum MaterialCategory {
  // 半导体专用物料分类
  WAFER = 'wafer', // 晶圆
  DIE_BANK = 'die_bank', // 芯片库
  LEADFRAME = 'leadframe', // 引脚框架
  SUBSTRATE = 'substrate', // 基板
  PACKAGING_MATERIAL = 'packaging_material', // 封装材料
  CHEMICAL = 'chemical', // 化学品
  CONSUMABLE = 'consumable', // 易耗品
  ADHESIVE = 'adhesive', // 胶粘剂
  WIRE_BOND = 'wire_bond', // 键合金线
  MOLD_COMPOUND = 'mold_compound' // 塑封料
}

export enum StorageCondition {
  // ESD安全和温控要求
  ESD_SAFE = 'esd_safe', // ESD安全区域
  TEMPERATURE_CONTROLLED = 'temperature_controlled', // 温控环境
  HUMIDITY_CONTROLLED = 'humidity_controlled', // 湿度控制
  CLEAN_ROOM = 'clean_room', // 洁净室要求
  NITROGEN_CABINET = 'nitrogen_cabinet', // 氮气柜
  VACUUM_SEALED = 'vacuum_sealed' // 真空密封
}

export enum MaterialStatus {
  INCOMING = 'incoming', // 来料
  INSPECTED = 'inspected', // 已检验
  APPROVED = 'approved', // 已放行
  IN_USE = 'in_use', // 使用中
  RESERVED = 'reserved', // 已预留
  QUARANTINE = 'quarantine', // 隔离
  EXPIRED = 'expired', // 过期
  CONSUMED = 'consumed' // 已消耗
}

export interface MaterialSpecification {
  // 物料规格参数
  materialCode: string // 物料代码
  materialName: string // 物料名称
  specification: string // 规格型号
  manufacturer: string // 制造商
  lotNumber?: string // 批次号
  serialNumber?: string // 序列号
  expiryDate?: string // 有效期
  storageRequirements: StorageCondition[] // 存储要求
}

export interface DieBank {
  // Die Bank专业管理
  dieBankId: string // Die Bank ID
  waferLot: string // 晶圆批次
  dieType: string // 芯片类型
  totalDieCount: number // 总芯片数量
  availableDieCount: number // 可用芯片数量
  dieSize: {
    // 芯片尺寸
    width: number // 宽度(mm)
    height: number // 高度(mm)
    thickness: number // 厚度(μm)
  }
  binMap: DieBinInfo[] // 芯片分选图
  storageLocation: string // 存储位置
  temperature: number // 存储温度
  humidity: number // 存储湿度
  lastAccessTime: string // 最后访问时间
}

export interface DieBinInfo {
  // 芯片分选信息
  binCode: string // 分选代码
  binDescription: string // 分选描述
  dieCount: number // 该等级芯片数量
  qualityGrade: 'A' | 'B' | 'C' | 'D' // 质量等级
  testResults?: {
    // 测试结果
    electricalTest: boolean // 电测结果
    visualInspection: boolean // 外观检查
    reliability: number // 可靠性分数
  }
}

export interface WarehouseZone {
  // 半导体专用仓库区域
  zoneId: string // 区域ID
  zoneName: string // 区域名称
  zoneType: 'ESD_SAFE' | 'TEMP_CONTROLLED' | 'CLEAN_ROOM' | 'CHEMICAL'
  capacity: number // 容量
  currentUtilization: number // 当前利用率
  environmentalConditions: {
    temperature: number // 温度(°C)
    humidity: number // 湿度(%)
    cleanliness: string // 洁净度等级
    esdCompliance: boolean // ESD合规性
  }
  allowedMaterials: MaterialCategory[] // 允许存放的物料类型
  restrictions: string[] // 存储限制
}

export interface MaterialInventory {
  // 库存记录
  inventoryId: string // 库存ID
  materialSpec: MaterialSpecification // 物料规格
  category: MaterialCategory // 物料分类
  currentStock: number // 当前库存
  reservedStock: number // 预留库存
  availableStock: number // 可用库存
  reorderPoint: number // 再订货点
  maxStock: number // 最大库存
  unitOfMeasure: string // 计量单位
  unitCost: number // 单位成本
  totalValue: number // 总价值
  warehouseZone: WarehouseZone // 存储区域
  status: MaterialStatus // 状态
  lastMovement: {
    // 最后变动
    date: string // 变动日期
    type: 'IN' | 'OUT' | 'TRANSFER' // 变动类型
    quantity: number // 变动数量
    reason: string // 变动原因
    operator: string // 操作员
  }
  supplier: {
    // 供应商信息
    supplierId: string // 供应商ID
    supplierName: string // 供应商名称
    leadTime: number // 供货周期(天)
    qualityRating: number // 质量评级
  }
}

export interface MaterialTransaction {
  // 物料事务
  transactionId: string // 事务ID
  transactionType: 'RECEIPT' | 'ISSUE' | 'TRANSFER' | 'ADJUSTMENT' | 'RETURN'
  materialCode: string // 物料代码
  quantity: number // 数量
  fromLocation?: string // 源位置
  toLocation?: string // 目标位置
  workOrder?: string // 关联工单
  operator: string // 操作员
  timestamp: string // 时间戳
  approver?: string // 审批人
  notes?: string // 备注
  batchNumber?: string // 批次号
  qualityInspection?: {
    // 质量检验
    inspector: string // 检验员
    inspectionDate: string // 检验日期
    result: 'PASS' | 'FAIL' | 'PENDING' // 检验结果
    remarks?: string // 检验备注
  }
}

export interface SupplierPerformance {
  // 供应商绩效
  supplierId: string // 供应商ID
  supplierName: string // 供应商名称
  performancePeriod: {
    // 绩效周期
    startDate: string // 开始日期
    endDate: string // 结束日期
  }
  kpi: {
    // 关键绩效指标
    onTimeDelivery: number // 准时交货率(%)
    qualityRating: number // 质量评级(%)
    responseTime: number // 响应时间(小时)
    costPerformance: number // 成本绩效(%)
    defectRate: number // 缺陷率(ppm)
    returnRate: number // 退货率(%)
  }
  totalOrders: number // 总订单数
  onTimeOrders: number // 准时订单数
  qualityIssues: number // 质量问题数
  totalValue: number // 总采购金额
  averageLeadTime: number // 平均供货周期
  certifications: string[] // 认证证书
}

// API响应类型
export interface MaterialResponse {
  data: MaterialInventory[]
  total: number
  page: number
  pageSize: number
}

export interface TransactionResponse {
  data: MaterialTransaction[]
  total: number
  page: number
  pageSize: number
}

export interface SupplierResponse {
  data: SupplierPerformance[]
  total: number
  page: number
  pageSize: number
}

// 查询参数类型
export interface MaterialQueryParams {
  category?: MaterialCategory
  status?: MaterialStatus
  warehouseZone?: string
  lowStock?: boolean
  expired?: boolean
  search?: string
  page?: number
  pageSize?: number
}

export interface TransactionQueryParams {
  transactionType?: string
  materialCode?: string
  dateFrom?: string
  dateTo?: string
  operator?: string
  page?: number
  pageSize?: number
}
