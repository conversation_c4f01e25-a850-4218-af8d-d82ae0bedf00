<template>
  <div class="product-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="readonly"
    >
      <!-- 基本信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <h3>基本信息</h3>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品代码" prop="productCode">
              <el-input
                v-model="formData.productCode"
                placeholder="请输入产品代码"
                :readonly="readonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input
                v-model="formData.productName"
                placeholder="请输入产品名称"
                :readonly="readonly"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品分类" prop="category">
              <el-select
                v-model="formData.category"
                placeholder="请选择产品分类"
                style="width: 100%"
                :disabled="readonly"
              >
                <el-option
                  v-for="option in productCategoryOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="应用领域" prop="applicationField">
              <el-select
                v-model="formData.applicationField"
                placeholder="请选择应用领域"
                style="width: 100%"
                :disabled="readonly"
              >
                <el-option
                  v-for="option in applicationFieldOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户名称" prop="customerCode">
              <CustomerSelector
                v-model="formData.customerCode"
                :disabled="readonly"
                @change="handleCustomerChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品状态" prop="status">
              <el-select
                v-model="formData.status"
                placeholder="请选择产品状态"
                style="width: 100%"
                :disabled="readonly"
              >
                <el-option
                  v-for="option in productStatusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="产品描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入产品描述"
            :readonly="readonly"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品特性">
              <el-select
                v-model="formData.features"
                multiple
                filterable
                allow-create
                placeholder="请输入产品特性"
                style="width: 100%"
                :disabled="readonly"
              >
                <el-option
                  v-for="feature in commonFeatures"
                  :key="feature"
                  :label="feature"
                  :value="feature"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主要应用">
              <el-select
                v-model="formData.applications"
                multiple
                filterable
                allow-create
                placeholder="请输入主要应用"
                style="width: 100%"
                :disabled="readonly"
              >
                <el-option
                  v-for="app in commonApplications"
                  :key="app"
                  :label="app"
                  :value="app"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 技术规格 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <h3>技术规格</h3>
        </template>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="工艺节点" prop="technicalSpec.processNode">
              <el-select
                v-model="formData.technicalSpec.processNode"
                placeholder="请选择工艺节点"
                style="width: 100%"
                :disabled="readonly"
              >
                <el-option
                  v-for="option in processNodeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Die尺寸X" prop="technicalSpec.dieSizeX">
              <el-input-number
                v-model="formData.technicalSpec.dieSizeX"
                :min="0.1"
                :max="50"
                :precision="2"
                :step="0.1"
                style="width: 100%"
                :disabled="readonly"
              />
              <span class="unit">mm</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Die尺寸Y" prop="technicalSpec.dieSizeY">
              <el-input-number
                v-model="formData.technicalSpec.dieSizeY"
                :min="0.1"
                :max="50"
                :precision="2"
                :step="0.1"
                style="width: 100%"
                :disabled="readonly"
              />
              <span class="unit">mm</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="引脚数" prop="technicalSpec.pinCount">
              <el-input-number
                v-model="formData.technicalSpec.pinCount"
                :min="1"
                :max="5000"
                :step="1"
                style="width: 100%"
                :disabled="readonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="封装尺寸X" prop="technicalSpec.packageSizeX">
              <el-input-number
                v-model="formData.technicalSpec.packageSizeX"
                :min="1"
                :max="100"
                :precision="2"
                :step="0.1"
                style="width: 100%"
                :disabled="readonly"
              />
              <span class="unit">mm</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="封装尺寸Y" prop="technicalSpec.packageSizeY">
              <el-input-number
                v-model="formData.technicalSpec.packageSizeY"
                :min="1"
                :max="100"
                :precision="2"
                :step="0.1"
                style="width: 100%"
                :disabled="readonly"
              />
              <span class="unit">mm</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="封装高度" prop="technicalSpec.packageHeight">
              <el-input-number
                v-model="formData.technicalSpec.packageHeight"
                :min="0.1"
                :max="10"
                :precision="2"
                :step="0.1"
                style="width: 100%"
                :disabled="readonly"
              />
              <span class="unit">mm</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工作温度范围">
              <div class="temp-range">
                <el-input-number
                  v-model="formData.technicalSpec.operatingTempMin"
                  :min="-200"
                  :max="200"
                  style="width: 45%"
                  :disabled="readonly"
                />
                <span>~</span>
                <el-input-number
                  v-model="formData.technicalSpec.operatingTempMax"
                  :min="-200"
                  :max="200"
                  style="width: 45%"
                  :disabled="readonly"
                />
                <span class="unit">°C</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="供电电压范围">
              <div class="voltage-range">
                <el-input-number
                  v-model="formData.technicalSpec.supplyVoltageMin"
                  :min="0"
                  :max="100"
                  :precision="2"
                  :step="0.1"
                  style="width: 45%"
                  :disabled="readonly"
                />
                <span>~</span>
                <el-input-number
                  v-model="formData.technicalSpec.supplyVoltageMax"
                  :min="0"
                  :max="100"
                  :precision="2"
                  :step="0.1"
                  style="width: 45%"
                  :disabled="readonly"
                />
                <span class="unit">V</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="功耗" prop="technicalSpec.powerConsumption">
              <el-input-number
                v-model="formData.technicalSpec.powerConsumption"
                :min="0"
                :max="1000"
                :precision="3"
                :step="0.1"
                style="width: 100%"
                :disabled="readonly"
              />
              <span class="unit">W</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="频率" prop="technicalSpec.frequency">
              <el-input-number
                v-model="formData.technicalSpec.frequency"
                :min="0"
                :max="10000"
                :precision="1"
                :step="1"
                style="width: 100%"
                :disabled="readonly"
              />
              <span class="unit">MHz</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 封装信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <h3>封装信息</h3>
        </template>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="封装类型" prop="packageInfo.packageType">
              <el-select
                v-model="formData.packageInfo.packageType"
                placeholder="请选择封装类型"
                style="width: 100%"
                :disabled="readonly"
              >
                <el-option
                  v-for="option in packageTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="基板类型" prop="packageInfo.substrateType">
              <el-select
                v-model="formData.packageInfo.substrateType"
                placeholder="请选择基板类型"
                style="width: 100%"
                :disabled="readonly"
              >
                <el-option label="FR4" value="FR4" />
                <el-option label="陶瓷" value="CERAMIC" />
                <el-option label="玻璃" value="GLASS" />
                <el-option label="柔性板" value="FLEX" />
                <el-option label="刚柔结合板" value="RIGID_FLEX" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="金线规格" prop="packageInfo.wireBondType">
              <el-select
                v-model="formData.packageInfo.wireBondType"
                placeholder="请选择金线规格"
                style="width: 100%"
                :disabled="readonly"
              >
                <el-option label="金线15μm" value="AU_15UM" />
                <el-option label="金线20μm" value="AU_20UM" />
                <el-option label="金线25μm" value="AU_25UM" />
                <el-option label="铜线18μm" value="CU_18UM" />
                <el-option label="铜线23μm" value="CU_23UM" />
                <el-option label="铝线25μm" value="AL_25UM" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="引线框架" prop="packageInfo.leadFrame">
              <el-input
                v-model="formData.packageInfo.leadFrame"
                placeholder="请输入引线框架型号"
                :readonly="readonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="封装料" prop="packageInfo.moldingCompound">
              <el-input
                v-model="formData.packageInfo.moldingCompound"
                placeholder="请输入封装料型号"
                :readonly="readonly"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="电镀类型" prop="packageInfo.platingType">
              <el-select
                v-model="formData.packageInfo.platingType"
                placeholder="请选择电镀类型"
                style="width: 100%"
                :disabled="readonly"
              >
                <el-option label="NiPdAu" value="NiPdAu" />
                <el-option label="ENIG" value="ENIG" />
                <el-option label="OSP" value="OSP" />
                <el-option label="HASL" value="HASL" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标识方式" prop="packageInfo.markingMethod">
              <el-select
                v-model="formData.packageInfo.markingMethod"
                placeholder="请选择标识方式"
                style="width: 100%"
                :disabled="readonly"
              >
                <el-option label="激光标记" value="激光标记" />
                <el-option label="印刷标记" value="印刷标记" />
                <el-option label="热转印" value="热转印" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 管理信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <h3>管理信息</h3>
        </template>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="负责工程师" prop="engineerName">
              <el-input
                v-model="formData.engineerName"
                placeholder="请输入负责工程师"
                :readonly="readonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="负责商务" prop="businessName">
              <el-input
                v-model="formData.businessName"
                placeholder="请输入负责商务"
                :readonly="readonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="版本号" prop="version">
              <el-input
                v-model="formData.version"
                placeholder="请输入版本号"
                :readonly="readonly || isEdit"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效日期" prop="effectiveDate">
              <el-date-picker
                v-model="formData.effectiveDate"
                type="date"
                placeholder="请选择生效日期"
                style="width: 100%"
                :disabled="readonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="失效日期">
              <el-date-picker
                v-model="formData.expiryDate"
                type="date"
                placeholder="请选择失效日期（可选）"
                style="width: 100%"
                :disabled="readonly"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item v-if="isEdit" label="变更说明" prop="changeDescription">
          <el-input
            v-model="formData.changeDescription"
            type="textarea"
            :rows="2"
            placeholder="请输入变更说明"
            :readonly="readonly"
          />
        </el-form-item>
      </el-card>
    </el-form>

    <!-- 操作按钮 -->
    <div v-if="!readonly" class="form-footer">
      <el-button size="large" @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        size="large"
        :loading="saveLoading"
        @click="handleSave"
      >
        {{ isEdit ? '更新' : '保存' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
// Element Plus组件通过unplugin-auto-import自动导入

import type {
  Product,
  CreateProductData,
  ICProductCategory,
  ApplicationField,
  ProductStatus,
  ProcessNode,
  PackageType,
  SubstrateType,
  WireBondType,
  TechnicalSpecification,
  PackageInfo
} from '@/types/product'

import {
  productCategoryOptions,
  applicationFieldOptions,
  productStatusOptions,
  processNodeOptions,
  packageTypeOptions
} from '@/utils/mockData/products'

import CustomerSelector from '@/components/business/CustomerSelector.vue'

// ================================
// Props & Emits
// ================================

interface Props {
  product?: Product | null
  readonly?: boolean
}

interface Emits {
  (e: 'save', product: CreateProductData): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  product: null,
  readonly: false
})

const emit = defineEmits<Emits>()

// ================================
// 响应式数据
// ================================

const formRef = ref()
const saveLoading = ref(false)

// 判断是否编辑模式
const isEdit = computed(() => {
  return props.product && props.product.id
})

// 表单数据
const formData = reactive<CreateProductData & { 
  changeDescription?: string
  expiryDate?: Date 
}>({
  productCode: '',
  productName: '',
  customerCode: '',
  category: ICProductCategory.MCU,
  applicationField: ApplicationField.CONSUMER,
  description: '',
  features: [],
  applications: [],
  technicalSpec: {
    processNode: ProcessNode.N_28NM,
    dieSizeX: 5.0,
    dieSizeY: 5.0,
    pinCount: 64,
    packageSizeX: 10.0,
    packageSizeY: 10.0,
    packageHeight: 1.0,
    operatingTempMin: -40,
    operatingTempMax: 85,
    supplyVoltageMin: 3.0,
    supplyVoltageMax: 3.6,
    powerConsumption: 0.1,
    frequency: 100
  } as TechnicalSpecification,
  packageInfo: {
    packageType: PackageType.QFP,
    substrateType: SubstrateType.FR4,
    wireBondType: WireBondType.AU_25UM,
    leadFrame: '',
    moldingCompound: '',
    platingType: '',
    markingMethod: ''
  } as PackageInfo,
  cpTestParams: {
    testTemperature: [25, 85],
    testVoltage: [3.0, 3.3, 3.6],
    testCurrent: [0.001, 0.1],
    testItems: [],
    yieldTarget: 99.0,
    testTime: 0.2
  },
  ftTestParams: {
    functionalTests: [],
    electricalTests: [],
    thermalTests: [],
    reliabilityTests: [],
    burnInHours: 48,
    burnInTemp: 125,
    finalYieldTarget: 98.0
  },
  qualityReq: {
    defectRate: 10,
    reliabilityLevel: 'Grade 1',
    certificationStandards: [],
    qualityGrade: 'Consumer',
    inspectionLevel: 'S3'
  },
  costParams: {
    dieCost: 0,
    substrateCost: 0,
    wireCost: 0,
    moldingCost: 0,
    cpTestCost: 0,
    assemblyCost: 0,
    ftTestCost: 0,
    packagingCost: 0,
    shippingCost: 0,
    overheadRate: 15,
    profitMargin: 25
  },
  engineerName: '',
  businessName: '',
  version: 'V1.0',
  effectiveDate: new Date()
})

// 常用选项
const commonFeatures = [
  '低功耗', '高性能', '高集成度', '高可靠性', '宽温度范围',
  '小封装', '多核心', '内置存储', '丰富接口', 'EMC优化'
]

const commonApplications = [
  '智能手机', '平板电脑', '笔记本电脑', '台式电脑', '服务器',
  '汽车电子', '工业控制', '物联网设备', '可穿戴设备', '智能家居'
]

// 表单验证规则
const formRules = {
  productCode: [
    { required: true, message: '请输入产品代码', trigger: 'blur' },
    { pattern: /^[A-Z0-9\-_]+$/, message: '产品代码只能包含大写字母、数字、横线和下划线', trigger: 'blur' }
  ],
  productName: [
    { required: true, message: '请输入产品名称', trigger: 'blur' },
    { min: 2, max: 100, message: '产品名称长度在2到100个字符之间', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择产品分类', trigger: 'change' }
  ],
  applicationField: [
    { required: true, message: '请选择应用领域', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入产品描述', trigger: 'blur' },
    { min: 10, max: 500, message: '产品描述长度在10到500个字符之间', trigger: 'blur' }
  ],
  'technicalSpec.processNode': [
    { required: true, message: '请选择工艺节点', trigger: 'change' }
  ],
  'technicalSpec.dieSizeX': [
    { required: true, message: '请输入Die尺寸X', trigger: 'blur' },
    { type: 'number', min: 0.1, max: 50, message: '尺寸范围0.1-50mm', trigger: 'blur' }
  ],
  'technicalSpec.dieSizeY': [
    { required: true, message: '请输入Die尺寸Y', trigger: 'blur' },
    { type: 'number', min: 0.1, max: 50, message: '尺寸范围0.1-50mm', trigger: 'blur' }
  ],
  'technicalSpec.pinCount': [
    { required: true, message: '请输入引脚数', trigger: 'blur' },
    { type: 'number', min: 1, max: 5000, message: '引脚数范围1-5000', trigger: 'blur' }
  ],
  'packageInfo.packageType': [
    { required: true, message: '请选择封装类型', trigger: 'change' }
  ],
  engineerName: [
    { required: true, message: '请输入负责工程师', trigger: 'blur' }
  ],
  businessName: [
    { required: true, message: '请输入负责商务', trigger: 'blur' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
    { pattern: /^V\d+\.\d+$/, message: '版本号格式应为V1.0', trigger: 'blur' }
  ]
}

// ================================
// 方法定义
// ================================

// 初始化表单数据
const initFormData = () => {
  if (props.product) {
    // 编辑模式，填充现有数据
    Object.assign(formData, {
      productCode: props.product.productCode,
      productName: props.product.productName,
      customerCode: props.product.customerCode,
      category: props.product.category,
      applicationField: props.product.applicationField,
      description: props.product.description,
      features: [...props.product.features],
      applications: [...props.product.applications],
      technicalSpec: { ...props.product.technicalSpec },
      packageInfo: { ...props.product.packageInfo },
      cpTestParams: { ...props.product.cpTestParams },
      ftTestParams: { ...props.product.ftTestParams },
      qualityReq: { ...props.product.qualityReq },
      costParams: { ...props.product.costParams },
      engineerName: props.product.engineerName,
      businessName: props.product.businessName,
      version: props.product.version,
      effectiveDate: props.product.effectiveDate,
      expiryDate: props.product.expiryDate
    })
  }
}

// 客户变化处理
const handleCustomerChange = (customerCode: string) => {
  // 可以根据客户更新一些默认值
}

// 保存处理
const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saveLoading.value = true
    
    // 准备保存数据
    const saveData: CreateProductData = {
      productCode: formData.productCode,
      productName: formData.productName,
      customerCode: formData.customerCode,
      category: formData.category,
      applicationField: formData.applicationField,
      description: formData.description,
      features: formData.features,
      applications: formData.applications,
      technicalSpec: formData.technicalSpec,
      packageInfo: formData.packageInfo,
      cpTestParams: formData.cpTestParams,
      ftTestParams: formData.ftTestParams,
      qualityReq: formData.qualityReq,
      costParams: formData.costParams,
      engineerName: formData.engineerName,
      businessName: formData.businessName,
      effectiveDate: formData.effectiveDate
    }
    
    emit('save', saveData)
  } catch (error) {
    ElMessage.error('请检查表单输入')
  } finally {
    saveLoading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  emit('cancel')
}

// ================================
// 生命周期
// ================================

onMounted(() => {
  initFormData()
})

// 监听产品数据变化
watch(
  () => props.product,
  () => {
    initFormData()
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.product-form {
  .form-section {
    margin-bottom: var(--spacing-4);
    border: none;

    :deep(.el-card__header) {
      padding: var(--spacing-3) var(--spacing-4);
      background-color: var(--color-bg-tertiary);

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--color-text-primary);
      }
    }

    :deep(.el-card__body) {
      padding: var(--spacing-4);
    }
  }

  .unit {
    margin-left: var(--spacing-1);
    font-size: 12px;
    color: var(--color-text-secondary);
  }

  .temp-range,
  .voltage-range {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;

    span:not(.unit) {
      color: var(--color-text-secondary);
    }
  }

  .form-footer {
    display: flex;
    gap: var(--spacing-3);
    justify-content: center;
    padding: var(--spacing-4) 0;
    margin-top: var(--spacing-4);
    border-top: 1px solid var(--color-border-light);
  }

  // 只读模式样式
  &:has(.el-form[disabled]) {
    .form-footer {
      display: none;
    }

    :deep(.el-input__wrapper) {
      background-color: var(--color-bg-secondary);
    }

    :deep(.el-select__wrapper) {
      background-color: var(--color-bg-secondary);
    }

    :deep(.el-textarea__inner) {
      background-color: var(--color-bg-secondary);
    }
  }

  // 响应式设计
  @media (width <= 768px) {
    .form-section {
      :deep(.el-col) {
        span: 24 !important;
        margin-bottom: var(--spacing-2);
      }
    }

    .temp-range,
    .voltage-range {
      flex-direction: column;
      gap: var(--spacing-1);
      align-items: stretch;

      .el-input-number {
        width: 100% !important;
      }
    }
  }
}
</style>