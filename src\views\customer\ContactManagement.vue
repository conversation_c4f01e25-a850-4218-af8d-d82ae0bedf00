<template>
  <div class="contact-management">
    <!-- 页面头部 -->
    <div class="contact-management__header">
      <div class="contact-management__header-left">
        <h2 class="contact-management__title">客户联系人管理</h2>
        <p class="contact-management__subtitle">管理IC设计客户的关键联系人信息</p>
      </div>
      <div class="contact-management__header-right">
        <el-button type="primary"
:icon="Plus" @click="handleAddContact"
>
新增联系人
</el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="contact-management__search-card"
shadow="never">
      <el-form :model="searchForm"
class="contact-management__search-form" inline>
        <el-form-item label="关键词搜索">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索联系人姓名、邮箱、电话"
            :prefix-icon="Search"
            clearable
            style="width: 240px"
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-form-item>

        <el-form-item label="所属客户">
          <el-select
            v-model="searchForm.customerId"
            placeholder="选择客户公司"
            clearable
            filterable
            style="width: 200px"
          >
            <el-option
              v-for="customer in customerOptions"
              :key="customer.id"
              :label="customer.name"
              :value="customer.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="联系人角色">
          <el-select
            v-model="searchForm.roles"
            placeholder="选择角色"
            multiple
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="role in contactRoleOptions"
              :key="role.value"
              :label="role.label"
              :value="role.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="联系人类型">
          <el-select
            v-model="searchForm.contactTypes"
            placeholder="选择类型"
            multiple
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="type in contactTypeOptions"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="重要程度">
          <el-select
            v-model="searchForm.importance"
            placeholder="选择重要程度"
            multiple
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="level in importanceOptions"
              :key="level.value"
              :label="level.label"
              :value="level.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="选择状态"
            multiple
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="status in statusOptions"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary"
:icon="Search" @click="handleSearch"
>
搜索
</el-button>
          <el-button :icon="Refresh"
@click="handleReset"
>
重置
</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 联系人列表 -->
    <el-card class="contact-management__table-card"
shadow="never">
      <!-- 表格工具栏 -->
      <div class="contact-management__table-toolbar">
        <div class="contact-management__table-toolbar-left">
          <el-checkbox
            v-model="selectAll"
            :indeterminate="isIndeterminate"
            @change="handleSelectAll"
          >
            全选
          </el-checkbox>
          <el-button
            v-if="selectedContacts.length > 0"
            type="danger"
            :icon="Delete"
            @click="handleBatchDelete"
          >
            批量删除 ({{ selectedContacts.length }})
          </el-button>
          <el-button v-if="selectedContacts.length > 0" :icon="Download" @click="handleExport">
            导出选中
          </el-button>
        </div>
        <div class="contact-management__table-toolbar-right">
          <el-tooltip content="按客户分组显示">
            <el-switch v-model="groupByCustomer" active-text="分组" inactive-text="列表" />
          </el-tooltip>
          <el-tooltip content="刷新列表">
            <el-button :icon="Refresh"
circle @click="fetchContacts" />
          </el-tooltip>
        </div>
      </div>

      <!-- 分组显示 -->
      <div v-if="groupByCustomer"
class="contact-management__grouped-list">
        <el-collapse v-model="activeGroups">
          <el-collapse-item
            v-for="group in groupedContacts"
            :key="group.customerId"
            :title="`${group.customerName} (${group.contacts.length}人)`"
            :name="group.customerId"
          >
            <template #title>
              <div class="contact-management__group-title">
                <span class="contact-management__group-name">{{ group.customerName }}</span>
                <el-tag
type="info" size="small">{{ group.contacts.length }}人</el-tag>
              </div>
            </template>

            <div class="contact-management__group-content">
              <div
                v-for="contact in group.contacts"
                :key="contact.id"
                class="contact-management__contact-card"
                @click="handleViewContact(contact)"
              >
                <div class="contact-management__contact-card-header">
                  <el-checkbox
                    :model-value="selectedContacts.includes(contact.id)"
                    @change="val => handleContactSelect(contact.id, val as boolean)"
                    @click.stop
                  />
                  <el-avatar :size="40"
:src="contact.avatar">
                    {{ contact.name.charAt(0) }}
                  </el-avatar>
                  <div class="contact-management__contact-info">
                    <div class="contact-management__contact-name">
                      {{ contact.name }}
                      <el-tag v-if="contact.isPrimaryContact"
type="primary" size="small">
                        主要
                      </el-tag>
                      <el-tag v-if="contact.isDecisionMaker"
type="success" size="small">
                        决策者
                      </el-tag>
                    </div>
                    <div class="contact-management__contact-position">
                      {{ contact.position }} · {{ contact.department }}
                    </div>
                  </div>
                  <div class="contact-management__contact-actions">
                    <el-dropdown @command="command => handleContactAction(command, contact)">
                      <el-button text
:icon="More" />
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item
command="view" :icon="View">查看详情</el-dropdown-item>
                          <el-dropdown-item
command="edit" :icon="Edit">编辑</el-dropdown-item>
                          <el-dropdown-item command="communicate"
:icon="ChatDotRound">
                            沟通记录
                          </el-dropdown-item>
                          <el-dropdown-item command="delete"
:icon="Delete" divided>
                            删除
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>

                <div class="contact-management__contact-card-body">
                  <div class="contact-management__contact-details">
                    <div class="contact-management__contact-detail">
                      <el-icon><Phone /></el-icon>
                      <span>{{ contact.phone }}</span>
                    </div>
                    <div class="contact-management__contact-detail">
                      <el-icon><Message /></el-icon>
                      <span>{{ contact.email }}</span>
                    </div>
                    <div class="contact-management__contact-detail">
                      <el-icon><User /></el-icon>
                      <el-tag size="small">
                        {{ getRoleLabel(contact.role) }}
                      </el-tag>
                      <el-tag
                        v-for="type in contact.contactType"
                        :key="type"
                        size="small"
                        type="info"
                      >
                        {{ getContactTypeLabel(type) }}
                      </el-tag>
                    </div>
                  </div>

                  <div class="contact-management__contact-metrics">
                    <div class="contact-management__metric">
                      <span class="label">重要程度</span>
                      <el-rate
                        :model-value="contact.importance"
                        disabled
                        size="small"
                        :colors="['#ff9900', '#ff9900', '#ff9900']"
                      />
                    </div>
                    <div class="contact-management__metric">
                      <span class="label">影响力</span>
                      <el-progress
                        :percentage="contact.influenceScore * 10"
                        :stroke-width="6"
                        :show-text="false"
                        color="#409EFF"
                      />
                      <span class="value">{{ contact.influenceScore }}/10</span>
                    </div>
                    <div class="contact-management__metric">
                      <span class="label">最后联系</span>
                      <span class="value">
                        {{ formatLastContactDate(contact.lastContactDate) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 表格显示 -->
      <el-table
        v-else
        v-loading="loading"
        :data="contacts"
        stripe
        row-key="id"
        class="contact-management__table"
        @selection-change="handleSelectionChange"
        @row-click="handleViewContact"
      >
        <el-table-column type="selection"
width="50" />

        <el-table-column label="联系人"
min-width="200" fixed="left">
          <template #default="{ row }">
            <div class="contact-management__contact-cell">
              <el-avatar :size="32"
:src="row.avatar">
                {{ row.name.charAt(0) }}
              </el-avatar>
              <div class="contact-management__contact-cell-info">
                <div class="contact-management__contact-cell-name">
                  {{ row.name }}
                  <el-tag
v-if="row.isPrimaryContact" type="primary" size="small">主要</el-tag>
                  <el-tag
v-if="row.isDecisionMaker" type="success" size="small">决策者</el-tag>
                </div>
                <div class="contact-management__contact-cell-position">
                  {{ row.position }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="所属客户"
prop="customerName" width="180" />

        <el-table-column label="部门"
prop="department" width="120" />

        <el-table-column label="联系方式"
width="200">
          <template #default="{ row }">
            <div class="contact-management__contact-methods">
              <div>
                <el-icon><Phone /></el-icon>
                {{ row.phone }}
              </div>
              <div>
                <el-icon><Message /></el-icon>
                {{ row.email }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="角色"
width="120">
          <template #default="{ row }">
            <el-tag size="small">
              {{ getRoleLabel(row.role) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="类型"
width="140">
          <template #default="{ row }">
            <el-tag
              v-for="type in row.contactType"
              :key="type"
              size="small"
              type="info"
              style="margin-right: 4px"
            >
              {{ getContactTypeLabel(type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="重要程度"
width="120">
          <template #default="{ row }">
            <el-rate
              :model-value="row.importance"
              disabled
              size="small"
              :colors="['#ff9900', '#ff9900', '#ff9900']"
            />
          </template>
        </el-table-column>

        <el-table-column label="影响力"
width="100">
          <template #default="{ row }">
            <div class="contact-management__influence">
              <el-progress
                :percentage="row.influenceScore * 10"
                :stroke-width="4"
                :show-text="false"
                color="#409EFF"
              />
              <span class="contact-management__influence-text">{{ row.influenceScore }}/10</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态"
width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="最后联系"
width="120">
          <template #default="{ row }">
            <span class="contact-management__last-contact">
              {{ formatLastContactDate(row.lastContactDate) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作"
width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" text size="small" @click.stop="handleEditContact(row)">
              编辑
            </el-button>
            <el-button type="danger" text size="small" @click.stop="handleDeleteContact(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="contact-management__pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentPageChange"
        />
      </div>
    </el-card>

    <!-- 联系人详情抽屉 -->
    <el-drawer v-model="contactDrawerVisible" title="联系人详情" size="600px" direction="rtl">
      <template v-if="selectedContact">
        <div class="contact-management__detail">
          <!-- 基本信息 -->
          <div class="contact-management__detail-section">
            <h3>基本信息</h3>
            <div class="contact-management__detail-info">
              <el-avatar :size="60"
:src="selectedContact.avatar">
                {{ selectedContact.name.charAt(0) }}
              </el-avatar>
              <div class="contact-management__detail-info-content">
                <h4>{{ selectedContact.name }}</h4>
                <p>{{ selectedContact.position }} · {{ selectedContact.department }}</p>
                <div class="contact-management__detail-tags">
                  <el-tag v-if="selectedContact.isPrimaryContact"
type="primary" size="small">
                    主要联系人
                  </el-tag>
                  <el-tag v-if="selectedContact.isDecisionMaker"
type="success" size="small">
                    决策者
                  </el-tag>
                  <el-tag :type="getStatusType(selectedContact.status)" size="small">
                    {{ getStatusLabel(selectedContact.status) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>

          <!-- 联系方式 -->
          <div class="contact-management__detail-section">
            <h3>联系方式</h3>
            <div class="contact-management__detail-contacts">
              <div class="contact-management__detail-contact">
                <el-icon><Phone /></el-icon>
                <span>电话：{{ selectedContact.phone }}</span>
                <el-button
text type="primary" size="small">拨打</el-button>
              </div>
              <div v-if="selectedContact.mobile"
class="contact-management__detail-contact">
                <el-icon><Phone /></el-icon>
                <span>手机：{{ selectedContact.mobile }}</span>
                <el-button
text type="primary" size="small">拨打</el-button>
              </div>
              <div class="contact-management__detail-contact">
                <el-icon><Message /></el-icon>
                <span>邮箱：{{ selectedContact.email }}</span>
                <el-button
text type="primary" size="small">发送</el-button>
              </div>
              <div v-if="selectedContact.wechat"
class="contact-management__detail-contact">
                <el-icon><ChatDotRound /></el-icon>
                <span>微信：{{ selectedContact.wechat }}</span>
              </div>
              <div v-if="selectedContact.dingTalk"
class="contact-management__detail-contact">
                <el-icon><ChatDotRound /></el-icon>
                <span>钉钉：{{ selectedContact.dingTalk }}</span>
              </div>
            </div>
          </div>

          <!-- 职业信息 -->
          <div class="contact-management__detail-section">
            <h3>职业信息</h3>
            <el-descriptions :column="2"
size="small">
              <el-descriptions-item label="角色">
                {{ getRoleLabel(selectedContact.role) }}
              </el-descriptions-item>
              <el-descriptions-item label="职级">
                {{ selectedContact.level || '未设置' }}
              </el-descriptions-item>
              <el-descriptions-item label="联系人类型"
:span="2">
                <el-tag
                  v-for="type in selectedContact.contactType"
                  :key="type"
                  size="small"
                  type="info"
                  style="margin-right: 8px"
                >
                  {{ getContactTypeLabel(type) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="汇报对象">
                {{ selectedContact.reportTo || '未设置' }}
              </el-descriptions-item>
              <el-descriptions-item label="行业经验">
                {{
                  selectedContact.industryExperience
                    ? selectedContact.industryExperience + '年'
                    : '未设置'
                }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 关系评级 -->
          <div class="contact-management__detail-section">
            <h3>关系评级</h3>
            <div class="contact-management__detail-ratings">
              <div class="contact-management__detail-rating">
                <span>重要程度：</span>
                <el-rate
                  :model-value="selectedContact.importance"
                  disabled
                  size="small"
                  :colors="['#ff9900', '#ff9900', '#ff9900']"
                />
                <span>{{ selectedContact.importance }}/5</span>
              </div>
              <div class="contact-management__detail-rating">
                <span>信任度：</span>
                <el-rate
                  :model-value="selectedContact.trustLevel"
                  disabled
                  size="small"
                  :colors="['#67c23a', '#67c23a', '#67c23a']"
                />
                <span>{{ selectedContact.trustLevel }}/5</span>
              </div>
              <div class="contact-management__detail-rating">
                <span>影响力：</span>
                <el-progress
                  :percentage="selectedContact.influenceScore * 10"
                  :stroke-width="8"
                  color="#409EFF"
                />
                <span>{{ selectedContact.influenceScore }}/10</span>
              </div>
            </div>
          </div>

          <!-- 沟通偏好 -->
          <div
            v-if="selectedContact.communicationPreference?.length"
            class="contact-management__detail-section"
          >
            <h3>沟通偏好</h3>
            <div class="contact-management__detail-preferences">
              <el-tag
                v-for="pref in selectedContact.communicationPreference"
                :key="pref"
                type="info"
                size="small"
                style="margin-right: 8px; margin-bottom: 8px"
              >
                {{ getCommunicationPreferenceLabel(pref) }}
              </el-tag>
            </div>
            <p v-if="selectedContact.preferredContactTime"
class="contact-management__detail-note">
              最佳联系时间：{{ selectedContact.preferredContactTime }}
            </p>
          </div>

          <!-- 专长领域 -->
          <div
            v-if="selectedContact.specialties?.length"
            class="contact-management__detail-section"
          >
            <h3>专长领域</h3>
            <div class="contact-management__detail-specialties">
              <el-tag
                v-for="specialty in selectedContact.specialties"
                :key="specialty"
                type="success"
                size="small"
                effect="plain"
                style="margin-right: 8px; margin-bottom: 8px"
              >
                {{ specialty }}
              </el-tag>
            </div>
          </div>

          <!-- 备注 -->
          <div v-if="selectedContact.remarks"
class="contact-management__detail-section">
            <h3>备注信息</h3>
            <p class="contact-management__detail-remarks">
              {{ selectedContact.remarks }}
            </p>
          </div>

          <!-- 沟通历史 -->
          <div class="contact-management__detail-section">
            <h3>沟通统计</h3>
            <el-descriptions :column="2"
size="small">
              <el-descriptions-item label="沟通总次数">
                {{ selectedContact.totalCommunications || 0 }}次
              </el-descriptions-item>
              <el-descriptions-item label="月均沟通频次">
                {{ selectedContact.communicationFrequency || 0 }}次/月
              </el-descriptions-item>
              <el-descriptions-item label="最后沟通时间"
:span="2">
                {{ formatLastContactDate(selectedContact.lastContactDate) }}
              </el-descriptions-item>
            </el-descriptions>
            <el-button
              type="primary"
              style="margin-top: 16px"
              @click="handleViewCommunicationHistory"
            >
              查看沟通记录
            </el-button>
          </div>
        </div>
      </template>

      <template #footer>
        <div style="flex: auto">
          <el-button @click="contactDrawerVisible = false">关闭</el-button>
          <el-button type="primary"
@click="handleEditContact(selectedContact!)">
            编辑联系人
          </el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 联系人编辑对话框 -->
    <el-dialog
      v-model="contactDialogVisible"
      :title="isEditMode ? '编辑联系人' : '新增联系人'"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="contactFormRef"
        :model="contactForm"
        :rules="contactFormRules"
        label-width="100px"
      >
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="所属客户"
prop="customerId" required>
              <el-select
                v-model="contactForm.customerId"
                placeholder="选择客户公司"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="customer in customerOptions"
                  :key="customer.id"
                  :label="customer.name"
                  :value="customer.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人姓名"
prop="name" required>
              <el-input v-model="contactForm.name"
placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="英文姓名"
prop="englishName">
              <el-input v-model="contactForm.englishName"
placeholder="请输入英文姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位"
prop="position" required>
              <el-input v-model="contactForm.position"
placeholder="请输入职位" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="所在部门"
prop="department" required>
              <el-input v-model="contactForm.department"
placeholder="请输入部门" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职级"
prop="level">
              <el-select v-model="contactForm.level"
placeholder="选择职级" clearable>
                <el-option label="总监"
value="总监" />
                <el-option label="副总监"
value="副总监" />
                <el-option label="经理"
value="经理" />
                <el-option label="副经理"
value="副经理" />
                <el-option label="主管"
value="主管" />
                <el-option label="专员"
value="专员" />
                <el-option label="工程师"
value="工程师" />
                <el-option label="高级工程师"
value="高级工程师" />
                <el-option label="资深工程师"
value="资深工程师" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="联系人角色"
prop="role" required>
              <el-select v-model="contactForm.role"
placeholder="选择角色">
                <el-option
                  v-for="role in contactRoleOptions"
                  :key="role.value"
                  :label="role.label"
                  :value="role.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人类型"
prop="contactType" required>
              <el-select v-model="contactForm.contactType" placeholder="选择类型" multiple>
                <el-option
                  v-for="type in contactTypeOptions"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="电话"
prop="phone" required>
              <el-input v-model="contactForm.phone"
placeholder="请输入电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机"
prop="mobile">
              <el-input v-model="contactForm.mobile"
placeholder="请输入手机号码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="邮箱"
prop="email" required>
              <el-input v-model="contactForm.email"
placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="微信"
prop="wechat">
              <el-input v-model="contactForm.wechat"
placeholder="请输入微信号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="重要程度"
prop="importance" required>
              <el-rate
                v-model="contactForm.importance"
                :colors="['#ff9900', '#ff9900', '#ff9900']"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="信任度"
prop="trustLevel" required>
              <el-rate
                v-model="contactForm.trustLevel"
                :colors="['#67c23a', '#67c23a', '#67c23a']"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="影响力"
prop="influenceScore" required>
              <el-slider v-model="contactForm.influenceScore" :min="1" :max="10" show-input />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item>
              <el-checkbox v-model="contactForm.isDecisionMaker">决策者</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-checkbox v-model="contactForm.isPrimaryContact">主要联系人</el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="沟通偏好"
prop="communicationPreference">
          <el-select
            v-model="contactForm.communicationPreference"
            placeholder="选择沟通方式偏好"
            multiple
          >
            <el-option
              v-for="pref in communicationPreferenceOptions"
              :key="pref.value"
              :label="pref.label"
              :value="pref.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="专长领域"
prop="specialties">
          <el-select
            v-model="contactForm.specialties"
            placeholder="选择专长领域"
            multiple
            allow-create
            filterable
          >
            <el-option value="芯片设计" />
            <el-option value="模拟电路" />
            <el-option value="数字电路" />
            <el-option value="射频电路" />
            <el-option value="电源管理" />
            <el-option value="封装技术" />
            <el-option value="测试技术" />
            <el-option value="质量管理" />
            <el-option value="项目管理" />
            <el-option value="供应链管理" />
          </el-select>
        </el-form-item>

        <el-form-item label="备注"
prop="remarks">
          <el-input
            v-model="contactForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="contactDialogVisible = false">取消</el-button>
          <el-button type="primary"
:loading="submitting" @click="handleSubmitContact">
            {{ isEditMode ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
  import {
    Plus,
    Search,
    Refresh,
    Delete,
    Download,
    Edit,
    View,
    More,
    Phone,
    Message,
    User,
    ChatDotRound
  } from '@element-plus/icons-vue'
  import type {
    Contact,
    ContactRole,
    ContactType,
    ContactStatus,
    ContactImportance,
    InfluenceLevel,
    CommunicationPreference,
    ContactQueryParams,
    CreateContactData
  } from '@/types/customer'
  import dayjs from 'dayjs'

  // 路由和响应式数据
  const router = useRouter()
  const loading = ref(false)
  const submitting = ref(false)
  const contactDrawerVisible = ref(false)
  const contactDialogVisible = ref(false)
  const isEditMode = ref(false)
  const groupByCustomer = ref(false)
  const selectAll = ref(false)
  const activeGroups = ref<string[]>([])

  // 表单引用
  const contactFormRef = ref<FormInstance>()

  // 搜索表单
  const searchForm = reactive<ContactQueryParams>({
    keyword: '',
    customerId: '',
    roles: [],
    contactTypes: [],
    importance: [],
    status: [],
    page: 1,
    pageSize: 20
  })

  // 分页信息
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 联系人数据
  const contacts = ref<Contact[]>([])
  const selectedContacts = ref<string[]>([])
  const selectedContact = ref<Contact | null>(null)

  // 客户选项
  const customerOptions = ref([
    { id: 'customer_001', name: '华为海思半导体有限公司' },
    { id: 'customer_002', name: '紫光展锐科技有限公司' },
    { id: 'customer_003', name: '比亚迪半导体股份有限公司' },
    { id: 'customer_004', name: '兆易创新科技集团股份有限公司' },
    { id: 'customer_005', name: '汇顶科技股份有限公司' }
  ])

  // 联系人表单
  const contactForm = reactive<CreateContactData>({
    customerId: '',
    name: '',
    englishName: '',
    position: '',
    department: '',
    level: '',
    email: '',
    phone: '',
    mobile: '',
    wechat: '',
    dingTalk: '',
    role: 'FAE' as ContactRole,
    contactType: [] as ContactType[],
    importance: 3 as ContactImportance,
    trustLevel: 3 as ContactImportance,
    influenceScore: 5 as InfluenceLevel,
    isDecisionMaker: false,
    isPrimaryContact: false,
    communicationPreference: [] as CommunicationPreference[],
    specialties: [],
    remarks: ''
  })

  // 表单验证规则
  const contactFormRules: FormRules = {
    customerId: [{ required: true, message: '请选择所属客户', trigger: 'change' }],
    name: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
    position: [{ required: true, message: '请输入职位', trigger: 'blur' }],
    department: [{ required: true, message: '请输入部门', trigger: 'blur' }],
    role: [{ required: true, message: '请选择联系人角色', trigger: 'change' }],
    contactType: [{ required: true, message: '请选择联系人类型', trigger: 'change' }],
    phone: [{ required: true, message: '请输入电话', trigger: 'blur' }],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    importance: [{ required: true, message: '请设置重要程度', trigger: 'change' }],
    trustLevel: [{ required: true, message: '请设置信任度', trigger: 'change' }],
    influenceScore: [{ required: true, message: '请设置影响力', trigger: 'change' }]
  }

  // 选项数据
  const contactRoleOptions = [
    { value: 'FAE', label: '现场应用工程师' },
    { value: 'DesignManager', label: '设计经理' },
    { value: 'TestEngineer', label: '测试工程师' },
    { value: 'QualityEngineer', label: '质量工程师' },
    { value: 'PurchaseManager', label: '采购经理' },
    { value: 'SupplyChainDirector', label: '供应链总监' },
    { value: 'BusinessManager', label: '商务经理' },
    { value: 'LegalManager', label: '法务经理' },
    { value: 'CTO', label: '首席技术官' },
    { value: 'VP', label: '副总裁' },
    { value: 'ProjectDirector', label: '项目总监' },
    { value: 'TechnicalDirector', label: '技术总监' },
    { value: 'PackageEngineer', label: '封装工程师' },
    { value: 'ReliabilityEngineer', label: '可靠性工程师' },
    { value: 'PMManager', label: '项目经理' },
    { value: 'CustomerService', label: '客服代表' }
  ]

  const contactTypeOptions = [
    { value: 'Technical', label: '技术对接' },
    { value: 'Business', label: '商务对接' },
    { value: 'Quality', label: '质量对接' },
    { value: 'Procurement', label: '采购对接' },
    { value: 'Decision', label: '决策者' },
    { value: 'Support', label: '支持角色' }
  ]

  const communicationPreferenceOptions = [
    { value: 'Email', label: '邮件' },
    { value: 'Phone', label: '电话' },
    { value: 'WeChat', label: '微信' },
    { value: 'InPerson', label: '面谈' },
    { value: 'VideoConference', label: '视频会议' },
    { value: 'DingTalk', label: '钉钉' },
    { value: 'Teams', label: 'Teams' }
  ]

  const importanceOptions = [
    { value: 1, label: '★ 一般' },
    { value: 2, label: '★★ 重要' },
    { value: 3, label: '★★★ 很重要' },
    { value: 4, label: '★★★★ 非常重要' },
    { value: 5, label: '★★★★★ 极其重要' }
  ]

  const statusOptions = [
    { value: 'active', label: '活跃' },
    { value: 'inactive', label: '非活跃' },
    { value: 'departed', label: '已离职' },
    { value: 'suspended', label: '暂停' }
  ]

  // 计算属性
  const isIndeterminate = computed(() => {
    const selected = selectedContacts.value.length
    const total = contacts.value.length
    return selected > 0 && selected < total
  })

  const groupedContacts = computed(() => {
    const groups: Array<{
      customerId: string
      customerName: string
      contacts: Contact[]
    }> = []

    const customerMap = new Map<string, Contact[]>()

    contacts.value.forEach(contact => {
      if (!customerMap.has(contact.customerId)) {
        customerMap.set(contact.customerId, [])
      }
      customerMap.get(contact.customerId)!.push(contact)
    })

    customerMap.forEach((contacts, customerId) => {
      const customer = customerOptions.value.find(c => c.id === customerId)
      groups.push({
        customerId,
        customerName: customer?.name || '未知客户',
        contacts
      })
    })

    return groups
  })

  // 方法
  const fetchContacts = async () => {
    loading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟数据
      const mockContacts: Contact[] = [
        {
          id: 'contact_001',
          customerId: 'customer_001',
          name: '张伟',
          englishName: 'Wei Zhang',
          position: '采购总监',
          department: '供应链管理部',
          level: '总监',
          email: '<EMAIL>',
          phone: '021-12345678',
          mobile: '***********',
          wechat: 'zw_hisilicon',
          role: 'PurchaseManager' as ContactRole,
          contactType: ['Procurement', 'Business'] as ContactType[],
          status: 'active' as ContactStatus,
          importance: 5 as ContactImportance,
          trustLevel: 4 as ContactImportance,
          influenceScore: 9 as InfluenceLevel,
          isDecisionMaker: true,
          isPrimaryContact: true,
          communicationPreference: ['Email', 'Phone'] as CommunicationPreference[],
          preferredContactTime: '工作日 9:00-18:00',
          specialties: ['供应链管理', '成本控制', '供应商管理'],
          lastContactDate: '2024-01-15',
          totalCommunications: 45,
          communicationFrequency: 8,
          remarks: '重要决策者，负责供应商选择和合同谈判',
          createdAt: '2023-01-15T08:00:00Z',
          updatedAt: '2024-01-15T10:30:00Z',
          createdBy: 'admin'
        },
        {
          id: 'contact_002',
          customerId: 'customer_001',
          name: '李明',
          englishName: 'Ming Li',
          position: '封装技术经理',
          department: '技术部',
          level: '经理',
          email: '<EMAIL>',
          phone: '021-12345679',
          mobile: '13800138002',
          role: 'PackageEngineer' as ContactRole,
          contactType: ['Technical'] as ContactType[],
          status: 'active' as ContactStatus,
          importance: 4 as ContactImportance,
          trustLevel: 5 as ContactImportance,
          influenceScore: 7 as InfluenceLevel,
          isDecisionMaker: false,
          isPrimaryContact: false,
          communicationPreference: ['Email', 'WeChat'] as CommunicationPreference[],
          preferredContactTime: '工作日 10:00-17:00',
          specialties: ['封装技术', 'BGA封装', 'FC封装'],
          lastContactDate: '2024-01-12',
          totalCommunications: 28,
          communicationFrequency: 6,
          remarks: '技术专家，封装工艺方面的主要联系人',
          createdAt: '2023-02-20T08:00:00Z',
          updatedAt: '2024-01-12T14:20:00Z',
          createdBy: 'admin'
        },
        {
          id: 'contact_003',
          customerId: 'customer_002',
          name: '王强',
          englishName: 'Qiang Wang',
          position: '供应链总监',
          department: '供应链管理中心',
          level: '总监',
          email: '<EMAIL>',
          phone: '021-87654321',
          mobile: '***********',
          role: 'SupplyChainDirector' as ContactRole,
          contactType: ['Procurement', 'Business', 'Decision'] as ContactType[],
          status: 'active' as ContactStatus,
          importance: 5 as ContactImportance,
          trustLevel: 4 as ContactImportance,
          influenceScore: 8 as InfluenceLevel,
          isDecisionMaker: true,
          isPrimaryContact: true,
          communicationPreference: ['Phone', 'InPerson'] as CommunicationPreference[],
          preferredContactTime: '工作日 9:00-18:00',
          specialties: ['供应链战略', '成本优化', '风险管控'],
          lastContactDate: '2024-01-10',
          totalCommunications: 52,
          communicationFrequency: 10,
          remarks: '战略合作主要决策者，对成本和质量要求很高',
          createdAt: '2023-03-10T08:00:00Z',
          updatedAt: '2024-01-10T16:40:00Z',
          createdBy: 'admin'
        }
      ]

      // 添加客户名称
      mockContacts.forEach(contact => {
        const customer = customerOptions.value.find(c => c.id === contact.customerId)
        ;(contact as any).customerName = customer?.name || '未知客户'
      })

      contacts.value = mockContacts
      pagination.total = mockContacts.length
    } catch (error) {
      ElMessage.error('获取联系人列表失败')
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  const handleSearch = () => {
    pagination.page = 1
    fetchContacts()
  }

  const handleReset = () => {
    Object.assign(searchForm, {
      keyword: '',
      customerId: '',
      roles: [],
      contactTypes: [],
      importance: [],
      status: [],
      page: 1,
      pageSize: 20
    })
    handleSearch()
  }

  const handlePageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    fetchContacts()
  }

  const handleCurrentPageChange = (page: number) => {
    pagination.page = page
    fetchContacts()
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      selectedContacts.value = contacts.value.map(contact => contact.id)
    } else {
      selectedContacts.value = []
    }
  }

  const handleSelectionChange = (selection: Contact[]) => {
    selectedContacts.value = selection.map(contact => contact.id)
  }

  const handleContactSelect = (contactId: string, checked: boolean) => {
    if (checked) {
      if (!selectedContacts.value.includes(contactId)) {
        selectedContacts.value.push(contactId)
      }
    } else {
      selectedContacts.value = selectedContacts.value.filter(id => id !== contactId)
    }
  }

  const handleAddContact = () => {
    isEditMode.value = false
    resetContactForm()
    contactDialogVisible.value = true
  }

  const handleEditContact = (contact: Contact) => {
    isEditMode.value = true
    Object.assign(contactForm, {
      customerId: contact.customerId,
      name: contact.name,
      englishName: contact.englishName,
      position: contact.position,
      department: contact.department,
      level: contact.level,
      email: contact.email,
      phone: contact.phone,
      mobile: contact.mobile,
      wechat: contact.wechat,
      dingTalk: contact.dingTalk,
      role: contact.role,
      contactType: contact.contactType,
      importance: contact.importance,
      trustLevel: contact.trustLevel,
      influenceScore: contact.influenceScore,
      isDecisionMaker: contact.isDecisionMaker,
      isPrimaryContact: contact.isPrimaryContact,
      communicationPreference: contact.communicationPreference || [],
      specialties: contact.specialties || [],
      remarks: contact.remarks
    })
    selectedContact.value = contact
    contactDialogVisible.value = true
    contactDrawerVisible.value = false
  }

  const handleViewContact = (contact: Contact) => {
    selectedContact.value = contact
    contactDrawerVisible.value = true
  }

  const handleContactAction = (command: string, contact: Contact) => {
    switch (command) {
      case 'view':
        handleViewContact(contact)
        break
      case 'edit':
        handleEditContact(contact)
        break
      case 'communicate':
        handleViewCommunicationHistory()
        break
      case 'delete':
        handleDeleteContact(contact)
        break
    }
  }

  const handleDeleteContact = async (contact: Contact) => {
    try {
      await ElMessageBox.confirm(`确定要删除联系人 "${contact.name}" 吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      // 模拟删除操作
      ElMessage.success('删除成功')
      fetchContacts()
    } catch {
      // 用户取消删除
    }
  }

  const handleBatchDelete = async () => {
    if (selectedContacts.value.length === 0) {
      ElMessage.warning('请选择要删除的联系人')
      return
    }

    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${selectedContacts.value.length} 个联系人吗？`,
        '批量删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 模拟批量删除操作
      ElMessage.success(`成功删除 ${selectedContacts.value.length} 个联系人`)
      selectedContacts.value = []
      selectAll.value = false
      fetchContacts()
    } catch {
      // 用户取消删除
    }
  }

  const handleExport = () => {
    if (selectedContacts.value.length === 0) {
      ElMessage.warning('请选择要导出的联系人')
      return
    }

    ElMessage.success(`正在导出 ${selectedContacts.value.length} 个联系人的信息`)
  }

  const handleSubmitContact = async () => {
    if (!contactFormRef.value) return

    try {
      await contactFormRef.value.validate()

      submitting.value = true

      // 模拟提交操作
      await new Promise(resolve => setTimeout(resolve, 1000))

      ElMessage.success(isEditMode.value ? '更新成功' : '创建成功')
      contactDialogVisible.value = false
      fetchContacts()
    } catch (error) {
      console.error('表单验证失败:', error)
    } finally {
      submitting.value = false
    }
  }

  const handleDialogClose = () => {
    resetContactForm()
    contactFormRef.value?.resetFields()
  }

  const handleViewCommunicationHistory = () => {
    // 跳转到沟通记录页面
    router.push('/customer/communications')
  }

  const resetContactForm = () => {
    Object.assign(contactForm, {
      customerId: '',
      name: '',
      englishName: '',
      position: '',
      department: '',
      level: '',
      email: '',
      phone: '',
      mobile: '',
      wechat: '',
      dingTalk: '',
      role: 'FAE' as ContactRole,
      contactType: [] as ContactType[],
      importance: 3 as ContactImportance,
      trustLevel: 3 as ContactImportance,
      influenceScore: 5 as InfluenceLevel,
      isDecisionMaker: false,
      isPrimaryContact: false,
      communicationPreference: [] as CommunicationPreference[],
      specialties: [],
      remarks: ''
    })
  }

  // 辅助方法
  const getRoleLabel = (role: ContactRole) => {
    return contactRoleOptions.find(option => option.value === role)?.label || role
  }

  const getContactTypeLabel = (type: ContactType) => {
    return contactTypeOptions.find(option => option.value === type)?.label || type
  }

  const getCommunicationPreferenceLabel = (preference: CommunicationPreference) => {
    return (
      communicationPreferenceOptions.find(option => option.value === preference)?.label ||
      preference
    )
  }

  const getStatusType = (status: ContactStatus) => {
    const typeMap = {
      active: 'success',
      inactive: 'info',
      departed: 'warning',
      suspended: 'danger'
    }
    return typeMap[status] || 'info'
  }

  const getStatusLabel = (status: ContactStatus) => {
    return statusOptions.find(option => option.value === status)?.label || status
  }

  const formatLastContactDate = (date?: string) => {
    if (!date) return '从未联系'

    const daysDiff = dayjs().diff(dayjs(date), 'day')

    if (daysDiff === 0) return '今天'
    if (daysDiff === 1) return '昨天'
    if (daysDiff < 7) return `${daysDiff}天前`
    if (daysDiff < 30) return `${Math.floor(daysDiff / 7)}周前`
    if (daysDiff < 365) return `${Math.floor(daysDiff / 30)}个月前`

    return dayjs(date).format('YYYY-MM-DD')
  }

  // 初始化
  onMounted(() => {
    fetchContacts()
  })
</script>

<style lang="scss" scoped>
  .contact-management {
    min-height: calc(100vh - 60px);
    padding: var(--spacing-4);
    background-color: var(--color-bg-base);

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-6);

      &-left {
        .contact-management__title {
          margin: 0 0 var(--spacing-1) 0;
          font-size: 24px;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        .contact-management__subtitle {
          margin: 0;
          font-size: 14px;
          color: var(--color-text-regular);
        }
      }
    }

    &__search-card {
      margin-bottom: var(--spacing-4);

      .contact-management__search-form {
        .el-form-item {
          margin-bottom: var(--spacing-3);
        }
      }
    }

    &__table-card {
      .contact-management__table-toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-4);

        &-right {
          display: flex;
          gap: var(--spacing-3);
          align-items: center;
        }
      }
    }

    &__grouped-list {
      margin-bottom: var(--spacing-4);

      .contact-management__group-title {
        display: flex;
        gap: var(--spacing-2);
        align-items: center;

        .contact-management__group-name {
          font-weight: 500;
        }
      }

      .contact-management__group-content {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: var(--spacing-4);
        padding: var(--spacing-4) 0;
      }
    }

    &__contact-card {
      padding: var(--spacing-4);
      cursor: pointer;
      background-color: var(--color-bg-primary);
      border: 1px solid var(--color-border-light);
      border-radius: var(--radius-base);
      transition: all 0.2s;

      &:hover {
        border-color: var(--color-primary);
        box-shadow: 0 2px 8px rgb(64 158 255 / 10%);
      }

      &-header {
        display: flex;
        gap: var(--spacing-3);
        align-items: center;
        margin-bottom: var(--spacing-3);

        .contact-management__contact-info {
          flex: 1;
          min-width: 0;
        }

        .contact-management__contact-name {
          display: flex;
          gap: var(--spacing-1);
          align-items: center;
          margin-bottom: var(--spacing-1);
          font-size: 16px;
          font-weight: 500;
          color: var(--color-text-primary);
        }

        .contact-management__contact-position {
          font-size: 13px;
          color: var(--color-text-regular);
        }
      }

      &-body {
        .contact-management__contact-details {
          margin-bottom: var(--spacing-3);

          .contact-management__contact-detail {
            display: flex;
            gap: var(--spacing-2);
            align-items: center;
            margin-bottom: var(--spacing-2);
            font-size: 13px;
            color: var(--color-text-regular);

            .el-icon {
              color: var(--color-primary);
            }

            &:last-child {
              margin-bottom: 0;

              .el-tag {
                margin-right: var(--spacing-1);
              }
            }
          }
        }

        .contact-management__contact-metrics {
          .contact-management__metric {
            display: flex;
            gap: var(--spacing-2);
            align-items: center;
            margin-bottom: var(--spacing-2);
            font-size: 12px;

            .label {
              min-width: 60px;
              color: var(--color-text-regular);
            }

            .value {
              font-weight: 500;
              color: var(--color-text-primary);
            }

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    &__contact-cell {
      display: flex;
      gap: var(--spacing-3);
      align-items: center;

      &-info {
        flex: 1;
        min-width: 0;
      }

      &-name {
        display: flex;
        gap: var(--spacing-1);
        align-items: center;
        margin-bottom: var(--spacing-1);
        font-weight: 500;
        color: var(--color-text-primary);
      }

      &-position {
        font-size: 13px;
        color: var(--color-text-regular);
      }
    }

    &__contact-methods {
      font-size: 13px;
      color: var(--color-text-regular);

      div {
        display: flex;
        gap: var(--spacing-1);
        align-items: center;
        margin-bottom: var(--spacing-1);

        .el-icon {
          color: var(--color-primary);
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    &__influence {
      display: flex;
      gap: var(--spacing-2);
      align-items: center;

      &-text {
        min-width: 30px;
        font-size: 12px;
        color: var(--color-text-regular);
      }
    }

    &__last-contact {
      font-size: 13px;
      color: var(--color-text-regular);
    }

    &__pagination {
      display: flex;
      justify-content: flex-end;
      margin-top: var(--spacing-4);
    }

    &__detail {
      &-section {
        margin-bottom: var(--spacing-6);

        h3 {
          padding-bottom: var(--spacing-2);
          margin: 0 0 var(--spacing-3) 0;
          font-size: 16px;
          font-weight: 500;
          color: var(--color-text-primary);
          border-bottom: 1px solid var(--color-border-light);
        }
      }

      &-info {
        display: flex;
        gap: var(--spacing-4);

        &-content {
          flex: 1;

          h4 {
            margin: 0 0 var(--spacing-1) 0;
            font-size: 18px;
            font-weight: 500;
            color: var(--color-text-primary);
          }

          p {
            margin: 0 0 var(--spacing-2) 0;
            font-size: 14px;
            color: var(--color-text-regular);
          }
        }
      }

      &-tags {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-2);
      }

      &-contacts {
        .contact-management__detail-contact {
          display: flex;
          gap: var(--spacing-3);
          align-items: center;
          padding: var(--spacing-2) 0;
          border-bottom: 1px solid var(--color-border-lighter);

          .el-icon {
            color: var(--color-primary);
          }

          span {
            flex: 1;
            color: var(--color-text-regular);
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }

      &-ratings {
        .contact-management__detail-rating {
          display: flex;
          gap: var(--spacing-3);
          align-items: center;
          margin-bottom: var(--spacing-3);

          span {
            &:first-child {
              min-width: 80px;
              color: var(--color-text-regular);
            }

            &:last-child {
              font-weight: 500;
              color: var(--color-text-primary);
            }
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      &-preferences {
        margin-bottom: var(--spacing-3);
      }

      &-note {
        margin: 0;
        font-size: 13px;
        font-style: italic;
        color: var(--color-text-regular);
      }

      &-specialties {
        margin-bottom: var(--spacing-3);
      }

      &-remarks {
        padding: var(--spacing-3);
        margin: 0;
        line-height: 1.6;
        color: var(--color-text-regular);
        background-color: var(--color-bg-secondary);
        border-radius: var(--radius-base);
      }
    }

    // 表格样式优化
    &__table {
      :deep(.el-table__row) {
        cursor: pointer;

        &:hover {
          background-color: var(--color-bg-secondary);
        }
      }
    }

    // 响应式设计
    @media (width <= 768px) {
      padding: var(--spacing-3);

      &__header {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: flex-start;

        &-right {
          width: 100%;
        }
      }

      &__search-form {
        .el-form-item {
          width: 100%;

          .el-input,
          .el-select {
            width: 100%;
          }
        }
      }

      &__table-toolbar {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: flex-start;

        &-left,
        &-right {
          justify-content: flex-start;
          width: 100%;
        }
      }

      &__grouped-list {
        .contact-management__group-content {
          grid-template-columns: 1fr;
        }
      }

      &__contact-card {
        &-header {
          flex-wrap: wrap;
        }
      }
    }
  }

  // Element Plus 组件样式覆盖
  :deep(.el-table) {
    .el-table__cell {
      padding: 12px 8px;
    }
  }

  :deep(.el-collapse-item__header) {
    padding-left: 0;
  }

  :deep(.el-drawer__body) {
    padding: var(--spacing-4);
  }

  :deep(.el-dialog) {
    .el-dialog__body {
      padding: var(--spacing-4);
    }
  }
</style>
