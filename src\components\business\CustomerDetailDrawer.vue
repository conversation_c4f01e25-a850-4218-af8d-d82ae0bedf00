<template>
  <el-drawer
    v-model="visible"
    title="客户详情"
    :size="drawerSize"
    :with-header="false"
    class="customer-detail-drawer"
  >
    <div v-if="loading" class="drawer-loading">
      <el-skeleton :rows="8" animated />
    </div>

    <div v-else-if="customer" class="drawer-content">
      <!-- 抽屉头部 -->
      <div class="drawer-header">
        <div class="drawer-header__main">
          <div class="customer-avatar">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"
              />
            </svg>
          </div>
          <div class="customer-basic">
            <h2 class="customer-name">
              {{ customer.customerName }}
            </h2>
            <p v-if="customer.customerNameEn" class="customer-name-en">
              {{ customer.customerNameEn }}
            </p>
            <div class="customer-badges">
              <el-tag :type="getCustomerLevelType(customer.customerLevel)" size="small">
                {{ getCustomerLevelText(customer.customerLevel) }}
              </el-tag>
              <el-tag :type="getCustomerTypeType(customer.customerType)" size="small">
                {{ getCustomerTypeText(customer.customerType) }}
              </el-tag>
              <el-tag :type="getStatusType(customer.status)" size="small">
                {{ getStatusText(customer.status) }}
              </el-tag>
            </div>
          </div>
        </div>
        <div class="drawer-header__actions">
          <el-button type="primary" @click="handleEdit">
            <svg
              class="button-icon"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
            >
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
              <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z" />
            </svg>
            编辑
          </el-button>
          <el-button @click="visible = false">
            <svg
              class="button-icon"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
            >
              <path d="M18 6L6 18M6 6l12 12" />
            </svg>
            关闭
          </el-button>
        </div>
      </div>

      <!-- 抽屉内容 -->
      <div class="drawer-body">
        <el-tabs v-model="activeTab" class="detail-tabs">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <div class="tab-content">
              <div class="info-sections">
                <!-- 公司信息 -->
                <div class="info-section">
                  <h3 class="section-title">
                    <svg
                      class="section-icon"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="1.5"
                    >
                      <path d="M3 21h18" />
                      <path d="M5 21V7l8-4v18" />
                      <path d="M19 21V11l-6-4" />
                    </svg>
                    公司信息
                  </h3>
                  <div class="info-grid">
                    <div class="info-item">
                      <label class="info-label">客户编号</label>
                      <span class="info-value">{{ customer.customerCode }}</span>
                    </div>
                    <div class="info-item">
                      <label class="info-label">行业分类</label>
                      <span class="info-value">{{ getIndustryText(customer.industry) }}</span>
                    </div>
                    <div class="info-item">
                      <label class="info-label">应用领域</label>
                      <div class="info-value">
                        <el-tag
                          v-for="field in customer.applicationField"
                          :key="field"
                          size="small"
                          class="field-tag"
                        >
                          {{ getApplicationFieldText(field) }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">统一社会信用代码</label>
                      <span class="info-value">{{ customer.unifiedSocialCreditCode || '-' }}</span>
                    </div>
                    <div class="info-item full-width">
                      <label class="info-label">注册地址</label>
                      <span class="info-value">{{ customer.registeredAddress || '-' }}</span>
                    </div>
                    <div class="info-item full-width">
                      <label class="info-label">办公地址</label>
                      <span class="info-value">{{ customer.officeAddress || '-' }}</span>
                    </div>
                  </div>
                </div>

                <!-- 合作历史 -->
                <div class="info-section">
                  <h3 class="section-title">
                    <svg
                      class="section-icon"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="1.5"
                    >
                      <path d="M3 3v18h18" />
                      <path d="M7 12l3 3 7-10" />
                    </svg>
                    合作历史
                  </h3>
                  <div class="cooperation-stats">
                    <div class="coop-stat-item">
                      <div class="coop-stat-value">
                        {{ customer.cooperationHistory.cooperationYears }}
                      </div>
                      <div class="coop-stat-label">合作年限</div>
                    </div>
                    <div class="coop-stat-item">
                      <div class="coop-stat-value">
                        {{ customer.cooperationHistory.totalOrders }}
                      </div>
                      <div class="coop-stat-label">历史订单</div>
                    </div>
                    <div class="coop-stat-item">
                      <div class="coop-stat-value">
                        ¥{{ formatAmount(customer.cooperationHistory.totalSalesAmount) }}
                      </div>
                      <div class="coop-stat-label">历史销售额</div>
                    </div>
                    <div class="coop-stat-item">
                      <div class="coop-stat-value">
                        {{ customer.cooperationHistory.satisfactionScore }}/5.0
                      </div>
                      <div class="coop-stat-label">满意度评分</div>
                    </div>
                  </div>
                  <div class="cooperation-trend">
                    <h4 class="trend-title">近期合作趋势</h4>
                    <div class="trend-stats">
                      <div class="trend-item">
                        <span class="trend-label">近12个月订单：</span>
                        <span class="trend-value">
                          {{ customer.cooperationHistory.ordersLast12Months }}个
                        </span>
                      </div>
                      <div class="trend-item">
                        <span class="trend-label">近12个月销售额：</span>
                        <span class="trend-value">
                          ¥{{ formatAmount(customer.cooperationHistory.salesLast12Months) }}
                        </span>
                      </div>
                      <div class="trend-item">
                        <span class="trend-label">平均订单金额：</span>
                        <span class="trend-value">
                          ¥{{ formatAmount(customer.cooperationHistory.averageOrderAmount) }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 联系人信息 -->
          <el-tab-pane label="联系人信息" name="contacts">
            <div class="tab-content">
              <div class="contacts-list">
                <div v-for="contact in customer.contacts" :key="contact.id" class="contact-card">
                  <div class="contact-header">
                    <div class="contact-avatar">
                      <svg viewBox="0 0 24 24" fill="currentColor">
                        <path
                          d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"
                        />
                      </svg>
                    </div>
                    <div class="contact-basic">
                      <h4 class="contact-name">
                        {{ contact.name }}
                      </h4>
                      <p class="contact-position">
                        {{ contact.position }} | {{ contact.department }}
                      </p>
                      <div class="contact-badges">
                        <el-tag v-if="contact.isPrimary" type="danger" size="small">
                          主要联系人
                        </el-tag>
                        <el-tag
                          v-for="type in contact.contactType"
                          :key="type"
                          size="small"
                          class="type-tag"
                        >
                          {{ getContactTypeText(type) }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                  <div class="contact-info">
                    <div class="contact-item">
                      <svg
                        class="contact-icon"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="1.5"
                      >
                        <path
                          d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                        />
                      </svg>
                      <span>{{ contact.mobile }}</span>
                    </div>
                    <div class="contact-item">
                      <svg
                        class="contact-icon"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="1.5"
                      >
                        <rect width="20" height="16" x="2" y="4" rx="2" />
                        <path d="m22 7-10 5L2 7" />
                      </svg>
                      <span>{{ contact.email }}</span>
                    </div>
                    <div v-if="contact.wechat" class="contact-item">
                      <svg class="contact-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path
                          d="M8.5 11.5C9.33 11.5 10 10.83 10 10S9.33 8.5 8.5 8.5S7 9.17 7 10S7.67 11.5 8.5 11.5M15.5 11.5C16.33 11.5 17 10.83 17 10S16.33 8.5 15.5 8.5S14 9.17 14 10S14.67 11.5 15.5 11.5Z"
                        />
                      </svg>
                      <span>{{ contact.wechat }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 财务信息 -->
          <el-tab-pane label="财务信息" name="financial">
            <div class="tab-content">
              <div class="info-sections">
                <!-- 信用信息 -->
                <div class="info-section">
                  <h3 class="section-title">
                    <svg
                      class="section-icon"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="1.5"
                    >
                      <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
                      <line x1="8" y1="21" x2="16" y2="21" />
                      <line x1="12" y1="17" x2="12" y2="21" />
                    </svg>
                    信用信息
                  </h3>
                  <div class="info-grid">
                    <div class="info-item">
                      <label class="info-label">信用等级</label>
                      <el-tag
                        :type="getCreditLevelType(customer.financialInfo.creditLevel)"
                        size="small"
                      >
                        {{ customer.financialInfo.creditLevel }}级
                      </el-tag>
                    </div>
                    <div class="info-item">
                      <label class="info-label">信用额度</label>
                      <span class="info-value">
                        ¥{{ formatAmount(customer.financialInfo.creditLimit) }}
                      </span>
                    </div>
                    <div class="info-item">
                      <label class="info-label">付款方式</label>
                      <span class="info-value">{{ customer.financialInfo.paymentTerms }}</span>
                    </div>
                    <div class="info-item">
                      <label class="info-label">付款周期</label>
                      <span class="info-value">{{ customer.financialInfo.paymentCycle }}天</span>
                    </div>
                    <div class="info-item">
                      <label class="info-label">货币类型</label>
                      <span class="info-value">{{ customer.financialInfo.currency }}</span>
                    </div>
                    <div class="info-item">
                      <label class="info-label">税率</label>
                      <span class="info-value">
                        {{ (customer.financialInfo.taxRate * 100).toFixed(1) }}%
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 开票信息 -->
                <div class="info-section">
                  <h3 class="section-title">
                    <svg
                      class="section-icon"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="1.5"
                    >
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                      <polyline points="14,2 14,8 20,8" />
                      <line x1="16" y1="13" x2="8" y2="13" />
                      <line x1="16" y1="17" x2="8" y2="17" />
                      <polyline points="10,9 9,9 8,9" />
                    </svg>
                    开票信息
                  </h3>
                  <div class="info-grid">
                    <div class="info-item full-width">
                      <label class="info-label">发票抬头</label>
                      <span class="info-value">
                        {{ customer.financialInfo.invoiceInfo.invoiceTitle || '-' }}
                      </span>
                    </div>
                    <div class="info-item">
                      <label class="info-label">纳税人识别号</label>
                      <span class="info-value">
                        {{ customer.financialInfo.invoiceInfo.taxNumber || '-' }}
                      </span>
                    </div>
                    <div class="info-item">
                      <label class="info-label">开户银行</label>
                      <span class="info-value">
                        {{ customer.financialInfo.invoiceInfo.bankName || '-' }}
                      </span>
                    </div>
                    <div class="info-item full-width">
                      <label class="info-label">银行账号</label>
                      <span class="info-value">
                        {{ customer.financialInfo.invoiceInfo.bankAccount || '-' }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 技术信息 -->
          <el-tab-pane label="技术信息" name="technical">
            <div class="tab-content">
              <div class="info-sections">
                <div class="info-section">
                  <h3 class="section-title">
                    <svg
                      class="section-icon"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="1.5"
                    >
                      <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
                      <line x1="8" y1="21" x2="16" y2="21" />
                      <line x1="12" y1="17" x2="12" y2="21" />
                    </svg>
                    技术偏好
                  </h3>
                  <div class="technical-tags">
                    <div class="tag-group">
                      <label class="tag-group-label">主要产品类型：</label>
                      <div class="tag-list">
                        <el-tag
                          v-for="type in customer.technicalInfo.mainProductTypes"
                          :key="type"
                          size="small"
                          class="tech-tag"
                        >
                          {{ getProductTypeText(type) }}
                        </el-tag>
                      </div>
                    </div>

                    <div class="tag-group">
                      <label class="tag-group-label">封装类型偏好：</label>
                      <div class="tag-list">
                        <el-tag
                          v-for="type in customer.technicalInfo.packageTypePreferences"
                          :key="type"
                          type="success"
                          size="small"
                          class="tech-tag"
                        >
                          {{ type }}
                        </el-tag>
                      </div>
                    </div>

                    <div class="tag-group">
                      <label class="tag-group-label">测试要求：</label>
                      <div class="tag-list">
                        <el-tag
                          v-for="req in customer.technicalInfo.testRequirements"
                          :key="req"
                          type="warning"
                          size="small"
                          class="tech-tag"
                        >
                          {{ getTestRequirementText(req) }}
                        </el-tag>
                      </div>
                    </div>

                    <div class="tag-group">
                      <label class="tag-group-label">质量标准：</label>
                      <div class="tag-list">
                        <el-tag
                          v-for="standard in customer.technicalInfo.qualityStandards"
                          :key="standard"
                          type="danger"
                          size="small"
                          class="tech-tag"
                        >
                          {{ standard }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 备注信息 -->
          <el-tab-pane label="备注信息" name="remarks">
            <div class="tab-content">
              <div class="remarks-section">
                <div class="remarks-content">
                  <p v-if="customer.remarks" class="remarks-text">
                    {{ customer.remarks }}
                  </p>
                  <p
v-else class="no-remarks">暂无备注信息</p>
                </div>

                <div class="meta-info">
                  <div class="meta-item">
                    <label class="meta-label">创建时间：</label>
                    <span class="meta-value">{{ formatDateTime(customer.createdAt) }}</span>
                  </div>
                  <div class="meta-item">
                    <label class="meta-label">更新时间：</label>
                    <span class="meta-value">{{ formatDateTime(customer.updatedAt) }}</span>
                  </div>
                  <div class="meta-item">
                    <label class="meta-label">创建人：</label>
                    <span class="meta-value">{{ customer.createdBy }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <div v-else class="drawer-empty">
      <svg
        class="empty-icon"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="1.5"
      >
        <circle cx="11" cy="11" r="8" />
        <path d="m21 21-4.35-4.35" />
      </svg>
      <p class="empty-text">未找到客户信息</p>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import type { Customer } from '@/types/customer'
  import { useCustomerStore } from '@/stores/customer'

  interface Props {
    modelValue: boolean
    customerId?: string
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void
    (e: 'edit', customer: Customer): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  const customerStore = useCustomerStore()

  // 响应式数据
  const loading = ref(false)
  const customer = ref<Customer | null>(null)
  const activeTab = ref('basic')

  // 计算属性
  const visible = computed({
    get: () => props.modelValue,
    set: value => emit('update:modelValue', value)
  })

  const drawerSize = computed(() => {
    const width = window.innerWidth
    if (width <= 768) return '100%'
    if (width <= 1200) return '80%'
    return '60%'
  })

  // 监听客户ID变化
  watch(
    () => props.customerId,
    async newId => {
      if (newId && visible.value) {
        await fetchCustomerDetail(newId)
      }
    },
    { immediate: true }
  )

  // 监听抽屉显示状态
  watch(visible, async newVisible => {
    if (newVisible && props.customerId) {
      await fetchCustomerDetail(props.customerId)
    } else if (!newVisible) {
      customer.value = null
    }
  })

  // 获取客户详情
  const fetchCustomerDetail = async (id: string) => {
    loading.value = true
    try {
      customer.value = await customerStore.fetchCustomerById(id)
    } catch (error) {
      console.error('获取客户详情失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 处理编辑
  const handleEdit = () => {
    if (customer.value) {
      emit('edit', customer.value)
    }
  }

  // 工具函数
  const getCustomerLevelText = (level: string) => {
    const map: Record<string, string> = {
      STRATEGIC: '战略客户',
      IMPORTANT: '重要客户',
      NORMAL: '普通客户',
      POTENTIAL: '潜在客户'
    }
    return map[level] || level
  }

  const getCustomerLevelType = (level: string) => {
    const map: Record<string, string> = {
      STRATEGIC: 'danger',
      IMPORTANT: 'warning',
      NORMAL: 'info',
      POTENTIAL: 'success'
    }
    return map[level] || 'info'
  }

  const getCustomerTypeText = (type: string) => {
    const map: Record<string, string> = {
      IC_DESIGN: 'IC设计公司',
      FOUNDRY: 'Foundry代工厂',
      SYSTEM_INTEGRATOR: '系统集成商',
      OEM: 'OEM厂商',
      TRADER: '贸易商'
    }
    return map[type] || type
  }

  const getCustomerTypeType = (type: string) => {
    const map: Record<string, string> = {
      IC_DESIGN: 'primary',
      FOUNDRY: 'success',
      SYSTEM_INTEGRATOR: 'info',
      OEM: 'warning',
      TRADER: 'danger'
    }
    return map[type] || 'info'
  }

  const getStatusText = (status: string) => {
    const map: Record<string, string> = {
      ACTIVE: '活跃',
      SUSPENDED: '暂停合作',
      POTENTIAL: '潜在客户',
      INACTIVE: '已停止合作'
    }
    return map[status] || status
  }

  const getStatusType = (status: string) => {
    const map: Record<string, string> = {
      ACTIVE: 'success',
      SUSPENDED: 'warning',
      POTENTIAL: 'info',
      INACTIVE: 'danger'
    }
    return map[status] || 'info'
  }

  const getIndustryText = (industry: string) => {
    const map: Record<string, string> = {
      AUTOMOTIVE: '汽车电子',
      CONSUMER_ELECTRONICS: '消费电子',
      COMMUNICATION: '通信设备',
      INDUSTRIAL_CONTROL: '工业控制',
      COMPUTER: '计算机及周边',
      MEDICAL: '医疗电子',
      AEROSPACE: '航空航天',
      MILITARY: '军工'
    }
    return map[industry] || industry
  }

  const getApplicationFieldText = (field: string) => {
    const map: Record<string, string> = {
      SMART_DRIVING: '智能驾驶',
      NEW_ENERGY_VEHICLE: '新能源汽车',
      '5G': '5G通信',
      IOT: '物联网',
      AI: '人工智能',
      SMART_HOME: '智能家居',
      INDUSTRY_4_0: '工业4.0',
      NEW_ENERGY: '新能源'
    }
    return map[field] || field
  }

  const getContactTypeText = (type: string) => {
    const map: Record<string, string> = {
      TECHNICAL: '技术',
      BUSINESS: '商务',
      FINANCIAL: '财务',
      QUALITY: '质量',
      PROCUREMENT: '采购'
    }
    return map[type] || type
  }

  const getCreditLevelType = (level: string) => {
    const map: Record<string, string> = {
      AAA: 'success',
      AA: 'success',
      A: 'primary',
      BBB: 'info',
      BB: 'warning',
      B: 'warning',
      C: 'danger'
    }
    return map[level] || 'info'
  }

  const getProductTypeText = (type: string) => {
    const map: Record<string, string> = {
      ANALOG_IC: '模拟IC',
      DIGITAL_IC: '数字IC',
      MIXED_SIGNAL_IC: '混合信号IC',
      RF_IC: '射频IC',
      POWER_IC: '功率IC',
      MICROCONTROLLER: '微控制器',
      PROCESSOR: '处理器',
      MEMORY: '存储器'
    }
    return map[type] || type
  }

  const getTestRequirementText = (req: string) => {
    const map: Record<string, string> = {
      CP_TEST: 'CP测试',
      FT_TEST: 'FT测试',
      BURN_IN_TEST: '老化测试',
      RELIABILITY_TEST: '可靠性测试',
      ESS: '环境应力筛选'
    }
    return map[req] || req
  }

  const formatAmount = (amount: number) => {
    return (amount / 10000).toFixed(1) + '万'
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }
</script>

<style lang="scss" scoped>
  .customer-detail-drawer {
    :deep(.el-drawer__body) {
      padding: 0;
      background-color: var(--color-bg-secondary);
    }
  }

  .drawer-loading {
    padding: var(--spacing-6);
  }

  .drawer-content {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .drawer-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: var(--spacing-6);
    background: var(--color-bg-primary);
    border-bottom: 1px solid var(--color-border-light);

    &__main {
      display: flex;
      flex: 1;
      gap: var(--spacing-4);
    }

    &__actions {
      display: flex;
      gap: var(--spacing-3);
    }
  }

  .customer-avatar {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    color: var(--color-primary);
    background: var(--color-primary-light);
    border-radius: var(--radius-full);

    svg {
      width: 32px;
      height: 32px;
    }
  }

  .customer-basic {
    flex: 1;
  }

  .customer-name {
    margin: 0 0 var(--spacing-2) 0;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-primary);
  }

  .customer-name-en {
    margin: 0 0 var(--spacing-3) 0;
    font-size: var(--font-size-base);
    color: var(--color-text-secondary);
  }

  .customer-badges {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2);
  }

  .button-icon {
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-1);
  }

  .drawer-body {
    flex: 1;
    overflow: hidden;
  }

  .detail-tabs {
    height: 100%;

    :deep(.el-tabs__header) {
      padding: 0 var(--spacing-6);
      margin: 0;
      background: var(--color-bg-primary);
      border-bottom: 1px solid var(--color-border-light);
    }

    :deep(.el-tabs__content) {
      height: calc(100% - 56px);
      overflow-y: auto;
    }
  }

  .tab-content {
    padding: var(--spacing-6);
  }

  .info-sections {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
  }

  .info-section {
    padding: var(--spacing-6);
    background: var(--color-bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
  }

  .section-title {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
    margin: 0 0 var(--spacing-4) 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);

    .section-icon {
      width: 20px;
      height: 20px;
      color: var(--color-primary);
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);

    .info-item {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-1);

      &.full-width {
        grid-column: 1 / -1;
      }

      .info-label {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        color: var(--color-text-secondary);
      }

      .info-value {
        font-size: var(--font-size-base);
        color: var(--color-text-primary);
        word-break: break-all;

        .field-tag {
          margin-right: var(--spacing-1);
          margin-bottom: var(--spacing-1);
        }
      }
    }
  }

  .cooperation-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
  }

  .coop-stat-item {
    padding: var(--spacing-4);
    text-align: center;
    background: var(--color-bg-secondary);
    border-radius: var(--radius-base);

    .coop-stat-value {
      margin-bottom: var(--spacing-2);
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-primary);
    }

    .coop-stat-label {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }
  }

  .cooperation-trend {
    .trend-title {
      margin: 0 0 var(--spacing-3) 0;
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }

    .trend-stats {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .trend-item {
      display: flex;
      justify-content: space-between;
      padding: var(--spacing-2) 0;
      border-bottom: 1px solid var(--color-border-lighter);

      &:last-child {
        border-bottom: none;
      }

      .trend-label {
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }

      .trend-value {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        color: var(--color-text-primary);
      }
    }
  }

  .contacts-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
  }

  .contact-card {
    padding: var(--spacing-6);
    background: var(--color-bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
  }

  .contact-header {
    display: flex;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-4);
  }

  .contact-avatar {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    color: var(--color-success);
    background: var(--color-success-light);
    border-radius: var(--radius-full);

    svg {
      width: 24px;
      height: 24px;
    }
  }

  .contact-basic {
    flex: 1;
  }

  .contact-name {
    margin: 0 0 var(--spacing-1) 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
  }

  .contact-position {
    margin: 0 0 var(--spacing-2) 0;
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
  }

  .contact-badges {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-1);

    .type-tag {
      margin-right: var(--spacing-1);
    }
  }

  .contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .contact-item {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--color-text-primary);

    .contact-icon {
      width: 16px;
      height: 16px;
      color: var(--color-text-secondary);
    }
  }

  .technical-tags {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
  }

  .tag-group {
    .tag-group-label {
      display: block;
      margin-bottom: var(--spacing-2);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-secondary);
    }

    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-1);

      .tech-tag {
        margin-right: var(--spacing-1);
        margin-bottom: var(--spacing-1);
      }
    }
  }

  .remarks-section {
    padding: var(--spacing-6);
    background: var(--color-bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
  }

  .remarks-content {
    padding-bottom: var(--spacing-6);
    margin-bottom: var(--spacing-6);
    border-bottom: 1px solid var(--color-border-lighter);

    .remarks-text {
      margin: 0;
      font-size: var(--font-size-base);
      line-height: var(--line-height-relaxed);
      color: var(--color-text-primary);
      white-space: pre-wrap;
    }

    .no-remarks {
      margin: 0;
      font-size: var(--font-size-base);
      font-style: italic;
      color: var(--color-text-placeholder);
    }
  }

  .meta-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .meta-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .meta-label {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-secondary);
    }

    .meta-value {
      font-family: var(--font-family-mono);
      font-size: var(--font-size-sm);
      color: var(--color-text-primary);
    }
  }

  .drawer-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: var(--spacing-6);
    color: var(--color-text-secondary);

    .empty-icon {
      width: 64px;
      height: 64px;
      margin-bottom: var(--spacing-4);
      opacity: 0.5;
    }

    .empty-text {
      margin: 0;
      font-size: var(--font-size-lg);
    }
  }

  // 响应式设计
  @media (width <= 768px) {
    .drawer-header {
      flex-direction: column;
      gap: var(--spacing-4);

      &__main {
        flex-direction: column;
        gap: var(--spacing-3);
      }

      &__actions {
        align-self: stretch;
      }
    }

    .customer-basic {
      text-align: center;
    }

    .info-grid {
      grid-template-columns: 1fr;
    }

    .cooperation-stats {
      grid-template-columns: repeat(2, 1fr);
    }

    .contact-header {
      flex-direction: column;
      text-align: center;
    }

    .tab-content {
      padding: var(--spacing-4);
    }

    .info-section {
      padding: var(--spacing-4);
    }
  }
</style>
