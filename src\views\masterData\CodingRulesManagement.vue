<template>
  <div class="coding-rules-management">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon><Setting /></el-icon>
          编码规则管理
        </h1>
        <p class="page-description">管理系统各类业务单据的编码规则，确保数据标识的唯一性和规范性</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增规则
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区 -->
    <div class="search-section">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="规则名称">
          <el-input 
            v-model="searchForm.ruleName" 
            placeholder="请输入规则名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="业务类型">
          <el-select 
            v-model="searchForm.businessType" 
            placeholder="请选择业务类型"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="type in businessTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select 
            v-model="searchForm.status" 
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table 
        :data="tableData" 
        :loading="loading"
        row-key="id"
        class="data-table"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="ruleName" label="规则名称" min-width="160">
          <template #default="{ row }">
            <div class="rule-name">
              <el-icon class="rule-icon"><DocumentCopy /></el-icon>
              <span>{{ row.ruleName }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="businessType" label="业务类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getBusinessTypeTagType(row.businessType)" size="small">
              {{ getBusinessTypeLabel(row.businessType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="rulePattern" label="编码规则" min-width="200">
          <template #default="{ row }">
            <code class="rule-pattern">{{ row.rulePattern }}</code>
          </template>
        </el-table-column>
        
        <el-table-column prop="example" label="示例" min-width="140">
          <template #default="{ row }">
            <span class="example-code">{{ row.example }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="currentNumber" label="当前序号" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.currentNumber }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              active-value="active"
              inactive-value="inactive"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="lastUsedDate" label="最后使用" width="120" sortable="custom">
          <template #default="{ row }">
            <span class="date-text">{{ formatDate(row.lastUsedDate) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="创建时间" width="120" sortable="custom">
          <template #default="{ row }">
            <span class="date-text">{{ formatDate(row.createTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button link type="primary" @click="handleEdit(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button link type="primary" @click="handleTest(row)">
                <el-icon><View /></el-icon>
                测试
              </el-button>
              <el-button link type="danger" @click="handleDelete(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="rule-form"
      >
        <el-form-item label="规则名称" prop="ruleName">
          <el-input
            v-model="formData.ruleName"
            placeholder="请输入规则名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="业务类型" prop="businessType">
          <el-select
            v-model="formData.businessType"
            placeholder="请选择业务类型"
            style="width: 100%"
          >
            <el-option
              v-for="type in businessTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="编码规则" prop="rulePattern">
          <div class="pattern-input">
            <el-input
              v-model="formData.rulePattern"
              placeholder="例如：WO{YYYY}{MM}{DD}{###}"
              maxlength="50"
            />
            <div class="pattern-help">
              <el-text size="small" type="info">
                支持变量：{YYYY}年份，{MM}月份，{DD}日期，{###}序号（可指定位数）
              </el-text>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="起始序号" prop="startNumber">
          <el-input-number
            v-model="formData.startNumber"
            :min="1"
            :max="999999"
            placeholder="起始序号"
            style="width: 150px"
          />
        </el-form-item>
        
        <el-form-item label="序号步长" prop="stepNumber">
          <el-input-number
            v-model="formData.stepNumber"
            :min="1"
            :max="100"
            placeholder="步长"
            style="width: 150px"
          />
        </el-form-item>
        
        <el-form-item label="重置周期" prop="resetPeriod">
          <el-select
            v-model="formData.resetPeriod"
            placeholder="请选择重置周期"
            style="width: 200px"
          >
            <el-option label="永不重置" value="never" />
            <el-option label="每日重置" value="daily" />
            <el-option label="每月重置" value="monthly" />
            <el-option label="每年重置" value="yearly" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            placeholder="请输入规则描述"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio value="active">启用</el-radio>
            <el-radio value="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">
            <el-icon><Check /></el-icon>
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编码测试对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="编码规则测试"
      width="500px"
    >
      <div class="test-content">
        <div class="test-info">
          <p><strong>规则名称：</strong>{{ testData.ruleName }}</p>
          <p><strong>编码规则：</strong><code>{{ testData.rulePattern }}</code></p>
        </div>
        
        <el-divider />
        
        <div class="test-generate">
          <p><strong>生成示例：</strong></p>
          <div class="generated-codes">
            <div
              v-for="(code, index) in generatedCodes"
              :key="index"
              class="code-item"
            >
              <span class="code-text">{{ code }}</span>
            </div>
          </div>
          <el-button type="primary" @click="generateTestCodes" style="margin-top: 12px">
            <el-icon><Refresh /></el-icon>
            重新生成
          </el-button>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="testDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  Plus, Search, RefreshLeft, Edit, Delete, View, Check, Refresh,
  Setting, DocumentCopy
} from '@element-plus/icons-vue'
import { usePagination } from '@/composables/usePagination'
import { formatDate } from '@/utils/common'

// 业务类型配置
const businessTypes = [
  { label: '工单', value: 'work_order' },
  { label: '客户订单', value: 'customer_order' },
  { label: '采购订单', value: 'purchase_order' },
  { label: '入库单', value: 'inbound_receipt' },
  { label: '出库单', value: 'outbound_issue' },
  { label: '质检单', value: 'quality_check' },
  { label: '设备保养', value: 'maintenance' },
  { label: '产品批次', value: 'product_lot' },
  { label: '物料批次', value: 'material_lot' },
  { label: '客户编码', value: 'customer_code' },
  { label: '供应商编码', value: 'supplier_code' },
  { label: '物料编码', value: 'material_code' }
]

// 响应式数据
const loading = ref(false)
const tableData = ref([])

// 搜索表单
const searchForm = reactive({
  ruleName: '',
  businessType: '',
  status: ''
})

// 分页
const { pagination, handlePageChange, handlePageSizeChange } = usePagination()

// 对话框控制
const dialogVisible = ref(false)
const testDialogVisible = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  id: null as number | null,
  ruleName: '',
  businessType: '',
  rulePattern: '',
  startNumber: 1,
  stepNumber: 1,
  resetPeriod: 'never',
  description: '',
  status: 'active' as 'active' | 'inactive'
})

// 表单验证规则
const formRules: FormRules = {
  ruleName: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 50, message: '规则名称长度在2-50个字符', trigger: 'blur' }
  ],
  businessType: [
    { required: true, message: '请选择业务类型', trigger: 'change' }
  ],
  rulePattern: [
    { required: true, message: '请输入编码规则', trigger: 'blur' },
    { min: 5, max: 50, message: '编码规则长度在5-50个字符', trigger: 'blur' }
  ],
  startNumber: [
    { required: true, message: '请输入起始序号', trigger: 'blur' }
  ],
  stepNumber: [
    { required: true, message: '请输入序号步长', trigger: 'blur' }
  ],
  resetPeriod: [
    { required: true, message: '请选择重置周期', trigger: 'change' }
  ]
}

// 测试数据
const testData = reactive({
  ruleName: '',
  rulePattern: ''
})
const generatedCodes = ref<string[]>([])

// 计算属性
const dialogTitle = computed(() => formData.id ? '编辑编码规则' : '新增编码规则')

// 获取业务类型标签
const getBusinessTypeLabel = (value: string) => {
  return businessTypes.find(type => type.value === value)?.label || value
}

// 获取业务类型标签类型
const getBusinessTypeTagType = (businessType: string) => {
  const typeMap: Record<string, string> = {
    'work_order': 'primary',
    'customer_order': 'success',
    'purchase_order': 'warning',
    'inbound_receipt': 'info',
    'outbound_issue': 'danger',
    'quality_check': '',
    'maintenance': 'warning',
    'product_lot': 'primary',
    'material_lot': 'info',
    'customer_code': 'success',
    'supplier_code': 'warning',
    'material_code': ''
  }
  return typeMap[businessType] || ''
}

// 模拟数据
const mockData = [
  {
    id: 1,
    ruleName: '工单编码规则',
    businessType: 'work_order',
    rulePattern: 'WO{YYYY}{MM}{DD}{###}',
    example: 'WO20241227001',
    currentNumber: 156,
    startNumber: 1,
    stepNumber: 1,
    resetPeriod: 'daily',
    status: 'active',
    lastUsedDate: '2024-12-27T10:30:00',
    createTime: '2024-01-15T09:00:00',
    description: '工单编码规则，每日重置序号'
  },
  {
    id: 2,
    ruleName: '客户订单编码',
    businessType: 'customer_order',
    rulePattern: 'SO{YYYY}{####}',
    example: 'SO20240125',
    currentNumber: 892,
    startNumber: 1,
    stepNumber: 1,
    resetPeriod: 'yearly',
    status: 'active',
    lastUsedDate: '2024-12-27T15:45:00',
    createTime: '2024-01-10T14:20:00',
    description: '客户订单编码，每年重置'
  },
  {
    id: 3,
    ruleName: '入库单编码',
    businessType: 'inbound_receipt',
    rulePattern: 'IN{YYYY}{MM}{###}',
    example: 'IN202412045',
    currentNumber: 45,
    startNumber: 1,
    stepNumber: 1,
    resetPeriod: 'monthly',
    status: 'active',
    lastUsedDate: '2024-12-26T16:20:00',
    createTime: '2024-02-01T11:30:00',
    description: '入库单编码，每月重置'
  },
  {
    id: 4,
    ruleName: '质检单编码',
    businessType: 'quality_check',
    rulePattern: 'QC{YYYY}{MM}{DD}{##}',
    example: 'QC2024122712',
    currentNumber: 12,
    startNumber: 1,
    stepNumber: 1,
    resetPeriod: 'daily',
    status: 'inactive',
    lastUsedDate: '2024-12-25T08:15:00',
    createTime: '2024-03-15T10:45:00',
    description: '质检单编码规则'
  },
  {
    id: 5,
    ruleName: '物料编码规则',
    businessType: 'material_code',
    rulePattern: 'MAT{######}',
    example: 'MAT000234',
    currentNumber: 234,
    startNumber: 1,
    stepNumber: 1,
    resetPeriod: 'never',
    status: 'active',
    lastUsedDate: '2024-12-27T13:25:00',
    createTime: '2024-01-05T08:00:00',
    description: '物料主数据编码，永不重置'
  }
]

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 应用搜索过滤
    let filteredData = mockData.filter(item => {
      const matchName = !searchForm.ruleName || 
        item.ruleName.toLowerCase().includes(searchForm.ruleName.toLowerCase())
      const matchType = !searchForm.businessType || item.businessType === searchForm.businessType
      const matchStatus = !searchForm.status || item.status === searchForm.status
      
      return matchName && matchType && matchStatus
    })
    
    // 分页处理
    const start = (pagination.page - 1) * pagination.size
    const end = start + pagination.size
    tableData.value = filteredData.slice(start, end)
    pagination.total = filteredData.length
    
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error('Fetch data error:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    ruleName: '',
    businessType: '',
    status: ''
  })
  pagination.page = 1
  fetchData()
}

const handleAdd = () => {
  Object.assign(formData, {
    id: null,
    ruleName: '',
    businessType: '',
    rulePattern: '',
    startNumber: 1,
    stepNumber: 1,
    resetPeriod: 'never',
    description: '',
    status: 'active'
  })
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  Object.assign(formData, { ...row })
  dialogVisible.value = true
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(formData.id ? '更新成功' : '创建成功')
    dialogVisible.value = false
    fetchData()
    
  } catch (error) {
    if (error !== false) { // 不是验证错误
      ElMessage.error('操作失败')
      console.error('Submit error:', error)
    }
  } finally {
    loading.value = false
  }
}

const handleStatusChange = async (row: any) => {
  try {
    loading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success(`${row.status === 'active' ? '启用' : '禁用'}成功`)
    
  } catch (error) {
    // 恢复状态
    row.status = row.status === 'active' ? 'inactive' : 'active'
    ElMessage.error('操作失败')
    console.error('Status change error:', error)
  } finally {
    loading.value = false
  }
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除编码规则"${row.ruleName}"吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        buttonSize: 'default'
      }
    )
    
    loading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('删除成功')
    fetchData()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('Delete error:', error)
    }
  } finally {
    loading.value = false
  }
}

const handleTest = (row: any) => {
  testData.ruleName = row.ruleName
  testData.rulePattern = row.rulePattern
  generateTestCodes()
  testDialogVisible.value = true
}

const generateTestCodes = () => {
  const codes = []
  const now = new Date()
  const pattern = testData.rulePattern
  
  for (let i = 0; i < 5; i++) {
    let code = pattern
      .replace('{YYYY}', now.getFullYear().toString())
      .replace('{MM}', (now.getMonth() + 1).toString().padStart(2, '0'))
      .replace('{DD}', now.getDate().toString().padStart(2, '0'))
      .replace('{####}', (i + 1).toString().padStart(4, '0'))
      .replace('{###}', (i + 1).toString().padStart(3, '0'))
      .replace('{##}', (i + 1).toString().padStart(2, '0'))
      .replace('{######}', (i + 1).toString().padStart(6, '0'))
    
    codes.push(code)
  }
  
  generatedCodes.value = codes
}

const handleSortChange = ({ prop, order }: any) => {
  // 实现排序逻辑
  console.log('Sort change:', prop, order)
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.coding-rules-management {
  padding: 24px;
  background: var(--color-bg-1);
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--color-border-2);

  .header-left {
    flex: 1;
  }

  .page-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 24px;
    font-weight: 600;
    color: var(--color-text-1);
    margin: 0 0 8px 0;

    .el-icon {
      color: var(--color-primary);
    }
  }

  .page-description {
    color: var(--color-text-3);
    margin: 0;
    line-height: 1.5;
  }

  .header-actions {
    margin-left: 16px;
  }
}

.search-section {
  background: var(--color-bg-2);
  padding: 20px;
  border-radius: var(--radius-base);
  margin-bottom: 16px;

  .search-form {
    .el-form-item {
      margin-bottom: 0;
    }
  }
}

.table-section {
  background: var(--color-bg-2);
  border-radius: var(--radius-base);
  overflow: hidden;

  .data-table {
    .rule-name {
      display: flex;
      align-items: center;
      gap: 8px;

      .rule-icon {
        color: var(--color-primary);
        font-size: 16px;
      }
    }

    .rule-pattern {
      background: var(--color-fill-2);
      padding: 4px 8px;
      border-radius: 4px;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 13px;
      color: var(--color-primary);
    }

    .example-code {
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-weight: 500;
      color: var(--color-success);
    }

    .date-text {
      color: var(--color-text-2);
      font-size: 13px;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }
}

.pagination-wrapper {
  padding: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}

.rule-form {
  .pattern-input {
    .pattern-help {
      margin-top: 8px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.test-content {
  .test-info {
    p {
      margin: 8px 0;
      line-height: 1.6;

      code {
        background: var(--color-fill-2);
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        color: var(--color-primary);
      }
    }
  }

  .test-generate {
    .generated-codes {
      margin: 12px 0;
      
      .code-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background: var(--color-fill-1);
        border-radius: 6px;
        margin-bottom: 8px;

        .code-text {
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 14px;
          font-weight: 500;
          color: var(--color-success);
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .coding-rules-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .header-actions {
      margin-left: 0;
    }
  }

  .search-section {
    padding: 16px;

    .search-form {
      .el-form-item {
        display: block;
        margin-bottom: 16px;

        :deep(.el-form-item__content) {
          margin-left: 0 !important;
        }
      }
    }
  }

  .action-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .rule-pattern {
    background: var(--el-fill-color-darker);
    color: var(--el-color-primary-light-3);
  }

  .example-code {
    color: var(--el-color-success-light-3);
  }

  .generated-codes .code-item {
    background: var(--el-fill-color-darker);
    
    .code-text {
      color: var(--el-color-success-light-3);
    }
  }

  .test-info code {
    background: var(--el-fill-color-darker);
    color: var(--el-color-primary-light-3);
  }
}
</style>