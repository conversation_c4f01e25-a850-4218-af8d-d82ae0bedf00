// IC封测CIM系统 - 订单类型定义

// 订单状态枚举
export enum OrderStatus {
  PENDING = 'pending',        // 待确认
  CONFIRMED = 'confirmed',    // 已确认
  PROCESSING = 'processing',  // 生产中
  TESTING = 'testing',       // 测试中
  COMPLETED = 'completed',   // 已完成
  CANCELLED = 'cancelled',   // 已取消
  ON_HOLD = 'on_hold'       // 暂停
}

// 订单优先级枚举
export enum OrderPriority {
  LOW = 'low',
  MEDIUM = 'medium', 
  HIGH = 'high',
  URGENT = 'urgent'
}

// 封装类型枚举
export enum PackageType {
  QFP = 'QFP',           // Quad Flat Package
  BGA = 'BGA',           // Ball Grid Array
  CSP = 'CSP',           // Chip Scale Package
  FC = 'FC',             // Flip Chip
  SOP = 'SOP',           // Small Outline Package
  TSOP = 'TSOP',         // Thin Small Outline Package
  QFN = 'QFN',           // Quad Flat No-leads
  DFN = 'DFN',           // Dual Flat No-leads
  WLCSP = 'WLCSP',       // Wafer Level Chip Scale Package
  SSOP = 'SSOP',         // Shrink Small Outline Package
  TQFP = 'TQFP'          // Thin Quad Flat Package
}

// 货币类型
export enum CurrencyType {
  CNY = 'CNY',
  USD = 'USD',
  EUR = 'EUR'
}

export interface Order {
  id: string
  orderNumber: string
  customerId: string
  customer: {
    id: string
    name: string
    code: string
    contact?: {
      name: string
      phone: string
      email: string
    }
  }
  productInfo: {
    productName: string
    productCode: string
    packageType: PackageType
    quantity: number        // K pcs为单位
    waferSize: number      // 晶圆尺寸（英寸）
    dieSize: string        // 芯片尺寸
    leadCount: number      // 引脚数量
    specifications: string // 规格说明
  }
  pricing: {
    unitPrice: number      // 单价（元/K pcs）
    totalAmount: number    // 总金额
    currency: CurrencyType
    paymentTerms: string   // 付款条件
  }
  schedule: {
    orderDate: string      // 下单日期
    confirmedDate?: string // 确认日期
    startDate?: string     // 开始生产日期
    deliveryDate: string   // 交期
    actualDeliveryDate?: string // 实际交付日期
  }
  status: OrderStatus
  priority: OrderPriority
  progress: {
    overall: number        // 总进度（%）
    cpTesting: number     // CP测试进度（%）
    assembly: number      // 封装进度（%）
    ftTesting: number     // FT测试进度（%）
    packaging: number     // 包装进度（%）
  }
  qualityInfo: {
    yieldRequirement: number  // 良率要求（%）
    currentYield?: number     // 当前良率（%）
    defectRate?: number       // 缺陷率（ppm）
    qualityLevel: 'A' | 'B' | 'C' | 'D'
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  notes?: string
  attachments?: Array<{
    id: string
    name: string
    url: string
    type: string
    uploadAt: string
  }>
}

// 订单查询参数
export interface OrderQueryParams {
  page?: number
  pageSize?: number
  orderNumber?: string
  customerId?: string
  customerName?: string
  status?: OrderStatus[]
  priority?: OrderPriority[]
  packageType?: PackageType[]
  orderDateRange?: [string, string]
  deliveryDateRange?: [string, string]
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 搜索表单
export interface OrderSearchForm {
  orderNumber: string
  customerId: string
  customerName: string
  status: OrderStatus[]
  priority: OrderPriority[]
  packageType: PackageType[]
  orderDateRange: [string, string] | null
  deliveryDateRange: [string, string] | null
}

// 创建订单数据
export interface CreateOrderData {
  customerId: string
  productInfo: {
    productName: string
    productCode: string
    packageType: PackageType
    quantity: number
    waferSize: number
    dieSize: string
    leadCount: number
    specifications: string
  }
  pricing: {
    unitPrice: number
    currency: CurrencyType
    paymentTerms: string
  }
  schedule: {
    deliveryDate: string
  }
  priority: OrderPriority
  qualityInfo: {
    yieldRequirement: number
    qualityLevel: 'A' | 'B' | 'C' | 'D'
  }
  notes?: string
}

// 订单列表响应
export interface OrderListResponse {
  data: Order[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 分页信息
export interface Pagination {
  current: number
  pageSize: number
  total: number
  showSizeChanger: boolean
  showQuickJumper: boolean
  showTotal: boolean
}

// 订单统计数据
export interface OrderStats {
  totalOrders: number           // 总订单数
  processingOrders: number     // 生产中订单
  completedOrders: number      // 已完成订单
  overdueOrders: number        // 逾期订单
  totalVolume: number          // 总产能（K pcs）
  monthlyVolume: number        // 月产能（K pcs）
  averageYield: number         // 平均良率（%）
  onTimeDeliveryRate: number   // 准时交付率（%）
}

export interface OrderStatCard {
  key: string
  label: string
  value: string | number
  detail: string
  trend: 'up' | 'down' | 'stable'
  changePercent: string
  trendClass: string
  iconClass: string
  icon: string
  color: string
}

// 搜索表单类型
export interface SearchForm {
  orderNumber: string
  customerId: string
  status: OrderStatus[]
  priority: OrderPriority[]
  dateRange: [string, string] | null
}

// 分页类型
export interface Pagination {
  current: number
  pageSize: number
  total: number
  showSizeChanger: boolean
  showQuickJumper: boolean
  showTotal: boolean
}