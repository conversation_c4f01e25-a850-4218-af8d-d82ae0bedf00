// CSS Custom Properties for Theme System
:root {
  // 基础间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  // 基础圆角
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  
  // 字体
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  
  // 行高
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-loose: 1.75;
  
  // 过渡
  --transition-fast: 0.15s ease;
  --transition-normal: 0.25s ease;
  --transition-slow: 0.35s ease;
}

// 浅色主题 (Light Theme)
.light-theme {
  // 主色调 - 极简蓝色系
  --color-primary: #2563eb;
  --color-primary-hover: #3b82f6;
  --color-primary-active: #1d4ed8;
  --color-primary-light: #dbeafe;
  --color-primary-dark: #1e40af;

  // 功能色彩 - 柔和版本
  --color-success: #10b981;
  --color-success-hover: #34d399;
  --color-success-light: #d1fae5;
  --color-warning: #f59e0b;
  --color-warning-hover: #fbbf24;
  --color-warning-light: #fef3c7;
  --color-error: #ef4444;
  --color-error-hover: #f87171;
  --color-error-light: #fee2e2;
  --color-info: #6b7280;
  --color-info-hover: #9ca3af;
  --color-info-light: #f3f4f6;

  // 中性色阶 - 极简灰色系
  --color-text-primary: #111827;    // 主要文字
  --color-text-secondary: #6b7280;  // 次要文字
  --color-text-tertiary: #9ca3af;   // 第三级文字
  --color-text-disabled: #d1d5db;   // 禁用文字
  --color-text-placeholder: #9ca3af; // 占位符文字

  // 背景色 - 纯净白色系
  --color-bg-primary: #ffffff;      // 主背景 (纯白)
  --color-bg-secondary: #f9fafb;    // 次要背景
  --color-bg-tertiary: #f3f4f6;     // 第三背景
  --color-bg-hover: #f5f5f5;        // 悬停背景
  --color-bg-active: #e5e7eb;       // 激活背景
  --color-bg-disabled: #f9fafb;     // 禁用背景

  // 边框色 - 清淡边框
  --color-border-light: #f3f4f6;
  --color-border-base: #e5e7eb;
  --color-border-dark: #d1d5db;
  --color-border-focus: #2563eb;

  // 阴影色
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.08), 0 1px 2px 0 rgba(0, 0, 0, 0.04);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.04);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.04);

  // IC封测专业色彩
  --color-wafer: #e0f2fe;           // 晶圆色
  --color-die-pass: #dcfce7;        // 良品die
  --color-die-fail: #fee2e2;        // 不良die  
  --color-cp-test: #f0f9ff;         // CP测试
  --color-assembly: #f3e8ff;        // Assembly
  --color-ft-test: #ecfdf5;         // FT测试
}

// 深色主题 (Dark Theme)
.dark-theme {
  // 主色调 - 柔和蓝色系
  --color-primary: #3b82f6;
  --color-primary-hover: #60a5fa;
  --color-primary-active: #2563eb;
  --color-primary-light: #1e3a8a;
  --color-primary-dark: #1d4ed8;

  // 功能色彩 - 深色优化版本
  --color-success: #34d399;
  --color-success-hover: #6ee7b7;
  --color-success-light: #064e3b;
  --color-warning: #fbbf24;
  --color-warning-hover: #fcd34d;
  --color-warning-light: #78350f;
  --color-error: #f87171;
  --color-error-hover: #fca5a5;
  --color-error-light: #7f1d1d;
  --color-info: #94a3b8;
  --color-info-hover: #cbd5e1;
  --color-info-light: #334155;

  // 中性色阶 - 深色模式文字
  --color-text-primary: #f8fafc;    // 主要文字
  --color-text-secondary: #cbd5e1;  // 次要文字  
  --color-text-tertiary: #94a3b8;   // 第三级文字
  --color-text-disabled: #64748b;   // 禁用文字
  --color-text-placeholder: #64748b; // 占位符文字

  // 背景色 - 深色背景系
  --color-bg-primary: #0f172a;      // 主背景 (深蓝黑)
  --color-bg-secondary: #1e293b;    // 次要背景
  --color-bg-tertiary: #334155;     // 第三背景
  --color-bg-hover: #475569;        // 悬停背景
  --color-bg-active: #64748b;       // 激活背景
  --color-bg-disabled: #1e293b;     // 禁用背景

  // 边框色 - 深色边框
  --color-border-light: #334155;
  --color-border-base: #475569;
  --color-border-dark: #64748b;
  --color-border-focus: #3b82f6;

  // 阴影色
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.15);

  // IC封测专业色彩 (深色优化)
  --color-wafer: #1e3a8a;
  --color-die-pass: #166534;
  --color-die-fail: #991b1b;
  --color-cp-test: #0c4a6e;
  --color-assembly: #581c87;
  --color-ft-test: #14532d;
}