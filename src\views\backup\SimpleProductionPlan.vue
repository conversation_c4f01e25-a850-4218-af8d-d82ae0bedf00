<template>
  <div class="production-plan">
    <div class="page-header">
      <h1>📊 生产计划管理</h1>
      <p>IC封测CIM系统 - 智能化生产计划和甘特图管理</p>
    </div>

    <!-- 统计面板 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">📋</div>
        <div class="stat-content">
          <h3>{{ totalOrders }}</h3>
          <p>总订单数</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">⚡</div>
        <div class="stat-content">
          <h3>{{ activeOrders }}</h3>
          <p>进行中</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <h3>{{ completedToday }}</h3>
          <p>今日完成</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🎯</div>
        <div class="stat-content">
          <h3>{{ completionRate }}%</h3>
          <p>完成率</p>
        </div>
      </div>
    </div>

    <!-- 阶段切换 -->
    <div class="stage-tabs">
      <button
        v-for="stage in stages"
        :key="stage.key"
        :class="['stage-tab', { active: activeStage === stage.key }]"
        @click="activeStage = stage.key"
      >
        {{ stage.icon }} {{ stage.name }}
        <span class="stage-count">{{ getStageCount(stage.key) }}</span>
      </button>
    </div>

    <!-- 甘特图区域 -->
    <div class="gantt-section">
      <div class="gantt-header">
        <h3>生产甘特图 - {{ getCurrentStageName() }}</h3>
        <div class="gantt-controls">
          <select v-model="timeRange" class="time-selector">
            <option value="week">本周</option>
            <option value="month">本月</option>
            <option value="quarter">本季度</option>
          </select>
          <button
class="btn btn-secondary" @click="refreshGantt">🔄 刷新</button>
        </div>
      </div>

      <div class="gantt-container">
        <!-- 时间轴 -->
        <div class="gantt-timeline">
          <div class="timeline-header">时间轴</div>
          <div class="timeline-dates">
            <div v-for="date in timelineDates" :key="date" class="timeline-date">
              {{ formatDate(date) }}
            </div>
          </div>
        </div>

        <!-- 甘特图主体 -->
        <div class="gantt-main">
          <div class="gantt-tasks">
            <div v-for="task in filteredTasks" :key="task.id" class="gantt-row">
              <div class="task-info">
                <div class="task-name">
                  {{ task.name }}
                </div>
                <div class="task-meta">
                  <span class="task-order">{{ task.orderNo }}</span>
                  <span class="task-progress">{{ task.progress }}%</span>
                </div>
              </div>
              <div class="task-timeline">
                <div
class="task-bar" :class="task.status"
:style="getTaskBarStyle(task)"
>
                  <div
class="task-bar-progress" :style="`width: ${task.progress}%`" />
                  <span class="task-bar-text">{{ task.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="filteredTasks.length === 0" class="empty-gantt">
        <p>📊 当前阶段暂无生产任务</p>
      </div>
    </div>

    <!-- 设备状态监控 -->
    <div class="equipment-section">
      <h3>🔧 设备状态监控</h3>
      <div class="equipment-grid">
        <div v-for="equipment in equipmentList" :key="equipment.id" class="equipment-card">
          <div class="equipment-header">
            <h4>{{ equipment.name }}</h4>
            <span class="equipment-status" :class="equipment.status">
              {{ getEquipmentStatusText(equipment.status) }}
            </span>
          </div>
          <div class="equipment-details">
            <div class="detail-item">
              <span>利用率</span>
              <div class="progress-bar">
                <div
class="progress-fill" :style="`width: ${equipment.utilization}%`" />
              </div>
              <span>{{ equipment.utilization }}%</span>
            </div>
            <div class="detail-item">
              <span>当前任务</span>
              <span>{{ equipment.currentTask || '空闲' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 生产阶段详情 -->
    <div class="stage-details">
      <h3>📈 {{ getCurrentStageName() }} - 阶段详情</h3>
      <div class="stage-content">
        <div class="stage-info">
          <div class="info-grid">
            <div class="info-item">
              <label>阶段描述</label>
              <p>{{ getCurrentStageDescription() }}</p>
            </div>
            <div class="info-item">
              <label>关键参数</label>
              <div class="params-list">
                <div v-for="param in getCurrentStageParams()" :key="param.name" class="param-item">
                  <span>{{ param.name }}</span>
                  <span class="param-value">{{ param.value }}</span>
                </div>
              </div>
            </div>
            <div class="info-item">
              <label>质量指标</label>
              <div class="quality-metrics">
                <div class="metric">
                  <span>良品率</span>
                  <span class="metric-value good">{{ getCurrentStageYield() }}%</span>
                </div>
                <div class="metric">
                  <span>Cpk值</span>
                  <span class="metric-value">{{ getCurrentStageCpk() }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'

  // 生产阶段定义
  const stages = ref([
    { key: 'cp-test', name: 'CP测试', icon: '🔬', description: '晶圆电测，检测芯片电性能参数' },
    {
      key: 'assembly',
      name: '封装工艺',
      icon: '⚙️',
      description: '芯片封装，包括贴片、焊线、塑封等工序'
    },
    { key: 'ft-test', name: 'FT测试', icon: '🧪', description: '成品测试，验证最终产品功能和性能' },
    { key: 'delivery', name: '交付', icon: '📦', description: '产品包装、检验、发货交付客户' }
  ])

  // 页面状态
  const activeStage = ref('cp-test')
  const timeRange = ref('week')

  // 生产任务数据
  const productionTasks = ref([
    {
      id: 'T001',
      name: '*********-CP测试',
      orderNo: '*********',
      stage: 'cp-test',
      startDate: '2024-01-15',
      endDate: '2024-01-18',
      progress: 75,
      status: 'active'
    },
    {
      id: 'T002',
      name: '*********-封装工艺',
      orderNo: '*********',
      stage: 'assembly',
      startDate: '2024-01-16',
      endDate: '2024-01-20',
      progress: 45,
      status: 'active'
    },
    {
      id: 'T003',
      name: 'IC2024003-FT测试',
      orderNo: 'IC2024003',
      stage: 'ft-test',
      startDate: '2024-01-18',
      endDate: '2024-01-22',
      progress: 20,
      status: 'pending'
    },
    {
      id: 'T004',
      name: 'IC2024004-交付',
      orderNo: 'IC2024004',
      stage: 'delivery',
      startDate: '2024-01-20',
      endDate: '2024-01-22',
      progress: 100,
      status: 'completed'
    }
  ])

  // 设备列表
  const equipmentList = ref([
    {
      id: 'EQ001',
      name: 'ATE测试机-01',
      status: 'running',
      utilization: 85,
      currentTask: '*********-CP测试'
    },
    {
      id: 'EQ002',
      name: '贴片机-A1',
      status: 'running',
      utilization: 72,
      currentTask: '*********-封装'
    },
    {
      id: 'EQ003',
      name: '焊线机-B2',
      status: 'idle',
      utilization: 0,
      currentTask: null
    },
    {
      id: 'EQ004',
      name: '塑封机-C1',
      status: 'maintenance',
      utilization: 0,
      currentTask: null
    }
  ])

  // 计算属性
  const totalOrders = computed(() => productionTasks.value.length)
  const activeOrders = computed(
    () => productionTasks.value.filter(t => t.status === 'active').length
  )
  const completedToday = computed(
    () => productionTasks.value.filter(t => t.status === 'completed').length
  )
  const completionRate = computed(() => {
    const completed = productionTasks.value.filter(t => t.progress === 100).length
    return totalOrders.value > 0 ? Math.round((completed / totalOrders.value) * 100) : 0
  })

  const filteredTasks = computed(() => {
    return productionTasks.value.filter(task => task.stage === activeStage.value)
  })

  const timelineDates = computed(() => {
    const dates = []
    const start = new Date()
    start.setDate(start.getDate() - 3) // 从3天前开始

    for (let i = 0; i < 14; i++) {
      const date = new Date(start)
      date.setDate(start.getDate() + i)
      dates.push(date.toISOString().split('T')[0])
    }

    return dates
  })

  // 方法
  const getCurrentStageName = () => {
    const stage = stages.value.find(s => s.key === activeStage.value)
    return stage ? stage.name : ''
  }

  const getCurrentStageDescription = () => {
    const stage = stages.value.find(s => s.key === activeStage.value)
    return stage ? stage.description : ''
  }

  const getCurrentStageParams = () => {
    const paramMap = {
      'cp-test': [
        { name: '测试温度', value: '25°C ± 3°C' },
        { name: '测试电压', value: '3.3V ± 0.1V' },
        { name: '测试时间', value: '2.5s/die' }
      ],
      assembly: [
        { name: '贴片精度', value: '±15μm' },
        { name: '焊线参数', value: '25μm金线' },
        { name: '塑封温度', value: '175°C' }
      ],
      'ft-test': [
        { name: '测试覆盖率', value: '100%' },
        { name: '测试项目', value: '45项' },
        { name: '老化时间', value: '168hrs' }
      ],
      delivery: [
        { name: '包装规格', value: '管装/盘装' },
        { name: '防静电', value: 'ESD防护' },
        { name: '标识标签', value: '激光刻印' }
      ]
    }
    return paramMap[activeStage.value] || []
  }

  const getCurrentStageYield = () => {
    const yieldMap = {
      'cp-test': 98.5,
      assembly: 99.2,
      'ft-test': 97.8,
      delivery: 99.9
    }
    return yieldMap[activeStage.value] || 0
  }

  const getCurrentStageCpk = () => {
    const cpkMap = {
      'cp-test': 1.67,
      assembly: 1.83,
      'ft-test': 1.45,
      delivery: 2.1
    }
    return cpkMap[activeStage.value] || 0
  }

  const getStageCount = (stageKey: string) => {
    return productionTasks.value.filter(task => task.stage === stageKey).length
  }

  const getEquipmentStatusText = (status: string) => {
    const statusMap = {
      running: '运行中',
      idle: '空闲',
      maintenance: '维护中',
      error: '故障'
    }
    return statusMap[status] || status
  }

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return `${date.getMonth() + 1}/${date.getDate()}`
  }

  const getTaskBarStyle = task => {
    const startDate = new Date(task.startDate)
    const endDate = new Date(task.endDate)
    const timelineStart = new Date(timelineDates.value[0])
    const timelineEnd = new Date(timelineDates.value[timelineDates.value.length - 1])

    const totalDays = (timelineEnd - timelineStart) / (1000 * 60 * 60 * 24)
    const taskStart = Math.max(0, (startDate - timelineStart) / (1000 * 60 * 60 * 24))
    const taskDuration = (endDate - startDate) / (1000 * 60 * 60 * 24)

    const left = (taskStart / totalDays) * 100
    const width = (taskDuration / totalDays) * 100

    return {
      left: `${left}%`,
      width: `${Math.min(width, 100 - left)}%`
    }
  }

  const refreshGantt = () => {
    console.log('刷新甘特图')
  }

  onMounted(() => {
    console.log('生产计划页面加载完成')
  })
</script>

<style scoped>
  .production-plan {
    min-height: calc(100vh - 100px);
    padding: 1.5rem;
    background: #f9fafb;
  }

  .page-header {
    padding: 2rem;
    margin-bottom: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  }

  .page-header h1 {
    margin: 0 0 0.5rem;
    font-size: 2rem;
    color: #1f2937;
  }

  .page-header p {
    margin: 0;
    color: #6b7280;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .stat-card {
    display: flex;
    gap: 1rem;
    align-items: center;
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  }

  .stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
  }

  .stat-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
  }

  .stat-content p {
    margin: 0.25rem 0 0;
    font-size: 0.875rem;
    color: #6b7280;
  }

  .stage-tabs {
    display: flex;
    gap: 0.5rem;
    padding: 0.5rem;
    margin-bottom: 1.5rem;
    overflow-x: auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  }

  .stage-tab {
    display: flex;
    flex: 1;
    gap: 0.5rem;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    font-weight: 500;
    color: #6b7280;
    white-space: nowrap;
    cursor: pointer;
    background: transparent;
    border: none;
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .stage-tab:hover {
    color: #374151;
    background: #f3f4f6;
  }

  .stage-tab.active {
    color: white;
    background: #2563eb;
  }

  .stage-count {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    background: rgb(255 255 255 / 20%);
    border-radius: 12px;
  }

  .stage-tab.active .stage-count {
    background: rgb(255 255 255 / 30%);
  }

  .gantt-section {
    margin-bottom: 1.5rem;
    overflow: hidden;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  }

  .gantt-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .gantt-header h3 {
    margin: 0;
    color: #1f2937;
  }

  .gantt-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
  }

  .time-selector {
    padding: 0.5rem;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-weight: 500;
    cursor: pointer;
    border: none;
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .btn-secondary {
    color: #374151;
    background: #e5e7eb;
  }

  .btn-secondary:hover {
    background: #d1d5db;
  }

  .gantt-container {
    position: relative;
    overflow-x: auto;
  }

  .gantt-timeline {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
  }

  .timeline-header {
    width: 200px;
    padding: 1rem;
    font-weight: 600;
    background: #f9fafb;
    border-right: 1px solid #e5e7eb;
  }

  .timeline-dates {
    display: flex;
    flex: 1;
  }

  .timeline-date {
    flex: 1;
    min-width: 60px;
    padding: 1rem 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
    border-right: 1px solid #e5e7eb;
  }

  .gantt-main {
    position: relative;
  }

  .gantt-tasks {
    position: relative;
  }

  .gantt-row {
    display: flex;
    min-height: 60px;
    border-bottom: 1px solid #e5e7eb;
  }

  .task-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 200px;
    padding: 1rem;
    border-right: 1px solid #e5e7eb;
  }

  .task-name {
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #1f2937;
  }

  .task-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #6b7280;
  }

  .task-order {
    font-family: monospace;
  }

  .task-timeline {
    position: relative;
    flex: 1;
    padding: 0.75rem 0;
  }

  .task-bar {
    position: absolute;
    top: 50%;
    display: flex;
    align-items: center;
    min-width: 60px;
    height: 24px;
    overflow: hidden;
    border-radius: 4px;
    transform: translateY(-50%);
  }

  .task-bar.active {
    background: #2563eb;
  }

  .task-bar.pending {
    background: #f59e0b;
  }

  .task-bar.completed {
    background: #10b981;
  }

  .task-bar-progress {
    height: 100%;
    background: rgb(255 255 255 / 30%);
    transition: width 0.3s ease;
  }

  .task-bar-text {
    position: absolute;
    right: 0.5rem;
    left: 0.5rem;
    overflow: hidden;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .empty-gantt {
    padding: 3rem;
    color: #6b7280;
    text-align: center;
  }

  .equipment-section {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  }

  .equipment-section h3 {
    margin: 0 0 1.5rem;
    color: #1f2937;
  }

  .equipment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
  }

  .equipment-card {
    padding: 1rem;
    background: #fafafa;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
  }

  .equipment-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
  }

  .equipment-header h4 {
    margin: 0;
    font-size: 1rem;
    color: #1f2937;
  }

  .equipment-status {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 4px;
  }

  .equipment-status.running {
    color: #166534;
    background: #dcfce7;
  }

  .equipment-status.idle {
    color: #6b7280;
    background: #f3f4f6;
  }

  .equipment-status.maintenance {
    color: #92400e;
    background: #fef3c7;
  }

  .equipment-status.error {
    color: #dc2626;
    background: #fee2e2;
  }

  .equipment-details {
    space-y: 0.75rem;
  }

  .detail-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
  }

  .detail-item span:first-child {
    color: #6b7280;
  }

  .progress-bar {
    flex: 1;
    height: 6px;
    margin: 0 0.75rem;
    overflow: hidden;
    background: #e5e7eb;
    border-radius: 3px;
  }

  .progress-fill {
    height: 100%;
    background: #2563eb;
    border-radius: 3px;
    transition: width 0.3s ease;
  }

  .stage-details {
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  }

  .stage-details h3 {
    margin: 0 0 1.5rem;
    color: #1f2937;
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .info-item label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
  }

  .info-item p {
    margin: 0;
    line-height: 1.5;
    color: #6b7280;
  }

  .params-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .param-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    font-size: 0.875rem;
    background: #f9fafb;
    border-radius: 4px;
  }

  .param-value {
    font-weight: 500;
    color: #1f2937;
  }

  .quality-metrics {
    display: flex;
    gap: 1rem;
  }

  .metric {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.75rem;
    font-size: 0.875rem;
    background: #f9fafb;
    border-radius: 6px;
  }

  .metric-value {
    margin-top: 0.25rem;
    font-size: 1.25rem;
    font-weight: 700;
  }

  .metric-value.good {
    color: #10b981;
  }

  @media (width <= 768px) {
    .production-plan {
      padding: 1rem;
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .stage-tabs {
      overflow-x: auto;
    }

    .gantt-header {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;
    }

    .gantt-controls {
      justify-content: space-between;
      width: 100%;
    }

    .equipment-grid {
      grid-template-columns: 1fr;
    }

    .info-grid {
      grid-template-columns: 1fr;
    }

    .quality-metrics {
      flex-direction: column;
    }
  }
</style>
