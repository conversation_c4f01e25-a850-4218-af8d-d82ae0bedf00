<template>
  <div :class="panelClass">
    <div class="chart-panel__header">
      <div class="header-left">
        <h3 class="chart-panel__title">
          {{ title }}
        </h3>
        <p v-if="subtitle" class="chart-panel__subtitle">
          {{ subtitle }}
        </p>
      </div>

      <div class="header-right">
        <el-button-group v-if="timeRanges.length > 0" size="small">
          <el-button
            v-for="range in timeRanges"
            :key="range.value"
            :type="selectedTimeRange === range.value ? 'primary' : ''"
            size="small"
            @click="handleTimeRangeChange(range.value)"
          >
            {{ range.label }}
          </el-button>
        </el-button-group>

        <el-tooltip content="刷新数据">
          <el-button
            :icon="Refresh"
            size="small"
            circle
            :loading="loading"
            @click="handleRefresh"
          />
        </el-tooltip>

        <el-tooltip content="全屏显示">
          <el-button
:icon="FullScreen" size="small"
circle @click="handleFullscreen"
/>
        </el-tooltip>
      </div>
    </div>

    <div
      ref="chartContainer"
      v-loading="loading"
      class="chart-panel__content"
      element-loading-text="数据加载中..."
    >
      <div ref="chartElement"
:style="chartStyle" class="chart-element"
/>

      <!-- 无数据状态 -->
      <div v-if="!loading && isEmpty" class="empty-state">
        <el-icon class="empty-icon">
          <DataAnalysis />
        </el-icon>
        <p class="empty-text">暂无数据</p>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="error-state">
        <el-icon class="error-icon">
          <Warning />
        </el-icon>
        <p class="error-text">
          {{ error }}
        </p>
        <el-button
size="small" @click="handleRefresh">重新加载</el-button>
      </div>
    </div>

    <div v-if="showFooter" class="chart-panel__footer">
      <div class="footer-stats">
        <span v-if="dataCount" class="stat-item">
          <el-icon><DataBoard /></el-icon>
          数据点: {{ dataCount }}
        </span>
        <span v-if="updateTime" class="stat-item">
          <el-icon><Clock /></el-icon>
          更新时间: {{ formattedUpdateTime }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
  import {
    Refresh,
    FullScreen,
    DataAnalysis,
    Warning,
    Clock,
    DataBoard
  } from '@element-plus/icons-vue'
  import * as echarts from 'echarts'
  import type { ECharts, EChartsOption } from 'echarts'

  interface TimeRange {
    label: string
    value: string
  }

  interface Props {
    /** 面板标题 */
    title: string
    /** 面板副标题 */
    subtitle?: string
    /** 图表配置选项 */
    options: EChartsOption
    /** 是否显示加载状态 */
    loading?: boolean
    /** 错误信息 */
    error?: string
    /** 时间范围选项 */
    timeRanges?: TimeRange[]
    /** 默认选中的时间范围 */
    defaultTimeRange?: string
    /** 面板高度 */
    height?: string | number
    /** 是否显示底部信息 */
    showFooter?: boolean
    /** 数据点数量 */
    dataCount?: number
    /** 更新时间 */
    updateTime?: string
    /** 面板主题 */
    theme?: 'light' | 'dark' | 'auto'
  }

  interface Emits {
    /** 时间范围改变事件 */
    timeRangeChange: [value: string]
    /** 刷新事件 */
    refresh: []
    /** 全屏事件 */
    fullscreen: []
  }

  const props = withDefaults(defineProps<Props>(), {
    timeRanges: () => [],
    height: '400px',
    showFooter: true,
    theme: 'auto'
  })

  const emit = defineEmits<Emits>()

  // 响应式数据
  const chartContainer = ref<HTMLElement>()
  const chartElement = ref<HTMLElement>()
  const chartInstance = ref<ECharts>()
  const selectedTimeRange = ref(props.defaultTimeRange || props.timeRanges[0]?.value || '')
  const isFullscreen = ref(false)

  // 计算属性
  const panelClass = computed(() => [
    'chart-panel',
    {
      'chart-panel--fullscreen': isFullscreen.value,
      'chart-panel--dark': props.theme === 'dark'
    }
  ])

  const chartStyle = computed(() => ({
    height: typeof props.height === 'number' ? `${props.height}px` : props.height,
    width: '100%'
  }))

  const isEmpty = computed(() => {
    if (!props.options?.series) return true
    const series = Array.isArray(props.options.series)
      ? props.options.series
      : [props.options.series]
    return series.every(s => !s.data || (Array.isArray(s.data) && s.data.length === 0))
  })

  const formattedUpdateTime = computed(() => {
    if (!props.updateTime) return ''
    return new Date(props.updateTime).toLocaleString()
  })

  // 方法
  const initChart = async () => {
    if (!chartElement.value) return

    await nextTick()

    // 销毁已存在的实例
    if (chartInstance.value) {
      chartInstance.value.dispose()
    }

    // 创建新实例
    chartInstance.value = echarts.init(
      chartElement.value,
      props.theme === 'dark' ? 'dark' : undefined
    )

    // 设置配置
    updateChart()

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  }

  const updateChart = () => {
    if (!chartInstance.value) return

    // 合并默认配置
    const defaultOptions: EChartsOption = {
      backgroundColor: 'transparent',
      textStyle: {
        fontFamily: 'Inter, system-ui, sans-serif'
      },
      animation: true,
      animationDuration: 750,
      animationEasing: 'cubicOut',
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'transparent',
        textStyle: {
          color: '#fff'
        }
      }
    }

    const finalOptions = {
      ...defaultOptions,
      ...props.options
    }

    chartInstance.value.setOption(finalOptions, true)
  }

  const handleResize = () => {
    if (chartInstance.value) {
      chartInstance.value.resize()
    }
  }

  const handleTimeRangeChange = (value: string) => {
    selectedTimeRange.value = value
    emit('timeRangeChange', value)
  }

  const handleRefresh = () => {
    emit('refresh')
  }

  const handleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value

    if (isFullscreen.value) {
      chartContainer.value?.requestFullscreen?.()
    } else {
      document.exitFullscreen?.()
    }

    emit('fullscreen')

    // 延迟调整图表大小
    setTimeout(handleResize, 100)
  }

  // 监听器
  watch(() => props.options, updateChart, { deep: true })
  watch(() => props.theme, initChart)

  // 全屏状态监听
  const handleFullscreenChange = () => {
    isFullscreen.value = !!document.fullscreenElement
    setTimeout(handleResize, 100)
  }

  // 生命周期
  onMounted(() => {
    initChart()
    document.addEventListener('fullscreenchange', handleFullscreenChange)
  })

  onBeforeUnmount(() => {
    if (chartInstance.value) {
      chartInstance.value.dispose()
    }
    window.removeEventListener('resize', handleResize)
    document.removeEventListener('fullscreenchange', handleFullscreenChange)
  })

  // 暴露方法给父组件
  defineExpose({
    chart: chartInstance,
    resize: handleResize,
    refresh: handleRefresh
  })
</script>

<style lang="scss" scoped>
  .chart-panel {
    overflow: hidden;
    background: var(--color-bg-primary);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-md);
    }

    &--fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      z-index: 9999;
      width: 100vw !important;
      height: 100vh !important;
      border-radius: 0;
    }

    &__header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      padding: var(--spacing-5);
      border-bottom: 1px solid var(--color-border-lighter);

      .header-left {
        flex: 1;
      }

      .header-right {
        display: flex;
        gap: var(--spacing-2);
        align-items: center;
      }
    }

    &__title {
      margin: 0 0 var(--spacing-1) 0;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-text-primary);

      @media (width >= 1920px) {
        font-size: var(--font-size-xl);
      }
    }

    &__subtitle {
      margin: 0;
      font-size: var(--font-size-sm);
      line-height: 1.4;
      color: var(--color-text-secondary);
    }

    &__content {
      position: relative;
      min-height: 200px;

      .chart-element {
        min-height: inherit;
      }

      // 空状态
      .empty-state {
        position: absolute;
        top: 50%;
        left: 50%;
        color: var(--color-text-secondary);
        text-align: center;
        transform: translate(-50%, -50%);

        .empty-icon {
          margin-bottom: var(--spacing-3);
          font-size: 3rem;
          opacity: 0.5;
        }

        .empty-text {
          margin: 0;
          font-size: var(--font-size-base);
        }
      }

      // 错误状态
      .error-state {
        position: absolute;
        top: 50%;
        left: 50%;
        text-align: center;
        transform: translate(-50%, -50%);

        .error-icon {
          margin-bottom: var(--spacing-3);
          font-size: 3rem;
          color: var(--color-danger);
        }

        .error-text {
          margin: 0 0 var(--spacing-3) 0;
          font-size: var(--font-size-base);
          color: var(--color-text-primary);
        }
      }
    }

    &__footer {
      padding: var(--spacing-3) var(--spacing-5);
      background: var(--color-bg-secondary);
      border-top: 1px solid var(--color-border-lighter);

      .footer-stats {
        display: flex;
        gap: var(--spacing-4);
        align-items: center;

        .stat-item {
          display: flex;
          align-items: center;
          font-size: var(--font-size-xs);
          color: var(--color-text-secondary);

          .el-icon {
            margin-right: var(--spacing-1);
          }
        }
      }
    }

    // 深色主题
    &--dark,
    .dark & {
      background: var(--color-bg-secondary);
      border-color: var(--color-border-dark);

      .chart-panel__header {
        border-bottom-color: var(--color-border-dark);
      }

      .chart-panel__footer {
        background: var(--color-bg-tertiary);
        border-top-color: var(--color-border-dark);
      }
    }
  }

  // 全屏模式样式调整
  .chart-panel--fullscreen {
    .chart-panel__header {
      padding: var(--spacing-6);
    }

    .chart-panel__content {
      .chart-element {
        height: calc(100vh - 140px) !important;
      }
    }
  }

  // 响应式设计
  @media (width <= 768px) {
    .chart-panel {
      &__header {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: stretch;

        .header-right {
          justify-content: flex-end;
        }
      }

      &__title {
        font-size: var(--font-size-base);
      }
    }
  }
</style>
