<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="customer-form"
    >
      <el-tabs v-model="activeTab" class="form-tabs">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="客户编码" prop="code">
                <el-input 
                  v-model="formData.code" 
                  :disabled="mode === 'edit'"
                  placeholder="系统自动生成"
                >
                  <template #append>
                    <el-button @click="generateCode" :disabled="mode === 'edit'">
                      <el-icon><Refresh /></el-icon>
                    </el-button>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户类型" prop="type" required>
                <el-select v-model="formData.type" placeholder="请选择客户类型" @change="handleTypeChange">
                  <el-option
                    v-for="option in CUSTOMER_TYPE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="公司名称" prop="name" required>
                <el-input v-model="formData.name" placeholder="请输入公司全称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="英文名称" prop="englishName">
                <el-input v-model="formData.englishName" placeholder="请输入英文名称" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="简称" prop="shortName">
                <el-input v-model="formData.shortName" placeholder="请输入公司简称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户等级" prop="level" required>
                <el-select v-model="formData.level" placeholder="请选择客户等级">
                  <el-option
                    v-for="option in CUSTOMER_LEVEL_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="客户规模" prop="scale" required>
                <el-select v-model="formData.scale" placeholder="请选择客户规模">
                  <el-option
                    v-for="option in CUSTOMER_SCALE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="信用等级" prop="creditLevel" required>
                <el-select v-model="formData.creditLevel" placeholder="请选择信用等级">
                  <el-option
                    v-for="option in CREDIT_LEVEL_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="行业类型" prop="industryType" required>
                <el-select v-model="formData.industryType" placeholder="请选择行业类型">
                  <el-option label="通信" value="communication" />
                  <el-option label="消费电子" value="consumer" />
                  <el-option label="汽车电子" value="automotive" />
                  <el-option label="工业控制" value="industrial" />
                  <el-option label="AI芯片" value="ai" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="官方网站" prop="website">
                <el-input v-model="formData.website" placeholder="https://www.example.com" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 技术信息 -->
        <el-tab-pane label="技术信息" name="technical">
          <el-form-item label="应用领域" prop="applicationFields" required>
            <el-select 
              v-model="formData.applicationFields" 
              placeholder="请选择主要应用领域"
              multiple
              collapse-tags
              collapse-tags-tooltip
              style="width: 100%"
            >
              <el-option
                v-for="option in APPLICATION_FIELD_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="工艺节点" prop="processNodes" required>
            <el-select 
              v-model="formData.processNodes" 
              placeholder="请选择支持的工艺节点"
              multiple
              collapse-tags
              collapse-tags-tooltip
              style="width: 100%"
            >
              <el-option
                v-for="option in PROCESS_NODE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="封装偏好" prop="packagePreferences" required>
            <el-select 
              v-model="formData.packagePreferences" 
              placeholder="请选择偏好的封装类型"
              multiple
              collapse-tags
              collapse-tags-tooltip
              style="width: 100%"
            >
              <el-option
                v-for="option in PACKAGE_PREFERENCE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="质量标准" prop="qualityStandard">
                <el-select 
                  v-model="formData.qualityStandard" 
                  placeholder="请选择遵循的质量标准"
                  multiple
                  allow-create
                  filterable
                  default-first-option
                  style="width: 100%"
                >
                  <el-option label="JEDEC" value="JEDEC" />
                  <el-option label="AEC-Q100" value="AEC-Q100" />
                  <el-option label="ISO9001" value="ISO9001" />
                  <el-option label="TS16949" value="TS16949" />
                  <el-option label="IPC" value="IPC" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合规要求" prop="complianceRequirements">
                <el-select 
                  v-model="formData.complianceRequirements" 
                  placeholder="请选择合规要求"
                  multiple
                  allow-create
                  filterable
                  default-first-option
                  style="width: 100%"
                >
                  <el-option label="ROHS" value="ROHS" />
                  <el-option label="REACH" value="REACH" />
                  <el-option label="IATF16949" value="IATF16949" />
                  <el-option label="WEEE" value="WEEE" />
                  <el-option label="PFOS" value="PFOS" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 联系信息 -->
        <el-tab-pane label="联系信息" name="contact">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="联系人姓名" prop="contact.name" required>
                <el-input v-model="formData.contact.name" placeholder="请输入联系人姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="英文姓名" prop="contact.englishName">
                <el-input v-model="formData.contact.englishName" placeholder="请输入英文姓名" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="职位" prop="contact.position" required>
                <el-input v-model="formData.contact.position" placeholder="请输入职位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="部门" prop="contact.department" required>
                <el-input v-model="formData.contact.department" placeholder="请输入部门" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="邮箱" prop="contact.email" required>
                <el-input v-model="formData.contact.email" placeholder="请输入邮箱地址" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电话" prop="contact.phone" required>
                <el-input v-model="formData.contact.phone" placeholder="请输入电话号码" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="手机" prop="contact.mobile">
                <el-input v-model="formData.contact.mobile" placeholder="请输入手机号码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="微信" prop="contact.wechat">
                <el-input v-model="formData.contact.wechat" placeholder="请输入微信号" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="角色" prop="contact.role" required>
                <el-select v-model="formData.contact.role" placeholder="请选择角色">
                  <el-option label="商务经理" value="BusinessManager" />
                  <el-option label="采购经理" value="PurchaseManager" />
                  <el-option label="项目经理" value="PMManager" />
                  <el-option label="技术总监" value="TechnicalDirector" />
                  <el-option label="副总裁" value="VP" />
                  <el-option label="首席技术官" value="CTO" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="重要程度" prop="contact.importance" required>
                <el-rate 
                  v-model="formData.contact.importance" 
                  :max="5"
                  show-text
                  :texts="['很低', '较低', '一般', '重要', '非常重要']"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 地址信息 -->
        <el-tab-pane label="地址信息" name="address">
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="国家" prop="address.country" required>
                <el-select v-model="formData.address.country" placeholder="请选择国家">
                  <el-option label="中国" value="中国" />
                  <el-option label="美国" value="美国" />
                  <el-option label="日本" value="日本" />
                  <el-option label="韩国" value="韩国" />
                  <el-option label="台湾" value="台湾" />
                  <el-option label="新加坡" value="新加坡" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="省份" prop="address.province" required>
                <el-input v-model="formData.address.province" placeholder="请输入省份" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="城市" prop="address.city" required>
                <el-input v-model="formData.address.city" placeholder="请输入城市" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="区县" prop="address.district">
                <el-input v-model="formData.address.district" placeholder="请输入区县" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="邮政编码" prop="address.postalCode">
                <el-input v-model="formData.address.postalCode" placeholder="请输入邮政编码" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="详细地址" prop="address.street" required>
            <el-input 
              v-model="formData.address.street" 
              type="textarea"
              :rows="3"
              placeholder="请输入详细地址"
            />
          </el-form-item>
        </el-tab-pane>

        <!-- 财务信息 -->
        <el-tab-pane label="财务信息" name="financial">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="信用额度" prop="financialInfo.creditLimit" required>
                <el-input-number 
                  v-model="formData.financialInfo.creditLimit"
                  :min="0"
                  :max="1000000000"
                  :step="1000000"
                  placeholder="请输入信用额度"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="付款条件" prop="financialInfo.paymentTerms" required>
                <el-select v-model="formData.financialInfo.paymentTerms" placeholder="请选择付款条件">
                  <el-option label="NET15" value="NET15" />
                  <el-option label="NET30" value="NET30" />
                  <el-option label="NET45" value="NET45" />
                  <el-option label="NET60" value="NET60" />
                  <el-option label="预付款" value="PREPAID" />
                  <el-option label="货到付款" value="COD" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="结算货币" prop="financialInfo.currency" required>
                <el-select v-model="formData.financialInfo.currency" placeholder="请选择结算货币">
                  <el-option label="人民币 (CNY)" value="CNY" />
                  <el-option label="美元 (USD)" value="USD" />
                  <el-option label="欧元 (EUR)" value="EUR" />
                  <el-option label="日元 (JPY)" value="JPY" />
                  <el-option label="港币 (HKD)" value="HKD" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="税号" prop="financialInfo.taxId">
                <el-input v-model="formData.financialInfo.taxId" placeholder="请输入税号" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">取消</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ mode === 'create' ? '创建' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
// Element Plus组件通过unplugin-auto-import自动导入
import type { FormInstance, FormRules } from 'element-plus'
import type { Customer, CreateCustomerData, UpdateCustomerData } from '@/types/customer'
import { 
  CUSTOMER_TYPE_OPTIONS,
  CUSTOMER_LEVEL_OPTIONS,
  CUSTOMER_SCALE_OPTIONS,
  CREDIT_LEVEL_OPTIONS,
  APPLICATION_FIELD_OPTIONS,
  PROCESS_NODE_OPTIONS,
  PACKAGE_PREFERENCE_OPTIONS,
  generateNewCustomerCode
} from '@/utils/mockData/customerMaster'

interface Props {
  visible: boolean
  customer?: Customer | null
  mode: 'create' | 'edit'
}

interface Emits {
  'update:visible': [visible: boolean]
  'save': [data: CreateCustomerData | UpdateCustomerData]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()

// 表单状态
const activeTab = ref('basic')
const submitting = ref(false)

// 对话框标题
const dialogTitle = computed(() => {
  return props.mode === 'create' ? '新增客户' : '编辑客户'
})

// 表单数据
const formData = reactive<CreateCustomerData | UpdateCustomerData>({
  code: '',
  name: '',
  englishName: '',
  shortName: '',
  type: 'fabless',
  level: 'standard',
  scale: 'medium',
  industryType: 'communication',
  creditLevel: 'B',
  applicationFields: [],
  processNodes: [],
  packagePreferences: [],
  qualityStandard: [],
  complianceRequirements: [],
  contact: {
    customerId: '',
    name: '',
    englishName: '',
    position: '',
    department: '',
    email: '',
    phone: '',
    mobile: '',
    wechat: '',
    role: 'BusinessManager',
    contactType: ['Business'],
    status: 'active',
    importance: 3,
    trustLevel: 3,
    influenceScore: 5,
    isDecisionMaker: true,
    isPrimaryContact: true,
    createdAt: '',
    updatedAt: '',
    createdBy: ''
  },
  address: {
    country: '中国',
    province: '',
    city: '',
    district: '',
    street: '',
    postalCode: ''
  },
  financialInfo: {
    creditLimit: 10000000,
    paymentTerms: 'NET30',
    currency: 'CNY',
    taxId: ''
  }
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择客户类型', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择客户等级', trigger: 'change' }
  ],
  scale: [
    { required: true, message: '请选择客户规模', trigger: 'change' }
  ],
  industryType: [
    { required: true, message: '请选择行业类型', trigger: 'change' }
  ],
  creditLevel: [
    { required: true, message: '请选择信用等级', trigger: 'change' }
  ],
  applicationFields: [
    { required: true, message: '请选择应用领域', trigger: 'change' }
  ],
  processNodes: [
    { required: true, message: '请选择工艺节点', trigger: 'change' }
  ],
  packagePreferences: [
    { required: true, message: '请选择封装偏好', trigger: 'change' }
  ],
  'contact.name': [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' }
  ],
  'contact.position': [
    { required: true, message: '请输入职位', trigger: 'blur' }
  ],
  'contact.department': [
    { required: true, message: '请输入部门', trigger: 'blur' }
  ],
  'contact.email': [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  'contact.phone': [
    { required: true, message: '请输入电话号码', trigger: 'blur' }
  ],
  'contact.role': [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  'contact.importance': [
    { required: true, message: '请设置重要程度', trigger: 'change' }
  ],
  'address.country': [
    { required: true, message: '请选择国家', trigger: 'change' }
  ],
  'address.province': [
    { required: true, message: '请输入省份', trigger: 'blur' }
  ],
  'address.city': [
    { required: true, message: '请输入城市', trigger: 'blur' }
  ],
  'address.street': [
    { required: true, message: '请输入详细地址', trigger: 'blur' }
  ],
  'financialInfo.creditLimit': [
    { required: true, message: '请输入信用额度', trigger: 'blur' }
  ],
  'financialInfo.paymentTerms': [
    { required: true, message: '请选择付款条件', trigger: 'change' }
  ],
  'financialInfo.currency': [
    { required: true, message: '请选择结算货币', trigger: 'change' }
  ]
}

// 生成客户编码
const generateCode = () => {
  if (formData.type) {
    formData.code = generateNewCustomerCode(formData.type)
  }
}

// 处理客户类型变化
const handleTypeChange = () => {
  if (props.mode === 'create' && formData.type) {
    generateCode()
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  activeTab.value = 'basic'
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 模拟提交延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    emit('save', formData)
    
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否完整')
  } finally {
    submitting.value = false
  }
}

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      activeTab.value = 'basic'
      
      nextTick(() => {
        if (props.mode === 'edit' && props.customer) {
          // 编辑模式：填充现有数据
          Object.assign(formData, {
            ...props.customer,
            id: props.customer.id // 保持ID用于更新
          })
        } else {
          // 创建模式：重置为默认值
          Object.assign(formData, {
            code: '',
            name: '',
            englishName: '',
            shortName: '',
            type: 'fabless',
            level: 'standard',
            scale: 'medium',
            industryType: 'communication',
            creditLevel: 'B',
            applicationFields: [],
            processNodes: [],
            packagePreferences: [],
            qualityStandard: [],
            complianceRequirements: [],
            contact: {
              customerId: '',
              name: '',
              englishName: '',
              position: '',
              department: '',
              email: '',
              phone: '',
              mobile: '',
              wechat: '',
              role: 'BusinessManager',
              contactType: ['Business'],
              status: 'active',
              importance: 3,
              trustLevel: 3,
              influenceScore: 5,
              isDecisionMaker: true,
              isPrimaryContact: true,
              createdAt: '',
              updatedAt: '',
              createdBy: ''
            },
            address: {
              country: '中国',
              province: '',
              city: '',
              district: '',
              street: '',
              postalCode: ''
            },
            financialInfo: {
              creditLimit: 10000000,
              paymentTerms: 'NET30',
              currency: 'CNY',
              taxId: ''
            }
          })
          
          // 自动生成客户编码
          generateCode()
        }
      })
    }
  }
)
</script>

<style lang="scss" scoped>
.customer-form {
  .form-tabs {
    :deep(.el-tabs__content) {
      padding-top: 20px;
    }
    
    :deep(.el-tab-pane) {
      max-height: 500px;
      overflow-y: auto;
    }
  }
}

.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

// 响应式调整
@media (width <= 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 5% auto;
  }
  
  .customer-form {
    .el-col {
      margin-bottom: 16px;
    }
  }
}
</style>