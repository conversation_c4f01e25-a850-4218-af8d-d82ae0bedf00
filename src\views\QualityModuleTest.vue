<template>
  <div class="quality-module-test">
    <div class="test-header">
      <h1>质量管理模块功能测试</h1>
      <p>IC封装测试工厂质量管理系统 - 完整功能验证</p>
    </div>

    <div class="test-navigation">
      <el-card>
        <template #header>
          <span>质量管理模块导航</span>
        </template>
        <div class="nav-grid">
          <router-link
            v-for="module in qualityModules"
            :key="module.path"
            :to="module.path"
            class="nav-item"
          >
            <div class="nav-icon">
              <component :is="module.icon" />
            </div>
            <div class="nav-content">
              <h3>{{ module.title }}</h3>
              <p>{{ module.description }}</p>
            </div>
            <div class="nav-status" :class="module.status">
              <span>{{ module.statusText }}</span>
            </div>
          </router-link>
        </div>
      </el-card>
    </div>

    <div class="test-overview">
      <el-row :gutter="24">
        <el-col :span="8">
          <el-card>
            <div class="overview-item">
              <div class="overview-icon success">
                <el-icon><Check /></el-icon>
              </div>
              <div class="overview-content">
                <h3>5</h3>
                <p>核心模块</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <div class="overview-item">
              <div class="overview-icon primary">
                <el-icon><Document /></el-icon>
              </div>
              <div class="overview-content">
                <h3>15+</h3>
                <p>功能界面</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <div class="overview-item">
              <div class="overview-icon warning">
                <el-icon><DataLine /></el-icon>
              </div>
              <div class="overview-content">
                <h3>100%</h3>
                <p>开发完成</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="test-features">
      <el-card>
        <template #header>
          <span>功能特性验证清单</span>
        </template>
        <div class="features-grid">
          <div v-for="feature in features" :key="feature.title" class="feature-item">
            <div class="feature-header">
              <el-icon class="feature-icon" :class="feature.status">
                <component :is="feature.status === 'completed' ? 'Check' : 'Clock'" />
              </el-icon>
              <h4>{{ feature.title }}</h4>
            </div>
            <ul class="feature-list">
              <li v-for="item in feature.items" :key="item">
                {{ item }}
              </li>
            </ul>
          </div>
        </div>
      </el-card>
    </div>

    <div class="test-technical">
      <el-card>
        <template #header>
          <span>技术实现说明</span>
        </template>
        <div class="technical-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="前端框架">Vue 3 + TypeScript</el-descriptions-item>
            <el-descriptions-item label="UI组件库">Element Plus</el-descriptions-item>
            <el-descriptions-item label="图表库">ECharts 5</el-descriptions-item>
            <el-descriptions-item label="状态管理">Pinia + Composables</el-descriptions-item>
            <el-descriptions-item label="样式系统">SCSS + CSS变量</el-descriptions-item>
            <el-descriptions-item label="响应式设计">移动端优先</el-descriptions-item>
            <el-descriptions-item label="数据模拟">完整Mock数据</el-descriptions-item>
            <el-descriptions-item label="行业标准">IATF16949/JEDEC/IPC</el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>

    <div class="test-instructions">
      <el-card>
        <template #header>
          <span>使用说明</span>
        </template>
        <el-timeline>
          <el-timeline-item timestamp="Step 1" type="primary">
            <strong>SPC统计过程控制</strong>
            <p>访问 /quality/spc-control - 实时SPC监控、Nelson规则检查、过程能力分析</p>
          </el-timeline-item>
          <el-timeline-item timestamp="Step 2" type="success">
            <strong>质量追溯系统</strong>
            <p>访问 /quality/traceability - 前向后向追溯、根因分析、质量事件历史</p>
          </el-timeline-item>
          <el-timeline-item timestamp="Step 3" type="warning">
            <strong>IATF16949合规管理</strong>
            <p>访问 /quality/compliance - 文档控制、内审管理、纠正措施跟踪</p>
          </el-timeline-item>
          <el-timeline-item timestamp="Step 4" type="info">
            <strong>质量检验管理</strong>
            <p>访问 /quality/inspection - IQC/IPQC/FQC/OQC检验、统计分析</p>
          </el-timeline-item>
          <el-timeline-item timestamp="Step 5" type="danger">
            <strong>质量分析报告</strong>
            <p>访问 /quality/analytics - KPI仪表盘、良率分析、成本分析</p>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import {
    Check,
    Clock,
    Document,
    DataLine,
    TrendCharts,
    Link,
    DocumentChecked,
    Search,
    PieChart
  } from '@element-plus/icons-vue'

  const qualityModules = ref([
    {
      title: 'SPC统计过程控制',
      description: 'Statistical Process Control - 实时SPC监控和过程能力分析',
      path: '/quality/spc-control',
      icon: TrendCharts,
      status: 'completed',
      statusText: '已完成'
    },
    {
      title: '质量追溯系统',
      description: 'Quality Traceability - 完整的质量追溯链和根因分析',
      path: '/quality/traceability',
      icon: Link,
      status: 'completed',
      statusText: '已完成'
    },
    {
      title: 'IATF16949合规管理',
      description: 'Compliance Management - IATF16949文档控制和审核管理',
      path: '/quality/compliance',
      icon: DocumentChecked,
      status: 'completed',
      statusText: '已完成'
    },
    {
      title: '质量检验管理',
      description: 'Quality Inspection - IQC/IPQC/FQC/OQC检验管理',
      path: '/quality/inspection',
      icon: Search,
      status: 'completed',
      statusText: '已完成'
    },
    {
      title: '质量分析报告',
      description: 'Quality Analytics - KPI仪表盘和质量成本分析',
      path: '/quality/analytics',
      icon: PieChart,
      status: 'completed',
      statusText: '已完成'
    }
  ])

  const features = ref([
    {
      title: 'SPC统计过程控制',
      status: 'completed',
      items: [
        '实时SPC图表显示(X-bar, R, 控制限)',
        'Nelson规则自动检查和报警',
        'Cpk/Ppk过程能力指数计算',
        '违规事件自动记录和追踪',
        '多工艺参数同时监控',
        'SPC数据导出功能'
      ]
    },
    {
      title: '质量追溯系统',
      status: 'completed',
      items: [
        '从晶圆到成品完整追溯链',
        '前向追溯和后向追溯查询',
        '质量事件根因分析工具',
        '批次质量数据关联显示',
        '追溯路径可视化展示',
        '质量问题影响范围分析'
      ]
    },
    {
      title: 'IATF16949合规管理',
      status: 'completed',
      items: [
        'ISO文档分层管理和版本控制',
        '内审计划制定和执行跟踪',
        '不合格品控制流程管理',
        '纠正措施CAPA管理',
        'PPAP文档管理和状态跟踪',
        '供应商质量协议管理'
      ]
    },
    {
      title: '质量检验管理',
      status: 'completed',
      items: [
        'IQC来料检验管理',
        'IPQC过程检验监控',
        'FQC最终检验控制',
        'OQC出货检验管理',
        '检验标准和规范管理',
        '检验数据统计分析'
      ]
    },
    {
      title: '质量分析报告',
      status: 'completed',
      items: [
        '质量KPI仪表盘展示',
        '良率趋势分析和预测',
        '客户投诉分析和跟踪',
        '质量成本分析和控制',
        '质量绩效评估报告',
        '管理层质量决策支持'
      ]
    }
  ])
</script>

<style lang="scss" scoped>
  .quality-module-test {
    padding: 24px;
    background-color: var(--color-bg-primary);

    .test-header {
      margin-bottom: 32px;
      text-align: center;

      h1 {
        margin-bottom: 8px;
        font-size: 32px;
        color: var(--color-text-primary);
      }

      p {
        margin: 0;
        font-size: 16px;
        color: var(--color-text-secondary);
      }
    }

    .test-navigation {
      margin-bottom: 24px;

      .nav-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 16px;

        .nav-item {
          display: flex;
          gap: 16px;
          align-items: center;
          padding: 16px;
          text-decoration: none;
          background: var(--color-bg-light);
          border: 1px solid var(--color-border-light);
          border-radius: var(--radius-base);
          transition: all var(--transition-fast);

          &:hover {
            background: var(--color-bg-hover);
            box-shadow: var(--shadow-base);
            transform: translateY(-2px);
          }

          .nav-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            font-size: 24px;
            color: var(--color-primary);
            background: var(--color-primary-light);
            border-radius: var(--radius-base);
          }

          .nav-content {
            flex: 1;

            h3 {
              margin: 0 0 4px;
              font-size: 16px;
              color: var(--color-text-primary);
            }

            p {
              margin: 0;
              font-size: 14px;
              color: var(--color-text-secondary);
            }
          }

          .nav-status {
            padding: 4px 12px;
            font-size: 12px;
            font-weight: 500;
            border-radius: var(--radius-sm);

            &.completed {
              color: var(--color-success);
              background: var(--color-success-light);
            }
          }
        }
      }
    }

    .test-overview {
      margin-bottom: 24px;

      .overview-item {
        display: flex;
        gap: 16px;
        align-items: center;

        .overview-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          font-size: 24px;
          border-radius: 50%;

          &.success {
            color: var(--color-success);
            background: var(--color-success-light);
          }

          &.primary {
            color: var(--color-primary);
            background: var(--color-primary-light);
          }

          &.warning {
            color: var(--color-warning);
            background: var(--color-warning-light);
          }
        }

        .overview-content {
          h3 {
            margin: 0 0 4px;
            font-size: 24px;
            font-weight: 600;
            color: var(--color-text-primary);
          }

          p {
            margin: 0;
            font-size: 14px;
            color: var(--color-text-secondary);
          }
        }
      }
    }

    .test-features {
      margin-bottom: 24px;

      .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;

        .feature-item {
          padding: 16px;
          background: var(--color-bg-light);
          border-radius: var(--radius-base);

          .feature-header {
            display: flex;
            gap: 8px;
            align-items: center;
            margin-bottom: 12px;

            .feature-icon {
              font-size: 20px;

              &.completed {
                color: var(--color-success);
              }
            }

            h4 {
              margin: 0;
              font-size: 16px;
              color: var(--color-text-primary);
            }
          }

          .feature-list {
            padding-left: 20px;
            margin: 0;

            li {
              margin-bottom: 4px;
              font-size: 14px;
              color: var(--color-text-regular);
            }
          }
        }
      }
    }

    .test-technical {
      margin-bottom: 24px;
    }

    .test-instructions {
      margin-bottom: 24px;
    }
  }

  @media (width <= 768px) {
    .quality-module-test {
      padding: 16px;

      .test-navigation .nav-grid {
        grid-template-columns: 1fr;

        .nav-item {
          .nav-content h3 {
            font-size: 14px;
          }

          .nav-content p {
            font-size: 12px;
          }
        }
      }

      .test-features .features-grid {
        grid-template-columns: 1fr;
      }
    }
  }
</style>
