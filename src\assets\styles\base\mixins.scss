// 极简 SCSS Mixins 工具库
// 专注于常用的、实用的 mixins
@use 'sass:map';
@use 'sass:math';

// 清除浮动
@mixin clearfix {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

// 文本省略
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-ellipsis-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 居中对齐
@mixin center-absolute {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin center-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 尺寸简写
@mixin size($width, $height: $width) {
  width: $width;
  height: $height;
}

@mixin square($size) {
  @include size($size);
}

// 响应式断点
$breakpoints: (
  sm: 640px,
  md: 768px,
  lg: 1024px,
  xl: 1280px,
  2xl: 1536px
);

@mixin media($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    @media (min-width: map.get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

// 极简卡片样式
@mixin card($padding: var(--spacing-4)) {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-sm);
  padding: $padding;
  transition: box-shadow var(--transition-fast);
  
  &:hover {
    box-shadow: var(--shadow-base);
  }
}

// 按钮基础样式
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--button-padding-y) var(--button-padding-x);
  border: 1px solid transparent;
  border-radius: var(--radius-base);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
  
  &:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

// 主要按钮样式
@mixin button-primary {
  @include button-base;
  background: var(--color-primary);
  color: white;
  
  &:hover:not(:disabled) {
    background: var(--color-primary-hover);
  }
  
  &:active {
    background: var(--color-primary-active);
    transform: translateY(1px);
  }
}

// 次要按钮样式
@mixin button-secondary {
  @include button-base;
  background: var(--color-bg-primary);
  color: var(--color-text-primary);
  border-color: var(--color-border-base);
  
  &:hover:not(:disabled) {
    border-color: var(--color-primary);
    color: var(--color-primary);
  }
  
  &:active {
    transform: translateY(1px);
  }
}

// 文本按钮样式
@mixin button-text {
  @include button-base;
  background: transparent;
  color: var(--color-text-secondary);
  border: none;
  
  &:hover:not(:disabled) {
    color: var(--color-primary);
    background: var(--color-bg-hover);
  }
}

// 输入框样式
@mixin input-base {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--color-border-base);
  border-radius: var(--radius-base);
  background: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-family: inherit;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-base);
  transition: border-color var(--transition-fast), 
              box-shadow var(--transition-fast);
  
  &::placeholder {
    color: var(--color-text-placeholder);
  }
  
  &:hover {
    border-color: var(--color-border-dark);
  }
  
  &:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
  }
  
  &:disabled {
    background: var(--color-bg-disabled);
    color: var(--color-text-disabled);
    cursor: not-allowed;
  }
}

// 滚动条样式
@mixin scrollbar($width: 6px) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }

  &::-webkit-scrollbar-track {
    background: var(--color-bg-secondary);
    border-radius: math.div($width, 2);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-border-dark);
    border-radius: math.div($width, 2);
    
    &:hover {
      background: var(--color-text-tertiary);
    }
  }

  scrollbar-width: thin;
  scrollbar-color: var(--color-border-dark) var(--color-bg-secondary);
}

// 加载动画
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@mixin loading-spin {
  animation: spin 1s linear infinite;
}

// 淡入动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@mixin fade-in($duration: var(--transition-normal)) {
  animation: fadeIn $duration ease;
}

// 滑入动画
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@mixin slide-in-up($duration: var(--transition-normal)) {
  animation: slideInUp $duration ease;
}

// 网格布局快捷方式
@mixin grid($columns, $gap: var(--spacing-4)) {
  display: grid;
  grid-template-columns: repeat($columns, 1fr);
  gap: $gap;
}

@mixin grid-auto($min-width: 250px, $gap: var(--spacing-4)) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax($min-width, 1fr));
  gap: $gap;
}

// Flex 布局快捷方式
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

// 隐藏但保持可访问性
@mixin visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

// 专业色彩 mixin（针对 IC 封测）
@mixin wafer-status($status: 'normal') {
  @if $status == 'pass' {
    background: var(--color-die-pass);
    color: var(--color-success);
  } @else if $status == 'fail' {
    background: var(--color-die-fail);
    color: var(--color-error);
  } @else {
    background: var(--color-wafer);
    color: var(--color-info);
  }
}