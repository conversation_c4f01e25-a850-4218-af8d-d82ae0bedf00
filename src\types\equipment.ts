/**
 * IC封装测试工厂设备管理类型定义
 * Equipment Management Types for IC Assembly and Testing Factory
 */

// 设备基础状态枚举
export enum EquipmentStatus {
  RUN = 'RUN',
  IDLE = 'IDLE',
  DOWN = 'DOWN',
  PM = 'PM',
  SETUP = 'SETUP',
  ALARM = 'ALARM'
}

// 设备类型枚举
export enum EquipmentType {
  ATE = 'ATE', // 自动测试设备 Automatic Test Equipment
  BONDER = 'BONDER', // 键合机 Wire Bonder
  DIE_ATTACH = 'DIE_ATTACH', // 贴片机 Die Attach Machine
  MOLDING = 'MOLDING', // 塑封机 Molding Machine
  HANDLER = 'HANDLER', // 分选机 Test Handler
  TRIM_FORM = 'TRIM_FORM', // 切筋成形机 Trim & Form Machine
  PROBER = 'PROBER', // 探针台 Wafer Prober
  DICING_SAW = 'DICING_SAW', // 切割机 Dicing Saw
  PICK_PLACE = 'PICK_PLACE', // 取放机 Pick & Place Machine
  BURN_IN = 'BURN_IN' // 老化炉 Burn-in Oven
}

// GEM状态模型
export enum GemState {
  EQUIPMENT_OFFLINE = 'EQUIPMENT_OFFLINE',
  ATTEMPT_ONLINE = 'ATTEMPT_ONLINE',
  HOST_OFFLINE = 'HOST_OFFLINE',
  ONLINE_LOCAL = 'ONLINE_LOCAL',
  ONLINE_REMOTE = 'ONLINE_REMOTE'
}

// SECS-II消息类型
export interface SecsMessage {
  id: string
  stream: number
  function: number
  direction: 'H→E' | 'E→H' // Host to Equipment or Equipment to Host
  timestamp: string
  data: any
  status: 'SENT' | 'RECEIVED' | 'TIMEOUT' | 'ERROR'
  description: string
}

// 设备基础信息
export interface Equipment {
  id: string
  name: string
  code: string
  type: EquipmentType
  model: string
  manufacturer: string
  serialNumber: string
  location: string
  installDate: string
  status: EquipmentStatus
  gemState: GemState
  isConnected: boolean
  description?: string
  specifications: Record<string, any>
  capabilities: string[]
  createdAt: string
  updatedAt: string
}

// 设备状态详情
export interface EquipmentStatusDetail {
  equipmentId: string
  status: EquipmentStatus
  gemState: GemState
  isConnected: boolean
  uptime: number // 运行时间 (秒)
  downtime: number // 停机时间 (秒)
  lastStatusChange: string
  currentRecipe?: string
  currentLot?: string
  processingCount: number
  totalCount: number
  errorCount: number
  warningCount: number
  temperature?: number
  pressure?: number
  humidity?: number
  oeeData: {
    availability: number // 可用率
    performance: number // 性能率
    quality: number // 质量率
    oee: number // 总体设备效率
  }
}

// 设备性能指标
export interface EquipmentPerformance {
  equipmentId: string
  date: string
  uptime: number
  downtime: number
  mtbf: number // Mean Time Between Failures
  mttr: number // Mean Time To Repair
  availability: number
  performance: number
  quality: number
  oee: number
  throughput: number
  yieldRate: number
  cycleTime: number
  utilizationRate: number
}

// 设备告警信息
export interface EquipmentAlarm {
  id: string
  equipmentId: string
  equipmentName: string
  alarmCode: string
  alarmText: string
  severity: 'CRITICAL' | 'MAJOR' | 'MINOR' | 'WARNING'
  category: 'MECHANICAL' | 'ELECTRICAL' | 'PROCESS' | 'SAFETY' | 'COMMUNICATION'
  timestamp: string
  acknowledgedBy?: string
  acknowledgedAt?: string
  clearedAt?: string
  isActive: boolean
  description?: string
  recommendations?: string[]
}

// 设备事件日志
export interface EquipmentEvent {
  id: string
  equipmentId: string
  eventType: 'STATUS_CHANGE' | 'ALARM' | 'MESSAGE' | 'RECIPE_CHANGE' | 'MAINTENANCE'
  message: string
  data?: Record<string, any>
  timestamp: string
  operator?: string
  severity: 'INFO' | 'WARNING' | 'ERROR'
}

// Recipe相关接口
export interface Recipe {
  id: string
  name: string
  version: string
  equipmentId: string
  equipmentType: EquipmentType
  description?: string
  parameters: RecipeParameter[]
  isActive: boolean
  createdBy: string
  createdAt: string
  updatedAt: string
  approvedBy?: string
  approvedAt?: string
}

export interface RecipeParameter {
  name: string
  value: string | number | boolean
  unit?: string
  min?: number
  max?: number
  description?: string
  category: string
}

// Equipment Constants (GEM)
export interface EquipmentConstant {
  id: string
  name: string
  value: string | number | boolean
  unit?: string
  min?: number
  max?: number
  description?: string
  equipmentId: string
  isReadOnly: boolean
}

// 维护相关接口
export interface MaintenancePlan {
  id: string
  equipmentId: string
  equipmentName: string
  type: 'PREVENTIVE' | 'CORRECTIVE' | 'PREDICTIVE'
  title: string
  description: string
  frequency: string // CRON表达式或描述
  estimatedDuration: number // 预计维护时间(分钟)
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  isActive: boolean
  nextDue: string
  lastCompleted?: string
  createdBy: string
  createdAt: string
  checklist: MaintenanceCheckItem[]
  requiredParts: RequiredPart[]
}

export interface MaintenanceTask {
  id: string
  planId: string
  equipmentId: string
  equipmentName: string
  type: 'PREVENTIVE' | 'CORRECTIVE' | 'PREDICTIVE'
  title: string
  description: string
  scheduledDate: string
  actualStartTime?: string
  actualEndTime?: string
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'OVERDUE'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  assignedTo: string[]
  estimatedDuration: number
  actualDuration?: number
  checklist: MaintenanceCheckItem[]
  requiredParts: RequiredPart[]
  usedParts: UsedPart[]
  notes?: string
  completionNotes?: string
  createdAt: string
}

export interface MaintenanceCheckItem {
  id: string
  name: string
  description: string
  isCompleted: boolean
  notes?: string
  checkType: 'VISUAL' | 'MEASUREMENT' | 'TEST' | 'REPLACEMENT'
  expectedValue?: string
  actualValue?: string
  unit?: string
}

export interface RequiredPart {
  partNumber: string
  partName: string
  quantity: number
  unit: string
  isAvailable: boolean
  stockQuantity: number
  minStock: number
}

export interface UsedPart {
  partNumber: string
  partName: string
  quantity: number
  unit: string
  lotNumber?: string
  notes?: string
}

// 维护记录
export interface MaintenanceRecord {
  id: string
  taskId: string
  equipmentId: string
  equipmentName: string
  maintenanceType: 'PREVENTIVE' | 'CORRECTIVE' | 'PREDICTIVE'
  title: string
  description: string
  startTime: string
  endTime: string
  duration: number
  assignedTo: string[]
  completedBy: string
  status: 'COMPLETED' | 'CANCELLED'
  checklist: MaintenanceCheckItem[]
  usedParts: UsedPart[]
  notes?: string
  cost?: number
  nextScheduledDate?: string
  createdAt: string
}

// 设备查询参数
export interface EquipmentQueryParams {
  page?: number
  limit?: number
  type?: EquipmentType
  status?: EquipmentStatus
  location?: string
  isConnected?: boolean
  keyword?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 告警查询参数
export interface AlarmQueryParams {
  page?: number
  limit?: number
  equipmentId?: string
  severity?: string
  category?: string
  isActive?: boolean
  startDate?: string
  endDate?: string
  acknowledged?: boolean
}

// 事件查询参数
export interface EventQueryParams {
  page?: number
  limit?: number
  equipmentId?: string
  eventType?: string
  severity?: string
  startDate?: string
  endDate?: string
}

// 维护任务查询参数
export interface MaintenanceQueryParams {
  page?: number
  limit?: number
  equipmentId?: string
  type?: string
  status?: string
  priority?: string
  assignedTo?: string
  startDate?: string
  endDate?: string
  overdue?: boolean
}

// API响应接口
export interface EquipmentListResponse {
  code: number
  message: string
  data: {
    list: Equipment[]
    total: number
    page: number
    limit: number
  }
}

export interface EquipmentStatusResponse {
  code: number
  message: string
  data: EquipmentStatusDetail[]
}

export interface AlarmListResponse {
  code: number
  message: string
  data: {
    list: EquipmentAlarm[]
    total: number
    page: number
    limit: number
  }
}

export interface EventListResponse {
  code: number
  message: string
  data: {
    list: EquipmentEvent[]
    total: number
    page: number
    limit: number
  }
}

export interface MaintenanceListResponse {
  code: number
  message: string
  data: {
    list: MaintenanceTask[]
    total: number
    page: number
    limit: number
  }
}

export interface RecipeListResponse {
  code: number
  message: string
  data: {
    list: Recipe[]
    total: number
    page: number
    limit: number
  }
}

export interface SecsMessageListResponse {
  code: number
  message: string
  data: {
    list: SecsMessage[]
    total: number
    page: number
    limit: number
  }
}

// 统计数据接口
export interface EquipmentStatistics {
  totalEquipment: number
  runningEquipment: number
  idleEquipment: number
  downEquipment: number
  pmEquipment: number
  alarmEquipment: number
  overallOEE: number
  avgAvailability: number
  avgPerformance: number
  avgQuality: number
}

export interface MaintenanceStatistics {
  totalTasks: number
  completedTasks: number
  overdueTasks: number
  scheduledTasks: number
  avgCompletionTime: number
  totalCost: number
  monthlyTrend: {
    month: string
    completed: number
    overdue: number
    cost: number
  }[]
}

// WebSocket实时数据
export interface EquipmentRealTimeData {
  type: 'EQUIPMENT_STATUS' | 'ALARM' | 'EVENT' | 'PERFORMANCE'
  equipmentId: string
  timestamp: string
  data: any
}
