<template>
  <div class="system-parameters-management">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon><Tools /></el-icon>
          系统参数管理
        </h1>
        <p class="page-description">配置和管理系统运行参数，包括业务配置、系统设置和功能开关</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增参数
        </el-button>
        <el-button type="warning" @click="handleBatchSave">
          <el-icon><Upload /></el-icon>
          批量保存
        </el-button>
      </div>
    </div>

    <!-- 参数分类标签页 -->
    <div class="parameter-tabs">
      <el-tabs v-model="activeTab" type="border-card" class="parameter-tabs-container">
        <el-tab-pane
          v-for="category in parameterCategories"
          :key="category.key"
          :label="category.label"
          :name="category.key"
        >
          <!-- 搜索筛选区 -->
          <div class="search-section">
            <el-form :model="searchForm" :inline="true" class="search-form">
              <el-form-item label="参数名称">
                <el-input 
                  v-model="searchForm.paramName" 
                  placeholder="请输入参数名称"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item label="参数类型">
                <el-select 
                  v-model="searchForm.paramType" 
                  placeholder="请选择参数类型"
                  clearable
                  style="width: 150px"
                >
                  <el-option label="字符串" value="string" />
                  <el-option label="数字" value="number" />
                  <el-option label="布尔值" value="boolean" />
                  <el-option label="JSON" value="json" />
                  <el-option label="列表" value="array" />
                </el-select>
              </el-form-item>
              <el-form-item label="状态">
                <el-select 
                  v-model="searchForm.status" 
                  placeholder="请选择状态"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="启用" value="active" />
                  <el-option label="禁用" value="inactive" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch">
                  <el-icon><Search /></el-icon>
                  查询
                </el-button>
                <el-button @click="handleReset">
                  <el-icon><RefreshLeft /></el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 参数表格 -->
          <div class="table-section">
            <el-table 
              :data="getFilteredData(category.key)" 
              :loading="loading"
              row-key="id"
              class="parameter-table"
              @sort-change="handleSortChange"
            >
              <el-table-column prop="paramName" label="参数名称" min-width="180">
                <template #default="{ row }">
                  <div class="param-name">
                    <el-icon :class="getParamIcon(row.paramType)"></el-icon>
                    <span>{{ row.paramName }}</span>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column prop="paramKey" label="参数键" min-width="160">
                <template #default="{ row }">
                  <code class="param-key">{{ row.paramKey }}</code>
                </template>
              </el-table-column>
              
              <el-table-column prop="paramType" label="数据类型" width="100">
                <template #default="{ row }">
                  <el-tag :type="getTypeTagType(row.paramType)" size="small">
                    {{ getTypeLabel(row.paramType) }}
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column prop="paramValue" label="参数值" min-width="200">
                <template #default="{ row }">
                  <div class="param-value">
                    <template v-if="row.paramType === 'boolean'">
                      <el-switch
                        v-model="row.paramValue"
                        :active-value="true"
                        :inactive-value="false"
                        @change="handleQuickUpdate(row)"
                      />
                    </template>
                    <template v-else-if="row.paramType === 'number'">
                      <el-input-number
                        v-model="row.paramValue"
                        :precision="row.precision || 0"
                        :min="row.minValue"
                        :max="row.maxValue"
                        size="small"
                        @change="handleQuickUpdate(row)"
                      />
                    </template>
                    <template v-else>
                      <span class="value-display" :class="{ 'json-value': row.paramType === 'json' }">
                        {{ formatValue(row.paramValue, row.paramType) }}
                      </span>
                    </template>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column prop="defaultValue" label="默认值" width="120">
                <template #default="{ row }">
                  <span class="default-value">{{ formatValue(row.defaultValue, row.paramType) }}</span>
                </template>
              </el-table-column>
              
              <el-table-column prop="status" label="状态" width="80" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                    {{ row.status === 'active' ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column prop="updateTime" label="更新时间" width="120" sortable="custom">
                <template #default="{ row }">
                  <span class="date-text">{{ formatDate(row.updateTime) }}</span>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="160" fixed="right">
                <template #default="{ row }">
                  <div class="action-buttons">
                    <el-button link type="primary" @click="handleEdit(row)">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                    <el-button link type="warning" @click="handleReset(row)">
                      <el-icon><RefreshLeft /></el-icon>
                      重置
                    </el-button>
                    <el-button 
                      link 
                      :type="row.status === 'active' ? 'danger' : 'success'"
                      @click="handleToggleStatus(row)"
                    >
                      <el-icon v-if="row.status === 'active'"><Close /></el-icon>
                      <el-icon v-else><Check /></el-icon>
                      {{ row.status === 'active' ? '禁用' : '启用' }}
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 新增/编辑参数对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="650px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="parameter-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="参数名称" prop="paramName">
              <el-input
                v-model="formData.paramName"
                placeholder="请输入参数名称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="参数键" prop="paramKey">
              <el-input
                v-model="formData.paramKey"
                placeholder="例如：system.timeout"
                maxlength="100"
                :disabled="!!formData.id"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="参数分类" prop="category">
              <el-select
                v-model="formData.category"
                placeholder="请选择分类"
                style="width: 100%"
              >
                <el-option
                  v-for="cat in parameterCategories"
                  :key="cat.key"
                  :label="cat.label"
                  :value="cat.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据类型" prop="paramType">
              <el-select
                v-model="formData.paramType"
                placeholder="请选择类型"
                style="width: 100%"
                @change="handleTypeChange"
              >
                <el-option label="字符串" value="string" />
                <el-option label="数字" value="number" />
                <el-option label="布尔值" value="boolean" />
                <el-option label="JSON对象" value="json" />
                <el-option label="数组" value="array" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 动态参数值输入 -->
        <el-form-item label="参数值" prop="paramValue">
          <template v-if="formData.paramType === 'boolean'">
            <el-radio-group v-model="formData.paramValue">
              <el-radio :value="true">是(True)</el-radio>
              <el-radio :value="false">否(False)</el-radio>
            </el-radio-group>
          </template>
          
          <template v-else-if="formData.paramType === 'number'">
            <el-input-number
              v-model="formData.paramValue"
              :precision="formData.precision || 0"
              :min="formData.minValue"
              :max="formData.maxValue"
              style="width: 200px"
            />
          </template>
          
          <template v-else-if="formData.paramType === 'json'">
            <el-input
              v-model="formData.paramValue"
              type="textarea"
              placeholder='{"key": "value"}'
              :rows="4"
              style="width: 100%"
            />
          </template>
          
          <template v-else-if="formData.paramType === 'array'">
            <el-input
              v-model="formData.paramValue"
              type="textarea"
              placeholder='["item1", "item2"]'
              :rows="3"
              style="width: 100%"
            />
          </template>
          
          <template v-else>
            <el-input
              v-model="formData.paramValue"
              placeholder="请输入参数值"
              maxlength="500"
              show-word-limit
            />
          </template>
        </el-form-item>

        <!-- 数字类型特有配置 -->
        <template v-if="formData.paramType === 'number'">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="最小值" prop="minValue">
                <el-input-number
                  v-model="formData.minValue"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最大值" prop="maxValue">
                <el-input-number
                  v-model="formData.maxValue"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="小数位数" prop="precision">
                <el-input-number
                  v-model="formData.precision"
                  :min="0"
                  :max="10"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <el-form-item label="默认值" prop="defaultValue">
          <el-input
            v-model="formData.defaultValue"
            placeholder="请输入默认值"
          />
        </el-form-item>

        <el-form-item label="参数描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            placeholder="请输入参数描述"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio value="active">启用</el-radio>
            <el-radio value="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">
            <el-icon><Check /></el-icon>
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  Plus, Search, RefreshLeft, Edit, Check, Close, Upload, Tools
} from '@element-plus/icons-vue'
import { formatDate } from '@/utils/common'

// 参数分类配置
const parameterCategories = [
  { key: 'system', label: '系统配置', icon: 'Tools' },
  { key: 'business', label: '业务配置', icon: 'Briefcase' },
  { key: 'workflow', label: '工作流程', icon: 'Connection' },
  { key: 'notification', label: '通知设置', icon: 'Bell' },
  { key: 'integration', label: '集成配置', icon: 'Link' },
  { key: 'security', label: '安全设置', icon: 'Lock' }
]

// 响应式数据
const loading = ref(false)
const activeTab = ref('system')
const allParametersData = ref<any[]>([])

// 搜索表单
const searchForm = reactive({
  paramName: '',
  paramType: '',
  status: ''
})

// 对话框控制
const dialogVisible = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  id: null as number | null,
  paramName: '',
  paramKey: '',
  category: '',
  paramType: 'string',
  paramValue: '',
  defaultValue: '',
  minValue: undefined as number | undefined,
  maxValue: undefined as number | undefined,
  precision: 0,
  description: '',
  status: 'active' as 'active' | 'inactive'
})

// 表单验证规则
const formRules: FormRules = {
  paramName: [
    { required: true, message: '请输入参数名称', trigger: 'blur' },
    { min: 2, max: 50, message: '参数名称长度在2-50个字符', trigger: 'blur' }
  ],
  paramKey: [
    { required: true, message: '请输入参数键', trigger: 'blur' },
    { pattern: /^[a-z][a-z0-9._]*[a-z0-9]$/, message: '参数键格式不正确，应为小写字母开头，可包含数字、点号、下划线', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择参数分类', trigger: 'change' }
  ],
  paramType: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ],
  paramValue: [
    { required: true, message: '请输入参数值', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => formData.id ? '编辑系统参数' : '新增系统参数')

// 获取参数图标
const getParamIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    'string': 'el-icon-document',
    'number': 'el-icon-promotion',
    'boolean': 'el-icon-switch',
    'json': 'el-icon-document-copy',
    'array': 'el-icon-menu'
  }
  return iconMap[type] || 'el-icon-document'
}

// 获取类型标签类型
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'string': 'primary',
    'number': 'success',
    'boolean': 'warning',
    'json': 'info',
    'array': 'danger'
  }
  return typeMap[type] || ''
}

// 获取类型标签
const getTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    'string': '字符',
    'number': '数字',
    'boolean': '布尔',
    'json': 'JSON',
    'array': '数组'
  }
  return labelMap[type] || type
}

// 格式化值显示
const formatValue = (value: any, type: string) => {
  if (value === null || value === undefined) return '-'
  
  switch (type) {
    case 'boolean':
      return value ? '是' : '否'
    case 'json':
    case 'array':
      return typeof value === 'string' ? value : JSON.stringify(value)
    default:
      return value.toString()
  }
}

// 获取过滤后的数据
const getFilteredData = (category: string) => {
  let data = allParametersData.value.filter(item => item.category === category)
  
  if (searchForm.paramName) {
    data = data.filter(item => 
      item.paramName.toLowerCase().includes(searchForm.paramName.toLowerCase())
    )
  }
  
  if (searchForm.paramType) {
    data = data.filter(item => item.paramType === searchForm.paramType)
  }
  
  if (searchForm.status) {
    data = data.filter(item => item.status === searchForm.status)
  }
  
  return data
}

// 模拟数据
const mockData = [
  // 系统配置
  {
    id: 1,
    category: 'system',
    paramName: '会话超时时间',
    paramKey: 'system.session.timeout',
    paramType: 'number',
    paramValue: 1800,
    defaultValue: 1800,
    minValue: 300,
    maxValue: 7200,
    precision: 0,
    description: '用户会话超时时间（秒）',
    status: 'active',
    updateTime: '2024-12-27T10:30:00',
    createTime: '2024-01-15T09:00:00'
  },
  {
    id: 2,
    category: 'system',
    paramName: '系统维护模式',
    paramKey: 'system.maintenance.enabled',
    paramType: 'boolean',
    paramValue: false,
    defaultValue: false,
    description: '系统维护模式开关',
    status: 'active',
    updateTime: '2024-12-20T14:20:00',
    createTime: '2024-01-15T09:00:00'
  },
  {
    id: 3,
    category: 'system',
    paramName: '数据库连接池大小',
    paramKey: 'system.database.pool.size',
    paramType: 'number',
    paramValue: 20,
    defaultValue: 10,
    minValue: 5,
    maxValue: 100,
    precision: 0,
    description: '数据库连接池最大连接数',
    status: 'active',
    updateTime: '2024-12-15T16:45:00',
    createTime: '2024-02-01T11:30:00'
  },
  
  // 业务配置
  {
    id: 4,
    category: 'business',
    paramName: '订单自动审核',
    paramKey: 'business.order.auto.approve',
    paramType: 'boolean',
    paramValue: true,
    defaultValue: false,
    description: '订单是否启用自动审核',
    status: 'active',
    updateTime: '2024-12-26T09:15:00',
    createTime: '2024-03-01T14:20:00'
  },
  {
    id: 5,
    category: 'business',
    paramName: '最大订单金额',
    paramKey: 'business.order.max.amount',
    paramType: 'number',
    paramValue: 1000000.00,
    defaultValue: 500000.00,
    minValue: 1000.00,
    maxValue: 10000000.00,
    precision: 2,
    description: '单个订单最大金额限制',
    status: 'active',
    updateTime: '2024-12-25T11:30:00',
    createTime: '2024-03-15T10:45:00'
  },
  {
    id: 6,
    category: 'business',
    paramName: '晶圆测试参数',
    paramKey: 'business.wafer.test.config',
    paramType: 'json',
    paramValue: '{"temperature": 25, "voltage": 3.3, "current": 100}',
    defaultValue: '{"temperature": 25, "voltage": 3.3, "current": 100}',
    description: '晶圆测试默认参数配置',
    status: 'active',
    updateTime: '2024-12-27T08:45:00',
    createTime: '2024-04-01T09:20:00'
  },
  
  // 工作流程
  {
    id: 7,
    category: 'workflow',
    paramName: '工单审批流程',
    paramKey: 'workflow.workorder.approval.steps',
    paramType: 'array',
    paramValue: '["team_leader", "department_manager", "production_manager"]',
    defaultValue: '["team_leader", "department_manager"]',
    description: '工单审批流程步骤',
    status: 'active',
    updateTime: '2024-12-24T13:20:00',
    createTime: '2024-05-01T15:30:00'
  },
  {
    id: 8,
    category: 'workflow',
    paramName: '质检间隔时间',
    paramKey: 'workflow.quality.check.interval',
    paramType: 'number',
    paramValue: 240,
    defaultValue: 240,
    minValue: 60,
    maxValue: 480,
    precision: 0,
    description: '质检作业间隔时间（分钟）',
    status: 'active',
    updateTime: '2024-12-23T16:10:00',
    createTime: '2024-05-15T11:45:00'
  },
  
  // 通知设置
  {
    id: 9,
    category: 'notification',
    paramName: '邮件通知开关',
    paramKey: 'notification.email.enabled',
    paramType: 'boolean',
    paramValue: true,
    defaultValue: true,
    description: '系统邮件通知总开关',
    status: 'active',
    updateTime: '2024-12-22T10:25:00',
    createTime: '2024-06-01T08:30:00'
  },
  {
    id: 10,
    category: 'notification',
    paramName: 'SMTP服务器',
    paramKey: 'notification.smtp.server',
    paramType: 'string',
    paramValue: 'smtp.company.com',
    defaultValue: 'localhost',
    description: 'SMTP邮件服务器地址',
    status: 'active',
    updateTime: '2024-12-21T14:15:00',
    createTime: '2024-06-01T08:30:00'
  },
  
  // 集成配置
  {
    id: 11,
    category: 'integration',
    paramName: 'ERP系统接口',
    paramKey: 'integration.erp.api.url',
    paramType: 'string',
    paramValue: 'https://erp.company.com/api/v1',
    defaultValue: '',
    description: 'ERP系统API接口地址',
    status: 'active',
    updateTime: '2024-12-20T09:40:00',
    createTime: '2024-07-01T10:20:00'
  },
  {
    id: 12,
    category: 'integration',
    paramName: 'SECS/GEM连接超时',
    paramKey: 'integration.secsgem.timeout',
    paramType: 'number',
    paramValue: 30,
    defaultValue: 30,
    minValue: 5,
    maxValue: 120,
    precision: 0,
    description: 'SECS/GEM设备连接超时时间（秒）',
    status: 'active',
    updateTime: '2024-12-19T15:55:00',
    createTime: '2024-07-15T13:40:00'
  },
  
  // 安全设置
  {
    id: 13,
    category: 'security',
    paramName: '密码复杂度检查',
    paramKey: 'security.password.complexity.enabled',
    paramType: 'boolean',
    paramValue: true,
    defaultValue: true,
    description: '是否启用密码复杂度检查',
    status: 'active',
    updateTime: '2024-12-18T11:20:00',
    createTime: '2024-08-01T09:15:00'
  },
  {
    id: 14,
    category: 'security',
    paramName: '登录失败次数限制',
    paramKey: 'security.login.max.attempts',
    paramType: 'number',
    paramValue: 5,
    defaultValue: 5,
    minValue: 3,
    maxValue: 10,
    precision: 0,
    description: '登录失败次数限制',
    status: 'active',
    updateTime: '2024-12-17T08:30:00',
    createTime: '2024-08-01T09:15:00'
  }
]

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    allParametersData.value = mockData
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error('Fetch data error:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在getFilteredData中实现
}

const handleReset = () => {
  Object.assign(searchForm, {
    paramName: '',
    paramType: '',
    status: ''
  })
}

const handleAdd = () => {
  Object.assign(formData, {
    id: null,
    paramName: '',
    paramKey: '',
    category: activeTab.value,
    paramType: 'string',
    paramValue: '',
    defaultValue: '',
    minValue: undefined,
    maxValue: undefined,
    precision: 0,
    description: '',
    status: 'active'
  })
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  Object.assign(formData, { ...row })
  dialogVisible.value = true
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(formData.id ? '更新成功' : '创建成功')
    dialogVisible.value = false
    fetchData()
    
  } catch (error) {
    if (error !== false) { // 不是验证错误
      ElMessage.error('操作失败')
      console.error('Submit error:', error)
    }
  } finally {
    loading.value = false
  }
}

const handleQuickUpdate = async (row: any) => {
  try {
    loading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('参数更新成功')
    
  } catch (error) {
    ElMessage.error('更新失败')
    console.error('Quick update error:', error)
  } finally {
    loading.value = false
  }
}

const handleToggleStatus = async (row: any) => {
  try {
    loading.value = true
    const newStatus = row.status === 'active' ? 'inactive' : 'active'
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    row.status = newStatus
    ElMessage.success(`${newStatus === 'active' ? '启用' : '禁用'}成功`)
    
  } catch (error) {
    ElMessage.error('操作失败')
    console.error('Toggle status error:', error)
  } finally {
    loading.value = false
  }
}

const handleResetParam = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置参数"${row.paramName}"为默认值吗？`,
      '确认重置',
      {
        confirmButtonText: '确定重置',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    row.paramValue = row.defaultValue
    ElMessage.success('参数重置成功')
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置失败')
      console.error('Reset parameter error:', error)
    }
  } finally {
    loading.value = false
  }
}

const handleBatchSave = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要批量保存当前所有参数的修改吗？',
      '确认批量保存',
      {
        confirmButtonText: '确定保存',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    ElMessage.success('批量保存成功')
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量保存失败')
      console.error('Batch save error:', error)
    }
  } finally {
    loading.value = false
  }
}

const handleTypeChange = () => {
  // 类型改变时重置相关字段
  formData.paramValue = ''
  formData.minValue = undefined
  formData.maxValue = undefined
  formData.precision = 0
}

const handleSortChange = ({ prop, order }: any) => {
  // 实现排序逻辑
  console.log('Sort change:', prop, order)
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.system-parameters-management {
  padding: 24px;
  background: var(--color-bg-1);
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--color-border-2);

  .header-left {
    flex: 1;
  }

  .page-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 24px;
    font-weight: 600;
    color: var(--color-text-1);
    margin: 0 0 8px 0;

    .el-icon {
      color: var(--color-primary);
    }
  }

  .page-description {
    color: var(--color-text-3);
    margin: 0;
    line-height: 1.5;
  }

  .header-actions {
    margin-left: 16px;
    display: flex;
    gap: 12px;
  }
}

.parameter-tabs {
  .parameter-tabs-container {
    border: 1px solid var(--color-border-2);
    border-radius: var(--radius-base);
    overflow: hidden;

    :deep(.el-tabs__header) {
      background: var(--color-bg-2);
      margin: 0;
    }

    :deep(.el-tabs__content) {
      padding: 0;
    }

    :deep(.el-tab-pane) {
      background: var(--color-bg-1);
    }
  }
}

.search-section {
  background: var(--color-bg-2);
  padding: 20px;
  border-bottom: 1px solid var(--color-border-2);

  .search-form {
    .el-form-item {
      margin-bottom: 0;
    }
  }
}

.table-section {
  .parameter-table {
    background: var(--color-bg-2);

    .param-name {
      display: flex;
      align-items: center;
      gap: 8px;

      .el-icon {
        color: var(--color-primary);
      }
    }

    .param-key {
      background: var(--color-fill-2);
      padding: 4px 8px;
      border-radius: 4px;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 13px;
      color: var(--color-primary);
    }

    .param-value {
      .value-display {
        &.json-value {
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 13px;
          background: var(--color-fill-1);
          padding: 4px 8px;
          border-radius: 4px;
          display: inline-block;
          max-width: 180px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .default-value {
      color: var(--color-text-3);
      font-size: 13px;
    }

    .date-text {
      color: var(--color-text-2);
      font-size: 13px;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }
}

.parameter-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .system-parameters-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .header-actions {
      margin-left: 0;
    }
  }

  .search-section {
    padding: 16px;

    .search-form {
      .el-form-item {
        display: block;
        margin-bottom: 16px;

        :deep(.el-form-item__content) {
          margin-left: 0 !important;
        }
      }
    }
  }

  .action-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .param-key {
    background: var(--el-fill-color-darker);
    color: var(--el-color-primary-light-3);
  }

  .json-value {
    background: var(--el-fill-color-darker) !important;
  }

  .parameter-tabs-container {
    :deep(.el-tabs__header) {
      background: var(--el-fill-color-light);
    }
  }
}
</style>