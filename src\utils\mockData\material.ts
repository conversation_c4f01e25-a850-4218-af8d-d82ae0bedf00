// 物料与库存管理模拟数据 - OSAT专业版
// 严格按照第一阶段规划实现半导体专用仓库管理

import type {
  MaterialInventory,
  MaterialTransaction,
  WarehouseZone,
  DieBank,
  SupplierPerformance
} from '@/types/material'

import { MaterialCategory, StorageCondition, MaterialStatus } from '@/types/material'

// 半导体专用仓库区域配置
export const mockWarehouseZones: WarehouseZone[] = [
  {
    zoneId: 'ESD-001',
    zoneName: 'ESD安全存储区A',
    zoneType: 'ESD_SAFE',
    capacity: 10000,
    currentUtilization: 75.5,
    environmentalConditions: {
      temperature: 22,
      humidity: 45,
      cleanliness: 'Class 1000',
      esdCompliance: true
    },
    allowedMaterials: [
      MaterialCategory.DIE_BANK,
      MaterialCategory.WAFER,
      MaterialCategory.WIRE_BOND
    ],
    restrictions: ['防静电服装要求', '湿度控制<50%', '温度控制20-25°C']
  },
  {
    zoneId: 'TEMP-001',
    zoneName: '温控化学品区',
    zoneType: 'TEMP_CONTROLLED',
    capacity: 5000,
    currentUtilization: 82.3,
    environmentalConditions: {
      temperature: 4,
      humidity: 30,
      cleanliness: 'Class 10000',
      esdCompliance: false
    },
    allowedMaterials: [
      MaterialCategory.CHEMICAL,
      MaterialCategory.ADHESIVE,
      MaterialCategory.MOLD_COMPOUND
    ],
    restrictions: ['低温存储', '化学品隔离', '防火防爆']
  },
  {
    zoneId: 'CLEAN-001',
    zoneName: '洁净室材料区',
    zoneType: 'CLEAN_ROOM',
    capacity: 8000,
    currentUtilization: 68.9,
    environmentalConditions: {
      temperature: 21,
      humidity: 40,
      cleanliness: 'Class 100',
      esdCompliance: true
    },
    allowedMaterials: [
      MaterialCategory.LEADFRAME,
      MaterialCategory.SUBSTRATE,
      MaterialCategory.PACKAGING_MATERIAL
    ],
    restrictions: ['洁净室等级要求', 'HEPA过滤', '人员进入管制']
  }
]

// Die Bank管理数据
export const mockDieBanks: DieBank[] = [
  {
    dieBankId: 'DB-HK9000-001',
    waferLot: 'W240815-001',
    dieType: 'HiSilicon Kirin 9000',
    totalDieCount: 2850,
    availableDieCount: 2456,
    dieSize: {
      width: 10.8,
      height: 9.6,
      thickness: 75
    },
    binMap: [
      {
        binCode: 'BIN1',
        binDescription: '最高性能等级',
        dieCount: 1820,
        qualityGrade: 'A',
        testResults: {
          electricalTest: true,
          visualInspection: true,
          reliability: 98.5
        }
      },
      {
        binCode: 'BIN2',
        binDescription: '标准性能等级',
        dieCount: 636,
        qualityGrade: 'B',
        testResults: {
          electricalTest: true,
          visualInspection: true,
          reliability: 95.2
        }
      },
      {
        binCode: 'BIN_FAIL',
        binDescription: '失效芯片',
        dieCount: 394,
        qualityGrade: 'D',
        testResults: {
          electricalTest: false,
          visualInspection: true,
          reliability: 0
        }
      }
    ],
    storageLocation: 'ESD-001-A12',
    temperature: 22.1,
    humidity: 44.8,
    lastAccessTime: '2024-08-26T08:30:00Z'
  },
  {
    dieBankId: 'DB-MTK8000-002',
    waferLot: 'W240820-003',
    dieType: 'MediaTek Dimensity 8000',
    totalDieCount: 3200,
    availableDieCount: 2890,
    dieSize: {
      width: 9.2,
      height: 8.8,
      thickness: 70
    },
    binMap: [
      {
        binCode: 'BIN1',
        binDescription: '高性能等级',
        dieCount: 2156,
        qualityGrade: 'A',
        testResults: {
          electricalTest: true,
          visualInspection: true,
          reliability: 97.8
        }
      },
      {
        binCode: 'BIN2',
        binDescription: '标准等级',
        dieCount: 734,
        qualityGrade: 'B',
        testResults: {
          electricalTest: true,
          visualInspection: true,
          reliability: 94.1
        }
      }
    ],
    storageLocation: 'ESD-001-B08',
    temperature: 21.8,
    humidity: 43.2,
    lastAccessTime: '2024-08-26T09:15:00Z'
  }
]

// 物料库存数据
export const mockMaterialInventory: MaterialInventory[] = [
  {
    inventoryId: 'INV-001',
    materialSpec: {
      materialCode: 'LF-QFP144-001',
      materialName: 'QFP144引脚框架',
      specification: '144-Pin QFP Leadframe',
      manufacturer: 'Advanced Semiconductor Materials',
      lotNumber: 'LF240820001',
      storageRequirements: [StorageCondition.ESD_SAFE, StorageCondition.CLEAN_ROOM]
    },
    category: MaterialCategory.LEADFRAME,
    currentStock: 15800,
    reservedStock: 2300,
    availableStock: 13500,
    reorderPoint: 5000,
    maxStock: 25000,
    unitOfMeasure: 'PCS',
    unitCost: 0.85,
    totalValue: 13430.0,
    warehouseZone: mockWarehouseZones[2],
    status: MaterialStatus.APPROVED,
    lastMovement: {
      date: '2024-08-25T14:20:00Z',
      type: 'OUT',
      quantity: 500,
      reason: '生产投料',
      operator: 'Zhang Wei'
    },
    supplier: {
      supplierId: 'SUP-ASM-001',
      supplierName: 'Advanced Semiconductor Materials',
      leadTime: 14,
      qualityRating: 98.5
    }
  },
  {
    inventoryId: 'INV-002',
    materialSpec: {
      materialCode: 'SUB-BGA256-002',
      materialName: 'BGA256基板',
      specification: '256-Ball BGA Substrate',
      manufacturer: 'Precision Substrates Corp',
      lotNumber: 'SUB240818005',
      storageRequirements: [StorageCondition.ESD_SAFE, StorageCondition.HUMIDITY_CONTROLLED]
    },
    category: MaterialCategory.SUBSTRATE,
    currentStock: 8900,
    reservedStock: 1200,
    availableStock: 7700,
    reorderPoint: 3000,
    maxStock: 15000,
    unitOfMeasure: 'PCS',
    unitCost: 2.35,
    totalValue: 20915.0,
    warehouseZone: mockWarehouseZones[2],
    status: MaterialStatus.APPROVED,
    lastMovement: {
      date: '2024-08-26T09:30:00Z',
      type: 'IN',
      quantity: 2000,
      reason: '供应商来料',
      operator: 'Liu Ming'
    },
    supplier: {
      supplierId: 'SUP-PSC-002',
      supplierName: 'Precision Substrates Corp',
      leadTime: 21,
      qualityRating: 97.2
    }
  },
  {
    inventoryId: 'INV-003',
    materialSpec: {
      materialCode: 'WIRE-AU-25',
      materialName: '金线25μm',
      specification: 'Gold Wire 25μm Diameter',
      manufacturer: 'Premium Wire Solutions',
      lotNumber: 'GW240801012',
      expiryDate: '2025-08-01',
      storageRequirements: [StorageCondition.ESD_SAFE, StorageCondition.TEMPERATURE_CONTROLLED]
    },
    category: MaterialCategory.WIRE_BOND,
    currentStock: 125.5,
    reservedStock: 15.2,
    availableStock: 110.3,
    reorderPoint: 30.0,
    maxStock: 200.0,
    unitOfMeasure: 'KG',
    unitCost: 58200.0,
    totalValue: 7304100.0,
    warehouseZone: mockWarehouseZones[0],
    status: MaterialStatus.APPROVED,
    lastMovement: {
      date: '2024-08-25T16:45:00Z',
      type: 'OUT',
      quantity: 2.8,
      reason: '键合工艺消耗',
      operator: 'Chen Lei'
    },
    supplier: {
      supplierId: 'SUP-PWS-003',
      supplierName: 'Premium Wire Solutions',
      leadTime: 35,
      qualityRating: 99.1
    }
  },
  {
    inventoryId: 'INV-004',
    materialSpec: {
      materialCode: 'MOLD-EMC-A1',
      materialName: 'A1级塑封料',
      specification: 'Epoxy Molding Compound Grade A1',
      manufacturer: 'Elite Molding Materials',
      lotNumber: 'EMC240815023',
      expiryDate: '2025-02-15',
      storageRequirements: [
        StorageCondition.TEMPERATURE_CONTROLLED,
        StorageCondition.HUMIDITY_CONTROLLED
      ]
    },
    category: MaterialCategory.MOLD_COMPOUND,
    currentStock: 2850,
    reservedStock: 350,
    availableStock: 2500,
    reorderPoint: 1000,
    maxStock: 5000,
    unitOfMeasure: 'KG',
    unitCost: 125.0,
    totalValue: 356250.0,
    warehouseZone: mockWarehouseZones[1],
    status: MaterialStatus.APPROVED,
    lastMovement: {
      date: '2024-08-24T11:20:00Z',
      type: 'OUT',
      quantity: 85,
      reason: '塑封工艺投料',
      operator: 'Wang Fang'
    },
    supplier: {
      supplierId: 'SUP-EMM-004',
      supplierName: 'Elite Molding Materials',
      leadTime: 28,
      qualityRating: 96.8
    }
  },
  {
    inventoryId: 'INV-005',
    materialSpec: {
      materialCode: 'CHEM-FLUX-B2',
      materialName: 'B2级助焊剂',
      specification: 'Soldering Flux Grade B2',
      manufacturer: 'Chemical Solutions Ltd',
      lotNumber: 'FL240810007',
      expiryDate: '2024-11-10',
      storageRequirements: [
        StorageCondition.TEMPERATURE_CONTROLLED,
        StorageCondition.NITROGEN_CABINET
      ]
    },
    category: MaterialCategory.CHEMICAL,
    currentStock: 145,
    reservedStock: 25,
    availableStock: 120,
    reorderPoint: 50,
    maxStock: 300,
    unitOfMeasure: 'L',
    unitCost: 180.0,
    totalValue: 26100.0,
    warehouseZone: mockWarehouseZones[1],
    status: MaterialStatus.APPROVED,
    lastMovement: {
      date: '2024-08-23T13:15:00Z',
      type: 'OUT',
      quantity: 12,
      reason: '焊接工艺消耗',
      operator: 'Zhao Jun'
    },
    supplier: {
      supplierId: 'SUP-CSL-005',
      supplierName: 'Chemical Solutions Ltd',
      leadTime: 10,
      qualityRating: 94.5
    }
  }
]

// 物料事务记录
export const mockMaterialTransactions: MaterialTransaction[] = [
  {
    transactionId: 'TXN-20240826-001',
    transactionType: 'RECEIPT',
    materialCode: 'SUB-BGA256-002',
    quantity: 2000,
    toLocation: 'CLEAN-001-C15',
    operator: 'Liu Ming',
    timestamp: '2024-08-26T09:30:00Z',
    approver: 'Manager Chen',
    notes: '供应商正常来料',
    batchNumber: 'SUB240818005',
    qualityInspection: {
      inspector: 'QC Team A',
      inspectionDate: '2024-08-26T10:00:00Z',
      result: 'PASS',
      remarks: '外观检查合格，尺寸符合要求'
    }
  },
  {
    transactionId: 'TXN-20240825-015',
    transactionType: 'ISSUE',
    materialCode: 'LF-QFP144-001',
    quantity: 500,
    fromLocation: 'CLEAN-001-B08',
    workOrder: 'WO-HK9000-240825-03',
    operator: 'Zhang Wei',
    timestamp: '2024-08-25T14:20:00Z',
    notes: '封装工艺投料',
    batchNumber: 'LF240820001'
  },
  {
    transactionId: 'TXN-20240825-012',
    transactionType: 'ISSUE',
    materialCode: 'WIRE-AU-25',
    quantity: 2.8,
    fromLocation: 'ESD-001-A05',
    workOrder: 'WO-MTK8000-240825-01',
    operator: 'Chen Lei',
    timestamp: '2024-08-25T16:45:00Z',
    notes: '键合工艺金线消耗',
    batchNumber: 'GW240801012'
  },
  {
    transactionId: 'TXN-20240824-008',
    transactionType: 'ISSUE',
    materialCode: 'MOLD-EMC-A1',
    quantity: 85,
    fromLocation: 'TEMP-001-D02',
    workOrder: 'WO-HK9000-240824-05',
    operator: 'Wang Fang',
    timestamp: '2024-08-24T11:20:00Z',
    notes: '塑封工艺投料',
    batchNumber: 'EMC240815023'
  },
  {
    transactionId: 'TXN-20240823-022',
    transactionType: 'TRANSFER',
    materialCode: 'CHEM-FLUX-B2',
    quantity: 12,
    fromLocation: 'TEMP-001-E08',
    toLocation: '生产线-B02',
    operator: 'Zhao Jun',
    timestamp: '2024-08-23T13:15:00Z',
    notes: '转移至焊接工位',
    batchNumber: 'FL240810007'
  }
]

// 供应商绩效数据
export const mockSupplierPerformance: SupplierPerformance[] = [
  {
    supplierId: 'SUP-ASM-001',
    supplierName: 'Advanced Semiconductor Materials',
    performancePeriod: {
      startDate: '2024-07-01',
      endDate: '2024-07-31'
    },
    kpi: {
      onTimeDelivery: 98.5,
      qualityRating: 98.5,
      responseTime: 4.2,
      costPerformance: 96.8,
      defectRate: 15.2,
      returnRate: 0.5
    },
    totalOrders: 28,
    onTimeOrders: 27,
    qualityIssues: 1,
    totalValue: 1250000,
    averageLeadTime: 13.5,
    certifications: ['ISO9001', 'IATF16949', 'ISO14001']
  },
  {
    supplierId: 'SUP-PSC-002',
    supplierName: 'Precision Substrates Corp',
    performancePeriod: {
      startDate: '2024-07-01',
      endDate: '2024-07-31'
    },
    kpi: {
      onTimeDelivery: 97.2,
      qualityRating: 97.2,
      responseTime: 6.8,
      costPerformance: 94.5,
      defectRate: 28.3,
      returnRate: 1.2
    },
    totalOrders: 18,
    onTimeOrders: 17,
    qualityIssues: 2,
    totalValue: 850000,
    averageLeadTime: 20.2,
    certifications: ['ISO9001', 'ISO14001', 'RoHS']
  },
  {
    supplierId: 'SUP-PWS-003',
    supplierName: 'Premium Wire Solutions',
    performancePeriod: {
      startDate: '2024-07-01',
      endDate: '2024-07-31'
    },
    kpi: {
      onTimeDelivery: 99.1,
      qualityRating: 99.1,
      responseTime: 2.1,
      costPerformance: 98.2,
      defectRate: 5.8,
      returnRate: 0.1
    },
    totalOrders: 12,
    onTimeOrders: 12,
    qualityIssues: 0,
    totalValue: 3200000,
    averageLeadTime: 34.8,
    certifications: ['ISO9001', 'IATF16949', 'ISO14001', 'AS9100']
  }
]

// 库存预警分析
export const getStockWarnings = () => {
  return mockMaterialInventory
    .filter(item => {
      const stockRatio = item.availableStock / item.maxStock
      const isLowStock = item.availableStock <= item.reorderPoint
      const isExpiringSoon =
        item.materialSpec.expiryDate &&
        new Date(item.materialSpec.expiryDate) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)

      return isLowStock || isExpiringSoon || stockRatio < 0.2
    })
    .map(item => ({
      materialCode: item.materialSpec.materialCode,
      materialName: item.materialSpec.materialName,
      currentStock: item.availableStock,
      reorderPoint: item.reorderPoint,
      warningType: item.availableStock <= item.reorderPoint ? 'LOW_STOCK' : 'EXPIRING_SOON',
      urgencyLevel: item.availableStock <= item.reorderPoint * 0.5 ? 'HIGH' : 'MEDIUM',
      suggestedAction:
        item.availableStock <= item.reorderPoint
          ? `紧急补货 - 建议订购 ${item.maxStock - item.currentStock} ${item.unitOfMeasure}`
          : '关注到期日期 - 考虑使用优先级调整'
    }))
}

// 获取仓库利用率统计
export const getWarehouseUtilization = () => {
  return mockWarehouseZones.map(zone => ({
    zoneId: zone.zoneId,
    zoneName: zone.zoneName,
    utilization: zone.currentUtilization,
    capacity: zone.capacity,
    utilizationTrend:
      zone.currentUtilization > 80 ? 'HIGH' : zone.currentUtilization > 60 ? 'MEDIUM' : 'LOW',
    recommendation:
      zone.currentUtilization > 85
        ? '考虑扩容或优化布局'
        : zone.currentUtilization < 40
          ? '利用率偏低，可考虑整合'
          : '利用率正常'
  }))
}
