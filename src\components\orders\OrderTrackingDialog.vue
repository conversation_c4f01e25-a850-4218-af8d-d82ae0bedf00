<template>
  <el-dialog
    v-model="visible"
    title="订单追踪"
    width="900px"
    destroy-on-close
    @close="handleClose"
  >
    <div v-if="orderData" class="tracking-content">
      <!-- 订单基本信息 -->
      <div class="order-header">
        <div class="order-info">
          <h3>{{ orderData.orderNumber }}</h3>
          <p>{{ orderData.customer.name }} - {{ orderData.productInfo.name }}</p>
        </div>
        <el-tag :type="getStatusTagType(orderData.status)" size="large">
          {{ getStatusText(orderData.status) }}
        </el-tag>
      </div>

      <!-- 工艺流程追踪 -->
      <div class="tracking-section">
        <h4 class="section-title">生产工艺追踪</h4>
        <div class="process-timeline">
          <div 
            v-for="(stage, index) in processStages" 
            :key="index"
            class="process-stage"
            :class="{ 
              'stage--completed': stage.status === 'completed',
              'stage--processing': stage.status === 'processing',
              'stage--pending': stage.status === 'pending'
            }"
          >
            <div class="stage-connector" v-if="index > 0"></div>
            <div class="stage-icon">
              <svg v-html="stage.icon" viewBox="0 0 24 24"></svg>
            </div>
            <div class="stage-content">
              <h5 class="stage-title">{{ stage.title }}</h5>
              <p class="stage-description">{{ stage.description }}</p>
              <div class="stage-details">
                <div v-if="stage.startTime" class="detail-item">
                  <span class="label">开始时间：</span>
                  <span class="value">{{ formatDateTime(stage.startTime) }}</span>
                </div>
                <div v-if="stage.endTime" class="detail-item">
                  <span class="label">完成时间：</span>
                  <span class="value">{{ formatDateTime(stage.endTime) }}</span>
                </div>
                <div v-if="stage.duration" class="detail-item">
                  <span class="label">用时：</span>
                  <span class="value">{{ stage.duration }}</span>
                </div>
                <div v-if="stage.yield" class="detail-item">
                  <span class="label">良率：</span>
                  <span class="value" :class="getYieldClass(stage.yield)">{{ stage.yield }}%</span>
                </div>
              </div>
              <div v-if="stage.issues && stage.issues.length > 0" class="stage-issues">
                <h6>异常记录：</h6>
                <div v-for="issue in stage.issues" :key="issue.id" class="issue-item">
                  <el-tag type="warning" size="small">{{ issue.type }}</el-tag>
                  <span>{{ issue.description }}</span>
                  <span class="issue-time">{{ formatDateTime(issue.time) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 质量数据 -->
      <div class="tracking-section">
        <h4 class="section-title">质量追踪数据</h4>
        <div class="quality-grid">
          <div v-for="metric in qualityMetrics" :key="metric.key" class="quality-card">
            <div class="metric-header">
              <div class="metric-icon" :class="metric.iconClass">
                <svg v-html="metric.icon" viewBox="0 0 24 24"></svg>
              </div>
              <div class="metric-info">
                <h5 class="metric-title">{{ metric.title }}</h5>
                <div class="metric-value" :class="metric.valueClass">{{ metric.value }}</div>
              </div>
            </div>
            <div class="metric-details">
              <div v-for="detail in metric.details" :key="detail.label" class="detail-row">
                <span class="detail-label">{{ detail.label }}：</span>
                <span class="detail-value">{{ detail.value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 设备追踪 -->
      <div class="tracking-section">
        <h4 class="section-title">设备使用记录</h4>
        <el-table :data="equipmentRecords" style="width: 100%" size="small">
          <el-table-column prop="stage" label="工艺阶段" width="120" />
          <el-table-column prop="equipment" label="设备编号" width="120" />
          <el-table-column prop="operator" label="操作员" width="100" />
          <el-table-column prop="startTime" label="开始时间" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.startTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="endTime" label="结束时间" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.endTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getEquipmentStatusType(row.status)" size="small">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="remarks" label="备注" />
        </el-table>
      </div>

      <!-- 物料追踪 -->
      <div class="tracking-section">
        <h4 class="section-title">原材料追踪</h4>
        <div class="material-tracking">
          <div class="material-flow">
            <div v-for="material in materialFlow" :key="material.id" class="material-item">
              <div class="material-icon">
                <svg v-html="material.icon" viewBox="0 0 24 24"></svg>
              </div>
              <div class="material-info">
                <h6>{{ material.name }}</h6>
                <p>{{ material.description }}</p>
                <div class="material-details">
                  <span class="batch">批次: {{ material.batch }}</span>
                  <span class="supplier">供应商: {{ material.supplier }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExportTracking">导出追踪报告</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue: boolean
  orderData?: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 工艺阶段数据
const processStages = ref([
  {
    title: 'CP晶圆电测',
    description: '晶圆探针测试，检验芯片电性能',
    status: 'completed',
    startTime: '2024-01-16T08:00:00',
    endTime: '2024-01-17T16:30:00',
    duration: '1天8.5小时',
    yield: 98.5,
    icon: '<circle cx="12" cy="12" r="3"/><path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>',
    issues: []
  },
  {
    title: 'Die Attach',
    description: '芯片贴装，将芯片固定到基板',
    status: 'completed',
    startTime: '2024-01-18T08:00:00',
    endTime: '2024-01-18T18:00:00',
    duration: '10小时',
    yield: 99.2,
    icon: '<rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>',
    issues: []
  },
  {
    title: 'Wire Bond',
    description: '引线键合，连接芯片与外部引脚',
    status: 'processing',
    startTime: '2024-01-19T08:00:00',
    endTime: null,
    duration: null,
    yield: null,
    icon: '<path d="M5 12h14"/>',
    issues: [
      {
        id: 1,
        type: '参数异常',
        description: '键合力度超出标准范围',
        time: '2024-01-19T14:30:00'
      }
    ]
  },
  {
    title: 'Molding',
    description: '塑封成型，保护芯片不受环境影响',
    status: 'pending',
    startTime: null,
    endTime: null,
    duration: null,
    yield: null,
    icon: '<rect x="3" y="11" width="18" height="10" rx="2"/>',
    issues: []
  },
  {
    title: 'FT最终测试',
    description: '成品功能测试和电性能验证',
    status: 'pending',
    startTime: null,
    endTime: null,
    duration: null,
    yield: null,
    icon: '<path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>',
    issues: []
  }
])

// 质量指标数据
const qualityMetrics = ref([
  {
    key: 'overall_yield',
    title: '整体良率',
    value: '98.5%',
    valueClass: 'metric-value--excellent',
    iconClass: 'metric-icon--success',
    icon: '<path d="M22 12h-4l-3 9L9 3l-3 9H2"/>',
    details: [
      { label: '目标良率', value: '≥95%' },
      { label: '当前状态', value: '优秀' },
      { label: '改善建议', value: '继续保持' }
    ]
  },
  {
    key: 'defect_rate',
    title: '缺陷率',
    value: '1.5%',
    valueClass: 'metric-value--good',
    iconClass: 'metric-icon--warning',
    icon: '<path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>',
    details: [
      { label: '主要缺陷', value: '键合异常' },
      { label: '缺陷分布', value: 'Wire Bond 80%' },
      { label: '改善措施', value: '调整参数' }
    ]
  },
  {
    key: 'process_capability',
    title: '工艺能力',
    value: 'Cpk 1.67',
    valueClass: 'metric-value--excellent',
    iconClass: 'metric-icon--primary',
    icon: '<circle cx="12" cy="12" r="10"/><path d="M8 14s1.5 2 4 2 4-2 4-2"/>',
    details: [
      { label: '控制状态', value: '稳定' },
      { label: '能力评级', value: '优秀' },
      { label: '改善空间', value: '持续监控' }
    ]
  }
])

// 设备使用记录
const equipmentRecords = ref([
  {
    stage: 'CP测试',
    equipment: 'ATE-001',
    operator: '张三',
    startTime: '2024-01-16T08:00:00',
    endTime: '2024-01-17T16:30:00',
    status: '正常',
    remarks: '设备运行正常'
  },
  {
    stage: 'Die Attach',
    equipment: 'DA-002',
    operator: '李四',
    startTime: '2024-01-18T08:00:00',
    endTime: '2024-01-18T18:00:00',
    status: '正常',
    remarks: '温度控制良好'
  },
  {
    stage: 'Wire Bond',
    equipment: 'WB-003',
    operator: '王五',
    startTime: '2024-01-19T08:00:00',
    endTime: '2024-01-19T16:00:00',
    status: '异常',
    remarks: '键合力度需要调整'
  }
])

// 物料追踪数据
const materialFlow = ref([
  {
    id: 1,
    name: '晶圆',
    description: '12寸硅晶圆，A级品',
    batch: 'W240115001',
    supplier: 'TSMC',
    icon: '<circle cx="12" cy="12" r="10"/>'
  },
  {
    id: 2,
    name: '基板',
    description: 'FC-BGA基板，多层结构',
    batch: 'S240110002',
    supplier: 'ASE Group',
    icon: '<rect x="2" y="2" width="20" height="20" rx="2"/>'
  },
  {
    id: 3,
    name: '金线',
    description: '25微米金线，高纯度',
    batch: 'G240108001',
    supplier: 'Tanaka',
    icon: '<path d="M5 12h14"/>'
  },
  {
    id: 4,
    name: '塑封材料',
    description: '环氧树脂，阻燃等级UL94-V0',
    batch: 'M240112001',
    supplier: 'Sumitomo',
    icon: '<rect x="3" y="11" width="18" height="10" rx="2"/>'
  }
])

// 辅助方法
const getStatusText = (status: string) => {
  const statusMap = {
    'pending': '待确认',
    'processing': '生产中',
    'testing': '测试中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const getStatusTagType = (status: string) => {
  const typeMap = {
    'pending': 'info',
    'processing': 'warning',
    'testing': 'primary',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return typeMap[status as keyof typeof typeMap] || 'info'
}

const getYieldClass = (yieldValue: number) => {
  if (yieldValue >= 98) return 'yield--excellent'
  if (yieldValue >= 95) return 'yield--good'
  if (yieldValue >= 90) return 'yield--acceptable'
  return 'yield--poor'
}

const getEquipmentStatusType = (status: string) => {
  const typeMap = {
    '正常': 'success',
    '异常': 'danger',
    '维护': 'warning'
  }
  return typeMap[status as keyof typeof typeMap] || 'info'
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleClose = () => {
  visible.value = false
}

const handleExportTracking = () => {
  ElMessage.success('追踪报告导出中...')
}
</script>

<style lang="scss" scoped>
.tracking-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

// ===== 订单头部 =====
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-4);
  background-color: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-4);
  
  .order-info {
    h3 {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-text-primary);
      margin-bottom: var(--spacing-1);
    }
    
    p {
      color: var(--color-text-secondary);
      margin: 0;
    }
  }
}

// ===== 追踪区域 =====
.tracking-section {
  .section-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-2);
    border-bottom: 2px solid var(--color-primary);
  }
}

// ===== 工艺流程时间线 =====
.process-timeline {
  position: relative;
  
  .process-stage {
    position: relative;
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-6);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .stage-connector {
      position: absolute;
      left: 24px;
      top: -24px;
      width: 2px;
      height: 24px;
      background-color: var(--color-border-light);
      
      .stage--completed + & {
        background-color: var(--color-success);
      }
      
      .stage--processing + & {
        background-color: var(--color-warning);
      }
    }
    
    .stage-icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-right: var(--spacing-4);
      
      svg {
        width: 20px;
        height: 20px;
        stroke: currentColor;
        fill: none;
        stroke-width: 1.5;
      }
    }
    
    &.stage--completed .stage-icon {
      background-color: var(--color-success);
      color: white;
    }
    
    &.stage--processing .stage-icon {
      background-color: var(--color-warning);
      color: white;
    }
    
    &.stage--pending .stage-icon {
      background-color: var(--color-bg-tertiary);
      color: var(--color-text-tertiary);
    }
    
    .stage-content {
      flex: 1;
      
      .stage-title {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-medium);
        color: var(--color-text-primary);
        margin-bottom: var(--spacing-1);
      }
      
      .stage-description {
        color: var(--color-text-secondary);
        margin-bottom: var(--spacing-3);
      }
      
      .stage-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-2);
        margin-bottom: var(--spacing-3);
        
        .detail-item {
          display: flex;
          
          .label {
            font-weight: var(--font-weight-medium);
            color: var(--color-text-secondary);
            min-width: 80px;
          }
          
          .value {
            color: var(--color-text-primary);
            
            &.yield--excellent {
              color: var(--color-success);
              font-weight: var(--font-weight-medium);
            }
            
            &.yield--good {
              color: var(--color-primary);
            }
            
            &.yield--acceptable {
              color: var(--color-warning);
            }
            
            &.yield--poor {
              color: var(--color-error);
            }
          }
        }
      }
      
      .stage-issues {
        h6 {
          color: var(--color-warning);
          margin-bottom: var(--spacing-2);
        }
        
        .issue-item {
          display: flex;
          align-items: center;
          gap: var(--spacing-2);
          margin-bottom: var(--spacing-1);
          font-size: var(--font-size-sm);
          
          .issue-time {
            color: var(--color-text-tertiary);
            margin-left: auto;
          }
        }
      }
    }
  }
}

// ===== 质量指标网格 =====
.quality-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-4);
}

.quality-card {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-5);
  
  .metric-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-4);
    
    .metric-icon {
      width: 40px;
      height: 40px;
      border-radius: var(--radius-base);
      display: flex;
      align-items: center;
      justify-content: center;
      
      svg {
        width: 20px;
        height: 20px;
        stroke: currentColor;
        fill: none;
        stroke-width: 1.5;
      }
      
      &--success {
        background-color: color-mix(in srgb, var(--color-success) 10%, transparent);
        color: var(--color-success);
      }
      
      &--warning {
        background-color: color-mix(in srgb, var(--color-warning) 10%, transparent);
        color: var(--color-warning);
      }
      
      &--primary {
        background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);
        color: var(--color-primary);
      }
    }
    
    .metric-info {
      flex: 1;
      
      .metric-title {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-medium);
        color: var(--color-text-secondary);
        margin-bottom: var(--spacing-1);
      }
      
      .metric-value {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        
        &--excellent {
          color: var(--color-success);
        }
        
        &--good {
          color: var(--color-primary);
        }
        
        &--warning {
          color: var(--color-warning);
        }
      }
    }
  }
  
  .metric-details {
    .detail-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: var(--spacing-1);
      font-size: var(--font-size-sm);
      
      .detail-label {
        color: var(--color-text-secondary);
      }
      
      .detail-value {
        color: var(--color-text-primary);
        font-weight: var(--font-weight-medium);
      }
    }
  }
}

// ===== 物料追踪 =====
.material-tracking {
  .material-flow {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-4);
  }
  
  .material-item {
    background-color: var(--color-card-bg);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-base);
    padding: var(--spacing-4);
    display: flex;
    gap: var(--spacing-3);
    
    .material-icon {
      width: 40px;
      height: 40px;
      border-radius: var(--radius-base);
      background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);
      color: var(--color-primary);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      
      svg {
        width: 20px;
        height: 20px;
        stroke: currentColor;
        fill: none;
        stroke-width: 1.5;
      }
    }
    
    .material-info {
      flex: 1;
      
      h6 {
        font-weight: var(--font-weight-medium);
        color: var(--color-text-primary);
        margin-bottom: var(--spacing-1);
      }
      
      p {
        color: var(--color-text-secondary);
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-2);
      }
      
      .material-details {
        display: flex;
        flex-direction: column;
        gap: 2px;
        font-size: var(--font-size-xs);
        
        .batch,
        .supplier {
          color: var(--color-text-tertiary);
        }
      }
    }
  }
}

// ===== 对话框底部 =====
.dialog-footer {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
}

// ===== 响应式优化 =====
@media (max-width: 768px) {
  .order-header {
    flex-direction: column;
    gap: var(--spacing-3);
  }
  
  .quality-grid {
    grid-template-columns: 1fr;
  }
  
  .material-flow {
    grid-template-columns: 1fr;
  }
  
  .stage-details {
    grid-template-columns: 1fr;
  }
}
</style>