<template>
  <div class="customer-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="top"
      class="customer-form__form"
    >
      <div class="form-grid">
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">基本信息</h3>
          </div>
          
          <div class="section-content">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="客户名称" prop="customerName">
                  <el-input
                    v-model="formData.customerName"
                    placeholder="请输入客户名称"
                    :maxlength="100"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户英文名称" prop="customerNameEn">
                  <el-input
                    v-model="formData.customerNameEn"
                    placeholder="请输入客户英文名称"
                    :maxlength="100"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item label="客户类型" prop="customerType">
                  <el-select v-model="formData.customerType" placeholder="请选择客户类型" class="w-full">
                    <el-option label="IC设计公司" value="IC_DESIGN" />
                    <el-option label="Foundry代工厂" value="FOUNDRY" />
                    <el-option label="系统集成商" value="SYSTEM_INTEGRATOR" />
                    <el-option label="OEM厂商" value="OEM" />
                    <el-option label="贸易商" value="TRADER" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="客户等级" prop="customerLevel">
                  <el-select v-model="formData.customerLevel" placeholder="请选择客户等级" class="w-full">
                    <el-option label="战略客户" value="STRATEGIC" />
                    <el-option label="重要客户" value="IMPORTANT" />
                    <el-option label="普通客户" value="NORMAL" />
                    <el-option label="潜在客户" value="POTENTIAL" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="行业分类" prop="industry">
                  <el-select v-model="formData.industry" placeholder="请选择行业分类" class="w-full">
                    <el-option label="汽车电子" value="AUTOMOTIVE" />
                    <el-option label="消费电子" value="CONSUMER_ELECTRONICS" />
                    <el-option label="工业控制" value="INDUSTRIAL_CONTROL" />
                    <el-option label="通信设备" value="COMMUNICATION" />
                    <el-option label="计算机及周边" value="COMPUTER" />
                    <el-option label="医疗电子" value="MEDICAL" />
                    <el-option label="航空航天" value="AEROSPACE" />
                    <el-option label="军工" value="MILITARY" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="应用领域" prop="applicationField">
                  <el-select
                    v-model="formData.applicationField"
                    multiple
                    placeholder="请选择应用领域"
                    class="w-full"
                  >
                    <el-option label="智能驾驶" value="SMART_DRIVING" />
                    <el-option label="新能源汽车" value="NEW_ENERGY_VEHICLE" />
                    <el-option label="5G通信" value="5G" />
                    <el-option label="物联网" value="IOT" />
                    <el-option label="人工智能" value="AI" />
                    <el-option label="智能家居" value="SMART_HOME" />
                    <el-option label="工业4.0" value="INDUSTRY_4_0" />
                    <el-option label="新能源" value="NEW_ENERGY" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="统一社会信用代码" prop="unifiedSocialCreditCode">
                  <el-input
                    v-model="formData.unifiedSocialCreditCode"
                    placeholder="请输入统一社会信用代码"
                    :maxlength="18"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="注册地址" prop="registeredAddress">
                  <el-input
                    v-model="formData.registeredAddress"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入注册地址"
                    :maxlength="200"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="办公地址" prop="officeAddress">
                  <el-input
                    v-model="formData.officeAddress"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入办公地址"
                    :maxlength="200"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 联系人信息 -->
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">联系人信息</h3>
            <el-button type="primary" :icon="Plus" size="small" @click="addContact">
              添加联系人
            </el-button>
          </div>

          <div class="section-content">
            <div
              v-for="(contact, index) in formData.contacts"
              :key="index"
              class="contact-item"
            >
              <div class="contact-header">
                <span class="contact-title">联系人 {{ index + 1 }}</span>
                <el-button
                  v-if="formData.contacts.length > 1"
                  type="danger"
                  :icon="Delete"
                  size="small"
                  text
                  @click="removeContact(index)"
                >
                  删除
                </el-button>
              </div>

              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item :prop="`contacts.${index}.name`" label="姓名" :rules="contactRules.name">
                    <el-input v-model="contact.name" placeholder="请输入姓名" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :prop="`contacts.${index}.position`" label="职位">
                    <el-input v-model="contact.position" placeholder="请输入职位" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :prop="`contacts.${index}.department`" label="部门">
                    <el-input v-model="contact.department" placeholder="请输入部门" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item :prop="`contacts.${index}.mobile`" label="手机号" :rules="contactRules.mobile">
                    <el-input v-model="contact.mobile" placeholder="请输入手机号" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :prop="`contacts.${index}.email`" label="邮箱" :rules="contactRules.email">
                    <el-input v-model="contact.email" placeholder="请输入邮箱" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :prop="`contacts.${index}.wechat`" label="微信">
                    <el-input v-model="contact.wechat" placeholder="请输入微信号" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item :prop="`contacts.${index}.contactType`" label="联系人类型">
                    <el-select v-model="contact.contactType" multiple placeholder="请选择联系人类型" class="w-full">
                      <el-option label="技术联系人" value="TECHNICAL" />
                      <el-option label="商务联系人" value="BUSINESS" />
                      <el-option label="财务联系人" value="FINANCIAL" />
                      <el-option label="质量联系人" value="QUALITY" />
                      <el-option label="采购联系人" value="PROCUREMENT" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :prop="`contacts.${index}.isPrimary`" label="主要联系人">
                    <el-switch
                      v-model="contact.isPrimary"
                      @change="handlePrimaryContactChange(index)"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>

        <!-- 财务信息 -->
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">财务信息</h3>
          </div>

          <div class="section-content">
            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item label="信用等级" prop="financialInfo.creditLevel">
                  <el-select v-model="formData.financialInfo.creditLevel" placeholder="请选择信用等级" class="w-full">
                    <el-option label="AAA级" value="AAA" />
                    <el-option label="AA级" value="AA" />
                    <el-option label="A级" value="A" />
                    <el-option label="BBB级" value="BBB" />
                    <el-option label="BB级" value="BB" />
                    <el-option label="B级" value="B" />
                    <el-option label="C级" value="C" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="信用额度(万元)" prop="financialInfo.creditLimit">
                  <el-input-number
                    v-model="formData.financialInfo.creditLimit"
                    :min="0"
                    :step="10"
                    class="w-full"
                    placeholder="请输入信用额度"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="付款周期(天)" prop="financialInfo.paymentCycle">
                  <el-input-number
                    v-model="formData.financialInfo.paymentCycle"
                    :min="0"
                    :step="1"
                    class="w-full"
                    placeholder="请输入付款周期"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="付款方式" prop="financialInfo.paymentTerms">
                  <el-input
                    v-model="formData.financialInfo.paymentTerms"
                    placeholder="如：月结30天、现金等"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="货币类型" prop="financialInfo.currency">
                  <el-select v-model="formData.financialInfo.currency" placeholder="请选择货币类型" class="w-full">
                    <el-option label="人民币(CNY)" value="CNY" />
                    <el-option label="美元(USD)" value="USD" />
                    <el-option label="欧元(EUR)" value="EUR" />
                    <el-option label="日元(JPY)" value="JPY" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 开票信息 -->
            <div class="invoice-section">
              <h4 class="subsection-title">开票信息</h4>
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="发票抬头" prop="financialInfo.invoiceInfo.invoiceTitle">
                    <el-input
                      v-model="formData.financialInfo.invoiceInfo.invoiceTitle"
                      placeholder="请输入发票抬头"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="纳税人识别号" prop="financialInfo.invoiceInfo.taxNumber">
                    <el-input
                      v-model="formData.financialInfo.invoiceInfo.taxNumber"
                      placeholder="请输入纳税人识别号"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="开户银行" prop="financialInfo.invoiceInfo.bankName">
                    <el-input
                      v-model="formData.financialInfo.invoiceInfo.bankName"
                      placeholder="请输入开户银行"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="银行账号" prop="financialInfo.invoiceInfo.bankAccount">
                    <el-input
                      v-model="formData.financialInfo.invoiceInfo.bankAccount"
                      placeholder="请输入银行账号"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>

        <!-- 技术信息 -->
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">技术信息</h3>
          </div>

          <div class="section-content">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="主要产品类型" prop="technicalInfo.mainProductTypes">
                  <el-select
                    v-model="formData.technicalInfo.mainProductTypes"
                    multiple
                    placeholder="请选择主要产品类型"
                    class="w-full"
                  >
                    <el-option label="模拟IC" value="ANALOG_IC" />
                    <el-option label="数字IC" value="DIGITAL_IC" />
                    <el-option label="混合信号IC" value="MIXED_SIGNAL_IC" />
                    <el-option label="射频IC" value="RF_IC" />
                    <el-option label="功率IC" value="POWER_IC" />
                    <el-option label="微控制器" value="MICROCONTROLLER" />
                    <el-option label="处理器" value="PROCESSOR" />
                    <el-option label="存储器" value="MEMORY" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="封装类型偏好" prop="technicalInfo.packageTypePreferences">
                  <el-select
                    v-model="formData.technicalInfo.packageTypePreferences"
                    multiple
                    placeholder="请选择封装类型偏好"
                    class="w-full"
                  >
                    <el-option label="QFP" value="QFP" />
                    <el-option label="BGA" value="BGA" />
                    <el-option label="CSP" value="CSP" />
                    <el-option label="FC" value="FC" />
                    <el-option label="QFN" value="QFN" />
                    <el-option label="DFN" value="DFN" />
                    <el-option label="SOP" value="SOP" />
                    <el-option label="TSOP" value="TSOP" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="测试要求" prop="technicalInfo.testRequirements">
                  <el-select
                    v-model="formData.technicalInfo.testRequirements"
                    multiple
                    placeholder="请选择测试要求"
                    class="w-full"
                  >
                    <el-option label="CP测试" value="CP_TEST" />
                    <el-option label="FT测试" value="FT_TEST" />
                    <el-option label="老化测试" value="BURN_IN_TEST" />
                    <el-option label="可靠性测试" value="RELIABILITY_TEST" />
                    <el-option label="环境应力筛选" value="ESS" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="质量标准" prop="technicalInfo.qualityStandards">
                  <el-select
                    v-model="formData.technicalInfo.qualityStandards"
                    multiple
                    placeholder="请选择质量标准"
                    class="w-full"
                  >
                    <el-option label="IATF16949" value="IATF16949" />
                    <el-option label="ISO9001" value="ISO9001" />
                    <el-option label="ISO14001" value="ISO14001" />
                    <el-option label="JEDEC" value="JEDEC" />
                    <el-option label="AEC-Q100" value="AEC_Q100" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">备注信息</h3>
          </div>

          <div class="section-content">
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model="formData.remarks"
                type="textarea"
                :rows="4"
                placeholder="请输入备注信息"
                :maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>
      </div>
    </el-form>

    <!-- 表单操作按钮 -->
    <div class="form-actions">
      <el-button size="large" @click="handleCancel">取消</el-button>
      <el-button type="primary" size="large" :loading="loading" @click="handleSubmit">
        {{ loading ? '保存中...' : '保存' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { CreateCustomerData } from '@/types/customer'
import { CustomerType, CustomerLevel, IndustryType, CreditLevel, CurrencyType } from '@/types/customer'

interface Props {
  /** 初始数据 */
  initialData?: Partial<CreateCustomerData>
  /** 加载状态 */
  loading?: boolean
}

interface Emits {
  'submit': [data: CreateCustomerData]
  'cancel': []
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive<CreateCustomerData>({
  customerName: '',
  customerNameEn: '',
  customerType: CustomerType.IC_DESIGN,
  customerLevel: CustomerLevel.NORMAL,
  industry: IndustryType.CONSUMER_ELECTRONICS,
  applicationField: [],
  unifiedSocialCreditCode: '',
  registeredAddress: '',
  officeAddress: '',
  contacts: [{
    name: '',
    position: '',
    department: '',
    mobile: '',
    email: '',
    wechat: '',
    isPrimary: true,
    contactType: []
  }],
  financialInfo: {
    creditLevel: CreditLevel.A,
    creditLimit: 1000000,
    paymentTerms: '月结30天',
    paymentCycle: 30,
    currency: CurrencyType.CNY,
    taxRate: 0.13,
    invoiceInfo: {
      invoiceTitle: '',
      taxNumber: '',
      bankName: '',
      bankAccount: '',
      registeredAddress: '',
      registeredPhone: ''
    }
  },
  technicalInfo: {
    mainProductTypes: [],
    packageTypePreferences: [],
    testRequirements: [],
    qualityStandards: []
  },
  remarks: ''
})

// 表单验证规则
const formRules: FormRules = {
  customerName: [
    { required: true, message: '请输入客户名称', trigger: 'blur' },
    { min: 2, max: 100, message: '客户名称长度在2-100个字符', trigger: 'blur' }
  ],
  customerType: [
    { required: true, message: '请选择客户类型', trigger: 'change' }
  ],
  customerLevel: [
    { required: true, message: '请选择客户等级', trigger: 'change' }
  ],
  industry: [
    { required: true, message: '请选择行业分类', trigger: 'change' }
  ],
  applicationField: [
    { required: true, message: '请选择应用领域', trigger: 'change' }
  ]
}

// 联系人验证规则
const contactRules = {
  name: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' }
  ],
  mobile: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 添加联系人
const addContact = () => {
  formData.contacts.push({
    name: '',
    position: '',
    department: '',
    mobile: '',
    email: '',
    wechat: '',
    isPrimary: false,
    contactType: []
  })
}

// 删除联系人
const removeContact = (index: number) => {
  formData.contacts.splice(index, 1)
  
  // 如果删除的是主要联系人，将第一个联系人设为主要联系人
  const hasPrimary = formData.contacts.some(contact => contact.isPrimary)
  if (!hasPrimary && formData.contacts.length > 0) {
    formData.contacts[0].isPrimary = true
  }
}

// 处理主要联系人变更
const handlePrimaryContactChange = (index: number) => {
  if (formData.contacts[index].isPrimary) {
    // 将其他联系人设为非主要联系人
    formData.contacts.forEach((contact, i) => {
      if (i !== index) {
        contact.isPrimary = false
      }
    })
  }
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    emit('submit', { ...formData })
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单信息')
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}

// 监听初始数据变化
watch(
  () => props.initialData,
  (newData) => {
    if (newData) {
      Object.assign(formData, {
        ...newData,
        contacts: newData.contacts?.length ? newData.contacts : formData.contacts,
        financialInfo: { ...formData.financialInfo, ...newData.financialInfo },
        technicalInfo: { ...formData.technicalInfo, ...newData.technicalInfo }
      })
    }
  },
  { immediate: true, deep: true }
)

// 暴露验证方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields()
})
</script>

<style lang="scss" scoped>
.customer-form {
  max-width: 100%;

  &__form {
    .form-grid {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-6);
    }
  }
}

.form-section {
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-base);
  background-color: var(--color-bg-primary);

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--color-border-lighter);
    background-color: var(--color-bg-secondary);
    border-radius: var(--radius-base) var(--radius-base) 0 0;
  }

  .section-title {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
  }

  .section-content {
    padding: var(--spacing-6);
  }
}

.contact-item {
  border: 1px solid var(--color-border-lighter);
  border-radius: var(--radius-base);
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-4);

  &:last-child {
    margin-bottom: 0;
  }

  .contact-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-3);

    .contact-title {
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }
  }
}

.invoice-section {
  margin-top: var(--spacing-4);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--color-border-lighter);

  .subsection-title {
    margin: 0 0 var(--spacing-4) 0;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
  }
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4);
  padding: var(--spacing-6) 0;
  margin-top: var(--spacing-6);
  border-top: 1px solid var(--color-border-light);
}

// 响应式设计
@media (max-width: 768px) {
  .form-section {
    .section-header {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-2);
    }

    .section-content {
      padding: var(--spacing-4);
    }
  }

  .contact-item {
    padding: var(--spacing-3);

    .contact-header {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-2);
    }
  }

  .form-actions {
    flex-direction: column;
  }
}

// 全局样式覆盖
:deep(.el-form-item__label) {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.w-full) {
  width: 100%;
}
</style>