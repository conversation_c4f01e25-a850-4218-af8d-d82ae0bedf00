// 极简样式重置
// 基于现代 CSS Reset 理念，保持最小化和实用性

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: var(--line-height-base);
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  scroll-behavior: smooth;
  height: 100%;
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-base);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  transition: background-color var(--transition-normal), 
              color var(--transition-normal);
  min-height: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 应用根节点
#app {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
}

// 移除默认样式
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
  background: transparent;
}

// 按钮重置
button,
[role="button"] {
  cursor: pointer;
  border: none;
  background: transparent;
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

// 焦点样式
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
[tabindex]:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

// 链接样式
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
  
  &:hover {
    color: var(--color-primary-hover);
  }
  
  &:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

// 标题样式
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  margin: 0;
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

// 段落和文本
p {
  margin: 0;
  line-height: var(--line-height-base);
}

// 列表
ul, ol {
  list-style: none;
  margin: 0;
  padding: 0;
}

// 图片
img {
  max-width: 100%;
  height: auto;
  display: block;
}

// 表格
table {
  border-collapse: collapse;
  width: 100%;
}

// 表单元素
input, textarea, select {
  width: 100%;
  border: 1px solid var(--color-border-base);
  border-radius: var(--radius-base);
  padding: var(--spacing-2) var(--spacing-3);
  background: var(--color-bg-primary);
  color: var(--color-text-primary);
  transition: border-color var(--transition-fast), 
              box-shadow var(--transition-fast);
  
  &:hover {
    border-color: var(--color-border-dark);
  }
  
  &:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
  
  &::placeholder {
    color: var(--color-text-placeholder);
  }
  
  &:disabled {
    background: var(--color-bg-disabled);
    color: var(--color-text-disabled);
    cursor: not-allowed;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-dark);
  border-radius: 3px;
  
  &:hover {
    background: var(--color-text-tertiary);
  }
}

::-webkit-scrollbar-corner {
  background: var(--color-bg-secondary);
}

// Firefox 滚动条
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border-dark) var(--color-bg-secondary);
}

// 选择文本样式
::selection {
  background: rgba(37, 99, 235, 0.2);
  color: var(--color-text-primary);
}

::-moz-selection {
  background: rgba(37, 99, 235, 0.2);
  color: var(--color-text-primary);
}

// 隐藏元素
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}