/**
 * IC封测CIM系统 - 客户管理类型定义
 * Customer Management Type Definitions
 */

export interface Customer {
  /** 客户ID */
  id: string
  /** 客户编号 */
  customerCode: string
  /** 客户名称 */
  customerName: string
  /** 客户英文名称 */
  customerNameEn?: string
  /** 客户类型 */
  customerType: CustomerType
  /** 客户等级 */
  customerLevel: CustomerLevel
  /** 行业分类 */
  industry: IndustryType
  /** 应用领域 */
  applicationField: ApplicationField[]
  /** 统一社会信用代码 */
  unifiedSocialCreditCode?: string
  /** 注册地址 */
  registeredAddress?: string
  /** 办公地址 */
  officeAddress?: string
  /** 联系人信息 */
  contacts: CustomerContact[]
  /** 财务信息 */
  financialInfo: CustomerFinancialInfo
  /** 技术信息 */
  technicalInfo: CustomerTechnicalInfo
  /** 合作历史 */
  cooperationHistory: CooperationHistory
  /** 客户状态 */
  status: CustomerStatus
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
  /** 创建人 */
  createdBy: string
  /** 备注 */
  remarks?: string
}

export interface CustomerContact {
  /** 联系人ID */
  id: string
  /** 姓名 */
  name: string
  /** 职位 */
  position: string
  /** 部门 */
  department: string
  /** 手机号 */
  mobile: string
  /** 邮箱 */
  email: string
  /** 微信 */
  wechat?: string
  /** 是否主要联系人 */
  isPrimary: boolean
  /** 联系人类型 */
  contactType: ContactType[]
  /** 沟通记录 */
  communicationRecords: CommunicationRecord[]
}

export interface CommunicationRecord {
  /** 记录ID */
  id: string
  /** 沟通时间 */
  communicationTime: string
  /** 沟通方式 */
  communicationMethod: CommunicationMethod
  /** 沟通内容 */
  content: string
  /** 沟通结果 */
  result: string
  /** 后续跟进 */
  followUp?: string
  /** 记录人 */
  recordedBy: string
  /** 附件 */
  attachments?: string[]
}

export interface CustomerFinancialInfo {
  /** 信用等级 */
  creditLevel: CreditLevel
  /** 信用额度 */
  creditLimit: number
  /** 付款方式 */
  paymentTerms: string
  /** 付款周期（天） */
  paymentCycle: number
  /** 货币类型 */
  currency: CurrencyType
  /** 税率 */
  taxRate: number
  /** 开票信息 */
  invoiceInfo: InvoiceInfo
}

export interface InvoiceInfo {
  /** 发票抬头 */
  invoiceTitle: string
  /** 纳税人识别号 */
  taxNumber: string
  /** 开户银行 */
  bankName: string
  /** 银行账号 */
  bankAccount: string
  /** 注册地址 */
  registeredAddress: string
  /** 注册电话 */
  registeredPhone: string
}

export interface CustomerTechnicalInfo {
  /** 主要产品类型 */
  mainProductTypes: ProductType[]
  /** 封装类型偏好 */
  packageTypePreferences: PackageType[]
  /** 测试要求 */
  testRequirements: TestRequirement[]
  /** 质量标准 */
  qualityStandards: QualityStandard[]
  /** 特殊工艺要求 */
  specialProcessRequirements?: string[]
}

export interface CooperationHistory {
  /** 首次合作时间 */
  firstCooperationTime: string
  /** 合作年限 */
  cooperationYears: number
  /** 历史订单总数 */
  totalOrders: number
  /** 历史销售总额 */
  totalSalesAmount: number
  /** 近12个月订单数 */
  ordersLast12Months: number
  /** 近12个月销售额 */
  salesLast12Months: number
  /** 平均订单金额 */
  averageOrderAmount: number
  /** 客户满意度评分 */
  satisfactionScore: number
  /** 主要合作产品 */
  mainCooperationProducts: string[]
}

// 枚举类型定义
export enum CustomerType {
  /** IC设计公司 */
  IC_DESIGN = 'IC_DESIGN',
  /** Foundry代工厂 */
  FOUNDRY = 'FOUNDRY',
  /** 系统集成商 */
  SYSTEM_INTEGRATOR = 'SYSTEM_INTEGRATOR',
  /** OEM厂商 */
  OEM = 'OEM',
  /** 贸易商 */
  TRADER = 'TRADER'
}

export enum CustomerLevel {
  /** 战略客户 */
  STRATEGIC = 'STRATEGIC',
  /** 重要客户 */
  IMPORTANT = 'IMPORTANT',
  /** 普通客户 */
  NORMAL = 'NORMAL',
  /** 潜在客户 */
  POTENTIAL = 'POTENTIAL'
}

export enum IndustryType {
  /** 汽车电子 */
  AUTOMOTIVE = 'AUTOMOTIVE',
  /** 消费电子 */
  CONSUMER_ELECTRONICS = 'CONSUMER_ELECTRONICS',
  /** 工业控制 */
  INDUSTRIAL_CONTROL = 'INDUSTRIAL_CONTROL',
  /** 通信设备 */
  COMMUNICATION = 'COMMUNICATION',
  /** 计算机及周边 */
  COMPUTER = 'COMPUTER',
  /** 医疗电子 */
  MEDICAL = 'MEDICAL',
  /** 航空航天 */
  AEROSPACE = 'AEROSPACE',
  /** 军工 */
  MILITARY = 'MILITARY'
}

export enum ApplicationField {
  /** 智能驾驶 */
  SMART_DRIVING = 'SMART_DRIVING',
  /** 新能源汽车 */
  NEW_ENERGY_VEHICLE = 'NEW_ENERGY_VEHICLE',
  /** 5G通信 */
  COMMUNICATION_5G = '5G',
  /** 物联网 */
  IOT = 'IOT',
  /** 人工智能 */
  AI = 'AI',
  /** 智能家居 */
  SMART_HOME = 'SMART_HOME',
  /** 工业4.0 */
  INDUSTRY_4_0 = 'INDUSTRY_4_0',
  /** 新能源 */
  NEW_ENERGY = 'NEW_ENERGY'
}

export enum ContactType {
  /** 技术联系人 */
  TECHNICAL = 'TECHNICAL',
  /** 商务联系人 */
  BUSINESS = 'BUSINESS',
  /** 财务联系人 */
  FINANCIAL = 'FINANCIAL',
  /** 质量联系人 */
  QUALITY = 'QUALITY',
  /** 采购联系人 */
  PROCUREMENT = 'PROCUREMENT'
}

export enum CommunicationMethod {
  /** 电话 */
  PHONE = 'PHONE',
  /** 邮件 */
  EMAIL = 'EMAIL',
  /** 微信 */
  WECHAT = 'WECHAT',
  /** 视频会议 */
  VIDEO_CONFERENCE = 'VIDEO_CONFERENCE',
  /** 面对面 */
  FACE_TO_FACE = 'FACE_TO_FACE',
  /** QQ */
  QQ = 'QQ'
}

export enum CreditLevel {
  /** AAA级 */
  AAA = 'AAA',
  /** AA级 */
  AA = 'AA',
  /** A级 */
  A = 'A',
  /** BBB级 */
  BBB = 'BBB',
  /** BB级 */
  BB = 'BB',
  /** B级 */
  B = 'B',
  /** C级 */
  C = 'C'
}

export enum CurrencyType {
  /** 人民币 */
  CNY = 'CNY',
  /** 美元 */
  USD = 'USD',
  /** 欧元 */
  EUR = 'EUR',
  /** 日元 */
  JPY = 'JPY'
}

export enum ProductType {
  /** 模拟IC */
  ANALOG_IC = 'ANALOG_IC',
  /** 数字IC */
  DIGITAL_IC = 'DIGITAL_IC',
  /** 混合信号IC */
  MIXED_SIGNAL_IC = 'MIXED_SIGNAL_IC',
  /** 射频IC */
  RF_IC = 'RF_IC',
  /** 功率IC */
  POWER_IC = 'POWER_IC',
  /** 微控制器 */
  MICROCONTROLLER = 'MICROCONTROLLER',
  /** 处理器 */
  PROCESSOR = 'PROCESSOR',
  /** 存储器 */
  MEMORY = 'MEMORY'
}

export enum PackageType {
  /** QFP */
  QFP = 'QFP',
  /** BGA */
  BGA = 'BGA',
  /** CSP */
  CSP = 'CSP',
  /** FC */
  FC = 'FC',
  /** QFN */
  QFN = 'QFN',
  /** DFN */
  DFN = 'DFN',
  /** SOP */
  SOP = 'SOP',
  /** TSOP */
  TSOP = 'TSOP'
}

export enum TestRequirement {
  /** CP测试 */
  CP_TEST = 'CP_TEST',
  /** FT测试 */
  FT_TEST = 'FT_TEST',
  /** 老化测试 */
  BURN_IN_TEST = 'BURN_IN_TEST',
  /** 可靠性测试 */
  RELIABILITY_TEST = 'RELIABILITY_TEST',
  /** 环境应力筛选 */
  ESS = 'ESS'
}

export enum QualityStandard {
  /** IATF16949 */
  IATF16949 = 'IATF16949',
  /** ISO9001 */
  ISO9001 = 'ISO9001',
  /** ISO14001 */
  ISO14001 = 'ISO14001',
  /** JEDEC */
  JEDEC = 'JEDEC',
  /** AEC-Q100 */
  AEC_Q100 = 'AEC_Q100'
}

export enum CustomerStatus {
  /** 活跃 */
  ACTIVE = 'ACTIVE',
  /** 暂停合作 */
  SUSPENDED = 'SUSPENDED',
  /** 潜在客户 */
  POTENTIAL = 'POTENTIAL',
  /** 已停止合作 */
  INACTIVE = 'INACTIVE'
}

// 查询和响应接口
export interface CustomerQueryParams {
  /** 分页页码 */
  page?: number
  /** 分页大小 */
  pageSize?: number
  /** 搜索关键词 */
  keyword?: string
  /** 客户类型 */
  customerType?: CustomerType
  /** 客户等级 */
  customerLevel?: CustomerLevel
  /** 行业分类 */
  industry?: IndustryType
  /** 客户状态 */
  status?: CustomerStatus
  /** 创建时间范围 */
  createdTimeRange?: [string, string]
  /** 排序字段 */
  sortBy?: string
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc'
}

export interface CustomerListResponse {
  /** 客户列表 */
  data: Customer[]
  /** 总数 */
  total: number
  /** 当前页 */
  page: number
  /** 页大小 */
  pageSize: number
  /** 总页数 */
  totalPages: number
}

export interface CreateCustomerData {
  /** 客户名称 */
  customerName: string
  /** 客户英文名称 */
  customerNameEn?: string
  /** 客户类型 */
  customerType: CustomerType
  /** 客户等级 */
  customerLevel: CustomerLevel
  /** 行业分类 */
  industry: IndustryType
  /** 应用领域 */
  applicationField: ApplicationField[]
  /** 统一社会信用代码 */
  unifiedSocialCreditCode?: string
  /** 注册地址 */
  registeredAddress?: string
  /** 办公地址 */
  officeAddress?: string
  /** 联系人信息 */
  contacts: Omit<CustomerContact, 'id' | 'communicationRecords'>[]
  /** 财务信息 */
  financialInfo: CustomerFinancialInfo
  /** 技术信息 */
  technicalInfo: CustomerTechnicalInfo
  /** 备注 */
  remarks?: string
}