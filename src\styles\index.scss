// IC封测CIM系统 - 样式统一入口
// 严格按照极简主义设计系统构建

// 1. 基础样式导入
@import './base/reset.scss';
@import './base/typography.scss';
@import './base/layout.scss';
@import './base/utilities.scss';

// 2. 主题系统导入
@import './themes/variables.scss';
@import './themes/light.scss';
@import './themes/dark.scss';

// 3. 组件样式导入
@import './components/button.scss';
@import './components/form.scss';
@import './components/table.scss';
@import './components/card.scss';
@import './components/modal.scss';
@import './components/layout.scss';

// 4. 页面样式导入
@import './pages/dashboard.scss';
@import './pages/orders.scss';
@import './pages/production.scss';

// 5. IC封测专业样式
@import './professional/wafer-map.scss';
@import './professional/equipment.scss';
@import './professional/quality.scss';