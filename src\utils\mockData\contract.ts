/**
 * 合同管理模拟数据
 * Contract Management Mock Data
 */

import {
  Contract,
  ContractStatus,
  ContractType,
  SignatureStatus,
  ChangeType,
  ContractTemplate,
  ContractClause,
  ContractStatistics
} from '@/types/contract'

// 模拟合同数据
export const mockContracts: Contract[] = [
  {
    id: 'CT001',
    contractNumber: 'CT-2024-001',
    title: 'APPLE A17 Pro 封装测试服务合同',
    type: ContractType.MASS_PRODUCTION,
    status: ContractStatus.EXECUTING,
    customerId: 'C001',
    customerName: '苹果公司',
    quotationId: 'Q001',
    orderId: 'O001',
    signedDate: new Date('2024-01-15'),
    effectiveDate: new Date('2024-02-01'),
    expiryDate: new Date('2024-12-31'),
    totalValue: 25000000,
    currency: 'CNY',
    technicalSpec: {
      packageType: 'FC-BGA',
      testProgram: 'A17_PRO_TP_V2.1',
      qualityStandard: 'IATF16949:2016',
      reliabilitySpec: 'JEDEC-22A',
      yieldRequirement: 99.5,
      electricalSpec: {
        vdd: '1.1V ± 5%',
        frequency: '3.78GHz',
        power: '< 15W',
        temperature: '-40℃ ~ +125℃'
      }
    },
    commercialTerms: {
      priceModel: 'tiered',
      paymentTerms: '30天账期',
      deliveryTerms: '8周交期',
      penaltyClause: '延迟交付按合同金0.5%/天赔偿',
      priceValidityPeriod: 90,
      minimumOrderQuantity: 10000
    },
    qualityTerms: {
      iatfCompliance: true,
      customerQualityReq: 'Apple QMS V3.2',
      returnPolicy: '7天无理由退货',
      qualityEscalation: '24小时响应',
      ppapRequirement: true,
      statisticalRequirement: 'Cpk ≥ 1.67'
    },
    customClauses: [
      {
        id: 'CC001',
        type: 'technical',
        title: '特殊工艺要求',
        content: '采用专利铜柱技术，确保信号完整性',
        isRequired: true,
        category: 'process',
        riskLevel: 'medium'
      }
    ],
    signatures: [
      {
        id: 'S001',
        signerName: '李总',
        signerRole: 'CEO',
        signerCompany: '苹果公司',
        signedAt: new Date('2024-01-15T10:30:00'),
        signatureMethod: 'electronic',
        ipAddress: '*************',
        location: '上海'
      },
      {
        id: 'S002',
        signerName: '王总',
        signerRole: 'CEO',
        signerCompany: '本公司',
        signedAt: new Date('2024-01-15T14:20:00'),
        signatureMethod: 'electronic',
        ipAddress: '*********',
        location: '深圳'
      }
    ],
    signatureStatus: SignatureStatus.COMPLETED,
    changes: [
      {
        id: 'CH001',
        changeType: ChangeType.PRICE_ADJUSTMENT,
        changeReason: '原材料涨价',
        changeDescription: '封装料价格上调5%',
        oldValue: 2.5,
        newValue: 2.625,
        requestedBy: '采购部',
        requestedAt: new Date('2024-03-01'),
        approvedBy: '李总',
        approvedAt: new Date('2024-03-02'),
        status: 'approved',
        impact: {
          cost: 312500,
          schedule: 0,
          risk: 'low'
        }
      }
    ],
    executionMonitoring: {
      contractId: 'CT001',
      deliveryProgress: {
        planned: 60,
        actual: 58,
        onTimeRate: 96.7
      },
      paymentProgress: {
        totalAmount: 25000000,
        paidAmount: 15000000,
        pendingAmount: 8000000,
        overdueAmount: 2000000
      },
      qualityMetrics: {
        yieldRate: 99.2,
        defectRate: 0.8,
        customerComplaints: 2,
        returnRate: 0.3
      },
      riskIndicators: {
        deliveryRisk: 'medium',
        qualityRisk: 'low',
        paymentRisk: 'high'
      }
    },
    documentVersion: '1.2',
    documentUrl: '/contracts/CT-2024-001.pdf',
    templateId: 'TEMP001',
    reviewComments: [
      {
        reviewer: '张律师',
        role: 'legal',
        comment: '合同条款符合法律规定',
        status: 'approved',
        reviewedAt: new Date('2024-01-10')
      }
    ],
    createdBy: '合同管理员',
    createdAt: new Date('2024-01-01'),
    updatedBy: '合同管理员',
    updatedAt: new Date('2024-03-02'),
    attachments: [
      {
        id: 'ATT001',
        name: '技术规格书.pdf',
        type: 'pdf',
        size: 2048000,
        url: '/attachments/tech-spec.pdf',
        uploadedAt: new Date('2024-01-01')
      }
    ]
  },
  {
    id: 'CT002',
    contractNumber: 'CT-2024-002',
    title: '高通 SM8650 NPI开发合同',
    type: ContractType.NPI_DEVELOPMENT,
    status: ContractStatus.PENDING_SIGN,
    customerId: 'C002',
    customerName: '高通公司',
    quotationId: 'Q002',
    effectiveDate: new Date('2024-04-01'),
    expiryDate: new Date('2024-12-31'),
    totalValue: 8000000,
    currency: 'USD',
    technicalSpec: {
      packageType: 'FC-CSP',
      testProgram: 'SM8650_NPI_TP_V1.0',
      qualityStandard: 'IATF16949:2016',
      reliabilitySpec: 'JEDEC-22B',
      yieldRequirement: 95.0,
      electricalSpec: {
        vdd: '0.9V ± 3%',
        frequency: '3.2GHz',
        power: '< 12W',
        temperature: '-40℃ ~ +105℃'
      }
    },
    commercialTerms: {
      priceModel: 'cost_plus',
      paymentTerms: '15天账期',
      deliveryTerms: '12周交期',
      penaltyClause: 'NPI项目延期按周计算罚款',
      priceValidityPeriod: 60,
      minimumOrderQuantity: 5000
    },
    qualityTerms: {
      iatfCompliance: true,
      customerQualityReq: 'Qualcomm QMS V2.1',
      returnPolicy: '不可退货，重工解决',
      qualityEscalation: '12小时响应',
      ppapRequirement: false,
      statisticalRequirement: 'Cpk ≥ 1.33'
    },
    customClauses: [],
    signatures: [
      {
        id: 'S003',
        signerName: '王总',
        signerRole: 'CEO',
        signerCompany: '本公司',
        signatureMethod: 'electronic'
      }
    ],
    signatureStatus: SignatureStatus.PARTIAL,
    changes: [],
    documentVersion: '1.0',
    templateId: 'TEMP002',
    reviewComments: [],
    createdBy: '商务部',
    createdAt: new Date('2024-03-15'),
    updatedBy: '商务部',
    updatedAt: new Date('2024-03-20'),
    attachments: []
  },
  {
    id: 'CT003',
    contractNumber: 'CT-2024-003',
    title: '联发科MT6897框架合同',
    type: ContractType.FRAMEWORK,
    status: ContractStatus.DRAFT,
    customerId: 'C003',
    customerName: '联发科技',
    effectiveDate: new Date('2024-05-01'),
    expiryDate: new Date('2025-04-30'),
    totalValue: 50000000,
    currency: 'CNY',
    technicalSpec: {
      packageType: 'QFN',
      testProgram: 'MT6897_TP_V1.5',
      qualityStandard: 'ISO9001:2015',
      reliabilitySpec: 'JEDEC-22A',
      yieldRequirement: 98.0,
      electricalSpec: {
        vdd: '1.2V ± 5%',
        frequency: '2.8GHz',
        power: '< 8W',
        temperature: '-20℃ ~ +85℃'
      }
    },
    commercialTerms: {
      priceModel: 'fixed',
      paymentTerms: '45天账期',
      deliveryTerms: '6周交期',
      penaltyClause: '按月度评估罚款',
      priceValidityPeriod: 180,
      minimumOrderQuantity: 20000
    },
    qualityTerms: {
      iatfCompliance: false,
      customerQualityReq: 'MTK QMS V1.8',
      returnPolicy: '14天退货期',
      qualityEscalation: '48小时响应',
      ppapRequirement: false,
      statisticalRequirement: 'Cpk ≥ 1.33'
    },
    customClauses: [],
    signatures: [],
    signatureStatus: SignatureStatus.UNSIGNED,
    changes: [],
    documentVersion: '0.1',
    templateId: 'TEMP003',
    reviewComments: [],
    createdBy: '商务部',
    createdAt: new Date('2024-03-25'),
    updatedBy: '商务部',
    updatedAt: new Date('2024-03-25'),
    attachments: []
  }
]

// 模拟合同模板
export const mockContractTemplates: ContractTemplate[] = [
  {
    id: 'TEMP001',
    name: '量产封装测试标准合同',
    type: ContractType.MASS_PRODUCTION,
    version: '2.1',
    description: '适用于大批量量产封装测试服务',
    clauses: [
      {
        id: 'CL001',
        type: 'technical',
        title: '技术规格要求',
        content: '乙方应按照甲方提供的技术规格书进行封装测试...',
        isRequired: true,
        category: 'specification'
      },
      {
        id: 'CL002',
        type: 'quality',
        title: '质量保证',
        content: '乙方应保证产品质量符合IATF16949标准...',
        isRequired: true,
        category: 'quality',
        riskLevel: 'high'
      }
    ],
    defaultTerms: {
      commercial: {
        paymentTerms: '30天账期',
        deliveryTerms: '8周交期',
        priceValidityPeriod: 90
      },
      quality: {
        iatfCompliance: true,
        ppapRequirement: true
      }
    },
    isActive: true,
    createdBy: '法务部',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2024-02-01')
  },
  {
    id: 'TEMP002',
    name: 'NPI开发项目合同',
    type: ContractType.NPI_DEVELOPMENT,
    version: '1.3',
    description: '适用于新产品引入开发项目',
    clauses: [
      {
        id: 'CL003',
        type: 'commercial',
        title: '项目成本结算',
        content: '采用成本加成模式，成本透明...',
        isRequired: true,
        category: 'pricing'
      }
    ],
    defaultTerms: {
      commercial: {
        priceModel: 'cost_plus',
        paymentTerms: '15天账期'
      },
      quality: {
        iatfCompliance: true,
        ppapRequirement: false
      }
    },
    isActive: true,
    createdBy: '法务部',
    createdAt: new Date('2023-06-01'),
    updatedAt: new Date('2024-01-15')
  }
]

// 模拟统计数据
export const mockContractStatistics: ContractStatistics = {
  totalContracts: 25,
  statusDistribution: {
    [ContractStatus.DRAFT]: 3,
    [ContractStatus.PENDING_REVIEW]: 2,
    [ContractStatus.PENDING_SIGN]: 4,
    [ContractStatus.EXECUTING]: 12,
    [ContractStatus.COMPLETED]: 3,
    [ContractStatus.TERMINATED]: 1,
    [ContractStatus.EXPIRED]: 0
  },
  typeDistribution: {
    [ContractType.MASS_PRODUCTION]: 15,
    [ContractType.NPI_DEVELOPMENT]: 5,
    [ContractType.FRAMEWORK]: 3,
    [ContractType.URGENT_ORDER]: 1,
    [ContractType.CUSTOM_SERVICE]: 1
  },
  totalValue: 180000000,
  averageValue: 7200000,
  expiringContracts: 3,
  overduePayments: 2,
  riskContracts: 4
}

// 模拟合同条款库
export const mockContractClauses: ContractClause[] = [
  {
    id: 'CL001',
    type: 'technical',
    title: '封装工艺要求',
    content: '采用先进的翅片级封装技术，精度达到±5微米',
    isRequired: true,
    category: 'process',
    riskLevel: 'medium'
  },
  {
    id: 'CL002',
    type: 'quality',
    title: 'IATF16949符合性',
    content: '严格按照IATF16949:2016标准执行质量管理体系',
    isRequired: true,
    category: 'compliance',
    riskLevel: 'high'
  },
  {
    id: 'CL003',
    type: 'commercial',
    title: '价格调整机制',
    content: '原材料价格波动超过5%时，双方协商调整价格',
    isRequired: false,
    category: 'pricing',
    riskLevel: 'medium'
  },
  {
    id: 'CL004',
    type: 'legal',
    title: '知识产权保护',
    content: '双方应保护对方的商业秘密和技术机密',
    isRequired: true,
    category: 'ip',
    riskLevel: 'high'
  },
  {
    id: 'CL005',
    type: 'risk',
    title: '不可抗力条款',
    content: '因自然灾害、疑情等不可抗力因素导致的延迟，双方不承担赔偿责任',
    isRequired: true,
    category: 'force_majeure',
    riskLevel: 'low'
  }
]
