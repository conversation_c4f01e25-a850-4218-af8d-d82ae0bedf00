<template>
  <div class="equipment-status-page">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">设备状态监控</h1>
        <p class="page-description">实时监控所有生产设备的运行状态、OEE指标和告警信息</p>
      </div>
      <div class="header-actions">
        <el-button-group>
          <el-button
            :type="viewMode === 'dashboard' ? 'primary' : 'default'"
            @click="viewMode = 'dashboard'"
          >
            <el-icon><Monitor /></el-icon>
            总览
          </el-button>
          <el-button
:type="viewMode === 'grid' ? 'primary' : 'default'" @click="viewMode = 'grid'"
>
            <el-icon><Grid /></el-icon>
            网格
          </el-button>
          <el-button
:type="viewMode === 'list' ? 'primary' : 'default'" @click="viewMode = 'list'"
>
            <el-icon><List /></el-icon>
            列表
          </el-button>
        </el-button-group>

        <el-switch
v-model="autoRefresh" active-text="自动刷新"
@change="toggleAutoRefresh"
/>

        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <el-row :gutter="24">
        <el-col :span="6">
          <el-card class="stat-card running">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><VideoPlay /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">
                  {{ equipmentCount.running }}
                </div>
                <div class="stat-label">运行中</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card idle">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><VideoPause /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">
                  {{ equipmentCount.idle }}
                </div>
                <div class="stat-label">空闲</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card down">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">
                  {{ equipmentCount.down }}
                </div>
                <div class="stat-label">故障</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card maintenance">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Tools /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">
                  {{ equipmentCount.pm }}
                </div>
                <div class="stat-label">保养中</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 总体OEE指标 -->
    <div
v-if="statistics" class="oee-overview"
>
      <el-card>
        <template #header>
          <div class="card-header">
            <span class="card-title">总体OEE指标</span>
            <el-tag type="info">实时数据</el-tag>
          </div>
        </template>

        <el-row :gutter="24">
          <el-col :span="6">
            <div class="oee-metric">
              <div class="metric-chart">
                <el-progress
                  type="circle"
                  :percentage="Math.round(statistics.overallOEE)"
                  :color="getOEEColor(statistics.overallOEE)"
                  :width="100"
                >
                  <span class="percentage-text">{{ statistics.overallOEE.toFixed(1) }}%</span>
                </el-progress>
              </div>
              <div class="metric-label">总体OEE</div>
            </div>
          </el-col>

          <el-col :span="6">
            <div class="oee-metric">
              <div class="metric-chart">
                <el-progress
                  type="circle"
                  :percentage="Math.round(statistics.avgAvailability)"
                  color="#67C23A"
                  :width="80"
                />
              </div>
              <div class="metric-label">平均可用率</div>
              <div class="metric-value">{{ statistics.avgAvailability.toFixed(1) }}%</div>
            </div>
          </el-col>

          <el-col :span="6">
            <div class="oee-metric">
              <div class="metric-chart">
                <el-progress
                  type="circle"
                  :percentage="Math.round(statistics.avgPerformance)"
                  color="#409EFF"
                  :width="80"
                />
              </div>
              <div class="metric-label">平均性能率</div>
              <div class="metric-value">{{ statistics.avgPerformance.toFixed(1) }}%</div>
            </div>
          </el-col>

          <el-col :span="6">
            <div class="oee-metric">
              <div class="metric-chart">
                <el-progress
                  type="circle"
                  :percentage="Math.round(statistics.avgQuality)"
                  color="#E6A23C"
                  :width="80"
                />
              </div>
              <div class="metric-label">平均质量率</div>
              <div class="metric-value">{{ statistics.avgQuality.toFixed(1) }}%</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 告警概览 -->
    <div
v-if="activeAlarms.length > 0" class="alarms-overview"
>
      <el-card>
        <template #header>
          <div class="card-header">
            <span class="card-title">
              <el-icon><Warning /></el-icon>
              活动告警 ({{ activeAlarms.length }})
            </span>
            <el-button type="text" @click="showAllAlarms = !showAllAlarms">
              {{ showAllAlarms ? '收起' : '查看全部' }}
            </el-button>
          </div>
        </template>

        <div class="alarm-summary">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="alarm-stat critical">
                <el-icon><Warning /></el-icon>
                <span>严重: {{ criticalAlarms.length }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="alarm-stat major">
                <el-icon><InfoFilled /></el-icon>
                <span>重要: {{ majorAlarms.length }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="alarm-stat">
                <el-icon><Check /></el-icon>
                <span>已确认: {{ acknowledgedAlarms.length }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="alarm-stat">
                <el-icon><Clock /></el-icon>
                <span>未确认: {{ unacknowledgedAlarms.length }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <AlarmList
          v-if="showAllAlarms"
          :alarms="activeAlarms.slice(0, 10)"
          :equipment-list="equipment"
          :show-filters="false"
          :show-stats="false"
          :show-pagination="false"
          @acknowledge="handleAcknowledgeAlarm"
          @clear="handleClearAlarm"
        />
      </el-card>
    </div>

    <!-- 设备状态展示 -->
    <div class="equipment-display">
      <!-- 筛选器 -->
      <div class="filters">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-input
v-model="searchKeyword" placeholder="搜索设备名称或编码"
clearable
>
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="statusFilter" placeholder="状态筛选" clearable>
              <el-option label="运行" value="RUN" />
              <el-option label="空闲" value="IDLE" />
              <el-option label="故障" value="DOWN" />
              <el-option label="保养" value="PM" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="typeFilter" placeholder="类型筛选" clearable>
              <el-option label="测试设备" value="ATE" />
              <el-option label="键合机" value="BONDER" />
              <el-option label="贴片机" value="DIE_ATTACH" />
              <el-option label="分选机" value="HANDLER" />
              <el-option label="塑封机" value="MOLDING" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="locationFilter" placeholder="位置筛选" clearable>
              <el-option label="测试区域A" value="测试区域A" />
              <el-option label="测试区域B" value="测试区域B" />
              <el-option label="封装区域A" value="封装区域A" />
              <el-option label="封装区域B" value="封装区域B" />
              <el-option label="封装区域C" value="封装区域C" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button-group>
              <el-button
:type="sortBy === 'name' ? 'primary' : 'default'" @click="sortBy = 'name'"
>
                按名称
              </el-button>
              <el-button
                :type="sortBy === 'status' ? 'primary' : 'default'"
                @click="sortBy = 'status'"
              >
                按状态
              </el-button>
              <el-button
:type="sortBy === 'oee' ? 'primary' : 'default'" @click="sortBy = 'oee'"
>
                按OEE
              </el-button>
            </el-button-group>
          </el-col>
        </el-row>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="equipment-grid">
        <el-row :gutter="24">
          <el-col
            v-for="eq in filteredEquipment"
            :key="eq.id"
            :span="8"
            style="margin-bottom: 24px"
          >
            <EquipmentStatusCard
              :equipment="eq"
              :status-detail="getEquipmentStatusById(eq.id)"
              :alarms="getActiveAlarmsByEquipmentId(eq.id)"
              @start="handleStartEquipment(eq.id)"
              @stop="handleStopEquipment(eq.id)"
              @detail="handleViewEquipmentDetail(eq)"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 列表视图 -->
      <div v-if="viewMode === 'list'" class="equipment-table">
        <el-table :data="filteredEquipment" stripe>
          <el-table-column prop="name" label="设备名称" width="200" />
          <el-table-column prop="code" label="设备编码" width="120" />
          <el-table-column prop="type" label="类型" width="100">
            <template #default="{ row }">
              {{ getEquipmentTypeText(row.type) }}
            </template>
          </el-table-column>
          <el-table-column prop="location" label="位置" width="120" />
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="连接" width="80">
            <template #default="{ row }">
              <el-tag :type="row.isConnected ? 'success' : 'danger'" size="small">
                {{ row.isConnected ? '已连接' : '未连接' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="OEE" width="100">
            <template #default="{ row }">
              <span v-if="getEquipmentStatusById(row.id)?.oeeData">
                {{ getEquipmentStatusById(row.id)!.oeeData.oee.toFixed(1) }}%
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="告警" width="80">
            <template #default="{ row }">
              <el-badge
                :value="getActiveAlarmsByEquipmentId(row.id).length"
                :hidden="getActiveAlarmsByEquipmentId(row.id).length === 0"
                type="danger"
              >
                <el-icon><Bell /></el-icon>
              </el-badge>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button
                v-if="row.status === 'IDLE'"
                type="success"
                size="small"
                :disabled="!row.isConnected"
                @click="handleStartEquipment(row.id)"
              >
                启动
              </el-button>
              <el-button
                v-if="row.status === 'RUN'"
                type="warning"
                size="small"
                :disabled="!row.isConnected"
                @click="handleStopEquipment(row.id)"
              >
                停止
              </el-button>
              <el-button
type="primary" size="small"
@click="handleViewEquipmentDetail(row)"
>
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 大屏视图 -->
      <div v-if="viewMode === 'dashboard'" class="equipment-dashboard">
        <div class="dashboard-grid">
          <div
            v-for="eq in equipment"
            :key="eq.id"
            :class="['equipment-tile', `status-${eq.status.toLowerCase()}`]"
            @click="handleViewEquipmentDetail(eq)"
          >
            <div class="tile-header">
              <h3>{{ eq.name }}</h3>
              <div :class="['status-dot', `status-${eq.status.toLowerCase()}`]" />
            </div>
            <div class="tile-content">
              <div class="status-text">
                {{ getStatusText(eq.status) }}
              </div>
              <div v-if="getEquipmentStatusById(eq.id)?.oeeData" class="oee-value">
                OEE: {{ getEquipmentStatusById(eq.id)!.oeeData.oee.toFixed(1) }}%
              </div>
              <div
v-if="getActiveAlarmsByEquipmentId(eq.id).length > 0" class="alarm-count"
>
                <el-icon><Warning /></el-icon>
                {{ getActiveAlarmsByEquipmentId(eq.id).length }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设备详情弹窗 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="selectedEquipment?.name"
      width="80%"
      @close="selectedEquipment = null"
    >
      <div v-if="selectedEquipment" class="equipment-detail">
        <!-- 详情内容待补充 -->
        <el-descriptions :column="3" border>
          <el-descriptions-item label="设备编码">
            {{ selectedEquipment.code }}
          </el-descriptions-item>
          <el-descriptions-item label="设备类型">
            {{ getEquipmentTypeText(selectedEquipment.type) }}
          </el-descriptions-item>
          <el-descriptions-item label="制造商">
            {{ selectedEquipment.manufacturer }}
          </el-descriptions-item>
          <el-descriptions-item label="型号">
            {{ selectedEquipment.model }}
          </el-descriptions-item>
          <el-descriptions-item label="序列号">
            {{ selectedEquipment.serialNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="安装位置">
            {{ selectedEquipment.location }}
          </el-descriptions-item>
          <el-descriptions-item label="安装日期">
            {{ selectedEquipment.installDate }}
          </el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag :type="getStatusTagType(selectedEquipment.status)">
              {{ getStatusText(selectedEquipment.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="连接状态">
            <el-tag :type="selectedEquipment.isConnected ? 'success' : 'danger'">
              {{ selectedEquipment.isConnected ? '已连接' : '未连接' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import {
    Monitor,
    Grid,
    List,
    Refresh,
    VideoPlay,
    VideoPause,
    Close,
    Tools,
    Warning,
    InfoFilled,
    Check,
    Clock,
    Search,
    Bell
  } from '@element-plus/icons-vue'
  import {
    useEquipmentList,
    useEquipmentStatus,
    useEquipmentAlarms,
    useEquipmentControl
  } from '@/composables/useEquipment'
  import { EquipmentStatusCard, AlarmList } from '@/components/equipment'
  import type { Equipment } from '@/types/equipment'
  import { EquipmentStatus, EquipmentType } from '@/types/equipment'

  // 页面状态
  const viewMode = ref<'dashboard' | 'grid' | 'list'>('grid')
  const showAllAlarms = ref(false)
  const showDetailDialog = ref(false)
  const selectedEquipment = ref<Equipment | null>(null)
  const sortBy = ref<'name' | 'status' | 'oee'>('name')

  // 使用组合式函数
  const {
    equipment,
    filteredEquipment: baseFilteredEquipment,
    loading,
    equipmentCount,
    searchKeyword,
    statusFilter,
    typeFilter,
    locationFilter,
    refresh: refreshEquipmentList
  } = useEquipmentList()

  const {
    equipmentStatus,
    statistics,
    autoRefresh,
    refreshStatus,
    toggleAutoRefresh,
    getStatusColor,
    getStatusText
  } = useEquipmentStatus()

  const {
    alarms,
    activeAlarms,
    criticalAlarms,
    majorAlarms,
    handleAcknowledgeAlarm,
    handleClearAlarm
  } = useEquipmentAlarms()

  const { startEquipment, stopEquipment } = useEquipmentControl()

  // 计算属性
  const filteredEquipment = computed(() => {
    let result = baseFilteredEquipment.value

    // 排序
    switch (sortBy.value) {
      case 'name':
        result = result.sort((a, b) => a.name.localeCompare(b.name))
        break
      case 'status':
        result = result.sort((a, b) => a.status.localeCompare(b.status))
        break
      case 'oee':
        result = result.sort((a, b) => {
          const aOee = getEquipmentStatusById(a.id)?.oeeData?.oee || 0
          const bOee = getEquipmentStatusById(b.id)?.oeeData?.oee || 0
          return bOee - aOee
        })
        break
    }

    return result
  })

  const acknowledgedAlarms = computed(() =>
    activeAlarms.value.filter(alarm => alarm.acknowledgedBy)
  )

  const unacknowledgedAlarms = computed(() =>
    activeAlarms.value.filter(alarm => !alarm.acknowledgedBy)
  )

  // 工具方法
  const getEquipmentStatusById = (id: string) => {
    return equipmentStatus.value.find(status => status.equipmentId === id)
  }

  const getActiveAlarmsByEquipmentId = (equipmentId: string) => {
    return activeAlarms.value.filter(alarm => alarm.equipmentId === equipmentId)
  }

  const getEquipmentTypeText = (type: EquipmentType): string => {
    const textMap: Record<EquipmentType, string> = {
      [EquipmentType.ATE]: '测试设备',
      [EquipmentType.BONDER]: '键合机',
      [EquipmentType.DIE_ATTACH]: '贴片机',
      [EquipmentType.MOLDING]: '塑封机',
      [EquipmentType.HANDLER]: '分选机',
      [EquipmentType.TRIM_FORM]: '切筋成形机',
      [EquipmentType.PROBER]: '探针台',
      [EquipmentType.DICING_SAW]: '切割机',
      [EquipmentType.PICK_PLACE]: '取放机',
      [EquipmentType.BURN_IN]: '老化炉'
    }
    return textMap[type] || type
  }

  const getStatusTagType = (status: EquipmentStatus): string => {
    const typeMap: Record<EquipmentStatus, string> = {
      [EquipmentStatus.RUN]: 'success',
      [EquipmentStatus.IDLE]: 'warning',
      [EquipmentStatus.DOWN]: 'danger',
      [EquipmentStatus.PM]: 'info',
      [EquipmentStatus.SETUP]: 'primary',
      [EquipmentStatus.ALARM]: 'danger'
    }
    return typeMap[status] || 'info'
  }

  const getOEEColor = (oee: number): string => {
    if (oee >= 85) return '#67C23A'
    if (oee >= 70) return '#E6A23C'
    return '#F56C6C'
  }

  // 事件处理
  const refreshData = () => {
    refreshEquipmentList()
    refreshStatus()
  }

  const handleStartEquipment = (equipmentId: string) => {
    startEquipment(equipmentId)
  }

  const handleStopEquipment = (equipmentId: string) => {
    stopEquipment(equipmentId)
  }

  const handleViewEquipmentDetail = (equipment: Equipment) => {
    selectedEquipment.value = equipment
    showDetailDialog.value = true
  }
</script>

<style lang="scss" scoped>
  .equipment-status-page {
    padding: var(--spacing-4);

    .page-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: var(--spacing-6);

      .header-left {
        .page-title {
          margin: 0 0 var(--spacing-2);
          font-size: 1.75rem;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        .page-description {
          margin: 0;
          color: var(--color-text-secondary);
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-3);
        align-items: center;
      }
    }

    .stats-overview {
      margin-bottom: var(--spacing-6);

      .stat-card {
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: var(--shadow-large);
          transform: translateY(-2px);
        }

        .stat-content {
          display: flex;
          gap: var(--spacing-4);
          align-items: center;
        }

        .stat-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 60px;
          height: 60px;
          font-size: 24px;
          color: white;
          border-radius: 50%;
        }

        .stat-info {
          .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--color-text-primary);
          }

          .stat-label {
            font-size: 0.875rem;
            color: var(--color-text-secondary);
          }
        }

        &.running .stat-icon {
          background-color: var(--color-success);
        }

        &.idle .stat-icon {
          background-color: var(--color-warning);
        }

        &.down .stat-icon {
          background-color: var(--color-danger);
        }

        &.maintenance .stat-icon {
          background-color: var(--color-info);
        }
      }
    }

    .oee-overview {
      margin-bottom: var(--spacing-6);

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .card-title {
          font-weight: 600;
          color: var(--color-text-primary);
        }
      }

      .oee-metric {
        text-align: center;

        .metric-chart {
          margin-bottom: var(--spacing-3);

          .percentage-text {
            font-size: 1.2rem;
            font-weight: 600;
          }
        }

        .metric-label {
          margin-bottom: var(--spacing-1);
          font-size: 0.875rem;
          color: var(--color-text-secondary);
        }

        .metric-value {
          font-size: 1.1rem;
          font-weight: 600;
          color: var(--color-text-primary);
        }
      }
    }

    .alarms-overview {
      margin-bottom: var(--spacing-6);

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .card-title {
          display: flex;
          gap: var(--spacing-2);
          align-items: center;
          font-weight: 600;
          color: var(--color-text-primary);
        }
      }

      .alarm-summary {
        margin-bottom: var(--spacing-4);

        .alarm-stat {
          display: flex;
          gap: var(--spacing-2);
          align-items: center;
          padding: var(--spacing-2);
          background-color: var(--color-bg-soft);
          border-radius: var(--radius-base);

          &.critical {
            color: var(--color-danger);
          }

          &.major {
            color: var(--color-warning);
          }
        }
      }
    }

    .equipment-display {
      .filters {
        padding: var(--spacing-4);
        margin-bottom: var(--spacing-4);
        background-color: var(--color-bg-soft);
        border-radius: var(--radius-base);
      }

      .equipment-grid {
        min-height: 400px;
      }

      .equipment-table {
        padding: var(--spacing-2);
        background-color: var(--color-bg-primary);
        border-radius: var(--radius-base);
      }

      .equipment-dashboard {
        .dashboard-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: var(--spacing-4);

          .equipment-tile {
            padding: var(--spacing-4);
            cursor: pointer;
            background-color: var(--color-bg-primary);
            border: 2px solid var(--color-border-light);
            border-radius: var(--radius-base);
            transition: all 0.3s ease;

            &:hover {
              box-shadow: var(--shadow-base);
              transform: translateY(-2px);
            }

            &.status-run {
              border-left-color: var(--color-success);
              border-left-width: 4px;
            }

            &.status-idle {
              border-left-color: var(--color-warning);
              border-left-width: 4px;
            }

            &.status-down {
              border-left-color: var(--color-danger);
              border-left-width: 4px;
              animation: pulse-error 2s infinite;
            }

            &.status-pm {
              border-left-color: var(--color-info);
              border-left-width: 4px;
            }

            .tile-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: var(--spacing-3);

              h3 {
                margin: 0;
                font-size: 1.1rem;
                font-weight: 600;
                color: var(--color-text-primary);
              }

              .status-dot {
                width: 16px;
                height: 16px;
                border-radius: 50%;

                &.status-run {
                  background-color: var(--color-success);
                  animation: pulse-success 2s infinite;
                }

                &.status-idle {
                  background-color: var(--color-warning);
                }

                &.status-down {
                  background-color: var(--color-danger);
                  animation: pulse-error 1s infinite;
                }

                &.status-pm {
                  background-color: var(--color-info);
                }
              }
            }

            .tile-content {
              .status-text {
                margin-bottom: var(--spacing-2);
                font-size: 1rem;
                font-weight: 500;
              }

              .oee-value {
                margin-bottom: var(--spacing-2);
                font-size: 0.875rem;
                color: var(--color-text-secondary);
              }

              .alarm-count {
                display: flex;
                gap: var(--spacing-1);
                align-items: center;
                font-size: 0.875rem;
                font-weight: 500;
                color: var(--color-danger);
              }
            }
          }
        }
      }
    }
  }

  @keyframes pulse-success {
    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.6;
    }
  }

  @keyframes pulse-error {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }

    50% {
      opacity: 0.8;
      transform: scale(1.1);
    }
  }
</style>
