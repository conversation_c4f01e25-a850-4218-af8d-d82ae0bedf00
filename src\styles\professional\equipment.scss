// IC封测CIM系统 - 设备监控专业组件样式

.equipment-monitor {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  
  &__header {
    padding: var(--spacing-4) var(--spacing-5);
    background-color: var(--color-bg-secondary);
    border-bottom: 1px solid var(--color-border-light);
    
    &-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
      margin-bottom: var(--spacing-1);
    }
    
    &-subtitle {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }
  }
  
  &__status {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-5);
    
    &-indicator {
      width: 12px;
      height: 12px;
      border-radius: var(--radius-full);
      
      &--online {
        background-color: var(--color-success);
      }
      
      &--offline {
        background-color: var(--color-error);
      }
      
      &--maintenance {
        background-color: var(--color-warning);
      }
    }
    
    &-text {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }
  }
  
  &__metrics {
    padding: var(--spacing-5);
    
    .metric-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: var(--spacing-4);
    }
    
    .metric-item {
      text-align: center;
      
      &__value {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        color: var(--color-text-primary);
        margin-bottom: var(--spacing-1);
      }
      
      &__label {
        font-size: var(--font-size-xs);
        color: var(--color-text-secondary);
      }
    }
  }
}

.equipment-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-4);
}

.equipment-card {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-base);
  padding: var(--spacing-4);
  transition: all var(--transition-fast);
  
  &:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--color-primary);
  }
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-3);
    
    &-title {
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }
    
    &-status {
      padding: var(--spacing-1) var(--spacing-2);
      border-radius: var(--radius-sm);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
      
      &--running {
        background-color: var(--color-success);
        color: var(--color-text-inverse);
      }
      
      &--idle {
        background-color: var(--color-info);
        color: var(--color-text-inverse);
      }
      
      &--error {
        background-color: var(--color-error);
        color: var(--color-text-inverse);
      }
    }
  }
  
  &__details {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    
    .detail-item {
      text-align: center;
      
      &__value {
        font-weight: var(--font-weight-medium);
        color: var(--color-text-primary);
        margin-bottom: 2px;
      }
      
      &__label {
        font-size: var(--font-size-xs);
      }
    }
  }
}