// 菜单切换性能测试脚本
// 在浏览器控制台运行此脚本来测试菜单切换性能

console.log('🔍 开始菜单切换性能测试...')

// 性能测试函数
function measureMenuPerformance() {
  const results = {}

  // 获取所有导航链接
  const navLinks = document.querySelectorAll('.app-nav__item[href], .app-nav__dropdown-item[href]')

  if (navLinks.length === 0) {
    console.warn('⚠️ 未找到导航链接，请确保页面已完全加载')
    return
  }

  console.log(`📊 找到 ${navLinks.length} 个导航链接`)

  // 测试页面过渡时间
  let currentIndex = 0

  function testNextRoute() {
    if (currentIndex >= Math.min(navLinks.length, 8)) {
      // 限制测试数量
      console.log('✅ 性能测试完成！')
      console.table(results)

      // 计算平均性能
      const times = Object.values(results)
      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length

      console.log(`🎯 平均切换时间: ${avgTime.toFixed(2)}ms`)

      if (avgTime < 100) {
        console.log('🚀 优秀！菜单切换性能很流畅')
      } else if (avgTime < 200) {
        console.log('👍 良好！菜单切换性能可接受')
      } else {
        console.log('⚠️ 需要优化！菜单切换较慢')
      }

      return
    }

    const link = navLinks[currentIndex]
    const routeName = link.textContent.trim() || `Route-${currentIndex}`

    console.log(`📱 测试: ${routeName}`)

    // 记录开始时间
    const startTime = performance.now()

    // 监听路由变化完成
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.target.classList?.contains('app-content')) {
          const endTime = performance.now()
          const duration = endTime - startTime

          results[routeName] = Math.round(duration)

          observer.disconnect()

          // 延迟进行下一个测试，避免并发
          setTimeout(() => {
            currentIndex++
            testNextRoute()
          }, 300)
        }
      })
    })

    // 开始监听DOM变化
    const appContent = document.querySelector('.app-content')
    if (appContent) {
      observer.observe(appContent, {
        childList: true,
        subtree: true
      })
    }

    // 触发路由切换
    try {
      link.click()
    } catch (error) {
      console.warn(`⚠️ 无法点击链接: ${routeName}`, error)
      currentIndex++
      observer.disconnect()
      setTimeout(testNextRoute, 100)
    }
  }

  // 开始测试
  testNextRoute()
}

// CSS动画性能检测
function checkCSSPerformance() {
  console.log('🎨 检查CSS过渡性能...')

  const styles = window.getComputedStyle(document.documentElement)
  const navTransition = styles.getPropertyValue('--transition-nav')
  const dropdownTransition = styles.getPropertyValue('--transition-dropdown')

  console.log(`📏 导航过渡时间: ${navTransition}`)
  console.log(`📏 下拉菜单过渡时间: ${dropdownTransition}`)

  // 检查GPU加速属性
  const navItems = document.querySelectorAll('.app-nav__item')
  let gpuAccelerated = 0

  navItems.forEach(item => {
    const transform = window.getComputedStyle(item).transform
    if (transform && transform !== 'none') {
      gpuAccelerated++
    }
  })

  console.log(`🔧 GPU加速的导航项: ${gpuAccelerated}/${navItems.length}`)
}

// KeepAlive缓存检测
function checkKeepAliveCache() {
  console.log('📦 检查KeepAlive缓存状态...')

  // 检查Vue DevTools中的缓存组件
  if (window.__VUE_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('🔍 Vue DevTools可用，建议在DevTools中查看KeepAlive缓存状态')
  } else {
    console.log('📝 建议安装Vue DevTools来监控KeepAlive缓存')
  }
}

// 内存使用情况检测
function checkMemoryUsage() {
  if (performance.memory) {
    const memory = performance.memory
    console.log('💾 内存使用情况:')
    console.log(`- 已使用: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`)
    console.log(`- 总计: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`)
    console.log(`- 限制: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`)
  } else {
    console.log('💾 浏览器不支持内存API')
  }
}

// 主测试函数
function runPerformanceTest() {
  console.clear()
  console.log('🚀 IC封测CIM系统 - 菜单切换性能测试')
  console.log('='.repeat(50))

  checkCSSPerformance()
  console.log('')

  checkKeepAliveCache()
  console.log('')

  checkMemoryUsage()
  console.log('')

  console.log('开始路由切换性能测试...')
  console.log('测试将依次点击各个菜单项并测量切换时间')
  console.log('')

  measureMenuPerformance()
}

// 导出测试函数到全局
window.runPerformanceTest = runPerformanceTest

// 自动运行测试
runPerformanceTest()
