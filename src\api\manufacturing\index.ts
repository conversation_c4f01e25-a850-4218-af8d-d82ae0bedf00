// 制造执行管理 API 接口
// Manufacturing Execution System API

import type {
  Equipment,
  CPTestRecord,
  AssemblyRecord,
  FinalTestRecord,
  ProbeCard,
  TestProgram,
  HandlerOperation,
  ProcessParameter,
  SPCData,
  SECSMessage,
  DashboardData,
  WaferMap
} from '@/types/manufacturing'

import { manufacturingMockData } from '@/utils/mockData/manufacturing'

// 模拟API延迟
const apiDelay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

/**
 * 设备管理 API
 */
export const equipmentApi = {
  /**
   * 获取设备列表
   */
  async getEquipments(params?: {
    type?: string
    status?: string
    station?: string
  }): Promise<Equipment[]> {
    await apiDelay(300)
    let equipments = manufacturingMockData.equipments

    if (params?.type) {
      equipments = equipments.filter(eq =>
        eq.station.toLowerCase().includes(params.type!.toLowerCase())
      )
    }

    if (params?.status) {
      equipments = equipments.filter(eq => eq.status === params.status)
    }

    if (params?.station) {
      equipments = equipments.filter(eq => eq.station.includes(params.station!))
    }

    return equipments
  },

  /**
   * 获取设备详情
   */
  async getEquipmentDetail(equipmentId: string): Promise<Equipment | null> {
    await apiDelay(200)
    return manufacturingMockData.equipments.find(eq => eq.id === equipmentId) || null
  },

  /**
   * 控制设备
   */
  async controlEquipment(
    equipmentId: string,
    command: string,
    params?: any
  ): Promise<{ success: boolean; message: string }> {
    await apiDelay(800)
    // 模拟设备控制
    return {
      success: Math.random() > 0.1, // 90%成功率
      message: Math.random() > 0.1 ? '命令执行成功' : '设备响应超时'
    }
  },

  /**
   * 更新设备状态
   */
  async updateEquipmentStatus(equipmentId: string, status: string): Promise<{ success: boolean }> {
    await apiDelay(300)
    // 模拟状态更新
    const equipment = manufacturingMockData.equipments.find(eq => eq.id === equipmentId)
    if (equipment) {
      equipment.status = status as any
      equipment.lastUpdated = new Date().toISOString()
    }
    return { success: true }
  }
}

/**
 * CP测试 API
 */
export const cpTestingApi = {
  /**
   * 获取当前测试列表
   */
  async getCurrentTests(params?: {
    status?: string
    customerPN?: string
    operator?: string
  }): Promise<CPTestRecord[]> {
    await apiDelay(500)
    let tests = manufacturingMockData.generateCPTestRecords()

    if (params?.status) {
      tests = tests.filter(test => test.waferInfo.status === params.status)
    }

    if (params?.customerPN) {
      tests = tests.filter(test => test.waferInfo.customerPN.includes(params.customerPN!))
    }

    if (params?.operator) {
      tests = tests.filter(test => test.operator === params.operator)
    }

    return tests
  },

  /**
   * 获取测试详情
   */
  async getTestDetail(testId: string): Promise<CPTestRecord | null> {
    await apiDelay(300)
    const tests = manufacturingMockData.generateCPTestRecords()
    return tests.find(test => test.id === testId) || null
  },

  /**
   * 创建新测试
   */
  async createTest(testData: {
    waferLot: string
    waferNumber: number
    customerPN: string
    productCode: string
    testerId: string
    probeCardId: string
    testProgram: string
    temperature: number
    operator: string
  }): Promise<{ success: boolean; testId?: string; message: string }> {
    await apiDelay(1000)

    // 模拟创建测试
    const testId = `cp-test-${Date.now()}`
    return {
      success: Math.random() > 0.05, // 95%成功率
      testId,
      message: Math.random() > 0.05 ? '测试创建成功' : '设备忙，创建失败'
    }
  },

  /**
   * 获取晶圆图
   */
  async getWaferMap(waferId: string): Promise<WaferMap> {
    await apiDelay(400)
    return manufacturingMockData.generateWaferMap(waferId)
  },

  /**
   * 导出晶圆图
   */
  async exportWaferMap(
    waferId: string,
    format: 'csv' | 'excel' | 'pdf'
  ): Promise<{ success: boolean; downloadUrl?: string }> {
    await apiDelay(2000)
    return {
      success: true,
      downloadUrl: `/api/export/wafer-map/${waferId}.${format}`
    }
  },

  /**
   * 获取探针卡列表
   */
  async getProbeCards(): Promise<ProbeCard[]> {
    await apiDelay(200)
    return manufacturingMockData.probeCards
  },

  /**
   * 管理探针卡
   */
  async manageProbeCard(
    cardId: string,
    action: 'clean' | 'pm' | 'retire'
  ): Promise<{ success: boolean; message: string }> {
    await apiDelay(800)
    const messages = {
      clean: '探针卡清洁完成',
      pm: '探针卡保养完成',
      retire: '探针卡已退役'
    }
    return {
      success: true,
      message: messages[action]
    }
  }
}

/**
 * 封装工艺 API
 */
export const assemblyApi = {
  /**
   * 获取生产批次列表
   */
  async getAssemblyRecords(params?: {
    status?: string
    packageType?: string
    customerPN?: string
  }): Promise<AssemblyRecord[]> {
    await apiDelay(400)
    let records = manufacturingMockData.generateAssemblyRecords()

    if (params?.status) {
      records = records.filter(record => record.status === params.status)
    }

    if (params?.packageType) {
      records = records.filter(record => record.packageType === params.packageType)
    }

    if (params?.customerPN) {
      records = records.filter(record => record.customerPN.includes(params.customerPN!))
    }

    return records
  },

  /**
   * 获取批次详情
   */
  async getAssemblyDetail(recordId: string): Promise<AssemblyRecord | null> {
    await apiDelay(300)
    const records = manufacturingMockData.generateAssemblyRecords()
    return records.find(record => record.id === recordId) || null
  },

  /**
   * 创建新批次
   */
  async createAssemblyLot(lotData: {
    lotNumber: string
    customerPN: string
    packageType: string
    quantity: number
    priority: string
    operator: string
  }): Promise<{ success: boolean; lotId?: string; message: string }> {
    await apiDelay(1200)

    const lotId = `asm-${Date.now()}`
    return {
      success: Math.random() > 0.02, // 98%成功率
      lotId,
      message: Math.random() > 0.02 ? '批次创建成功' : '系统繁忙，创建失败'
    }
  },

  /**
   * 更新批次状态
   */
  async updateLotStatus(lotId: string, status: string): Promise<{ success: boolean }> {
    await apiDelay(300)
    return { success: true }
  }
}

/**
 * 最终测试 API
 */
export const finalTestApi = {
  /**
   * 获取最终测试记录
   */
  async getFinalTestRecords(params?: {
    status?: string
    packageType?: string
    customerPN?: string
  }): Promise<FinalTestRecord[]> {
    await apiDelay(500)
    let records = manufacturingMockData.generateFinalTestRecords()

    if (params?.status) {
      records = records.filter(record => record.status === params.status)
    }

    if (params?.packageType) {
      records = records.filter(record => record.packageType === params.packageType)
    }

    if (params?.customerPN) {
      records = records.filter(record => record.customerPN.includes(params.customerPN!))
    }

    return records
  },

  /**
   * 获取测试详情
   */
  async getTestDetail(testId: string): Promise<FinalTestRecord | null> {
    await apiDelay(300)
    const records = manufacturingMockData.generateFinalTestRecords()
    return records.find(record => record.id === testId) || null
  },

  /**
   * 创建最终测试
   */
  async createFinalTest(testData: {
    lotNumber: string
    customerPN: string
    packageType: string
    testProgramId: string
    handlerId: string
    operator: string
    requireBurnIn: boolean
    burnInConfig?: {
      temperature: number
      voltage: number
      duration: number
    }
  }): Promise<{ success: boolean; testId?: string; message: string }> {
    await apiDelay(1000)

    const testId = `ft-${Date.now()}`
    return {
      success: Math.random() > 0.03, // 97%成功率
      testId,
      message: Math.random() > 0.03 ? '测试创建成功' : '分选机忙，创建失败'
    }
  },

  /**
   * 获取测试程序列表
   */
  async getTestPrograms(): Promise<TestProgram[]> {
    await apiDelay(200)
    return manufacturingMockData.testPrograms
  },

  /**
   * 编辑测试程序
   */
  async updateTestProgram(
    programId: string,
    programData: Partial<TestProgram>
  ): Promise<{ success: boolean }> {
    await apiDelay(600)
    return { success: true }
  },

  /**
   * 获取分选机列表
   */
  async getHandlers(): Promise<HandlerOperation[]> {
    await apiDelay(200)
    return manufacturingMockData.handlers
  },

  /**
   * 控制分选机
   */
  async controlHandler(
    handlerId: string,
    command: string
  ): Promise<{ success: boolean; message: string }> {
    await apiDelay(800)
    return {
      success: Math.random() > 0.05,
      message: Math.random() > 0.05 ? '分选机操作成功' : '分选机响应异常'
    }
  }
}

/**
 * 工艺参数 API
 */
export const processParameterApi = {
  /**
   * 获取工艺参数
   */
  async getProcessParameters(equipmentId?: string): Promise<ProcessParameter[]> {
    await apiDelay(300)
    return manufacturingMockData.generateProcessParameters()
  },

  /**
   * 更新工艺参数
   */
  async updateProcessParameter(parameterId: string, value: number): Promise<{ success: boolean }> {
    await apiDelay(500)
    return { success: Math.random() > 0.1 } // 90%成功率
  },

  /**
   * 获取参数历史数据
   */
  async getParameterHistory(
    parameterId: string,
    timeRange: { start: string; end: string }
  ): Promise<{
    data: Array<{ timestamp: string; value: number }>
  }> {
    await apiDelay(400)
    // 模拟历史数据
    const data = Array.from({ length: 100 }, (_, i) => ({
      timestamp: new Date(Date.now() - i * 60000).toISOString(),
      value: 380 + (Math.random() - 0.5) * 10
    })).reverse()

    return { data }
  },

  /**
   * 导出参数数据
   */
  async exportParameterData(
    parameterId: string,
    format: 'csv' | 'excel'
  ): Promise<{ success: boolean; downloadUrl?: string }> {
    await apiDelay(1500)
    return {
      success: true,
      downloadUrl: `/api/export/parameter/${parameterId}.${format}`
    }
  }
}

/**
 * SPC统计 API
 */
export const spcApi = {
  /**
   * 获取SPC数据
   */
  async getSPCData(
    parameterId: string,
    timeRange?: { start: string; end: string }
  ): Promise<SPCData> {
    await apiDelay(600)
    return manufacturingMockData.generateSPCData(parameterId)
  },

  /**
   * 更新控制限
   */
  async updateControlLimits(
    parameterId: string,
    limits: { ucl: number; lcl: number }
  ): Promise<{ success: boolean }> {
    await apiDelay(400)
    return { success: true }
  }
}

/**
 * SECS/GEM 通信 API
 */
export const secsApi = {
  /**
   * 获取SECS消息
   */
  async getSecsMessages(equipmentId: string, limit?: number): Promise<SECSMessage[]> {
    await apiDelay(300)
    const messages = manufacturingMockData.generateSecsMessages(equipmentId)
    return limit ? messages.slice(0, limit) : messages
  },

  /**
   * 发送SECS命令
   */
  async sendSecsCommand(
    equipmentId: string,
    stream: number,
    func: number,
    data: any
  ): Promise<{
    success: boolean
    messageId?: string
    response?: any
  }> {
    await apiDelay(1000)
    return {
      success: Math.random() > 0.05,
      messageId: `secs-${equipmentId}-${Date.now()}`,
      response: { status: 'ACK', data: 'Command executed' }
    }
  },

  /**
   * 清空消息历史
   */
  async clearMessages(equipmentId: string): Promise<{ success: boolean }> {
    await apiDelay(200)
    return { success: true }
  }
}

/**
 * 看板数据 API
 */
export const dashboardApi = {
  /**
   * 获取看板数据
   */
  async getDashboardData(): Promise<DashboardData> {
    await apiDelay(300)
    // 模拟实时数据变化
    const data = { ...manufacturingMockData.dashboardData }

    // 添加小幅度随机变化
    data.overallOEE += (Math.random() - 0.5) * 2
    data.dailyOutput += Math.floor((Math.random() - 0.5) * 5000)
    data.cpTesting.avgYield += (Math.random() - 0.5) * 1
    data.assembly.defectRate += (Math.random() - 0.5) * 0.001
    data.finalTest.passRate += (Math.random() - 0.5) * 1

    return data
  },

  /**
   * 获取实时KPI
   */
  async getRealtimeKPI(module: 'cp' | 'assembly' | 'final-test'): Promise<any> {
    await apiDelay(200)
    const data = manufacturingMockData.dashboardData

    switch (module) {
      case 'cp':
        return data.cpTesting
      case 'assembly':
        return data.assembly
      case 'final-test':
        return data.finalTest
      default:
        return {}
    }
  }
}

// 导出所有API
export const manufacturingApi = {
  equipment: equipmentApi,
  cpTesting: cpTestingApi,
  assembly: assemblyApi,
  finalTest: finalTestApi,
  processParameter: processParameterApi,
  spc: spcApi,
  secs: secsApi,
  dashboard: dashboardApi
}

export default manufacturingApi
