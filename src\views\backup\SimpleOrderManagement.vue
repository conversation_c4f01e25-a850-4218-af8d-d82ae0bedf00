<template>
  <div class="order-management">
    <div class="page-header">
      <h1>📋 订单管理</h1>
      <p>IC封测CIM系统 - 订单全生命周期管理</p>
      
      <div class="action-buttons">
        <button @click="showCreateModal = true" class="btn btn-primary">
          + 新建订单
        </button>
        <button @click="refreshData" class="btn btn-secondary">
          🔄 刷新数据
        </button>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <div class="search-section">
      <div class="search-grid">
        <input 
          v-model="searchForm.orderNo"
          type="text" 
          placeholder="订单号"
          class="input-field"
        />
        <input 
          v-model="searchForm.customerName"
          type="text" 
          placeholder="客户名称"
          class="input-field"
        />
        <select v-model="searchForm.status" class="input-field">
          <option value="">全部状态</option>
          <option value="pending">待处理</option>
          <option value="processing">进行中</option>
          <option value="completed">已完成</option>
          <option value="cancelled">已取消</option>
        </select>
        <select v-model="searchForm.packageType" class="input-field">
          <option value="">全部封装</option>
          <option value="QFP">QFP</option>
          <option value="BGA">BGA</option>
          <option value="CSP">CSP</option>
          <option value="FC">FC</option>
        </select>
        <button @click="searchOrders" class="btn btn-primary">🔍 搜索</button>
        <button @click="resetSearch" class="btn btn-secondary">重置</button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <div class="table-header">
        <h3>订单列表 ({{ filteredOrders.length }})</h3>
        <div class="pagination-info">
          第 {{ currentPage }} 页，共 {{ totalPages }} 页
        </div>
      </div>
      
      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>订单号</th>
              <th>客户名称</th>
              <th>封装类型</th>
              <th>数量</th>
              <th>金额</th>
              <th>状态</th>
              <th>当前阶段</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="order in paginatedOrders" :key="order.id">
              <td class="order-no">{{ order.orderNo }}</td>
              <td>{{ order.customerName }}</td>
              <td>
                <span class="package-type" :class="order.packageType.toLowerCase()">
                  {{ order.packageType }}
                </span>
              </td>
              <td class="number">{{ order.quantity.toLocaleString() }}</td>
              <td class="currency">¥{{ order.amount.toLocaleString() }}</td>
              <td>
                <span class="status-badge" :class="order.status">
                  {{ getStatusText(order.status) }}
                </span>
              </td>
              <td>
                <span class="stage-badge" :class="order.currentStage">
                  {{ getStageText(order.currentStage) }}
                </span>
              </td>
              <td>
                <button @click="viewOrder(order)" class="btn-small btn-info">查看</button>
                <button @click="editOrder(order)" class="btn-small btn-warning">编辑</button>
                <button @click="deleteOrder(order)" class="btn-small btn-danger">删除</button>
              </td>
            </tr>
          </tbody>
        </table>
        
        <div v-if="filteredOrders.length === 0" class="empty-state">
          <p>🔍 暂无订单数据</p>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <button 
          @click="currentPage = Math.max(1, currentPage - 1)"
          :disabled="currentPage === 1"
          class="btn btn-secondary"
        >
          上一页
        </button>
        <span class="page-numbers">
          <button 
            v-for="page in pageNumbers" 
            :key="page"
            @click="currentPage = page"
            :class="['page-btn', { active: currentPage === page }]"
          >
            {{ page }}
          </button>
        </span>
        <button 
          @click="currentPage = Math.min(totalPages, currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="btn btn-secondary"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 创建/编辑模态框 -->
    <div v-if="showCreateModal || showEditModal" class="modal-backdrop" @click="closeModals">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ showCreateModal ? '新建订单' : '编辑订单' }}</h3>
          <button @click="closeModals" class="modal-close">✕</button>
        </div>
        
        <div class="modal-body">
          <div class="form-grid">
            <div class="form-group">
              <label>订单号</label>
              <input v-model="orderForm.orderNo" type="text" class="input-field" placeholder="自动生成" readonly />
            </div>
            <div class="form-group">
              <label>客户名称 *</label>
              <input v-model="orderForm.customerName" type="text" class="input-field" placeholder="输入客户名称" />
            </div>
            <div class="form-group">
              <label>封装类型 *</label>
              <select v-model="orderForm.packageType" class="input-field">
                <option value="">请选择</option>
                <option value="QFP">QFP - 四方扁平封装</option>
                <option value="BGA">BGA - 球栅阵列封装</option>
                <option value="CSP">CSP - 芯片尺寸封装</option>
                <option value="FC">FC - 倒装芯片</option>
              </select>
            </div>
            <div class="form-group">
              <label>数量 *</label>
              <input v-model="orderForm.quantity" type="number" class="input-field" placeholder="芯片数量" />
            </div>
            <div class="form-group">
              <label>单价 (元)</label>
              <input v-model="orderForm.unitPrice" type="number" step="0.01" class="input-field" placeholder="单价" />
            </div>
            <div class="form-group">
              <label>总金额</label>
              <input :value="calculateTotal()" type="text" class="input-field" placeholder="自动计算" readonly />
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button @click="closeModals" class="btn btn-secondary">取消</button>
          <button @click="saveOrder" class="btn btn-primary">{{ showCreateModal ? '创建' : '保存' }}</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 数据状态
const orders = ref([
  {
    id: 'ORD001',
    orderNo: 'IC2024001',
    customerName: 'ABC半导体有限公司',
    packageType: 'QFP',
    quantity: 10000,
    amount: 50000,
    status: 'processing',
    currentStage: 'cp-test',
    createTime: '2024-01-15'
  },
  {
    id: 'ORD002',
    orderNo: 'IC2024002',
    customerName: 'XYZ电子科技',
    packageType: 'BGA',
    quantity: 5000,
    amount: 35000,
    status: 'completed',
    currentStage: 'delivery',
    createTime: '2024-01-10'
  },
  {
    id: 'ORD003',
    orderNo: 'IC2024003',
    customerName: 'DEF芯片公司',
    packageType: 'CSP',
    quantity: 15000,
    amount: 75000,
    status: 'pending',
    currentStage: 'pending',
    createTime: '2024-01-20'
  }
])

const searchForm = ref({
  orderNo: '',
  customerName: '',
  status: '',
  packageType: ''
})

const orderForm = ref({
  orderNo: '',
  customerName: '',
  packageType: '',
  quantity: null,
  unitPrice: null
})

// 模态框状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const editingOrder = ref(null)

// 分页状态
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const filteredOrders = computed(() => {
  let result = orders.value
  
  if (searchForm.value.orderNo) {
    result = result.filter(order => 
      order.orderNo.toLowerCase().includes(searchForm.value.orderNo.toLowerCase())
    )
  }
  
  if (searchForm.value.customerName) {
    result = result.filter(order => 
      order.customerName.toLowerCase().includes(searchForm.value.customerName.toLowerCase())
    )
  }
  
  if (searchForm.value.status) {
    result = result.filter(order => order.status === searchForm.value.status)
  }
  
  if (searchForm.value.packageType) {
    result = result.filter(order => order.packageType === searchForm.value.packageType)
  }
  
  return result
})

const totalPages = computed(() => Math.ceil(filteredOrders.value.length / pageSize.value))

const paginatedOrders = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredOrders.value.slice(start, end)
})

const pageNumbers = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value
  
  let start = Math.max(1, current - 2)
  let end = Math.min(total, start + 4)
  
  if (end - start < 4) {
    start = Math.max(1, end - 4)
  }
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// 方法
const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待处理',
    processing: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getStageText = (stage: string) => {
  const stageMap = {
    pending: '待开始',
    'cp-test': 'CP测试',
    assembly: '封装工艺',
    'ft-test': 'FT测试',
    delivery: '交付完成'
  }
  return stageMap[stage] || stage
}

const searchOrders = () => {
  currentPage.value = 1
  console.log('搜索订单:', searchForm.value)
}

const resetSearch = () => {
  searchForm.value = {
    orderNo: '',
    customerName: '',
    status: '',
    packageType: ''
  }
  currentPage.value = 1
}

const refreshData = () => {
  console.log('刷新数据')
}

const viewOrder = (order) => {
  console.log('查看订单:', order)
}

const editOrder = (order) => {
  editingOrder.value = order
  orderForm.value = { ...order }
  showEditModal.value = true
}

const deleteOrder = (order) => {
  if (confirm(`确定要删除订单 ${order.orderNo} 吗？`)) {
    const index = orders.value.findIndex(o => o.id === order.id)
    if (index > -1) {
      orders.value.splice(index, 1)
    }
  }
}

const closeModals = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingOrder.value = null
  orderForm.value = {
    orderNo: '',
    customerName: '',
    packageType: '',
    quantity: null,
    unitPrice: null
  }
}

const calculateTotal = () => {
  if (orderForm.value.quantity && orderForm.value.unitPrice) {
    return (orderForm.value.quantity * orderForm.value.unitPrice).toLocaleString()
  }
  return ''
}

const saveOrder = () => {
  // 简单验证
  if (!orderForm.value.customerName || !orderForm.value.packageType || !orderForm.value.quantity) {
    alert('请填写必填字段')
    return
  }
  
  if (showCreateModal.value) {
    // 创建新订单
    const newOrder = {
      id: 'ORD' + Date.now(),
      orderNo: 'IC2024' + String(orders.value.length + 1).padStart(3, '0'),
      customerName: orderForm.value.customerName,
      packageType: orderForm.value.packageType,
      quantity: Number(orderForm.value.quantity),
      amount: Number(orderForm.value.quantity) * Number(orderForm.value.unitPrice || 5),
      status: 'pending',
      currentStage: 'pending',
      createTime: new Date().toISOString().split('T')[0]
    }
    orders.value.push(newOrder)
  } else {
    // 更新订单
    const index = orders.value.findIndex(o => o.id === editingOrder.value.id)
    if (index > -1) {
      orders.value[index] = {
        ...orders.value[index],
        ...orderForm.value,
        quantity: Number(orderForm.value.quantity),
        amount: Number(orderForm.value.quantity) * Number(orderForm.value.unitPrice || 5)
      }
    }
  }
  
  closeModals()
}

onMounted(() => {
  console.log('订单管理页面加载完成')
})
</script>

<style scoped>
.order-management {
  padding: 1.5rem;
  background: #f9fafb;
  min-height: calc(100vh - 100px);
}

.page-header {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.page-header h1 {
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
}

.page-header p {
  color: #6b7280;
  margin: 0 0 1.5rem 0;
}

.action-buttons {
  display: flex;
  gap: 1rem;
}

.search-section {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.search-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.table-header h3 {
  margin: 0;
  color: #1f2937;
}

.pagination-info {
  color: #6b7280;
  font-size: 0.875rem;
}

.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.data-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.data-table tbody tr:hover {
  background: #f9fafb;
}

.order-no {
  font-family: monospace;
  font-weight: 600;
  color: #2563eb;
}

.number, .currency {
  text-align: right;
}

.package-type {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.package-type.qfp { background: #dbeafe; color: #1d4ed8; }
.package-type.bga { background: #dcfce7; color: #166534; }
.package-type.csp { background: #fef3c7; color: #92400e; }
.package-type.fc { background: #fce7f3; color: #be185d; }

.status-badge, .stage-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.pending { background: #fef3c7; color: #92400e; }
.status-badge.processing { background: #dbeafe; color: #1d4ed8; }
.status-badge.completed { background: #dcfce7; color: #166534; }
.status-badge.cancelled { background: #fee2e2; color: #dc2626; }

.stage-badge.pending { background: #f3f4f6; color: #6b7280; }
.stage-badge.cp-test { background: #dbeafe; color: #1d4ed8; }
.stage-badge.assembly { background: #fef3c7; color: #92400e; }
.stage-badge.ft-test { background: #dcfce7; color: #166534; }
.stage-badge.delivery { background: #e0f2fe; color: #0369a1; }

.btn, .btn-small {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-small {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  margin-right: 0.25rem;
}

.btn-primary { background: #2563eb; color: white; }
.btn-primary:hover { background: #1d4ed8; }

.btn-secondary { background: #e5e7eb; color: #374151; }
.btn-secondary:hover { background: #d1d5db; }

.btn-info { background: #06b6d4; color: white; }
.btn-warning { background: #f59e0b; color: white; }
.btn-danger { background: #dc2626; color: white; }

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.input-field {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.input-field:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
}

.page-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
}

.page-btn:hover, .page-btn.active {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  color: #1f2937;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
}

.modal-body {
  padding: 1.5rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
  .order-management {
    padding: 1rem;
  }
  
  .search-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .pagination {
    flex-wrap: wrap;
  }
  
  .modal-content {
    width: 95%;
    margin: 1rem;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
}
</style>