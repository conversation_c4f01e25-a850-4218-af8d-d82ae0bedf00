// Mock数据生成工具
export const mockData = {
  // 订单管理数据
  orders: [
    {
      id: '1',
      orderNumber: 'ORD-2024-001',
      customerName: '华为技术有限公司',
      productName: 'Kirin 9000',
      packageType: 'BGA',
      quantity: 50000,
      completedQuantity: 35000,
      status: 'in_progress',
      priority: 'high',
      createTime: '2024-01-15T08:00:00Z',
      dueDate: '2024-02-15T18:00:00Z',
      progress: 70,
      estimatedCompletion: '2024-02-10T15:30:00Z'
    },
    {
      id: '2',
      orderNumber: 'ORD-2024-002',
      customerName: '小米科技有限公司',
      productName: 'Snapdragon 8 Gen3',
      packageType: 'FC-BGA',
      quantity: 30000,
      completedQuantity: 30000,
      status: 'completed',
      priority: 'normal',
      createTime: '2024-01-10T09:00:00Z',
      dueDate: '2024-02-01T17:00:00Z',
      progress: 100,
      completedTime: '2024-01-28T14:20:00Z'
    }
  ],

  // 生产数据
  production: {
    currentShift: {
      shiftName: '白班',
      startTime: '08:00',
      endTime: '20:00',
      supervisor: '张工程师',
      operators: 15,
      plannedOutput: 25000,
      actualOutput: 22300,
      efficiency: 89.2
    },
    dailyOutput: [
      { date: '2024-01-15', planned: 25000, actual: 22300, efficiency: 89.2 },
      { date: '2024-01-14', planned: 24000, actual: 23800, efficiency: 99.2 },
      { date: '2024-01-13', planned: 26000, actual: 25200, efficiency: 96.9 }
    ],
    processSteps: [
      { step: 'CP测试', status: 'running', progress: 85, equipment: 'ATE-01' },
      { step: '切割', status: 'completed', progress: 100, equipment: 'DICING-01' },
      { step: '贴片', status: 'running', progress: 78, equipment: 'DIE-ATTACH-01' },
      { step: '线键合', status: 'pending', progress: 0, equipment: 'WIRE-BOND-01' }
    ]
  },

  // 设备数据
  equipment: [
    {
      id: 'ATE-01',
      name: 'Advantest T5503HS',
      type: 'ATE_TESTER',
      line: 'CP-Line-01',
      status: 'running',
      utilization: 92.5,
      temperature: 23.5,
      humidity: 45.2,
      lastMaintenance: '2024-01-01T10:00:00Z',
      nextMaintenance: '2024-02-01T10:00:00Z',
      alarms: [],
      efficiency: 95.8
    },
    {
      id: 'DICING-01',
      name: 'DISCO DFL7160',
      type: 'DICING_SAW',
      line: 'Assembly-Line-01',
      status: 'maintenance',
      utilization: 0,
      temperature: 25.1,
      humidity: 42.8,
      lastMaintenance: '2024-01-15T14:00:00Z',
      nextMaintenance: '2024-03-01T10:00:00Z',
      alarms: [
        { id: 'ALM-001', message: '设备维护中', severity: 'info', time: '2024-01-15T14:00:00Z' }
      ],
      efficiency: 0
    }
  ],

  // 质量数据
  quality: {
    yieldTrend: [
      { time: '08:00', cpYield: 98.5, ftYield: 96.8, overallYield: 95.4 },
      { time: '10:00', cpYield: 98.2, ftYield: 97.1, overallYield: 95.4 },
      { time: '12:00', cpYield: 98.8, ftYield: 96.5, overallYield: 95.4 },
      { time: '14:00', cpYield: 98.1, ftYield: 97.3, overallYield: 95.5 }
    ],
    defectTypes: [
      { type: '电性能不良', count: 245, percentage: 45.2 },
      { type: '外观不良', count: 123, percentage: 22.7 },
      { type: '尺寸不良', count: 89, percentage: 16.4 },
      { type: '其他', count: 85, percentage: 15.7 }
    ],
    spcData: {
      parameter: 'Vf电压',
      usl: 3.6,
      lsl: 3.2,
      target: 3.4,
      mean: 3.38,
      sigma: 0.05,
      cp: 1.33,
      cpk: 1.2,
      data: [3.35, 3.42, 3.39, 3.36, 3.41, 3.38, 3.37, 3.4, 3.34, 3.43]
    }
  },

  // 用户数据
  users: [
    {
      id: '1',
      username: 'admin',
      name: '系统管理员',
      email: '<EMAIL>',
      role: 'admin',
      department: '信息技术部',
      avatar: '',
      status: 'active',
      lastLogin: '2024-01-15T08:00:00Z'
    },
    {
      id: '2',
      username: 'operator001',
      name: '操作员001',
      email: '<EMAIL>',
      role: 'operator',
      department: '生产部',
      avatar: '',
      status: 'active',
      lastLogin: '2024-01-15T07:45:00Z'
    }
  ],

  // 报警数据
  alarms: [
    {
      id: 'ALM-001',
      equipmentId: 'ATE-01',
      equipmentName: 'Advantest T5503HS',
      type: 'TEMPERATURE_HIGH',
      severity: 'warning',
      message: '设备温度超过正常范围',
      value: 28.5,
      threshold: 25.0,
      timestamp: '2024-01-15T10:30:00Z',
      status: 'active',
      acknowledgedBy: '',
      acknowledgedTime: null
    },
    {
      id: 'ALM-002',
      equipmentId: 'DICING-01',
      equipmentName: 'DISCO DFL7160',
      type: 'EQUIPMENT_OFFLINE',
      severity: 'critical',
      message: '设备离线',
      timestamp: '2024-01-15T14:00:00Z',
      status: 'acknowledged',
      acknowledgedBy: '维护工程师',
      acknowledgedTime: '2024-01-15T14:05:00Z'
    }
  ],

  // 库存数据
  inventory: [
    {
      id: '1',
      materialCode: 'WAFER-001',
      materialName: '8寸硅晶圆',
      category: 'RAW_MATERIAL',
      currentStock: 500,
      safetyStock: 100,
      unit: '片',
      location: '原材料仓库-A区',
      supplier: '环球晶圆',
      lastUpdate: '2024-01-15T08:00:00Z'
    },
    {
      id: '2',
      materialCode: 'SUBSTRATE-BGA-001',
      materialName: 'BGA基板',
      category: 'SUBSTRATE',
      currentStock: 15000,
      safetyStock: 3000,
      unit: '片',
      location: '基板仓库-B区',
      supplier: '欣兴电子',
      lastUpdate: '2024-01-15T08:00:00Z'
    }
  ],

  // 图表数据生成器
  generateTimeSeriesData(hours: number = 24, interval: number = 1) {
    const data = []
    const now = new Date()

    for (let i = hours - 1; i >= 0; i -= interval) {
      const time = new Date(now.getTime() - i * 60 * 60 * 1000)
      data.push({
        time: time.toISOString(),
        value: Math.floor(Math.random() * 100) + 50,
        yield: Math.random() * 5 + 95,
        efficiency: Math.random() * 10 + 85
      })
    }

    return data
  },

  // 生成随机订单
  generateRandomOrder(overrides: Partial<any> = {}) {
    const customers = ['华为技术', '小米科技', 'OPPO', 'vivo', '联发科技']
    const products = ['Kirin 9000', 'Dimensity 9300', 'Snapdragon 8 Gen3']
    const packageTypes = ['BGA', 'FC-BGA', 'CSP', 'QFN']

    return {
      id: Math.random().toString(36).substr(2, 9),
      orderNumber: `ORD-${Date.now()}`,
      customerName: customers[Math.floor(Math.random() * customers.length)],
      productName: products[Math.floor(Math.random() * products.length)],
      packageType: packageTypes[Math.floor(Math.random() * packageTypes.length)],
      quantity: Math.floor(Math.random() * 50000) + 10000,
      completedQuantity: 0,
      status: 'pending',
      priority: Math.random() > 0.7 ? 'high' : 'normal',
      createTime: new Date().toISOString(),
      dueDate: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      ...overrides
    }
  }
}

// MockWebSocket类型定义
interface MockWebSocket {
  send: (data: string) => void
  close: () => void
  readyState: number
  simulateMessage: (data: string) => void
}

// WebSocket消息模拟器
export class MockWebSocketServer {
  private connections: MockWebSocket[] = []
  private intervals: NodeJS.Timeout[] = []

  addConnection(ws: MockWebSocket) {
    this.connections.push(ws)
  }

  removeConnection(ws: MockWebSocket) {
    const index = this.connections.indexOf(ws)
    if (index > -1) {
      this.connections.splice(index, 1)
    }
  }

  broadcast(message: any) {
    const data = JSON.stringify(message)
    this.connections.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.simulateMessage(data)
      }
    })
  }

  startHeartbeat(interval: number = 30000) {
    const heartbeatInterval = setInterval(() => {
      this.broadcast({ type: 'heartbeat', timestamp: Date.now() })
    }, interval)

    this.intervals.push(heartbeatInterval)
  }

  startDataSimulation() {
    // 模拟设备状态更新
    const equipmentInterval = setInterval(() => {
      this.broadcast({
        type: 'equipment_status',
        data: mockData.equipment.map(eq => ({
          ...eq,
          utilization: Math.random() * 20 + 80,
          temperature: Math.random() * 5 + 20
        }))
      })
    }, 5000)

    // 模拟生产数据更新
    const productionInterval = setInterval(() => {
      this.broadcast({
        type: 'production_update',
        data: {
          currentOutput: Math.floor(Math.random() * 1000) + 20000,
          efficiency: Math.random() * 10 + 85
        }
      })
    }, 10000)

    this.intervals.push(equipmentInterval, productionInterval)
  }

  stop() {
    this.intervals.forEach(interval => clearInterval(interval))
    this.intervals = []
    this.connections = []
  }
}
