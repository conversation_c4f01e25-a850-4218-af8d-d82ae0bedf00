/**
 * IC封测CIM系统 - API配置
 * API Configuration for IC Packaging & Testing CIM System
 */

// API响应基础接口
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message: string
  code: number
  timestamp: string
}

// 分页响应接口
export interface ApiPageResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    current: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// API错误类型
export enum ApiErrorCode {
  SUCCESS = 200,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  CONFLICT = 409,
  VALIDATION_ERROR = 422,
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503
}

// API错误信息映射
export const ApiErrorMessages = {
  [ApiErrorCode.SUCCESS]: '操作成功',
  [ApiErrorCode.BAD_REQUEST]: '请求参数错误',
  [ApiErrorCode.UNAUTHORIZED]: '未授权访问',
  [ApiErrorCode.FORBIDDEN]: '权限不足',
  [ApiErrorCode.NOT_FOUND]: '资源不存在',
  [ApiErrorCode.METHOD_NOT_ALLOWED]: '请求方法不允许',
  [ApiErrorCode.CONFLICT]: '资源冲突',
  [ApiErrorCode.VALIDATION_ERROR]: '数据验证失败',
  [ApiErrorCode.INTERNAL_SERVER_ERROR]: '服务器内部错误',
  [ApiErrorCode.SERVICE_UNAVAILABLE]: '服务暂不可用'
}

// API配置
export const ApiConfig = {
  // 基础URL
  BASE_URL: import.meta.env.VITE_API_BASE_URL || '/api',

  // 超时设置
  TIMEOUT: 30000, // 30秒

  // 重试配置
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000, // 1秒

  // 模拟延迟
  MOCK_DELAY: {
    MIN: 100,
    MAX: 800,
    SEARCH: 200,
    CREATE: 500,
    UPDATE: 400,
    DELETE: 300,
    EXPORT: 1000
  },

  // 分页默认配置
  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
    PAGE_SIZES: [10, 20, 50, 100]
  },

  // 搜索配置
  SEARCH: {
    MIN_KEYWORD_LENGTH: 2,
    MAX_SUGGESTIONS: 10,
    DEBOUNCE_DELAY: 300
  }
}

// 模拟API响应工具函数
export class MockApiHelper {
  /**
   * 创建成功响应
   */
  static createSuccessResponse<T>(data: T, message = '操作成功'): ApiResponse<T> {
    return {
      success: true,
      data,
      message,
      code: ApiErrorCode.SUCCESS,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * 创建分页响应
   */
  static createPageResponse<T>(
    data: T[],
    total: number,
    page: number,
    pageSize: number,
    message = '查询成功'
  ): ApiPageResponse<T> {
    return {
      success: true,
      data,
      message,
      code: ApiErrorCode.SUCCESS,
      timestamp: new Date().toISOString(),
      pagination: {
        current: page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    }
  }

  /**
   * 创建错误响应
   */
  static createErrorResponse(code: ApiErrorCode, message?: string, data?: any): ApiResponse {
    return {
      success: false,
      data: data || null,
      message: message || ApiErrorMessages[code],
      code,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * 模拟API延迟
   */
  static async delay(type: keyof typeof ApiConfig.MOCK_DELAY = 'MIN'): Promise<void> {
    const delays = ApiConfig.MOCK_DELAY
    let delayTime: number

    if (type in delays) {
      delayTime = delays[type]
    } else {
      // 随机延迟
      delayTime = Math.floor(Math.random() * (delays.MAX - delays.MIN + 1)) + delays.MIN
    }

    return new Promise(resolve => setTimeout(resolve, delayTime))
  }

  /**
   * 模拟网络错误
   */
  static simulateNetworkError(errorRate = 0.05): void {
    if (Math.random() < errorRate) {
      throw new Error('网络连接异常')
    }
  }

  /**
   * 模拟服务器错误
   */
  static simulateServerError(errorRate = 0.02): ApiResponse {
    if (Math.random() < errorRate) {
      return this.createErrorResponse(
        ApiErrorCode.INTERNAL_SERVER_ERROR,
        '服务器内部错误，请稍后重试'
      )
    }
    return this.createSuccessResponse(null)
  }

  /**
   * 验证分页参数
   */
  static validatePaginationParams(page?: number, pageSize?: number) {
    const validatedPage = Math.max(1, page || ApiConfig.PAGINATION.DEFAULT_PAGE)
    const validatedPageSize = Math.min(
      ApiConfig.PAGINATION.MAX_PAGE_SIZE,
      Math.max(1, pageSize || ApiConfig.PAGINATION.DEFAULT_PAGE_SIZE)
    )

    return {
      page: validatedPage,
      pageSize: validatedPageSize
    }
  }

  /**
   * 过滤和排序数据
   */
  static filterAndSort<T>(
    data: T[],
    filters?: Record<string, any>,
    sortBy?: string,
    sortOrder: 'asc' | 'desc' = 'asc'
  ): T[] {
    let result = [...data]

    // 应用过滤器
    if (filters) {
      result = result.filter(item => {
        return Object.entries(filters).every(([key, value]) => {
          if (value === undefined || value === null || value === '') {
            return true
          }

          const itemValue = (item as any)[key]

          // 数组过滤
          if (Array.isArray(value)) {
            return value.length === 0 || value.includes(itemValue)
          }

          // 字符串模糊匹配
          if (typeof value === 'string' && typeof itemValue === 'string') {
            return itemValue.toLowerCase().includes(value.toLowerCase())
          }

          // 精确匹配
          return itemValue === value
        })
      })
    }

    // 应用排序
    if (sortBy) {
      result.sort((a, b) => {
        let aValue = (a as any)[sortBy]
        let bValue = (b as any)[sortBy]

        // 处理日期
        if (typeof aValue === 'string' && aValue.includes('T')) {
          aValue = new Date(aValue).getTime()
          bValue = new Date(bValue).getTime()
        }

        // 处理数字
        if (typeof aValue === 'string' && !isNaN(Number(aValue))) {
          aValue = Number(aValue)
          bValue = Number(bValue)
        }

        if (aValue < bValue) return sortOrder === 'desc' ? 1 : -1
        if (aValue > bValue) return sortOrder === 'desc' ? -1 : 1
        return 0
      })
    }

    return result
  }

  /**
   * 应用分页
   */
  static applyPagination<T>(
    data: T[],
    page: number,
    pageSize: number
  ): { data: T[]; total: number; totalPages: number } {
    const total = data.length
    const totalPages = Math.ceil(total / pageSize)
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedData = data.slice(startIndex, endIndex)

    return {
      data: paginatedData,
      total,
      totalPages
    }
  }

  /**
   * 生成唯一ID
   */
  static generateId(prefix = 'id'): string {
    const timestamp = Date.now().toString(36)
    const randomStr = Math.random().toString(36).substring(2, 8)
    return `${prefix}_${timestamp}_${randomStr}`
  }

  /**
   * 验证必填字段
   */
  static validateRequiredFields(data: Record<string, any>, requiredFields: string[]): string[] {
    const errors: string[] = []

    requiredFields.forEach(field => {
      const value = data[field]
      if (value === undefined || value === null || value === '') {
        errors.push(`${field} 是必填字段`)
      }
    })

    return errors
  }

  /**
   * 格式化数据验证错误
   */
  static formatValidationErrors(errors: string[]): ApiResponse {
    return this.createErrorResponse(ApiErrorCode.VALIDATION_ERROR, '数据验证失败', { errors })
  }
}

// API端点配置
export const ApiEndpoints = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile'
  },

  // 客户管理
  CUSTOMERS: {
    LIST: '/customers',
    DETAIL: '/customers/:id',
    CREATE: '/customers',
    UPDATE: '/customers/:id',
    DELETE: '/customers/:id',
    BATCH_DELETE: '/customers/batch',
    EXPORT: '/customers/export',
    SUGGESTIONS: '/customers/suggestions',
    STATS: '/customers/stats'
  },

  // 订单管理
  ORDERS: {
    LIST: '/orders',
    DETAIL: '/orders/:id',
    CREATE: '/orders',
    UPDATE: '/orders/:id',
    DELETE: '/orders/:id',
    BATCH_DELETE: '/orders/batch',
    UPDATE_STATUS: '/orders/:id/status',
    EXPORT: '/orders/export',
    SUGGESTIONS: '/orders/suggestions',
    STATS: '/orders/stats',
    URGENT: '/orders/urgent',
    OVERDUE: '/orders/overdue'
  },

  // 基础数据管理
  BASIC_DATA: {
    // 产品
    PRODUCTS: {
      LIST: '/basic-data/products',
      DETAIL: '/basic-data/products/:id',
      CREATE: '/basic-data/products',
      UPDATE: '/basic-data/products/:id',
      DELETE: '/basic-data/products/:id',
      SUGGESTIONS: '/basic-data/products/suggestions',
      EXPORT: '/basic-data/products/export'
    },
    // 设备
    EQUIPMENT: {
      LIST: '/basic-data/equipment',
      DETAIL: '/basic-data/equipment/:id',
      CREATE: '/basic-data/equipment',
      UPDATE: '/basic-data/equipment/:id',
      DELETE: '/basic-data/equipment/:id',
      SUGGESTIONS: '/basic-data/equipment/suggestions',
      EXPORT: '/basic-data/equipment/export'
    },
    // 工艺参数
    PROCESS_PARAMETERS: {
      LIST: '/basic-data/process-parameters',
      DETAIL: '/basic-data/process-parameters/:id',
      CREATE: '/basic-data/process-parameters',
      UPDATE: '/basic-data/process-parameters/:id',
      DELETE: '/basic-data/process-parameters/:id'
    },
    // 质量标准
    QUALITY_STANDARDS: {
      LIST: '/basic-data/quality-standards',
      DETAIL: '/basic-data/quality-standards/:id',
      CREATE: '/basic-data/quality-standards',
      UPDATE: '/basic-data/quality-standards/:id',
      DELETE: '/basic-data/quality-standards/:id'
    },
    // 供应商
    SUPPLIERS: {
      LIST: '/basic-data/suppliers',
      DETAIL: '/basic-data/suppliers/:id',
      CREATE: '/basic-data/suppliers',
      UPDATE: '/basic-data/suppliers/:id',
      DELETE: '/basic-data/suppliers/:id'
    },
    // 统计
    STATS: '/basic-data/stats'
  },

  // 系统管理
  SYSTEM: {
    SETTINGS: '/system/settings',
    USERS: '/system/users',
    ROLES: '/system/roles',
    PERMISSIONS: '/system/permissions',
    LOGS: '/system/logs'
  }
}

export default {
  ApiConfig,
  MockApiHelper,
  ApiEndpoints,
  ApiErrorCode,
  ApiErrorMessages
}
