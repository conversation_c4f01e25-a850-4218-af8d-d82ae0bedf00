{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["src/**/*", "src/**/*.vue", "src/**/*.ts", "src/**/*.tsx", "tests/**/*", "cypress/**/*", ".eslintrc-auto-import.json"], "exclude": ["node_modules", "dist", "**/*.js"], "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@views/*": ["src/views/*"], "@assets/*": ["src/assets/*"], "@utils/*": ["src/utils/*"], "@stores/*": ["src/stores/*"], "@composables/*": ["src/composables/*"], "@api/*": ["src/api/*"], "@types/*": ["src/types/*"]}, "target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "strict": true, "noEmit": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "useDefineForClassFields": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "skipLibCheck": true, "resolveJsonModule": true, "allowJs": false, "declaration": false, "declarationMap": false, "sourceMap": false, "removeComments": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "verbatimModuleSyntax": true, "types": ["node", "element-plus/global", "vite/client"]}, "vueCompilerOptions": {"target": 3.4}}