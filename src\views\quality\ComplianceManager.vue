<template>
  <div class="compliance-manager">
    <div class="compliance-manager__header">
      <div class="header-content">
        <h1>IATF16949 合规管理</h1>
        <p>完整的汽车行业质量管理体系合规管理，确保满足IATF16949标准要求</p>
      </div>
      <div class="header-actions">
        <el-button
type="primary" @click="showAddDocumentDialog = true"
>
          <el-icon><Plus /></el-icon>
          新增文档
        </el-button>
        <el-button @click="showScheduleAuditDialog = true">
          <el-icon><Calendar /></el-icon>
          安排审核
        </el-button>
        <el-button @click="exportComplianceReport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 合规概览 -->
    <div class="compliance-overview">
      <el-card class="overview-card">
        <div class="overview-item">
          <div class="item-icon approved">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="item-content">
            <div class="item-value">
              {{ approvedDocuments }}
            </div>
            <div class="item-label">已批准文档</div>
          </div>
        </div>
      </el-card>

      <el-card class="overview-card">
        <div class="overview-item">
          <div class="item-icon pending">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="item-content">
            <div class="item-value">
              {{ pendingDocuments }}
            </div>
            <div class="item-label">待审批文档</div>
          </div>
        </div>
      </el-card>

      <el-card class="overview-card">
        <div class="overview-item">
          <div class="item-icon audit">
            <el-icon><DocumentChecked /></el-icon>
          </div>
          <div class="item-content">
            <div class="item-value">
              {{ upcomingAudits }}
            </div>
            <div class="item-label">待进行审核</div>
          </div>
        </div>
      </el-card>

      <el-card class="overview-card">
        <div class="overview-item">
          <div class="item-icon action">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="item-content">
            <div class="item-value">
              {{ openActions }}
            </div>
            <div class="item-label">开放的纠正措施</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容 -->
    <div class="compliance-content">
      <el-tabs v-model="activeTab" type="card">
        <!-- 文档管理 -->
        <el-tab-pane label="文档管理" name="documents">
          <div class="documents-section">
            <div class="section-filters">
              <el-select v-model="documentFilter.type" placeholder="文档类型" clearable>
                <el-option label="全部" value="" />
                <el-option label="控制计划" value="CONTROL_PLAN" />
                <el-option label="FMEA分析" value="FMEA" />
                <el-option label="测量系统分析" value="MSA" />
                <el-option label="PPAP文档" value="PPAP" />
                <el-option label="SPC文档" value="SPC" />
                <el-option label="程序文件" value="PROCEDURE" />
              </el-select>

              <el-select v-model="documentFilter.status" placeholder="文档状态" clearable>
                <el-option label="全部" value="" />
                <el-option label="草稿" value="DRAFT" />
                <el-option label="审批中" value="REVIEW" />
                <el-option label="已批准" value="APPROVED" />
                <el-option label="已废止" value="OBSOLETE" />
              </el-select>

              <el-date-picker
                v-model="documentFilter.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />

              <el-input
v-model="documentFilter.keyword" placeholder="搜索文档标题"
clearable
/>
            </div>

            <el-table
v-loading="documentsLoading" :data="filteredDocuments"
>
              <el-table-column prop="documentType" label="类型" width="120">
                <template #default="{ row }">
                  <el-tag :type="getDocumentTypeTagType(row.documentType)" size="small">
                    {{ getDocumentTypeText(row.documentType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="title" label="文档标题" min-width="200" />
              <el-table-column prop="version" label="版本" width="80" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getDocumentStatusTagType(row.status)" size="small">
                    {{ getDocumentStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="owner" label="文档所有者" width="120" />
              <el-table-column prop="effectiveDate" label="生效日期" width="120">
                <template #default="{ row }">
                  {{ formatDate(row.effectiveDate) }}
                </template>
              </el-table-column>
              <el-table-column prop="nextReviewDate" label="下次评审" width="120">
                <template #default="{ row }">
                  <span :class="{ overdue: isReviewOverdue(row.nextReviewDate) }">
                    {{ formatDate(row.nextReviewDate) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" type="primary" link @click="viewDocument(row)">
                    查看
                  </el-button>
                  <el-button size="small" type="primary" link @click="editDocument(row)">
                    编辑
                  </el-button>
                  <el-button size="small" type="primary" link @click="viewChangeHistory(row)">
                    变更历史
                  </el-button>
                  <el-dropdown trigger="click">
                    <el-button size="small" type="primary" link>
                      更多
                      <el-icon class="el-icon--right">
                        <ArrowDown />
                      </el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="downloadDocument(row)">
                          <el-icon><Download /></el-icon>
                          下载
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="row.status === 'REVIEW'"
                          @click="approveDocument(row)"
                        >
                          <el-icon><Check /></el-icon>
                          批准
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="row.status === 'APPROVED'"
                          @click="obsoleteDocument(row)"
                        >
                          <el-icon><Close /></el-icon>
                          废止
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 审核管理 -->
        <el-tab-pane label="审核管理" name="audits">
          <div class="audits-section">
            <div class="section-filters">
              <el-select v-model="auditFilter.type" placeholder="审核类型" clearable>
                <el-option label="全部" value="" />
                <el-option label="内部审核" value="INTERNAL" />
                <el-option label="外部审核" value="EXTERNAL" />
                <el-option label="供应商审核" value="SUPPLIER" />
                <el-option label="客户审核" value="CUSTOMER" />
              </el-select>

              <el-select v-model="auditFilter.status" placeholder="审核状态" clearable>
                <el-option label="全部" value="" />
                <el-option label="计划中" value="PLANNED" />
                <el-option label="进行中" value="IN_PROGRESS" />
                <el-option label="已完成" value="COMPLETED" />
                <el-option label="已取消" value="CANCELLED" />
              </el-select>

              <el-date-picker
                v-model="auditFilter.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </div>

            <el-table
v-loading="auditsLoading" :data="filteredAudits"
>
              <el-table-column prop="auditType" label="类型" width="100">
                <template #default="{ row }">
                  <el-tag :type="getAuditTypeTagType(row.auditType)" size="small">
                    {{ getAuditTypeText(row.auditType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="auditor" label="审核员" width="120" />
              <el-table-column prop="auditDate" label="审核日期" width="120">
                <template #default="{ row }">
                  {{ formatDate(row.auditDate) }}
                </template>
              </el-table-column>
              <el-table-column prop="scope" label="审核范围" min-width="200">
                <template #default="{ row }">
                  {{ row.scope.join(', ') }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getAuditStatusTagType(row.status)" size="small">
                    {{ getAuditStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="发现数" width="80">
                <template #default="{ row }">
                  <el-badge :value="row.findings.length" type="warning">
                    {{ row.findings.length }}
                  </el-badge>
                </template>
              </el-table-column>
              <el-table-column label="纠正措施" width="80">
                <template #default="{ row }">
                  <el-badge :value="row.correctionActions.length" type="info">
                    {{ row.correctionActions.length }}
                  </el-badge>
                </template>
              </el-table-column>
              <el-table-column prop="nextAuditDate" label="下次审核" width="120">
                <template #default="{ row }">
                  {{ formatDate(row.nextAuditDate) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" type="primary" link @click="viewAuditDetails(row)">
                    详情
                  </el-button>
                  <el-button size="small" type="primary" link @click="viewFindings(row)">
                    发现
                  </el-button>
                  <el-button size="small" type="primary" link @click="viewActions(row)">
                    措施
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 纠正措施 -->
        <el-tab-pane label="纠正措施" name="actions">
          <div class="actions-section">
            <div class="section-filters">
              <el-select v-model="actionFilter.status" placeholder="措施状态" clearable>
                <el-option label="全部" value="" />
                <el-option label="计划中" value="PLANNED" />
                <el-option label="进行中" value="IN_PROGRESS" />
                <el-option label="已完成" value="COMPLETED" />
                <el-option label="逾期" value="OVERDUE" />
              </el-select>

              <el-select v-model="actionFilter.effectiveness" placeholder="有效性" clearable>
                <el-option label="全部" value="" />
                <el-option label="未评估" value="NOT_EVALUATED" />
                <el-option label="有效" value="EFFECTIVE" />
                <el-option label="无效" value="INEFFECTIVE" />
              </el-select>

              <el-input
v-model="actionFilter.responsible" placeholder="负责人"
clearable
/>
            </div>

            <el-table
v-loading="actionsLoading" :data="filteredActions"
>
              <el-table-column prop="description" label="纠正措施描述" min-width="200" />
              <el-table-column prop="responsible" label="负责人" width="120" />
              <el-table-column prop="dueDate" label="截止日期" width="120">
                <template #default="{ row }">
                  <span :class="{ overdue: isActionOverdue(row.dueDate, row.status) }">
                    {{ formatDate(row.dueDate) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getActionStatusTagType(row.status)" size="small">
                    {{ getActionStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="effectiveness" label="有效性" width="100">
                <template #default="{ row }">
                  <el-tag :type="getEffectivenessTagType(row.effectiveness)" size="small">
                    {{ getEffectivenessText(row.effectiveness) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="verificationDate" label="验证日期" width="120">
                <template #default="{ row }">
                  {{ formatDate(row.verificationDate) }}
                </template>
              </el-table-column>
              <el-table-column prop="verifiedBy" label="验证人" width="120" />
              <el-table-column label="操作" width="120" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" type="primary" link @click="editAction(row)">
                    编辑
                  </el-button>
                  <el-button
                    v-if="row.status === 'COMPLETED' && row.effectiveness === 'NOT_EVALUATED'"
                    size="small"
                    type="success"
                    link
                    @click="verifyAction(row)"
                  >
                    验证
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 合规报告 -->
        <el-tab-pane label="合规报告" name="reports">
          <div class="reports-section">
            <div class="report-cards">
              <el-card class="report-card">
                <template #header>
                  <span>文档合规率</span>
                </template>
                <div class="report-content">
                  <div class="report-chart">
                    <el-progress
                      type="circle"
                      :percentage="documentComplianceRate"
                      :color="getProgressColor(documentComplianceRate)"
                      :width="120"
                    />
                  </div>
                  <div class="report-details">
                    <div class="detail-item">
                      <span class="label">已批准:</span>
                      <span class="value">{{ approvedDocuments }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">总数:</span>
                      <span class="value">{{ totalDocuments }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">逾期评审:</span>
                      <span class="value overdue">{{ overdueReviews }}</span>
                    </div>
                  </div>
                </div>
              </el-card>

              <el-card class="report-card">
                <template #header>
                  <span>审核完成率</span>
                </template>
                <div class="report-content">
                  <div class="report-chart">
                    <el-progress
                      type="circle"
                      :percentage="auditCompletionRate"
                      :color="getProgressColor(auditCompletionRate)"
                      :width="120"
                    />
                  </div>
                  <div class="report-details">
                    <div class="detail-item">
                      <span class="label">已完成:</span>
                      <span class="value">{{ completedAudits }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">计划总数:</span>
                      <span class="value">{{ totalAudits }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">平均发现数:</span>
                      <span class="value">{{ averageFindings }}</span>
                    </div>
                  </div>
                </div>
              </el-card>

              <el-card class="report-card">
                <template #header>
                  <span>纠正措施完成率</span>
                </template>
                <div class="report-content">
                  <div class="report-chart">
                    <el-progress
                      type="circle"
                      :percentage="actionCompletionRate"
                      :color="getProgressColor(actionCompletionRate)"
                      :width="120"
                    />
                  </div>
                  <div class="report-details">
                    <div class="detail-item">
                      <span class="label">已完成:</span>
                      <span class="value">{{ completedActions }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">总数:</span>
                      <span class="value">{{ totalActions }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">逾期数:</span>
                      <span class="value overdue">{{ overdueActions }}</span>
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 新增文档对话框 -->
    <el-dialog v-model="showAddDocumentDialog" title="新增合规文档" width="600px">
      <el-form
        ref="documentFormRef"
        :model="documentForm"
        :rules="documentRules"
        label-width="120px"
      >
        <el-form-item label="文档类型" prop="documentType">
          <el-select v-model="documentForm.documentType" placeholder="选择文档类型">
            <el-option label="控制计划" value="CONTROL_PLAN" />
            <el-option label="FMEA分析" value="FMEA" />
            <el-option label="测量系统分析" value="MSA" />
            <el-option label="PPAP文档" value="PPAP" />
            <el-option label="SPC文档" value="SPC" />
            <el-option label="程序文件" value="PROCEDURE" />
          </el-select>
        </el-form-item>
        <el-form-item label="文档标题" prop="title">
          <el-input v-model="documentForm.title" placeholder="输入文档标题" />
        </el-form-item>
        <el-form-item label="版本号" prop="version">
          <el-input v-model="documentForm.version" placeholder="如: V1.0" />
        </el-form-item>
        <el-form-item label="文档所有者" prop="owner">
          <el-input v-model="documentForm.owner" placeholder="输入所有者姓名" />
        </el-form-item>
        <el-form-item label="批准人" prop="approver">
          <el-input v-model="documentForm.approver" placeholder="输入批准人姓名" />
        </el-form-item>
        <el-form-item label="生效日期" prop="effectiveDate">
          <el-date-picker
            v-model="documentForm.effectiveDate"
            type="date"
            placeholder="选择生效日期"
          />
        </el-form-item>
        <el-form-item label="文档内容" prop="content">
          <el-input
            v-model="documentForm.content"
            type="textarea"
            :rows="6"
            placeholder="输入文档内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDocumentDialog = false">取消</el-button>
        <el-button
type="primary" @click="saveDocument" :loading="documentSaving">保存</el-button>
      </template>
    </el-dialog>

    <!-- 安排审核对话框 -->
    <el-dialog v-model="showScheduleAuditDialog" title="安排审核" width="600px">
      <el-form :model="auditForm" :rules="auditRules" ref="auditFormRef" label-width="120px">
        <el-form-item label="审核类型" prop="auditType">
          <el-select v-model="auditForm.auditType" placeholder="选择审核类型">
            <el-option label="内部审核" value="INTERNAL" />
            <el-option label="外部审核" value="EXTERNAL" />
            <el-option label="供应商审核" value="SUPPLIER" />
            <el-option label="客户审核" value="CUSTOMER" />
          </el-select>
        </el-form-item>
        <el-form-item label="审核员" prop="auditor">
          <el-input v-model="auditForm.auditor" placeholder="输入审核员姓名" />
        </el-form-item>
        <el-form-item label="审核日期" prop="auditDate">
          <el-date-picker v-model="auditForm.auditDate" type="date" placeholder="选择审核日期" />
        </el-form-item>
        <el-form-item label="审核范围" prop="scope">
          <el-select v-model="auditForm.scope" multiple placeholder="选择审核范围">
            <el-option label="质量管理体系" value="7.1 质量管理体系" />
            <el-option label="管理职责" value="5.1 管理承诺" />
            <el-option label="资源管理" value="7.1.1 总则" />
            <el-option label="产品实现" value="8.1 运行的策划和控制" />
            <el-option label="测量分析改进" value="9.1 监视、测量、分析和评价" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showScheduleAuditDialog = false">取消</el-button>
        <el-button type="primary" @click="scheduleAudit" :loading="auditScheduling">
          确认安排
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import {
    Plus,
    Calendar,
    Download,
    CircleCheck,
    Clock,
    DocumentChecked,
    Warning,
    ArrowDown,
    Check,
    Close
  } from '@element-plus/icons-vue'
  import type { ComplianceDocument, ComplianceAudit, CorrectiveAction } from '@/types/quality'
  import { complianceDocuments, complianceAudits } from '@/utils/mockData/quality'

  // 响应式数据
  const activeTab = ref('documents')
  const documentsLoading = ref(false)
  const auditsLoading = ref(false)
  const actionsLoading = ref(false)
  const documentSaving = ref(false)
  const auditScheduling = ref(false)

  const showAddDocumentDialog = ref(false)
  const showScheduleAuditDialog = ref(false)

  // 筛选条件
  const documentFilter = reactive({
    type: '',
    status: '',
    dateRange: null as [string, string] | null,
    keyword: ''
  })

  const auditFilter = reactive({
    type: '',
    status: '',
    dateRange: null as [Date, Date] | null
  })

  const actionFilter = reactive({
    status: '',
    effectiveness: '',
    responsible: ''
  })

  // 表单数据
  const documentFormRef = ref()
  const documentForm = reactive({
    documentType: '',
    title: '',
    version: '',
    owner: '',
    approver: '',
    effectiveDate: null as Date | null,
    content: ''
  })

  const auditFormRef = ref()
  const auditForm = reactive({
    auditType: '',
    auditor: '',
    auditDate: null as Date | null,
    scope: [] as string[]
  })

  // 表单验证规则
  const documentRules = {
    documentType: [{ required: true, message: '请选择文档类型', trigger: 'change' }],
    title: [{ required: true, message: '请输入文档标题', trigger: 'blur' }],
    version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
    owner: [{ required: true, message: '请输入文档所有者', trigger: 'blur' }],
    approver: [{ required: true, message: '请输入批准人', trigger: 'blur' }],
    effectiveDate: [{ required: true, message: '请选择生效日期', trigger: 'change' }],
    content: [{ required: true, message: '请输入文档内容', trigger: 'blur' }]
  }

  const auditRules = {
    auditType: [{ required: true, message: '请选择审核类型', trigger: 'change' }],
    auditor: [{ required: true, message: '请输入审核员', trigger: 'blur' }],
    auditDate: [{ required: true, message: '请选择审核日期', trigger: 'change' }],
    scope: [{ required: true, message: '请选择审核范围', trigger: 'change' }]
  }

  // Mock数据
  const documents = ref<ComplianceDocument[]>(complianceDocuments)
  const audits = ref<ComplianceAudit[]>(complianceAudits)
  const actions = ref<CorrectiveAction[]>([])

  // 计算属性
  const filteredDocuments = computed(() => {
    let filtered = documents.value

    if (documentFilter.type) {
      filtered = filtered.filter(doc => doc.documentType === documentFilter.type)
    }

    if (documentFilter.status) {
      filtered = filtered.filter(doc => doc.status === documentFilter.status)
    }

    if (documentFilter.keyword) {
      filtered = filtered.filter(doc =>
        doc.title.toLowerCase().includes(documentFilter.keyword.toLowerCase())
      )
    }

    return filtered
  })

  const filteredAudits = computed(() => {
    let filtered = audits.value

    if (auditFilter.type) {
      filtered = filtered.filter(audit => audit.auditType === auditFilter.type)
    }

    if (auditFilter.status) {
      filtered = filtered.filter(audit => audit.status === auditFilter.status)
    }

    return filtered
  })

  const filteredActions = computed(() => {
    // 从审核中提取纠正措施
    const allActions = audits.value.flatMap(audit => audit.correctionActions)
    let filtered = allActions

    if (actionFilter.status) {
      filtered = filtered.filter(action => action.status === actionFilter.status)
    }

    if (actionFilter.effectiveness) {
      filtered = filtered.filter(action => action.effectiveness === actionFilter.effectiveness)
    }

    if (actionFilter.responsible) {
      filtered = filtered.filter(action =>
        action.responsible.toLowerCase().includes(actionFilter.responsible.toLowerCase())
      )
    }

    return filtered
  })

  // 统计数据
  const approvedDocuments = computed(
    () => documents.value.filter(doc => doc.status === 'APPROVED').length
  )

  const pendingDocuments = computed(
    () => documents.value.filter(doc => doc.status === 'REVIEW').length
  )

  const upcomingAudits = computed(
    () => audits.value.filter(audit => audit.status === 'PLANNED').length
  )

  const openActions = computed(
    () =>
      filteredActions.value.filter(
        action => action.status === 'PLANNED' || action.status === 'IN_PROGRESS'
      ).length
  )

  const totalDocuments = computed(() => documents.value.length)
  const totalAudits = computed(() => audits.value.length)
  const totalActions = computed(() => filteredActions.value.length)

  const completedAudits = computed(
    () => audits.value.filter(audit => audit.status === 'COMPLETED').length
  )

  const completedActions = computed(
    () => filteredActions.value.filter(action => action.status === 'COMPLETED').length
  )

  const overdueReviews = computed(
    () => documents.value.filter(doc => isReviewOverdue(doc.nextReviewDate)).length
  )

  const overdueActions = computed(
    () =>
      filteredActions.value.filter(action => isActionOverdue(action.dueDate, action.status)).length
  )

  const averageFindings = computed(() => {
    const completedAuditsData = audits.value.filter(audit => audit.status === 'COMPLETED')
    if (completedAuditsData.length === 0) return 0
    const totalFindings = completedAuditsData.reduce((sum, audit) => sum + audit.findings.length, 0)
    return Math.round((totalFindings / completedAuditsData.length) * 10) / 10
  })

  const documentComplianceRate = computed(() => {
    if (totalDocuments.value === 0) return 0
    return Math.round((approvedDocuments.value / totalDocuments.value) * 100)
  })

  const auditCompletionRate = computed(() => {
    if (totalAudits.value === 0) return 0
    return Math.round((completedAudits.value / totalAudits.value) * 100)
  })

  const actionCompletionRate = computed(() => {
    if (totalActions.value === 0) return 0
    return Math.round((completedActions.value / totalActions.value) * 100)
  })

  // 方法
  const formatDate = (date?: Date): string => {
    if (!date) return ''
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).format(new Date(date))
  }

  const isReviewOverdue = (reviewDate: Date): boolean => {
    return new Date(reviewDate) < new Date()
  }

  const isActionOverdue = (dueDate: Date, status: string): boolean => {
    return new Date(dueDate) < new Date() && status !== 'COMPLETED'
  }

  const getDocumentTypeTagType = (type: string): string => {
    const typeMap: Record<string, string> = {
      CONTROL_PLAN: 'primary',
      FMEA: 'success',
      MSA: 'warning',
      PPAP: 'info',
      SPC: 'danger',
      PROCEDURE: 'default'
    }
    return typeMap[type] || 'default'
  }

  const getDocumentTypeText = (type: string): string => {
    const typeMap: Record<string, string> = {
      CONTROL_PLAN: '控制计划',
      FMEA: 'FMEA',
      MSA: 'MSA',
      PPAP: 'PPAP',
      SPC: 'SPC',
      PROCEDURE: '程序'
    }
    return typeMap[type] || type
  }

  const getDocumentStatusTagType = (status: string): string => {
    const statusMap: Record<string, string> = {
      DRAFT: 'info',
      REVIEW: 'warning',
      APPROVED: 'success',
      OBSOLETE: 'danger'
    }
    return statusMap[status] || 'default'
  }

  const getDocumentStatusText = (status: string): string => {
    const statusMap: Record<string, string> = {
      DRAFT: '草稿',
      REVIEW: '审批中',
      APPROVED: '已批准',
      OBSOLETE: '已废止'
    }
    return statusMap[status] || status
  }

  const getAuditTypeTagType = (type: string): string => {
    const typeMap: Record<string, string> = {
      INTERNAL: 'primary',
      EXTERNAL: 'warning',
      SUPPLIER: 'info',
      CUSTOMER: 'success'
    }
    return typeMap[type] || 'default'
  }

  const getAuditTypeText = (type: string): string => {
    const typeMap: Record<string, string> = {
      INTERNAL: '内部',
      EXTERNAL: '外部',
      SUPPLIER: '供应商',
      CUSTOMER: '客户'
    }
    return typeMap[type] || type
  }

  const getAuditStatusTagType = (status: string): string => {
    const statusMap: Record<string, string> = {
      PLANNED: 'info',
      IN_PROGRESS: 'warning',
      COMPLETED: 'success',
      CANCELLED: 'danger'
    }
    return statusMap[status] || 'default'
  }

  const getAuditStatusText = (status: string): string => {
    const statusMap: Record<string, string> = {
      PLANNED: '计划中',
      IN_PROGRESS: '进行中',
      COMPLETED: '已完成',
      CANCELLED: '已取消'
    }
    return statusMap[status] || status
  }

  const getActionStatusTagType = (status: string): string => {
    const statusMap: Record<string, string> = {
      PLANNED: 'info',
      IN_PROGRESS: 'warning',
      COMPLETED: 'success',
      OVERDUE: 'danger'
    }
    return statusMap[status] || 'default'
  }

  const getActionStatusText = (status: string): string => {
    const statusMap: Record<string, string> = {
      PLANNED: '计划中',
      IN_PROGRESS: '进行中',
      COMPLETED: '已完成',
      OVERDUE: '逾期'
    }
    return statusMap[status] || status
  }

  const getEffectivenessTagType = (effectiveness: string): string => {
    const effectivenessMap: Record<string, string> = {
      NOT_EVALUATED: 'info',
      EFFECTIVE: 'success',
      INEFFECTIVE: 'danger'
    }
    return effectivenessMap[effectiveness] || 'default'
  }

  const getEffectivenessText = (effectiveness: string): string => {
    const effectivenessMap: Record<string, string> = {
      NOT_EVALUATED: '未评估',
      EFFECTIVE: '有效',
      INEFFECTIVE: '无效'
    }
    return effectivenessMap[effectiveness] || effectiveness
  }

  const getProgressColor = (percentage: number): string => {
    if (percentage >= 90) return '#67C23A'
    if (percentage >= 70) return '#E6A23C'
    return '#F56C6C'
  }

  // 文档操作
  const viewDocument = (document: ComplianceDocument) => {
    ElMessage.info(`查看文档: ${document.title}`)
  }

  const editDocument = (document: ComplianceDocument) => {
    ElMessage.info(`编辑文档: ${document.title}`)
  }

  const viewChangeHistory = (document: ComplianceDocument) => {
    ElMessage.info(`查看变更历史: ${document.title}`)
  }

  const downloadDocument = (document: ComplianceDocument) => {
    ElMessage.success(`下载文档: ${document.title}`)
  }

  const approveDocument = (document: ComplianceDocument) => {
    ElMessageBox.confirm(`确认批准文档 "${document.title}" 吗？`, '文档批准', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        document.status = 'APPROVED'
        ElMessage.success('文档已批准')
      })
      .catch(() => {
        // 用户取消
      })
  }

  const obsoleteDocument = (document: ComplianceDocument) => {
    ElMessageBox.confirm(`确认废止文档 "${document.title}" 吗？`, '文档废止', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        document.status = 'OBSOLETE'
        ElMessage.success('文档已废止')
      })
      .catch(() => {
        // 用户取消
      })
  }

  const saveDocument = async () => {
    if (!documentFormRef.value) return

    const valid = await documentFormRef.value.validate()
    if (!valid) return

    documentSaving.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))

      const newDocument: ComplianceDocument = {
        id: `doc_${Date.now()}`,
        documentType: documentForm.documentType as any,
        title: documentForm.title,
        version: documentForm.version,
        status: 'DRAFT',
        effectiveDate: documentForm.effectiveDate!,
        nextReviewDate: new Date(documentForm.effectiveDate!.getTime() + 365 * 24 * 60 * 60 * 1000),
        owner: documentForm.owner,
        approver: documentForm.approver,
        content: documentForm.content,
        attachments: [],
        changeHistory: []
      }

      documents.value.unshift(newDocument)
      showAddDocumentDialog.value = false
      ElMessage.success('文档创建成功')

      // 重置表单
      Object.keys(documentForm).forEach(key => {
        if (key === 'effectiveDate') {
          documentForm[key as keyof typeof documentForm] = null
        } else {
          documentForm[key as keyof typeof documentForm] = ''
        }
      })
    } catch (error) {
      ElMessage.error('文档创建失败')
    } finally {
      documentSaving.value = false
    }
  }

  // 审核操作
  const viewAuditDetails = (audit: ComplianceAudit) => {
    ElMessage.info(`查看审核详情: ${audit.auditor}`)
  }

  const viewFindings = (audit: ComplianceAudit) => {
    ElMessage.info(`查看审核发现: ${audit.findings.length}项`)
  }

  const viewActions = (audit: ComplianceAudit) => {
    ElMessage.info(`查看纠正措施: ${audit.correctionActions.length}项`)
  }

  const scheduleAudit = async () => {
    if (!auditFormRef.value) return

    const valid = await auditFormRef.value.validate()
    if (!valid) return

    auditScheduling.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))

      const newAudit: ComplianceAudit = {
        id: `audit_${Date.now()}`,
        auditType: auditForm.auditType as any,
        auditor: auditForm.auditor,
        auditDate: auditForm.auditDate!,
        scope: auditForm.scope,
        findings: [],
        correctionActions: [],
        status: 'PLANNED'
      }

      audits.value.unshift(newAudit)
      showScheduleAuditDialog.value = false
      ElMessage.success('审核安排成功')

      // 重置表单
      Object.keys(auditForm).forEach(key => {
        if (key === 'auditDate') {
          auditForm[key as keyof typeof auditForm] = null
        } else if (key === 'scope') {
          auditForm[key as keyof typeof auditForm] = []
        } else {
          auditForm[key as keyof typeof auditForm] = ''
        }
      })
    } catch (error) {
      ElMessage.error('审核安排失败')
    } finally {
      auditScheduling.value = false
    }
  }

  // 纠正措施操作
  const editAction = (action: CorrectiveAction) => {
    ElMessage.info(`编辑纠正措施: ${action.description}`)
  }

  const verifyAction = (action: CorrectiveAction) => {
    ElMessageBox.confirm('请确认该纠正措施的有效性', '验证有效性', {
      confirmButtonText: '有效',
      cancelButtonText: '无效',
      distinguishCancelAndClose: true,
      type: 'info'
    })
      .then(() => {
        action.effectiveness = 'EFFECTIVE'
        action.verificationDate = new Date()
        action.verifiedBy = '质量经理'
        ElMessage.success('验证完成，标记为有效')
      })
      .catch((action: string) => {
        if (action === 'cancel') {
          action.effectiveness = 'INEFFECTIVE'
          action.verificationDate = new Date()
          action.verifiedBy = '质量经理'
          ElMessage.warning('验证完成，标记为无效')
        }
      })
  }

  const exportComplianceReport = () => {
    ElMessage.success('合规报告导出成功')
  }

  // 生命周期
  onMounted(() => {
    // 初始化数据
  })
</script>

<style lang="scss" scoped>
  .compliance-manager {
    padding: var(--spacing-6);

    &__header {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      padding-bottom: var(--spacing-4);
      margin-bottom: var(--spacing-6);
      border-bottom: 2px solid var(--color-border-light);

      .header-content {
        h1 {
          margin: 0 0 var(--spacing-2) 0;
          font-size: 28px;
          font-weight: 700;
          color: var(--color-text-primary);
        }

        p {
          margin: 0;
          font-size: 16px;
          color: var(--color-text-secondary);
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-2);
      }
    }

    .compliance-overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-6);

      .overview-card {
        .overview-item {
          display: flex;
          gap: var(--spacing-4);
          align-items: center;

          .item-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            font-size: 24px;
            border-radius: var(--radius-round);

            &.approved {
              color: var(--color-success);
              background: var(--color-success-light);
            }

            &.pending {
              color: var(--color-warning);
              background: var(--color-warning-light);
            }

            &.audit {
              color: var(--color-primary);
              background: var(--color-primary-light);
            }

            &.action {
              color: var(--color-danger);
              background: var(--color-danger-light);
            }
          }

          .item-content {
            .item-value {
              margin-bottom: var(--spacing-1);
              font-size: 32px;
              font-weight: 700;
              line-height: 1;
              color: var(--color-text-primary);
            }

            .item-label {
              font-size: 14px;
              color: var(--color-text-secondary);
            }
          }
        }
      }
    }

    .compliance-content {
      .section-filters {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-3);
        padding: var(--spacing-4);
        margin-bottom: var(--spacing-4);
        background: var(--color-bg-secondary);
        border-radius: var(--radius-base);

        .el-select,
        .el-input {
          min-width: 160px;
        }

        .el-date-picker {
          min-width: 240px;
        }
      }

      .overdue {
        font-weight: 600;
        color: var(--color-danger);
      }

      .reports-section {
        .report-cards {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
          gap: var(--spacing-6);

          .report-card {
            .report-content {
              display: flex;
              gap: var(--spacing-6);
              align-items: center;

              .report-chart {
                flex-shrink: 0;
              }

              .report-details {
                flex: 1;

                .detail-item {
                  display: flex;
                  justify-content: space-between;
                  margin-bottom: var(--spacing-2);

                  .label {
                    color: var(--color-text-secondary);
                  }

                  .value {
                    font-weight: 600;
                    color: var(--color-text-primary);

                    &.overdue {
                      color: var(--color-danger);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (width <= 768px) {
    .compliance-manager {
      padding: var(--spacing-4);

      &__header {
        flex-direction: column;
        gap: var(--spacing-4);
        align-items: stretch;

        .header-actions {
          justify-content: flex-start;
        }
      }

      .compliance-overview {
        grid-template-columns: 1fr;
      }

      .compliance-content {
        .section-filters {
          flex-direction: column;

          .el-select,
          .el-input,
          .el-date-picker {
            min-width: auto;
          }
        }

        .reports-section {
          .report-cards {
            grid-template-columns: 1fr;

            .report-card {
              .report-content {
                flex-direction: column;
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
</style>
