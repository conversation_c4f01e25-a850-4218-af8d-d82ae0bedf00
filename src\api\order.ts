/**
 * IC封测CIM系统 - 订单管理API
 * Order Management API Services
 */

import type {
  Order,
  OrderQueryParams,
  OrderListResponse,
  CreateOrderData,
  OrderStats,
  OrderStatCard
} from '@/types/order'
import { OrderStatus, OrderPriority } from '@/types/order'
import { allMockOrders, orderStats, orderStatCards } from '@/utils/mockData/orders'

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

/**
 * 获取订单列表
 */
export async function getOrders(params: OrderQueryParams = {}): Promise<OrderListResponse> {
  await delay(300) // 模拟网络延迟

  let filteredOrders = [...allMockOrders]

  // 订单编号搜索
  if (params.orderNumber) {
    const keyword = params.orderNumber.toLowerCase()
    filteredOrders = filteredOrders.filter(order =>
      order.orderNumber.toLowerCase().includes(keyword)
    )
  }

  // 客户ID筛选
  if (params.customerId) {
    filteredOrders = filteredOrders.filter(order => order.customerId === params.customerId)
  }

  // 客户名称搜索
  if (params.customerName) {
    const keyword = params.customerName.toLowerCase()
    filteredOrders = filteredOrders.filter(
      order =>
        order.customer.name.toLowerCase().includes(keyword) ||
        order.customer.code.toLowerCase().includes(keyword)
    )
  }

  // 订单状态筛选
  if (params.status && params.status.length > 0) {
    filteredOrders = filteredOrders.filter(order => params.status!.includes(order.status))
  }

  // 优先级筛选
  if (params.priority && params.priority.length > 0) {
    filteredOrders = filteredOrders.filter(order => params.priority!.includes(order.priority))
  }

  // 封装类型筛选
  if (params.packageType && params.packageType.length > 0) {
    filteredOrders = filteredOrders.filter(order =>
      params.packageType!.includes(order.productInfo.packageType)
    )
  }

  // 下单日期范围筛选
  if (params.orderDateRange && params.orderDateRange.length === 2) {
    const [startDate, endDate] = params.orderDateRange
    filteredOrders = filteredOrders.filter(order => {
      const orderDate = new Date(order.schedule.orderDate)
      return orderDate >= new Date(startDate) && orderDate <= new Date(endDate)
    })
  }

  // 交付日期范围筛选
  if (params.deliveryDateRange && params.deliveryDateRange.length === 2) {
    const [startDate, endDate] = params.deliveryDateRange
    filteredOrders = filteredOrders.filter(order => {
      const deliveryDate = new Date(order.schedule.deliveryDate)
      return deliveryDate >= new Date(startDate) && deliveryDate <= new Date(endDate)
    })
  }

  // 排序
  if (params.sortBy) {
    filteredOrders.sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (params.sortBy) {
        case 'orderNumber':
          aValue = a.orderNumber
          bValue = b.orderNumber
          break
        case 'customerName':
          aValue = a.customer.name
          bValue = b.customer.name
          break
        case 'orderDate':
          aValue = new Date(a.schedule.orderDate).getTime()
          bValue = new Date(b.schedule.orderDate).getTime()
          break
        case 'deliveryDate':
          aValue = new Date(a.schedule.deliveryDate).getTime()
          bValue = new Date(b.schedule.deliveryDate).getTime()
          break
        case 'totalAmount':
          aValue = a.pricing.totalAmount
          bValue = b.pricing.totalAmount
          break
        case 'progress':
          aValue = a.progress.overall
          bValue = b.progress.overall
          break
        default:
          aValue = a[params.sortBy as keyof Order]
          bValue = b[params.sortBy as keyof Order]
      }

      if (aValue < bValue) return params.sortOrder === 'desc' ? 1 : -1
      if (aValue > bValue) return params.sortOrder === 'desc' ? -1 : 1
      return 0
    })
  } else {
    // 默认按创建时间倒序排列
    filteredOrders.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  }

  // 分页
  const page = params.page || 1
  const pageSize = params.pageSize || 20
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedOrders = filteredOrders.slice(startIndex, endIndex)

  return {
    data: paginatedOrders,
    total: filteredOrders.length,
    page,
    pageSize,
    totalPages: Math.ceil(filteredOrders.length / pageSize)
  }
}

/**
 * 根据ID获取订单详情
 */
export async function getOrderById(id: string): Promise<Order | null> {
  await delay(200)

  const order = allMockOrders.find(o => o.id === id)
  return order || null
}

/**
 * 创建新订单
 */
export async function createOrder(data: CreateOrderData): Promise<Order> {
  await delay(500)

  // 生成新的订单ID和编号
  const newId = `order_${Date.now()}`
  const orderNumber = `PO${new Date().getFullYear().toString().slice(-2)}${(Math.floor(Math.random() * 9999) + 1).toString().padStart(4, '0')}`

  // 获取客户信息
  const { getCustomerById } = await import('./customer')
  const customer = await getCustomerById(data.customerId)

  if (!customer) {
    throw new Error('客户不存在')
  }

  const newOrder: Order = {
    id: newId,
    orderNumber,
    customerId: data.customerId,
    customer: {
      id: customer.id,
      name: customer.name,
      code: customer.code,
      contact: customer.contact
        ? {
            name: customer.contact.name,
            phone: customer.contact.mobile || customer.contact.phone || '',
            email: customer.contact.email
          }
        : undefined
    },
    productInfo: {
      ...data.productInfo
    },
    pricing: {
      ...data.pricing,
      totalAmount: data.productInfo.quantity * data.pricing.unitPrice
    },
    schedule: {
      orderDate: new Date().toISOString(),
      deliveryDate: data.schedule.deliveryDate
    },
    status: OrderStatus.PENDING,
    priority: data.priority,
    progress: {
      overall: 0,
      cpTesting: 0,
      assembly: 0,
      ftTesting: 0,
      packaging: 0
    },
    qualityInfo: data.qualityInfo,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'current_user', // 实际应用中应该从用户上下文获取
    notes: data.notes
  }

  // 模拟保存到数据库
  allMockOrders.unshift(newOrder) // 添加到数组开头

  return newOrder
}

/**
 * 更新订单信息
 */
export async function updateOrder(
  id: string,
  data: Partial<CreateOrderData & { status: OrderStatus }>
): Promise<Order> {
  await delay(400)

  const orderIndex = allMockOrders.findIndex(o => o.id === id)
  if (orderIndex === -1) {
    throw new Error('订单不存在')
  }

  const existingOrder = allMockOrders[orderIndex]
  const updatedOrder: Order = {
    ...existingOrder,
    // 更新产品信息
    ...(data.productInfo && {
      productInfo: {
        ...existingOrder.productInfo,
        ...data.productInfo
      }
    }),
    // 更新价格信息
    ...(data.pricing && {
      pricing: {
        ...existingOrder.pricing,
        ...data.pricing,
        totalAmount:
          (data.productInfo?.quantity || existingOrder.productInfo.quantity) *
          (data.pricing?.unitPrice || existingOrder.pricing.unitPrice)
      }
    }),
    // 更新交期信息
    ...(data.schedule && {
      schedule: {
        ...existingOrder.schedule,
        ...data.schedule
      }
    }),
    // 更新状态
    ...(data.status && { status: data.status }),
    // 更新优先级
    ...(data.priority && { priority: data.priority }),
    // 更新质量要求
    ...(data.qualityInfo && {
      qualityInfo: {
        ...existingOrder.qualityInfo,
        ...data.qualityInfo
      }
    }),
    // 更新备注
    ...(data.notes !== undefined && { notes: data.notes }),
    updatedAt: new Date().toISOString()
  }

  allMockOrders[orderIndex] = updatedOrder
  return updatedOrder
}

/**
 * 删除订单
 */
export async function deleteOrder(id: string): Promise<boolean> {
  await delay(300)

  const orderIndex = allMockOrders.findIndex(o => o.id === id)
  if (orderIndex === -1) {
    return false
  }

  // 检查订单状态，某些状态下不允许删除
  const order = allMockOrders[orderIndex]
  if ([OrderStatus.PROCESSING, OrderStatus.TESTING].includes(order.status)) {
    throw new Error('生产中的订单不允许删除')
  }

  allMockOrders.splice(orderIndex, 1)
  return true
}

/**
 * 批量删除订单
 */
export async function batchDeleteOrders(
  ids: string[]
): Promise<{ success: number; failed: number; errors: string[] }> {
  await delay(600)

  let success = 0
  let failed = 0
  const errors: string[] = []

  for (const id of ids) {
    try {
      const deleted = await deleteOrder(id)
      if (deleted) {
        success++
      } else {
        failed++
        errors.push(`订单 ${id} 不存在`)
      }
    } catch (error) {
      failed++
      errors.push(`订单 ${id} 删除失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  return { success, failed, errors }
}

/**
 * 更新订单状态
 */
export async function updateOrderStatus(id: string, status: OrderStatus): Promise<Order> {
  await delay(300)

  const order = await updateOrder(id, { status })

  // 根据状态更新进度
  switch (status) {
    case OrderStatus.CONFIRMED:
      order.progress = { overall: 5, cpTesting: 0, assembly: 0, ftTesting: 0, packaging: 0 }
      order.schedule.confirmedDate = new Date().toISOString()
      break
    case OrderStatus.PROCESSING:
      order.schedule.startDate = new Date().toISOString()
      break
    case OrderStatus.COMPLETED:
      order.progress = {
        overall: 100,
        cpTesting: 100,
        assembly: 100,
        ftTesting: 100,
        packaging: 100
      }
      order.schedule.actualDeliveryDate = new Date().toISOString()
      break
  }

  return order
}

/**
 * 获取订单统计信息
 */
export async function getOrderStats(): Promise<OrderStats> {
  await delay(200)
  return orderStats
}

/**
 * 获取订单统计卡片数据
 */
export async function getOrderStatCards(): Promise<OrderStatCard[]> {
  await delay(200)
  return orderStatCards
}

/**
 * 导出订单数据
 */
export async function exportOrders(params: OrderQueryParams = {}): Promise<Blob> {
  await delay(1000) // 模拟导出处理时间

  const { data } = await getOrders(params)

  // 转换为CSV格式
  const headers = [
    '订单编号',
    '客户名称',
    '产品名称',
    '封装类型',
    '数量(K pcs)',
    '单价(元/K)',
    '总金额(元)',
    '订单状态',
    '优先级',
    '下单日期',
    '交付日期',
    '进度(%)'
  ]

  const csvContent = [
    headers.join(','),
    ...data.map(order =>
      [
        order.orderNumber,
        order.customer.name,
        order.productInfo.productName,
        order.productInfo.packageType,
        order.productInfo.quantity,
        order.pricing.unitPrice,
        order.pricing.totalAmount,
        order.status,
        order.priority,
        order.schedule.orderDate.split('T')[0],
        order.schedule.deliveryDate.split('T')[0],
        order.progress.overall
      ].join(',')
    )
  ].join('\n')

  return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
}

/**
 * 搜索订单建议（用于自动完成）
 */
export async function searchOrderSuggestions(
  keyword: string
): Promise<Array<{ id: string; orderNumber: string; customerName: string }>> {
  await delay(150)

  if (!keyword || keyword.length < 2) {
    return []
  }

  const suggestions = allMockOrders
    .filter(
      order =>
        order.orderNumber.toLowerCase().includes(keyword.toLowerCase()) ||
        order.customer.name.toLowerCase().includes(keyword.toLowerCase()) ||
        order.productInfo.productName.toLowerCase().includes(keyword.toLowerCase())
    )
    .slice(0, 10)
    .map(order => ({
      id: order.id,
      orderNumber: order.orderNumber,
      customerName: order.customer.name
    }))

  return suggestions
}

/**
 * 获取急需处理的订单
 */
export async function getUrgentOrders(): Promise<Order[]> {
  await delay(200)

  const now = new Date()
  return allMockOrders.filter(
    order =>
      order.priority === OrderPriority.URGENT ||
      (order.status === OrderStatus.PROCESSING &&
        new Date(order.schedule.deliveryDate).getTime() - now.getTime() < 7 * 24 * 60 * 60 * 1000) // 一周内交付
  )
}

/**
 * 获取逾期订单
 */
export async function getOverdueOrders(): Promise<Order[]> {
  await delay(200)

  const now = new Date()
  return allMockOrders.filter(order => {
    const deliveryDate = new Date(order.schedule.deliveryDate)
    return (
      deliveryDate < now &&
      order.status !== OrderStatus.COMPLETED &&
      order.status !== OrderStatus.CANCELLED
    )
  })
}
