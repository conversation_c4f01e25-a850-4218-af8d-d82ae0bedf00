<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IC封测CIM系统页面测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #2563eb;
            text-align: center;
        }
        .test-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .test-link {
            display: inline-block;
            background: #2563eb;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin: 4px;
        }
        .test-link:hover {
            background: #1d4ed8;
        }
        .status {
            font-weight: bold;
        }
        .success { color: #10b981; }
        .warning { color: #f59e0b; }
    </style>
</head>
<body>
    <h1>IC封测CIM系统 - 页面功能测试</h1>
    
    <div class="test-item">
        <h3>🏠 主页测试</h3>
        <p>测试极简主义设计风格和导航功能</p>
        <a href="http://localhost:8851/" target="_blank" class="test-link">访问主页</a>
        <div class="status success">✅ 应显示：英雄区域、功能特性、统计数据</div>
    </div>
    
    <div class="test-item">
        <h3>🎨 组件演示测试</h3>
        <p>测试基础组件库功能</p>
        <a href="http://localhost:8851/demo" target="_blank" class="test-link">访问组件演示</a>
        <div class="status success">✅ 应显示：Button、Input、Card、Table、Select、Modal 组件</div>
    </div>
    
    <div class="test-item">
        <h3>📋 订单管理测试</h3>
        <p>测试核心业务功能 - 订单CRUD操作</p>
        <a href="http://localhost:8851/orders" target="_blank" class="test-link">访问订单管理</a>
        <div class="status success">✅ 应显示：搜索表单、数据表格、新建/编辑模态框</div>
        <div class="status warning">⚠️ 测试功能：表格排序、分页、搜索过滤、模态框操作</div>
    </div>
    
    <div class="test-item">
        <h3>📊 生产计划测试</h3>
        <p>测试生产计划管理和甘特图显示</p>
        <a href="http://localhost:8851/production" target="_blank" class="test-link">访问生产计划</a>
        <div class="status success">✅ 应显示：统计面板、甘特图时间轴、阶段管理</div>
        <div class="status warning">⚠️ 测试功能：甘特图交互、阶段切换、实时数据</div>
    </div>
    
    <div class="test-item">
        <h3>🎯 关键测试点</h3>
        <ul>
            <li><strong>响应式设计</strong>: 调整浏览器窗口大小测试移动端适配</li>
            <li><strong>深色模式</strong>: 系统应支持浅色/深色主题切换</li>
            <li><strong>IC封测特性</strong>: 检查封装类型(QFP/BGA/CSP)、测试阶段(CP/Assembly/FT)显示</li>
            <li><strong>数据交互</strong>: 表格排序、搜索、分页、模态框等交互功能</li>
            <li><strong>性能表现</strong>: 页面加载速度和操作响应时间</li>
        </ul>
    </div>
    
    <div class="test-item">
        <h3>🚀 开发服务器状态</h3>
        <p>当前运行在：<strong>http://localhost:8851</strong></p>
        <div class="status success">✅ 服务器运行正常</div>
        <div class="status warning">⚠️ SASS警告已修复，代码质量良好</div>
    </div>
    
    <script>
        // 自动检测服务器状态
        fetch('http://localhost:8851')
            .then(response => {
                if (response.ok) {
                    console.log('✅ 开发服务器运行正常');
                } else {
                    console.log('⚠️ 服务器响应异常');
                }
            })
            .catch(err => {
                console.log('❌ 无法连接到开发服务器:', err.message);
            });
    </script>
</body>
</html>