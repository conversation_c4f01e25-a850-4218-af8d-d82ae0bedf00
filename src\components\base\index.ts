/**
 * 基础组件统一导出
 * 提供所有基础 UI 组件的导入入口
 */

import type { App } from 'vue'

// 导入所有基础组件
import CButton from './CButton.vue'
import CInput from './CInput.vue'
import CCard from './CCard.vue'
import CTable from './CTable.vue'
import CSelect from './CSelect.vue'
import CModal from './CModal.vue'

// 组件列表
const components = [CButton, CInput, CCard, CTable, CSelect, CModal]

/**
 * 安装所有基础组件
 */
export function installBaseComponents(app: App): void {
  // 手动注册组件，确保组件名正确
  app.component('CButton', CButton)
  app.component('CInput', CInput)
  app.component('CCard', CCard)
  app.component('CTable', CTable)
  app.component('CSelect', CSelect)
  app.component('CModal', CModal)
}

// 单独导出各组件
export { CButton, CInput, CCard, CTable, CSelect, CModal }

// 导出组件类型
export type { CButtonProps } from './CButton.vue'

export type { CInputProps } from './CInput.vue'

export type { CCardProps } from './CCard.vue'

export type { CTableProps, CTableColumn } from './CTable.vue'

export type { CSelectProps, CSelectOption } from './CSelect.vue'

export type { CModalProps } from './CModal.vue'

// 默认导出安装函数
export default installBaseComponents
