// IC封测CIM系统 - 生产计划模拟数据 (第一阶段严格规划)
// 严格按照CLAUDE.md第一阶段要求实现

import type {
  Order,
  WorkOrder,
  ProductionPlan,
  OrderLifecycle,
  OrderStatus,
  OrderPriority,
  PackageType,
  WorkOrderType,
  WorkOrderStatus,
  OrderLifecycleStage
} from '@/types/order'

// ===== 第一阶段：生产计划模拟数据 =====

// Master Production Scheduling - OSAT优化
export const productionPlans: ProductionPlan[] = [
  {
    id: 'PP001',
    planNumber: 'MPS-2024-W01',
    planName: '2024年第1周主生产计划',
    planPeriod: {
      startDate: '2024-01-01',
      endDate: '2024-01-07',
      planType: 'weekly'
    },

    // 订单组 - 按封装类型和客户分组优化
    orderGroups: [
      {
        groupId: 'GRP-BGA-001',
        orders: ['ORD-001', 'ORD-003', 'ORD-005'],
        totalQuantity: 1500000, // 1500K pcs
        packageTypes: [PackageType.BGA],
        priority: OrderPriority.HIGH
      },
      {
        groupId: 'GRP-QFP-001',
        orders: ['ORD-002', 'ORD-004'],
        totalQuantity: 800000, // 800K pcs
        packageTypes: [PackageType.QFP, PackageType.TQFP],
        priority: OrderPriority.MEDIUM
      }
    ],

    // 产能规划 - 基于测试和封装设备可用性
    capacityPlan: {
      cpTestingCapacity: 2000000, // 2M pcs/日 CP测试产能
      assemblyCapacity: 1500000, // 1.5M pcs/日 封装产能
      ftTestingCapacity: 1800000, // 1.8M pcs/日 FT测试产能
      bottleneckProcess: 'assembly' // 封装为瓶颈工序
    },

    // 设备分配
    equipmentAllocation: [
      {
        processType: WorkOrderType.CP_TESTING,
        equipmentIds: ['EQ-CP-001', 'EQ-CP-002', 'EQ-CP-003'],
        allocatedHours: 168, // 7天 * 24小时
        utilization: 85 // 85%利用率
      },
      {
        processType: WorkOrderType.ASSEMBLY,
        equipmentIds: ['EQ-ASM-001', 'EQ-ASM-002'],
        allocatedHours: 168,
        utilization: 95 // 95%利用率（瓶颈）
      },
      {
        processType: WorkOrderType.FT_TESTING,
        equipmentIds: ['EQ-FT-001', 'EQ-FT-002', 'EQ-FT-003'],
        allocatedHours: 168,
        utilization: 78 // 78%利用率
      }
    ],

    status: 'executing',
    approvedBy: 'planning_manager',
    createdAt: '2023-12-25T10:00:00Z',
    updatedAt: '2024-01-01T08:00:00Z'
  },

  {
    id: 'PP002',
    planNumber: 'MPS-2024-W02',
    planName: '2024年第2周主生产计划',
    planPeriod: {
      startDate: '2024-01-08',
      endDate: '2024-01-14',
      planType: 'weekly'
    },

    orderGroups: [
      {
        groupId: 'GRP-CSP-001',
        orders: ['ORD-006', 'ORD-007'],
        totalQuantity: 1200000, // 1200K pcs
        packageTypes: [PackageType.CSP, PackageType.WLCSP],
        priority: OrderPriority.URGENT
      }
    ],

    capacityPlan: {
      cpTestingCapacity: 2000000,
      assemblyCapacity: 1500000,
      ftTestingCapacity: 1800000,
      bottleneckProcess: 'assembly'
    },

    equipmentAllocation: [
      {
        processType: WorkOrderType.CP_TESTING,
        equipmentIds: ['EQ-CP-001', 'EQ-CP-002'],
        allocatedHours: 168,
        utilization: 75
      },
      {
        processType: WorkOrderType.ASSEMBLY,
        equipmentIds: ['EQ-ASM-001', 'EQ-ASM-002', 'EQ-ASM-003'],
        allocatedHours: 168,
        utilization: 88
      },
      {
        processType: WorkOrderType.FT_TESTING,
        equipmentIds: ['EQ-FT-001', 'EQ-FT-002'],
        allocatedHours: 168,
        utilization: 70
      }
    ],

    status: 'approved',
    approvedBy: 'planning_manager',
    createdAt: '2024-01-01T14:00:00Z',
    updatedAt: '2024-01-02T09:00:00Z'
  }
]

// ===== 工作订单管理 - CP/Assembly/FT processes =====

export const workOrders: WorkOrder[] = [
  // CP晶圆测试工作订单
  {
    id: 'WO-CP-001',
    workOrderNumber: 'CP-2024-0001',
    parentOrderId: 'ORD-001',
    type: WorkOrderType.CP_TESTING,
    status: WorkOrderStatus.IN_PROGRESS,
    priority: OrderPriority.HIGH,

    productInfo: {
      lotId: 'LOT-BGA-001',
      quantity: 500000, // 500K pcs
      packageType: PackageType.BGA,
      waferSize: 8, // 8英寸晶圆
      diePerWafer: 2500
    },

    processInfo: {
      equipmentId: 'EQ-CP-001',
      recipeId: 'RCP-BGA-STD',
      testProgram: 'TP-BGA-V2.1',
      processTime: 480, // 8小时
      standardYield: 98.5 // 98.5%标准良率
    },

    schedule: {
      plannedStartTime: '2024-01-01T08:00:00Z',
      plannedEndTime: '2024-01-01T16:00:00Z',
      actualStartTime: '2024-01-01T08:15:00Z'
    },

    qualityResult: {
      actualYield: 98.2,
      defectCount: 9000,
      reworkCount: 0,
      passedQuantity: 491000,
      failedQuantity: 9000
    },

    assignedOperator: 'operator_cp_001',
    notes: 'CP测试正常进行，良率符合预期',
    createdAt: '2023-12-31T16:00:00Z',
    updatedAt: '2024-01-01T12:00:00Z'
  },

  // 封装工艺工作订单
  {
    id: 'WO-ASM-001',
    workOrderNumber: 'ASM-2024-0001',
    parentOrderId: 'ORD-001',
    type: WorkOrderType.ASSEMBLY,
    status: WorkOrderStatus.PENDING,
    priority: OrderPriority.HIGH,

    productInfo: {
      lotId: 'LOT-BGA-001',
      quantity: 491000, // 来自CP测试的良品数量
      packageType: PackageType.BGA,
      waferSize: 8
    },

    processInfo: {
      equipmentId: 'EQ-ASM-001',
      recipeId: 'RCP-BGA-ASM-STD',
      processTime: 720, // 12小时
      standardYield: 99.2
    },

    schedule: {
      plannedStartTime: '2024-01-01T18:00:00Z',
      plannedEndTime: '2024-01-02T06:00:00Z'
    },

    assignedOperator: 'operator_asm_001',
    notes: '等待CP测试完成后开始封装',
    createdAt: '2023-12-31T16:00:00Z',
    updatedAt: '2024-01-01T08:00:00Z'
  },

  // 最终测试工作订单
  {
    id: 'WO-FT-001',
    workOrderNumber: 'FT-2024-0001',
    parentOrderId: 'ORD-001',
    type: WorkOrderType.FT_TESTING,
    status: WorkOrderStatus.PENDING,
    priority: OrderPriority.HIGH,

    productInfo: {
      lotId: 'LOT-BGA-001',
      quantity: 487000, // 预估封装后良品数量
      packageType: PackageType.BGA,
      waferSize: 8
    },

    processInfo: {
      equipmentId: 'EQ-FT-001',
      recipeId: 'RCP-BGA-FT-STD',
      testProgram: 'TP-BGA-FINAL-V1.8',
      processTime: 360, // 6小时
      standardYield: 99.5
    },

    schedule: {
      plannedStartTime: '2024-01-02T08:00:00Z',
      plannedEndTime: '2024-01-02T14:00:00Z'
    },

    assignedOperator: 'operator_ft_001',
    notes: '等待封装完成后进行最终测试',
    createdAt: '2023-12-31T16:00:00Z',
    updatedAt: '2024-01-01T08:00:00Z'
  }
]

// ===== 订单生命周期管理 - 含封装规格 =====

export const orderLifecycles: OrderLifecycle[] = [
  {
    orderId: 'ORD-001',
    currentStage: OrderLifecycleStage.IN_PRODUCTION,
    stageHistory: [
      {
        stage: OrderLifecycleStage.INQUIRY,
        enteredAt: '2023-12-01T09:00:00Z',
        exitedAt: '2023-12-01T17:00:00Z',
        duration: 8,
        operator: 'sales_001',
        notes: '客户询价BGA封装产品'
      },
      {
        stage: OrderLifecycleStage.QUOTATION,
        enteredAt: '2023-12-01T17:00:00Z',
        exitedAt: '2023-12-03T10:00:00Z',
        duration: 41,
        operator: 'sales_001',
        notes: '报价确认，单价0.85元/K pcs'
      },
      {
        stage: OrderLifecycleStage.ORDER_PLACED,
        enteredAt: '2023-12-03T10:00:00Z',
        exitedAt: '2023-12-03T14:00:00Z',
        duration: 4,
        operator: 'customer_portal',
        notes: '客户正式下单'
      },
      {
        stage: OrderLifecycleStage.ORDER_CONFIRMED,
        enteredAt: '2023-12-03T14:00:00Z',
        exitedAt: '2023-12-10T08:00:00Z',
        duration: 162,
        operator: 'order_manager',
        notes: '订单确认，排产计划制定'
      },
      {
        stage: OrderLifecycleStage.IN_PRODUCTION,
        enteredAt: '2023-12-10T08:00:00Z',
        operator: 'production_manager',
        notes: '开始生产，当前CP测试阶段'
      }
    ],

    keyMilestones: {
      orderReceived: '2023-12-03T10:00:00Z',
      productionStarted: '2023-12-10T08:00:00Z',
      cpTestingCompleted: '2024-01-01T16:00:00Z'
    },

    // 封装类型规格 - OSAT专业要求
    packagingSpecifications: {
      packageType: PackageType.BGA,
      packageDimensions: {
        length: 15.0, // 15mm
        width: 15.0, // 15mm
        height: 1.2 // 1.2mm
      },
      leadPitch: 0.8, // 0.8mm球间距
      thermalCharacteristics: {
        thermalResistance: 18.5, // 18.5°C/W
        powerDissipation: 3.2 // 3.2W
      },
      testSpecifications: {
        cpTestLimits: 'VDD: 2.7V~3.6V, IDD: <50mA, Freq: 100MHz',
        ftTestLimits: 'Full functional test at 85°C, -40°C',
        reliabilityRequirements: 'HTOL 1000hrs @125°C, TC 1000 cycles -55°C~125°C'
      }
    }
  }
]

// 产能规划数据 - 基于测试和封装设备可用性
export const capacityData = {
  // 设备产能矩阵
  equipmentCapacity: [
    {
      equipmentId: 'EQ-CP-001',
      type: 'CP_TESTER',
      processType: WorkOrderType.CP_TESTING,
      maxCapacityPerHour: 50000, // 50K pcs/hour
      availabilityHours: 22, // 22小时/天可用
      dailyCapacity: 1100000, // 1.1M pcs/天
      supportedPackages: [PackageType.BGA, PackageType.QFP, PackageType.CSP]
    },
    {
      equipmentId: 'EQ-CP-002',
      type: 'CP_TESTER',
      processType: WorkOrderType.CP_TESTING,
      maxCapacityPerHour: 45000,
      availabilityHours: 22,
      dailyCapacity: 990000,
      supportedPackages: [PackageType.BGA, PackageType.QFP]
    },
    {
      equipmentId: 'EQ-ASM-001',
      type: 'DIE_BONDER',
      processType: WorkOrderType.ASSEMBLY,
      maxCapacityPerHour: 30000, // 30K pcs/hour
      availabilityHours: 20, // 20小时/天可用（含维护时间）
      dailyCapacity: 600000, // 600K pcs/天
      supportedPackages: [PackageType.BGA, PackageType.CSP]
    },
    {
      equipmentId: 'EQ-ASM-002',
      type: 'DIE_BONDER',
      processType: WorkOrderType.ASSEMBLY,
      maxCapacityPerHour: 35000,
      availabilityHours: 20,
      dailyCapacity: 700000,
      supportedPackages: [PackageType.QFP, PackageType.TQFP]
    },
    {
      equipmentId: 'EQ-FT-001',
      type: 'FINAL_TESTER',
      processType: WorkOrderType.FT_TESTING,
      maxCapacityPerHour: 80000, // 80K pcs/hour
      availabilityHours: 22,
      dailyCapacity: 1760000, // 1.76M pcs/天
      supportedPackages: [PackageType.BGA, PackageType.QFP, PackageType.CSP]
    }
  ],

  // 瓶颈分析
  bottleneckAnalysis: {
    currentBottleneck: 'assembly', // 当前瓶颈：封装工艺
    bottleneckUtilization: 95.2, // 瓶颈利用率
    constraintCapacity: 1300000, // 约束产能（pcs/天）
    recommendedActions: [
      '增加封装设备数量',
      '延长封装设备运行时间',
      '优化封装工艺参数提高效率',
      '考虑外包部分封装工序'
    ]
  }
}
