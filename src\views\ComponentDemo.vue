<template>
  <div class="demo-page">
    <div class="demo-header">
      <h1>基础组件演示</h1>
      <p>IC封测CIM系统极简基础组件库展示</p>
    </div>
    
    <div class="demo-sections">
      <!-- 按钮组件演示 -->
      <section class="demo-section">
        <c-card title="按钮组件 (CButton)">
          <div class="demo-content">
            <div class="demo-group">
              <h4>按钮类型</h4>
              <div class="demo-row">
                <c-button type="primary">主要按钮</c-button>
                <c-button type="secondary">次要按钮</c-button>
                <c-button type="text">文本按钮</c-button>
                <c-button type="link">链接按钮</c-button>
              </div>
            </div>
            
            <div class="demo-group">
              <h4>按钮尺寸</h4>
              <div class="demo-row">
                <c-button size="small">小号按钮</c-button>
                <c-button size="medium">中号按钮</c-button>
                <c-button size="large">大号按钮</c-button>
              </div>
            </div>
            
            <div class="demo-group">
              <h4>按钮状态</h4>
              <div class="demo-row">
                <c-button type="primary">正常状态</c-button>
                <c-button type="primary" disabled>禁用状态</c-button>
                <c-button type="primary" :loading="buttonLoading" @click="toggleLoading">
                  {{ buttonLoading ? '加载中' : '点击加载' }}
                </c-button>
              </div>
            </div>
            
            <div class="demo-group">
              <h4>其他样式</h4>
              <div class="demo-row">
                <c-button type="primary" round>圆角按钮</c-button>
                <c-button type="secondary" block>块级按钮</c-button>
              </div>
            </div>
          </div>
        </c-card>
      </section>
      
      <!-- 输入框组件演示 -->
      <section class="demo-section">
        <c-card title="输入框组件 (CInput)">
          <div class="demo-content">
            <div class="demo-group">
              <h4>基础输入框</h4>
              <div class="demo-column">
                <c-input
                  v-model="inputValues.basic"
                  label="基础输入框"
                  placeholder="请输入内容"
                />
                <c-input
                  v-model="inputValues.password"
                  type="password"
                  label="密码输入框"
                  placeholder="请输入密码"
                />
                <c-input
                  v-model="inputValues.email"
                  type="email"
                  label="邮箱输入框"
                  placeholder="请输入邮箱地址"
                />
              </div>
            </div>
            
            <div class="demo-group">
              <h4>输入框尺寸</h4>
              <div class="demo-column">
                <c-input v-model="inputValues.small" size="small" placeholder="小尺寸输入框" />
                <c-input v-model="inputValues.medium" size="medium" placeholder="中尺寸输入框" />
                <c-input v-model="inputValues.large" size="large" placeholder="大尺寸输入框" />
              </div>
            </div>
            
            <div class="demo-group">
              <h4>输入框状态</h4>
              <div class="demo-column">
                <c-input
                  v-model="inputValues.clearable"
                  label="可清除输入框"
                  placeholder="支持一键清除"
                  clearable
                />
                <c-input
                  v-model="inputValues.disabled"
                  label="禁用输入框"
                  placeholder="禁用状态"
                  disabled
                />
                <c-input
                  v-model="inputValues.readonly"
                  label="只读输入框"
                  placeholder="只读状态"
                  readonly
                />
                <c-input
                  v-model="inputValues.error"
                  label="错误状态"
                  placeholder="错误状态示例"
                  error-message="请输入正确的格式"
                />
                <c-input
                  v-model="inputValues.help"
                  label="帮助文本"
                  placeholder="带帮助提示"
                  help-text="这是帮助提示信息"
                />
              </div>
            </div>
            
            <div class="demo-group">
              <h4>文本域</h4>
              <c-input
                v-model="inputValues.textarea"
                type="textarea"
                label="多行文本输入"
                placeholder="请输入多行文本内容..."
                :rows="4"
              />
            </div>
          </div>
        </c-card>
      </section>
      
      <!-- 卡片组件演示 -->
      <section class="demo-section">
        <c-card title="卡片组件 (CCard)">
          <div class="demo-content">
            <div class="demo-group">
              <h4>基础卡片</h4>
              <div class="demo-grid">
                <c-card title="标题卡片">
                  <p>这是一个带有标题的基础卡片内容。</p>
                </c-card>
                
                <c-card>
                  <template #header>
                    <span>自定义头部</span>
                  </template>
                  <p>这是一个自定义头部的卡片内容。</p>
                  <template #footer>
                    <div class="card-footer-actions">
                      <c-button size="small">取消</c-button>
                      <c-button type="primary" size="small">确定</c-button>
                    </div>
                  </template>
                </c-card>
              </div>
            </div>
            
            <div class="demo-group">
              <h4>阴影样式</h4>
              <div class="demo-grid">
                <c-card title="从不显示阴影" shadow="never">
                  <p>shadow="never" - 从不显示阴影效果</p>
                </c-card>
                
                <c-card title="悬停显示阴影" shadow="hover">
                  <p>shadow="hover" - 悬停时显示阴影效果</p>
                </c-card>
                
                <c-card title="始终显示阴影" shadow="always">
                  <p>shadow="always" - 始终显示阴影效果</p>
                </c-card>
              </div>
            </div>
            
            <div class="demo-group">
              <h4>特殊效果</h4>
              <div class="demo-grid">
                <c-card title="可悬停卡片" hoverable>
                  <p>hoverable - 悬停时有浮起效果</p>
                </c-card>
                
                <c-card title="加载中卡片" :loading="cardLoading">
                  <p>loading - 显示加载遮罩效果</p>
                  <template #footer>
                    <c-button @click="toggleCardLoading">
                      {{ cardLoading ? '停止加载' : '开始加载' }}
                    </c-button>
                  </template>
                </c-card>
              </div>
            </div>
            
            <div class="demo-group">
              <h4>IC封测专用样式</h4>
              <div class="demo-grid">
                <c-card title="CP测试状态" class="c-card--process-step c-card--cp">
                  <p>CP (Chip Probing) 测试工序状态监控</p>
                  <div class="process-status">
                    <span class="status-dot status-dot--success"></span>
                    <span>测试通过</span>
                  </div>
                </c-card>
                
                <c-card title="Assembly封装" class="c-card--process-step c-card--assembly">
                  <p>Assembly 封装工序状态监控</p>
                  <div class="process-status">
                    <span class="status-dot status-dot--warning"></span>
                    <span>进行中</span>
                  </div>
                </c-card>
                
                <c-card title="FT测试状态" class="c-card--process-step c-card--ft">
                  <p>FT (Final Test) 最终测试状态监控</p>
                  <div class="process-status">
                    <span class="status-dot status-dot--info"></span>
                    <span>等待中</span>
                  </div>
                </c-card>
              </div>
            </div>
          </div>
        </c-card>
      </section>
      
      <!-- 响应式演示 -->
      <section class="demo-section">
        <c-card title="响应式设计演示">
          <div class="demo-content">
            <p class="demo-description">
              调整浏览器窗口大小查看组件的响应式效果。所有组件都支持移动端适配。
            </p>
            
            <div class="responsive-demo">
              <div class="responsive-grid">
                <c-card v-for="n in 6" :key="n" :title="`响应式卡片 ${n}`">
                  <p>这是第 {{ n }} 个响应式卡片，会根据屏幕大小自适应布局。</p>
                </c-card>
              </div>
            </div>
          </div>
        </c-card>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// 按钮加载状态
const buttonLoading = ref(false)

// 卡片加载状态
const cardLoading = ref(false)

// 输入框绑定值
const inputValues = reactive({
  basic: '',
  password: '',
  email: '',
  small: '',
  medium: '',
  large: '',
  clearable: 'can be cleared',
  disabled: 'disabled input',
  readonly: 'readonly input',
  error: 'error format',
  help: '',
  textarea: ''
})

// 切换按钮加载状态
const toggleLoading = () => {
  buttonLoading.value = true
  setTimeout(() => {
    buttonLoading.value = false
  }, 2000)
}

// 切换卡片加载状态
const toggleCardLoading = () => {
  cardLoading.value = !cardLoading.value
}
</script>

<style lang="scss">
.demo-page {
  padding: var(--spacing-6) 0;
}

.demo-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
  
  h1 {
    font-size: var(--font-size-2xl);
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-2);
  }
  
  p {
    color: var(--color-text-secondary);
    font-size: var(--font-size-base);
  }
}

.demo-sections {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
}

.demo-section {
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
}

.demo-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.demo-group {
  h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-3);
    border-bottom: 1px solid var(--color-border-light);
    padding-bottom: var(--spacing-1);
  }
}

.demo-row {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
  align-items: center;
}

.demo-column {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  max-width: 400px;
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-4);
}

.demo-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-base);
  margin-bottom: var(--spacing-4);
}

.card-footer-actions {
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
}

.process-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-3);
  
  span {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
  }
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  
  &--success {
    background: var(--color-success);
  }
  
  &--warning {
    background: var(--color-warning);
  }
  
  &--info {
    background: var(--color-info);
  }
}

.responsive-demo {
  margin-top: var(--spacing-4);
}

.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
  
  @media (min-width: 769px) and (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (min-width: 1025px) {
    grid-template-columns: repeat(3, 1fr);
  }
}

// 移动端适配
@media (max-width: 768px) {
  .demo-page {
    padding: var(--spacing-4) var(--spacing-3);
  }
  
  .demo-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .demo-column {
    max-width: none;
  }
  
  .demo-grid {
    grid-template-columns: 1fr;
  }
}
</style>