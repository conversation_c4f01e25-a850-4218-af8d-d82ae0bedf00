// Element Plus 类型扩展
// 修复 Element Plus 组件类型兼容性问题

declare module 'element-plus' {
  // 修复 ElTag type 属性
  interface TagProps {
    type?: 'primary' | 'success' | 'warning' | 'info' | 'danger' | string
  }
  
  // 修复 ElButton type 属性
  interface ButtonProps {
    type?: 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger' | string
  }
  
  // 修复 ElInput type 属性
  interface InputProps {
    type?: 'text' | 'textarea' | 'password' | 'number' | 'email' | 'url' | 'search' | 'tel' | 'datetime-local' | string
  }
}