/**
 * IC封测CIM系统 - 基础数据模拟数据
 * Basic Data Mock Data for IC Packaging & Testing CIM System
 */

import type {
  Product,
  Equipment,
  ProcessParameter,
  QualityStandard,
  Supplier,
  PackageTypeInfo,
  BasicDataStats
} from '@/types/basicData'

import {
  ProductCategory,
  PackageType,
  ProcessType,
  EquipmentType,
  EquipmentStatus,
  QualityStandardType,
  SupplierType,
  SupplierGrade
} from '@/types/basicData'

// ===== 封装类型信息数据 =====
export const packageTypeInfos: PackageTypeInfo[] = [
  {
    type: PackageType.QFP,
    name: 'Quad Flat Package',
    description: '四边扁平封装，引脚沿四边分布',
    features: ['成本低', '散热好', '可视焊接', '引脚易检查'],
    applications: ['微控制器', '数字信号处理器', '模拟IC'],
    pitchRange: '0.4mm - 1.0mm',
    pinCountRange: '32 - 256 pins',
    thermalCharacteristics: 'θJA: 25-45°C/W',
    electricalCharacteristics: '低电感，适中电容',
    packageSize: {
      minSize: '5mm x 5mm',
      maxSize: '28mm x 28mm',
      thickness: '1.4mm - 3.2mm'
    },
    assemblyComplexity: 'medium',
    cost: 'low',
    status: 'active'
  },
  {
    type: PackageType.BGA,
    name: 'Ball Grid Array',
    description: '球栅阵列封装，焊球分布在底部',
    features: ['高引脚密度', '优异电气性能', '良好散热', '小尺寸'],
    applications: ['微处理器', '内存芯片', '高性能FPGA'],
    pitchRange: '0.4mm - 1.27mm',
    pinCountRange: '49 - 2500+ balls',
    thermalCharacteristics: 'θJA: 15-30°C/W',
    electricalCharacteristics: '极低电感和电阻',
    packageSize: {
      minSize: '3mm x 3mm',
      maxSize: '52.5mm x 52.5mm',
      thickness: '0.8mm - 2.3mm'
    },
    assemblyComplexity: 'high',
    cost: 'medium',
    status: 'active'
  },
  {
    type: PackageType.WLCSP,
    name: 'Wafer Level Chip Scale Package',
    description: '晶圆级芯片规模封装，封装尺寸接近芯片尺寸',
    features: ['最小封装尺寸', '优异电气性能', '成本效益', '高集成度'],
    applications: ['移动设备', '可穿戴设备', 'IoT传感器'],
    pitchRange: '0.35mm - 0.8mm',
    pinCountRange: '4 - 200+ balls',
    thermalCharacteristics: 'θJA: 80-150°C/W',
    electricalCharacteristics: '超低电感',
    packageSize: {
      minSize: '1mm x 1mm',
      maxSize: '8mm x 8mm',
      thickness: '0.33mm - 0.6mm'
    },
    assemblyComplexity: 'high',
    cost: 'low',
    status: 'active'
  },
  {
    type: PackageType.QFN,
    name: 'Quad Flat No-Lead',
    description: '四边扁平无引脚封装，采用底部焊盘',
    features: ['低高度', '良好散热', '优异电气性能', '小尺寸'],
    applications: ['RF器件', '功率器件', '高频应用'],
    pitchRange: '0.4mm - 0.65mm',
    pinCountRange: '8 - 68 pads',
    thermalCharacteristics: 'θJA: 20-35°C/W',
    electricalCharacteristics: '低电感，低噪声',
    packageSize: {
      minSize: '1.5mm x 1.5mm',
      maxSize: '10mm x 10mm',
      thickness: '0.65mm - 0.9mm'
    },
    assemblyComplexity: 'medium',
    cost: 'low',
    status: 'active'
  }
]

// ===== 产品数据 =====
export const mockProducts: Product[] = [
  {
    id: 'product_001',
    productCode: 'MT32F103',
    productName: 'ARM Cortex-M3 32位微控制器',
    category: ProductCategory.MICROCONTROLLER,
    packageTypes: [PackageType.QFP, PackageType.BGA, PackageType.QFN],
    specifications: {
      workingVoltage: '2.0V - 3.6V',
      workingTemperature: '-40°C ~ +85°C',
      pinCount: 64,
      dieSize: '3.2mm x 3.2mm',
      applications: ['工业控制', '消费电子', '汽车电子']
    },
    processRequirements: {
      waferSize: 8,
      probeCardType: 'Cantilever',
      assemblyProcess: ['Die Attach', 'Wire Bond', 'Molding', 'Trim & Form'],
      testProgram: 'MT32F103_CP_V1.2'
    },
    qualityStandards: {
      yieldTarget: 98.5,
      reliabilityLevel: 'A',
      qualificationStandard: ['JEDEC', 'AEC-Q100']
    },
    status: 'active',
    createdAt: '2024-01-15T08:00:00.000Z',
    updatedAt: '2024-08-20T10:30:00.000Z',
    createdBy: 'admin'
  },
  {
    id: 'product_002',
    productCode: 'AD7886',
    productName: '16位高精度ADC转换器',
    category: ProductCategory.ANALOG_IC,
    packageTypes: [PackageType.QFN, PackageType.SSOP],
    specifications: {
      workingVoltage: '2.7V - 5.25V',
      workingTemperature: '-40°C ~ +105°C',
      pinCount: 20,
      dieSize: '2.1mm x 1.8mm',
      applications: ['精密测量', '数据采集', '医疗设备']
    },
    processRequirements: {
      waferSize: 6,
      probeCardType: 'Vertical',
      assemblyProcess: ['Die Attach', 'Wire Bond', 'Molding'],
      testProgram: 'AD7886_CP_V2.0'
    },
    qualityStandards: {
      yieldTarget: 97.0,
      reliabilityLevel: 'A',
      qualificationStandard: ['JEDEC', 'ISO9001']
    },
    status: 'active',
    createdAt: '2024-02-10T09:15:00.000Z',
    updatedAt: '2024-08-18T14:20:00.000Z',
    createdBy: 'admin'
  },
  {
    id: 'product_003',
    productCode: 'LM2596',
    productName: 'DC-DC降压转换器',
    category: ProductCategory.POWER_IC,
    packageTypes: [PackageType.SOP, PackageType.QFN],
    specifications: {
      workingVoltage: '4.5V - 40V',
      workingTemperature: '-40°C ~ +125°C',
      pinCount: 8,
      dieSize: '1.8mm x 1.5mm',
      applications: ['电源管理', '开关电源', '电池充电器']
    },
    processRequirements: {
      waferSize: 6,
      probeCardType: 'Cantilever',
      assemblyProcess: ['Die Attach', 'Wire Bond', 'Molding'],
      testProgram: 'LM2596_CP_V1.5'
    },
    qualityStandards: {
      yieldTarget: 96.5,
      reliabilityLevel: 'B',
      qualificationStandard: ['JEDEC']
    },
    status: 'active',
    createdAt: '2024-03-05T11:30:00.000Z',
    updatedAt: '2024-08-15T16:45:00.000Z',
    createdBy: 'admin'
  }
]

// ===== 设备数据 =====
export const mockEquipment: Equipment[] = [
  {
    id: 'equip_001',
    equipmentCode: 'PROB-001',
    equipmentName: 'Cascade Microtech PA200',
    type: EquipmentType.PROBER,
    model: 'PA200',
    manufacturer: 'Cascade Microtech',
    location: {
      building: 'FAB-1',
      floor: '2F',
      room: 'CP-Room-01',
      position: 'A-01'
    },
    specifications: {
      capacity: 120,
      accuracy: '±1μm',
      repeatability: '±0.5μm',
      workingEnvironment: {
        temperature: '20°C ± 2°C',
        humidity: '45% ± 5%',
        cleanLevel: 'Class 1000'
      }
    },
    status: EquipmentStatus.RUNNING,
    operationalInfo: {
      installDate: '2023-06-15T00:00:00.000Z',
      warrantyExpiry: '2025-06-15T00:00:00.000Z',
      lastMaintenanceDate: '2024-08-20T08:00:00.000Z',
      nextMaintenanceDate: '2024-11-20T08:00:00.000Z',
      operatingHours: 2850,
      cycleCount: 125000
    },
    capabilities: {
      supportedPackages: [PackageType.QFP, PackageType.BGA, PackageType.CSP],
      supportedProcesses: [ProcessType.CP_TEST],
      maxWaferSize: 8,
      parallelSites: 4
    },
    maintenanceInfo: {
      pmInterval: 720,
      pmItems: ['探针卡清洁', '光学系统校准', '运动系统检查', '气路检查'],
      sparePartsList: [
        { partName: '探针卡', partNumber: 'PC-QFP64', quantity: 2, supplier: 'FormFactor' },
        { partName: '真空传感器', partNumber: 'VS-001', quantity: 4, supplier: 'Cascade' }
      ]
    },
    createdAt: '2023-06-15T10:00:00.000Z',
    updatedAt: '2024-08-20T15:30:00.000Z',
    createdBy: 'admin'
  },
  {
    id: 'equip_002',
    equipmentCode: 'TEST-001',
    equipmentName: 'Advantest V93000',
    type: EquipmentType.TESTER,
    model: 'V93000',
    manufacturer: 'Advantest',
    location: {
      building: 'FAB-1',
      floor: '2F',
      room: 'CP-Room-01',
      position: 'A-02'
    },
    specifications: {
      capacity: 100,
      accuracy: '±0.01%',
      repeatability: '±0.005%',
      workingEnvironment: {
        temperature: '23°C ± 1°C',
        humidity: '50% ± 3%',
        cleanLevel: 'Class 1000'
      }
    },
    status: EquipmentStatus.IDLE,
    operationalInfo: {
      installDate: '2023-07-20T00:00:00.000Z',
      warrantyExpiry: '2026-07-20T00:00:00.000Z',
      lastMaintenanceDate: '2024-08-15T08:00:00.000Z',
      nextMaintenanceDate: '2024-12-15T08:00:00.000Z',
      operatingHours: 3200,
      cycleCount: 89000
    },
    capabilities: {
      supportedPackages: [PackageType.QFP, PackageType.BGA, PackageType.QFN],
      supportedProcesses: [ProcessType.CP_TEST, ProcessType.FT_TEST],
      maxWaferSize: 8,
      parallelSites: 8
    },
    maintenanceInfo: {
      pmInterval: 1000,
      pmItems: ['电源校准', '信号完整性检查', '温控系统维护', '软件更新'],
      sparePartsList: [
        { partName: '电源模块', partNumber: 'PS-V93K', quantity: 2, supplier: 'Advantest' },
        { partName: '继电器', partNumber: 'RL-001', quantity: 10, supplier: 'Advantest' }
      ]
    },
    createdAt: '2023-07-20T10:00:00.000Z',
    updatedAt: '2024-08-15T12:15:00.000Z',
    createdBy: 'admin'
  },
  {
    id: 'equip_003',
    equipmentCode: 'BOND-001',
    equipmentName: 'ASM Eagle 60AP',
    type: EquipmentType.WIRE_BONDER,
    model: 'Eagle 60AP',
    manufacturer: 'ASM Pacific',
    location: {
      building: 'FAB-1',
      floor: '1F',
      room: 'Assembly-01',
      position: 'B-01'
    },
    specifications: {
      capacity: 1800,
      accuracy: '±5μm',
      repeatability: '±2μm',
      workingEnvironment: {
        temperature: '25°C ± 3°C',
        humidity: '40% ± 10%',
        cleanLevel: 'Class 10000'
      }
    },
    status: EquipmentStatus.RUNNING,
    operationalInfo: {
      installDate: '2023-08-10T00:00:00.000Z',
      warrantyExpiry: '2025-08-10T00:00:00.000Z',
      lastMaintenanceDate: '2024-08-18T08:00:00.000Z',
      nextMaintenanceDate: '2024-09-18T08:00:00.000Z',
      operatingHours: 4150,
      cycleCount: 2850000
    },
    capabilities: {
      supportedPackages: [PackageType.QFP, PackageType.BGA, PackageType.SOP],
      supportedProcesses: [ProcessType.WIRE_BOND],
      maxWaferSize: 0,
      parallelSites: 1
    },
    maintenanceInfo: {
      pmInterval: 200,
      pmItems: ['键合工具更换', '超声发生器检查', '温度控制校准', '视觉系统清洁'],
      sparePartsList: [
        { partName: '键合工具', partNumber: 'BT-AU25', quantity: 20, supplier: 'ASM' },
        { partName: '超声换能器', partNumber: 'UST-001', quantity: 2, supplier: 'ASM' }
      ]
    },
    createdAt: '2023-08-10T10:00:00.000Z',
    updatedAt: '2024-08-18T14:20:00.000Z',
    createdBy: 'admin'
  }
]

// ===== 工艺参数数据 =====
export const mockProcessParameters: ProcessParameter[] = [
  {
    id: 'param_001',
    processType: ProcessType.WIRE_BOND,
    parameterName: '键合力',
    parameterCode: 'WB_BOND_FORCE',
    description: '金线键合时施加的键合力',
    unit: 'gf',
    dataType: 'number',
    defaultValue: 45,
    validRange: { min: 20, max: 80 },
    controlLimits: { lsl: 35, usl: 55, target: 45 },
    monitoringLevel: 'critical',
    category: '键合参数',
    applicablePackages: [PackageType.QFP, PackageType.BGA, PackageType.SOP],
    createdAt: '2024-01-10T08:00:00.000Z',
    updatedAt: '2024-08-15T10:30:00.000Z',
    createdBy: 'process_engineer'
  },
  {
    id: 'param_002',
    processType: ProcessType.MOLDING,
    parameterName: '塑封温度',
    parameterCode: 'MD_MOLD_TEMP',
    description: '塑封成型时的模具温度',
    unit: '°C',
    dataType: 'number',
    defaultValue: 175,
    validRange: { min: 150, max: 200 },
    controlLimits: { lsl: 170, usl: 180, target: 175 },
    monitoringLevel: 'critical',
    category: '塑封参数',
    applicablePackages: [PackageType.QFP, PackageType.BGA, PackageType.SOP],
    createdAt: '2024-01-15T09:00:00.000Z',
    updatedAt: '2024-08-10T14:15:00.000Z',
    createdBy: 'process_engineer'
  },
  {
    id: 'param_003',
    processType: ProcessType.CP_TEST,
    parameterName: 'VDD电压',
    parameterCode: 'CP_VDD_VOLT',
    description: 'CP测试时的供电电压',
    unit: 'V',
    dataType: 'number',
    defaultValue: 3.3,
    validRange: { min: 2.7, max: 3.6 },
    controlLimits: { lsl: 3.27, usl: 3.33, target: 3.3 },
    monitoringLevel: 'critical',
    category: '测试参数',
    applicablePackages: [PackageType.QFP, PackageType.BGA, PackageType.QFN],
    createdAt: '2024-02-01T10:00:00.000Z',
    updatedAt: '2024-08-20T11:45:00.000Z',
    createdBy: 'test_engineer'
  }
]

// ===== 质量标准数据 =====
export const mockQualityStandards: QualityStandard[] = [
  {
    id: 'std_001',
    standardCode: 'JEDEC-J-STD-020',
    standardName: 'JEDEC湿敏等级标准',
    type: QualityStandardType.JEDEC,
    version: 'E',
    description: 'JEDEC湿敏等级分类和回流焊接标准',
    applicableProducts: [ProductCategory.MICROCONTROLLER, ProductCategory.ANALOG_IC],
    requirements: [
      {
        category: '湿敏等级',
        parameter: 'MSL等级',
        specification: 'MSL1-6级分类',
        testMethod: '湿敏测试',
        acceptanceCriteria: '符合对应等级要求'
      },
      {
        category: '回流焊接',
        parameter: '峰值温度',
        specification: '260°C max (铅工艺)',
        testMethod: '回流焊接测试',
        acceptanceCriteria: '无封装开裂'
      }
    ],
    documentPath: '/standards/jedec/J-STD-020E.pdf',
    effectiveDate: '2024-01-01T00:00:00.000Z',
    reviewDate: '2025-01-01T00:00:00.000Z',
    status: 'active',
    createdAt: '2024-01-01T08:00:00.000Z',
    updatedAt: '2024-08-01T10:00:00.000Z',
    createdBy: 'quality_manager'
  },
  {
    id: 'std_002',
    standardCode: 'AEC-Q100-REV-H',
    standardName: 'AEC-Q100汽车IC可靠性标准',
    type: QualityStandardType.AEC_Q100,
    version: 'Rev-H',
    description: '汽车电子集成电路可靠性认证标准',
    applicableProducts: [ProductCategory.MICROCONTROLLER, ProductCategory.POWER_IC],
    requirements: [
      {
        category: '温度循环',
        parameter: '温度循环次数',
        specification: '1000次循环',
        testMethod: 'JESD22-A104',
        acceptanceCriteria: '零失效'
      },
      {
        category: '高温存储',
        parameter: '存储温度',
        specification: '150°C, 1000小时',
        testMethod: 'JESD22-A103',
        acceptanceCriteria: '参数漂移<5%'
      }
    ],
    documentPath: '/standards/aec/AEC-Q100-Rev-H.pdf',
    effectiveDate: '2024-02-01T00:00:00.000Z',
    reviewDate: '2025-02-01T00:00:00.000Z',
    status: 'active',
    createdAt: '2024-02-01T08:00:00.000Z',
    updatedAt: '2024-07-15T12:30:00.000Z',
    createdBy: 'quality_manager'
  }
]

// ===== 供应商数据 =====
export const mockSuppliers: Supplier[] = [
  {
    id: 'supplier_001',
    supplierCode: 'SUP-WF-001',
    supplierName: '中芯国际集成电路制造有限公司',
    supplierNameEn: 'Semiconductor Manufacturing International Corporation',
    type: SupplierType.WAFER,
    grade: SupplierGrade.A,
    contactInfo: {
      address: '上海市浦东新区张江路18号',
      city: '上海',
      country: '中国',
      postalCode: '201203',
      website: 'https://www.smics.com',
      primaryContact: {
        name: '李晓明',
        position: '销售经理',
        phone: '+86-21-3861-0000',
        email: '<EMAIL>'
      },
      salesContact: {
        name: '王华',
        phone: '+86-21-3861-1001',
        email: '<EMAIL>'
      },
      technicalContact: {
        name: '张工程师',
        phone: '+86-21-3861-1002',
        email: '<EMAIL>'
      }
    },
    businessInfo: {
      registrationNumber: '91310000633000045H',
      taxId: '91310000633000045H',
      establishedDate: '2000-04-03T00:00:00.000Z',
      employees: 17000,
      annualRevenue: ***********,
      certifications: ['ISO9001', 'IATF16949', 'ISO14001']
    },
    capabilities: {
      mainProducts: ['8英寸晶圆代工', '12英寸晶圆代工', '特色工艺'],
      productionCapacity: '月产能约50万片等效8英寸晶圆',
      qualitySystem: ['SPC', 'APC', '六西格玛'],
      deliveryPerformance: {
        onTimeDeliveryRate: 96.5,
        qualityScore: 98.2,
        responseTime: 4
      }
    },
    cooperationInfo: {
      cooperationStartDate: '2023-03-01T00:00:00.000Z',
      contractType: '长期供货协议',
      paymentTerms: 'T/T 30天',
      totalOrderValue: *********,
      lastOrderDate: '2024-08-15T00:00:00.000Z'
    },
    status: 'active',
    createdAt: '2023-03-01T08:00:00.000Z',
    updatedAt: '2024-08-20T16:30:00.000Z',
    createdBy: 'procurement'
  },
  {
    id: 'supplier_002',
    supplierCode: 'SUP-MAT-001',
    supplierName: '苏州晶方半导体科技股份有限公司',
    supplierNameEn: 'ChipMOS Technologies Inc.',
    type: SupplierType.MATERIAL,
    grade: SupplierGrade.A,
    contactInfo: {
      address: '江苏省苏州市工业园区星湖街218号',
      city: '苏州',
      country: '中国',
      postalCode: '215123',
      website: 'https://www.chipmos.com',
      primaryContact: {
        name: '刘经理',
        position: '大客户经理',
        phone: '+86-512-6285-0000',
        email: '<EMAIL>'
      }
    },
    businessInfo: {
      registrationNumber: '91320594743892156X',
      taxId: '91320594743892156X',
      establishedDate: '2005-01-15T00:00:00.000Z',
      employees: 3500,
      annualRevenue: 2800000000,
      certifications: ['ISO9001', 'ISO14001', 'OHSAS18001']
    },
    capabilities: {
      mainProducts: ['引线框架', '键合金线', '塑封料'],
      productionCapacity: '月产引线框架500万件',
      qualitySystem: ['SPC', 'MSA', 'FMEA'],
      deliveryPerformance: {
        onTimeDeliveryRate: 94.8,
        qualityScore: 96.5,
        responseTime: 6
      }
    },
    cooperationInfo: {
      cooperationStartDate: '2023-05-15T00:00:00.000Z',
      contractType: '年度采购协议',
      paymentTerms: '月结45天',
      totalOrderValue: 38000000,
      lastOrderDate: '2024-08-18T00:00:00.000Z'
    },
    status: 'active',
    createdAt: '2023-05-15T08:00:00.000Z',
    updatedAt: '2024-08-18T14:45:00.000Z',
    createdBy: 'procurement'
  }
]

// ===== 基础数据统计 =====
export const mockBasicDataStats: BasicDataStats = {
  products: {
    total: mockProducts.length,
    active: mockProducts.filter(p => p.status === 'active').length,
    byCategory: {
      [ProductCategory.MICROCONTROLLER]: 1,
      [ProductCategory.ANALOG_IC]: 1,
      [ProductCategory.DIGITAL_IC]: 0,
      [ProductCategory.MIXED_SIGNAL]: 0,
      [ProductCategory.POWER_IC]: 1,
      [ProductCategory.RF_IC]: 0,
      [ProductCategory.MEMORY_IC]: 0,
      [ProductCategory.SENSOR_IC]: 0
    }
  },
  equipment: {
    total: mockEquipment.length,
    running: mockEquipment.filter(e => e.status === EquipmentStatus.RUNNING).length,
    byType: {
      [EquipmentType.PROBER]: 1,
      [EquipmentType.TESTER]: 1,
      [EquipmentType.DIE_BONDER]: 0,
      [EquipmentType.WIRE_BONDER]: 1,
      [EquipmentType.MOLDING_PRESS]: 0,
      [EquipmentType.DEFLASH_SYSTEM]: 0,
      [EquipmentType.LASER_MARKER]: 0,
      [EquipmentType.TRIM_FORM]: 0,
      [EquipmentType.HANDLER]: 0,
      [EquipmentType.VISION_SYSTEM]: 0
    },
    utilizationRate: 78.5
  },
  suppliers: {
    total: mockSuppliers.length,
    active: mockSuppliers.filter(s => s.status === 'active').length,
    byType: {
      [SupplierType.WAFER]: 1,
      [SupplierType.MATERIAL]: 1,
      [SupplierType.EQUIPMENT]: 0,
      [SupplierType.SERVICE]: 0,
      [SupplierType.CONSUMABLE]: 0
    },
    byGrade: {
      [SupplierGrade.A]: 2,
      [SupplierGrade.B]: 0,
      [SupplierGrade.C]: 0,
      [SupplierGrade.D]: 0
    }
  },
  processParameters: {
    total: mockProcessParameters.length,
    critical: mockProcessParameters.filter(p => p.monitoringLevel === 'critical').length,
    byProcess: {
      [ProcessType.CP_TEST]: 1,
      [ProcessType.DIE_ATTACH]: 0,
      [ProcessType.WIRE_BOND]: 1,
      [ProcessType.MOLDING]: 1,
      [ProcessType.DEFLASH]: 0,
      [ProcessType.MARKING]: 0,
      [ProcessType.TRIM_FORM]: 0,
      [ProcessType.FT_TEST]: 0,
      [ProcessType.PACKAGING]: 0
    }
  },
  qualityStandards: {
    total: mockQualityStandards.length,
    active: mockQualityStandards.filter(s => s.status === 'active').length,
    byType: {
      [QualityStandardType.JEDEC]: 1,
      [QualityStandardType.IPC]: 0,
      [QualityStandardType.IATF16949]: 0,
      [QualityStandardType.ISO9001]: 0,
      [QualityStandardType.AEC_Q100]: 1,
      [QualityStandardType.MIL_STD]: 0,
      [QualityStandardType.COMPANY]: 0
    }
  }
}

// 生成更多模拟数据的辅助函数
export const generateMoreProducts = (count: number): Product[] => {
  const additionalProducts: Product[] = []
  const categories = Object.values(ProductCategory)
  const packageTypes = Object.values(PackageType)

  for (let i = 0; i < count; i++) {
    const category = categories[Math.floor(Math.random() * categories.length)]
    const selectedPackages = packageTypes.slice(0, Math.floor(Math.random() * 3) + 1)

    additionalProducts.push({
      id: `product_${(mockProducts.length + i + 1).toString().padStart(3, '0')}`,
      productCode: `IC${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
      productName: `${category} 产品 ${i + 1}`,
      category,
      packageTypes: selectedPackages,
      specifications: {
        workingVoltage: '3.3V ± 5%',
        workingTemperature: '-40°C ~ +85°C',
        pinCount: Math.floor(Math.random() * 200) + 8,
        dieSize: `${(Math.random() * 3 + 1).toFixed(1)}mm x ${(Math.random() * 3 + 1).toFixed(1)}mm`,
        applications: ['消费电子', '工业控制', '汽车电子']
      },
      processRequirements: {
        waferSize: [6, 8, 12][Math.floor(Math.random() * 3)],
        probeCardType: ['Cantilever', 'Vertical'][Math.floor(Math.random() * 2)],
        assemblyProcess: ['Die Attach', 'Wire Bond', 'Molding'],
        testProgram: `TEST_PROG_V${Math.floor(Math.random() * 9) + 1}.0`
      },
      qualityStandards: {
        yieldTarget: Math.random() * 5 + 95,
        reliabilityLevel: ['A', 'B', 'C'][Math.floor(Math.random() * 3)] as 'A' | 'B' | 'C',
        qualificationStandard: ['JEDEC', 'ISO9001']
      },
      status: 'active',
      createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      createdBy: 'system'
    })
  }

  return additionalProducts
}

// 导出所有基础数据
export const allBasicData = {
  products: mockProducts,
  equipment: mockEquipment,
  processParameters: mockProcessParameters,
  qualityStandards: mockQualityStandards,
  suppliers: mockSuppliers,
  packageTypeInfos,
  stats: mockBasicDataStats
}
