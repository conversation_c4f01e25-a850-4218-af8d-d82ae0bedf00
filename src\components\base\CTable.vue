<template>
  <div :class="tableWrapperClasses">
    <!-- 表格头部工具栏 -->
    <div v-if="showToolbar" class="c-table__toolbar">
      <div class="c-table__toolbar-left">
        <slot name="toolbar-left">
          <c-button
v-if="showRefresh" type="text"
size="small" @click="handleRefresh"
>
            刷新
          </c-button>
        </slot>
      </div>
      <div class="c-table__toolbar-right">
        <slot name="toolbar-right">
          <c-button
            v-if="showColumnSetting"
            type="text"
            size="small"
            @click="showColumnSettings = true"
          >
            列设置
          </c-button>
        </slot>
      </div>
    </div>

    <!-- 表格容器 -->
    <div class="c-table__container" :style="containerStyle">
      <table :class="tableClasses">
        <!-- 表头 -->
        <thead class="c-table__header">
          <tr>
            <th
              v-if="showSelection"
              class="c-table__cell c-table__cell--selection"
              :style="{ width: '48px' }"
            >
              <input
                type="checkbox"
                :checked="isAllSelected"
                :indeterminate="isIndeterminate"
                class="c-table__checkbox"
                @change="handleSelectAll"
              />
            </th>
            <th
              v-for="column in visibleColumns"
              :key="column.key"
              :class="getHeaderCellClasses(column)"
              :style="getColumnStyle(column)"
              @click="handleSort(column)"
            >
              <div class="c-table__cell-content">
                <span>{{ column.title }}</span>
                <span
v-if="column.sortable" :class="getSortIconClasses(column)"
>↕</span>
              </div>
            </th>
          </tr>
        </thead>

        <!-- 表体 -->
        <tbody class="c-table__body">
          <!-- 加载状态 -->
          <tr v-if="loading" class="c-table__loading-row">
            <td :colspan="totalColumns" class="c-table__loading-cell">
              <div class="c-table__loading">
                <div class="c-table__loading-spinner" />
                <span>{{ loadingText }}</span>
              </div>
            </td>
          </tr>

          <!-- 空数据状态 -->
          <tr v-else-if="!dataSource.length" class="c-table__empty-row">
            <td :colspan="totalColumns" class="c-table__empty-cell">
              <div class="c-table__empty">
                <div class="c-table__empty-icon">📋</div>
                <span>{{ emptyText }}</span>
              </div>
            </td>
          </tr>

          <!-- 数据行 -->
          <tr
            v-for="(record, index) in paginatedData"
            v-else
            :key="getRowKey(record, index)"
            :class="getRowClasses(record, index)"
            @click="handleRowClick(record, index)"
          >
            <!-- 选择列 -->
            <td v-if="showSelection" class="c-table__cell c-table__cell--selection">
              <input
                type="checkbox"
                :checked="selectedRowKeys.includes(getRowKey(record, index))"
                class="c-table__checkbox"
                @change="handleRowSelect(record, index, $event)"
                @click.stop
              />
            </td>

            <!-- 数据列 -->
            <td
              v-for="column in visibleColumns"
              :key="column.key"
              :class="getBodyCellClasses(column)"
              :style="getColumnStyle(column)"
            >
              <div class="c-table__cell-content">
                <slot
:name="column.key" :record="record"
:index="index" :column="column"
>
                  {{ getCellValue(record, column) }}
                </slot>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div v-if="showPagination" class="c-table__pagination">
      <div class="c-table__pagination-info">
        共 {{ total }} 条记录，第 {{ currentPage }}/{{ totalPages }} 页
      </div>
      <div class="c-table__pagination-controls">
        <c-button
          size="small"
          :disabled="currentPage <= 1"
          @click="handlePageChange(currentPage - 1)"
        >
          上一页
        </c-button>
        <span class="c-table__pagination-current">{{ currentPage }}</span>
        <c-button
          size="small"
          :disabled="currentPage >= totalPages"
          @click="handlePageChange(currentPage + 1)"
        >
          下一页
        </c-button>
        <c-select
          v-model="pageSizeValue"
          size="small"
          style="width: 80px; margin-left: 8px"
          @change="handlePageSizeChange"
        >
          <option v-for="size in pageSizeOptions"
:key="size" :value="size"
>
{{ size }}条/页
</option>
        </c-select>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue'

  export interface CTableColumn {
    key: string
    title: string
    dataIndex?: string
    width?: number | string
    minWidth?: number | string
    align?: 'left' | 'center' | 'right'
    sortable?: boolean
    fixed?: 'left' | 'right'
    ellipsis?: boolean
    render?: (value: any, record: any, index: number) => any
  }

  export interface CTableProps {
    /** 数据源 */
    dataSource?: any[]
    /** 列配置 */
    columns?: CTableColumn[]
    /** 行键字段名 */
    rowKey?: string | ((record: any) => string)
    /** 是否显示选择列 */
    showSelection?: boolean
    /** 选中的行键 */
    selectedRowKeys?: (string | number)[]
    /** 是否加载中 */
    loading?: boolean
    /** 加载文本 */
    loadingText?: string
    /** 空数据文本 */
    emptyText?: string
    /** 是否显示分页 */
    showPagination?: boolean
    /** 当前页码 */
    current?: number
    /** 每页条数 */
    pageSize?: number
    /** 总条数 */
    total?: number
    /** 每页条数选项 */
    pageSizeOptions?: number[]
    /** 表格大小 */
    size?: 'small' | 'medium' | 'large'
    /** 是否显示边框 */
    bordered?: boolean
    /** 是否显示斑马纹 */
    striped?: boolean
    /** 是否可悬停 */
    hoverable?: boolean
    /** 表格高度 */
    height?: number | string
    /** 是否显示工具栏 */
    showToolbar?: boolean
    /** 是否显示刷新按钮 */
    showRefresh?: boolean
    /** 是否显示列设置 */
    showColumnSetting?: boolean
  }

  const props = withDefaults(defineProps<CTableProps>(), {
    dataSource: () => [],
    columns: () => [],
    rowKey: 'id',
    showSelection: false,
    selectedRowKeys: () => [],
    loading: false,
    loadingText: '加载中...',
    emptyText: '暂无数据',
    showPagination: true,
    current: 1,
    pageSize: 20,
    total: 0,
    pageSizeOptions: () => [10, 20, 50, 100],
    size: 'medium',
    bordered: true,
    striped: true,
    hoverable: true,
    showToolbar: false,
    showRefresh: false,
    showColumnSetting: false
  })

  const emit = defineEmits<{
    'update:selectedRowKeys': [keys: (string | number)[]]
    'update:current': [page: number]
    'update:pageSize': [size: number]
    'selection-change': [selectedRows: any[], selectedRowKeys: (string | number)[]]
    'row-click': [record: any, index: number]
    'sort-change': [column: CTableColumn, direction: 'asc' | 'desc' | null]
    refresh: []
  }>()

  // 内部状态
  const currentPage = ref(props.current)
  const pageSizeValue = ref(props.pageSize)
  const sortColumn = ref<CTableColumn | null>(null)
  const sortDirection = ref<'asc' | 'desc' | null>(null)
  const showColumnSettings = ref(false)
  const hiddenColumns = ref<Set<string>>(new Set())

  // 计算属性
  const visibleColumns = computed(() =>
    props.columns.filter(col => !hiddenColumns.value.has(col.key))
  )

  const totalColumns = computed(() => {
    let count = visibleColumns.value.length
    if (props.showSelection) count++
    return count
  })

  const tableWrapperClasses = computed(() => [
    'c-table-wrapper',
    `c-table-wrapper--${props.size}`,
    {
      'c-table-wrapper--bordered': props.bordered,
      'c-table-wrapper--loading': props.loading
    }
  ])

  const tableClasses = computed(() => [
    'c-table',
    {
      'c-table--striped': props.striped,
      'c-table--hoverable': props.hoverable,
      'c-table--bordered': props.bordered
    }
  ])

  const containerStyle = computed(() => {
    const style: any = {}
    if (props.height) {
      style.height = typeof props.height === 'number' ? `${props.height}px` : props.height
      style.overflow = 'auto'
    }
    return style
  })

  const sortedData = computed(() => {
    if (!sortColumn.value || !sortDirection.value) return props.dataSource

    return [...props.dataSource].sort((a, b) => {
      const aVal = getCellValue(a, sortColumn.value!)
      const bVal = getCellValue(b, sortColumn.value!)

      if (sortDirection.value === 'asc') {
        return aVal > bVal ? 1 : aVal < bVal ? -1 : 0
      } else {
        return aVal < bVal ? 1 : aVal > bVal ? -1 : 0
      }
    })
  })

  const paginatedData = computed(() => {
    if (!props.showPagination) return sortedData.value

    const start = (currentPage.value - 1) * pageSizeValue.value
    const end = start + pageSizeValue.value
    return sortedData.value.slice(start, end)
  })

  const totalPages = computed(() =>
    Math.ceil((props.total || sortedData.value.length) / pageSizeValue.value)
  )

  const isAllSelected = computed(() => {
    if (!props.dataSource.length) return false
    return props.dataSource.every(record => props.selectedRowKeys.includes(getRowKey(record, 0)))
  })

  const isIndeterminate = computed(() => {
    const selectedCount = props.selectedRowKeys.length
    return selectedCount > 0 && selectedCount < props.dataSource.length
  })

  // 方法
  const getRowKey = (record: any, index: number): string | number => {
    if (typeof props.rowKey === 'function') {
      return props.rowKey(record)
    }
    return record[props.rowKey] ?? index
  }

  const getCellValue = (record: any, column: CTableColumn) => {
    const dataIndex = column.dataIndex || column.key
    return dataIndex.split('.').reduce((obj, key) => obj?.[key], record)
  }

  const getColumnStyle = (column: CTableColumn) => {
    const style: any = {}
    if (column.width) {
      style.width = typeof column.width === 'number' ? `${column.width}px` : column.width
    }
    if (column.minWidth) {
      style.minWidth =
        typeof column.minWidth === 'number' ? `${column.minWidth}px` : column.minWidth
    }
    return style
  }

  const getHeaderCellClasses = (column: CTableColumn) => [
    'c-table__cell',
    'c-table__header-cell',
    {
      'c-table__cell--sortable': column.sortable,
      'c-table__cell--sorted': sortColumn.value?.key === column.key,
      [`c-table__cell--${column.align || 'left'}`]: true
    }
  ]

  const getBodyCellClasses = (column: CTableColumn) => [
    'c-table__cell',
    'c-table__body-cell',
    {
      'c-table__cell--ellipsis': column.ellipsis,
      [`c-table__cell--${column.align || 'left'}`]: true
    }
  ]

  const getRowClasses = (record: any, index: number) => [
    'c-table__row',
    {
      'c-table__row--selected': props.selectedRowKeys.includes(getRowKey(record, index))
    }
  ]

  const getSortIconClasses = (column: CTableColumn) => [
    'c-table__sort-icon',
    {
      'c-table__sort-icon--asc':
        sortColumn.value?.key === column.key && sortDirection.value === 'asc',
      'c-table__sort-icon--desc':
        sortColumn.value?.key === column.key && sortDirection.value === 'desc'
    }
  ]

  const handleSelectAll = (event: Event) => {
    const target = event.target as HTMLInputElement
    const newSelectedKeys = target.checked
      ? props.dataSource.map((record, index) => getRowKey(record, index))
      : []

    emit('update:selectedRowKeys', newSelectedKeys)
    emit('selection-change', target.checked ? props.dataSource : [], newSelectedKeys)
  }

  const handleRowSelect = (record: any, index: number, event: Event) => {
    const target = event.target as HTMLInputElement
    const rowKey = getRowKey(record, index)
    const newSelectedKeys = target.checked
      ? [...props.selectedRowKeys, rowKey]
      : props.selectedRowKeys.filter(key => key !== rowKey)

    emit('update:selectedRowKeys', newSelectedKeys)
    emit(
      'selection-change',
      props.dataSource.filter(r => newSelectedKeys.includes(getRowKey(r, 0))),
      newSelectedKeys
    )
  }

  const handleRowClick = (record: any, index: number) => {
    emit('row-click', record, index)
  }

  const handleSort = (column: CTableColumn) => {
    if (!column.sortable) return

    if (sortColumn.value?.key === column.key) {
      // 切换排序方向: asc -> desc -> null
      if (sortDirection.value === 'asc') {
        sortDirection.value = 'desc'
      } else if (sortDirection.value === 'desc') {
        sortDirection.value = null
        sortColumn.value = null
      }
    } else {
      sortColumn.value = column
      sortDirection.value = 'asc'
    }

    emit('sort-change', column, sortDirection.value)
  }

  const handlePageChange = (page: number) => {
    currentPage.value = page
    emit('update:current', page)
  }

  const handlePageSizeChange = (size: number) => {
    pageSizeValue.value = size
    currentPage.value = 1
    emit('update:pageSize', size)
    emit('update:current', 1)
  }

  const handleRefresh = () => {
    emit('refresh')
  }

  // 监听props变化
  watch(
    () => props.current,
    val => {
      currentPage.value = val
    }
  )

  watch(
    () => props.pageSize,
    val => {
      pageSizeValue.value = val
    }
  )
</script>

<style lang="scss">
  .c-table-wrapper {
    background: var(--color-bg-primary);
    border-radius: var(--radius-base);

    &--bordered {
      border: 1px solid var(--color-border-light);
    }

    &--small {
      .c-table__cell {
        padding: var(--spacing-2);
        font-size: var(--font-size-xs);
      }
    }

    &--medium {
      .c-table__cell {
        padding: var(--spacing-3);
        font-size: var(--font-size-sm);
      }
    }

    &--large {
      .c-table__cell {
        padding: var(--spacing-4);
        font-size: var(--font-size-base);
      }
    }
  }

  .c-table__toolbar {
    @include flex-between;
    padding: var(--spacing-4);
    background: var(--color-bg-secondary);
    border-bottom: 1px solid var(--color-border-light);
  }

  .c-table__container {
    overflow-x: auto;
  }

  .c-table {
    width: 100%;
    border-collapse: collapse;

    &--striped {
      .c-table__row:nth-child(even) {
        background: var(--color-bg-secondary);
      }
    }

    &--hoverable {
      .c-table__row:hover {
        background: var(--color-bg-hover);
      }
    }

    &--bordered {
      .c-table__cell {
        border-right: 1px solid var(--color-border-light);

        &:last-child {
          border-right: none;
        }
      }
    }
  }

  .c-table__header {
    background: var(--color-bg-tertiary);

    th {
      border-bottom: 1px solid var(--color-border-base);
    }
  }

  .c-table__body {
    tr {
      border-bottom: 1px solid var(--color-border-light);

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .c-table__cell {
    padding: var(--spacing-3);
    text-align: left;
    vertical-align: middle;

    &--center {
      text-align: center;
    }

    &--right {
      text-align: right;
    }

    &--ellipsis {
      .c-table__cell-content {
        @include text-ellipsis;
      }
    }

    &--selection {
      width: 48px;
      text-align: center;
    }

    &--sortable {
      cursor: pointer;

      &:hover {
        background: var(--color-bg-hover);
      }
    }
  }

  .c-table__cell-content {
    display: flex;
    gap: var(--spacing-1);
    align-items: center;
  }

  .c-table__checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
  }

  .c-table__sort-icon {
    font-size: 12px;
    color: var(--color-text-tertiary);
    transition: color var(--transition-fast);

    &--asc,
    &--desc {
      color: var(--color-primary);
    }
  }

  .c-table__row {
    transition: background-color var(--transition-fast);

    &--selected {
      background: var(--color-primary-light) !important;
    }
  }

  .c-table__loading {
    @include flex-center;
    flex-direction: column;
    gap: var(--spacing-2);
    padding: var(--spacing-8);
    color: var(--color-text-secondary);
  }

  .c-table__loading-spinner {
    @include loading-spin;
    width: 24px;
    height: 24px;
    border: 2px solid var(--color-border-base);
    border-top-color: var(--color-primary);
    border-radius: 50%;
  }

  .c-table__empty {
    @include flex-center;
    flex-direction: column;
    gap: var(--spacing-2);
    padding: var(--spacing-8);
    color: var(--color-text-secondary);
  }

  .c-table__empty-icon {
    font-size: 32px;
    opacity: 0.5;
  }

  .c-table__pagination {
    @include flex-between;
    padding: var(--spacing-4);
    background: var(--color-bg-secondary);
    border-top: 1px solid var(--color-border-light);
  }

  .c-table__pagination-info {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
  }

  .c-table__pagination-controls {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
  }

  .c-table__pagination-current {
    padding: 0 var(--spacing-2);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
  }

  // IC封测专用样式
  .c-table--wafer-status {
    .status-pass {
      padding: 2px 6px;
      font-size: 12px;
      color: var(--color-success);
      background: var(--color-die-pass);
      border-radius: var(--radius-sm);
    }

    .status-fail {
      padding: 2px 6px;
      font-size: 12px;
      color: var(--color-error);
      background: var(--color-die-fail);
      border-radius: var(--radius-sm);
    }

    .wafer-id {
      padding: 2px 4px;
      font-family: var(--font-family-mono);
      background: var(--color-wafer);
      border-radius: var(--radius-sm);
    }
  }
</style>
