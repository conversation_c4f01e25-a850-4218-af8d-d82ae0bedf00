<template>
  <div class="quality-inspection">
    <div class="quality-inspection__header">
      <div class="header-content">
        <h1>质量检验管理</h1>
        <p>全面管理IQC来料检验、IPQC过程检验、FQC成品检验，确保产品质量符合标准</p>
      </div>
      <div class="header-actions">
        <el-button
type="primary" @click="showCreateInspectionDialog = true"
>
          <el-icon><Plus /></el-icon>
          新增检验
        </el-button>
        <el-button @click="showInspectionPlanDialog = true">
          <el-icon><Setting /></el-icon>
          检验计划
        </el-button>
        <el-button @click="exportInspectionReport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 检验概览 -->
    <div class="inspection-overview">
      <el-card class="overview-card iqc">
        <div class="overview-content">
          <div class="overview-icon">
            <el-icon><Goods /></el-icon>
          </div>
          <div class="overview-info">
            <div class="overview-title">来料检验 (IQC)</div>
            <div class="overview-stats">
              <div class="stat-item">
                <span class="stat-value">{{ iqcStats.pending }}</span>
                <span class="stat-label">待检</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ iqcStats.pass }}</span>
                <span class="stat-label">合格</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ iqcStats.fail }}</span>
                <span class="stat-label">不合格</span>
              </div>
            </div>
            <div class="overview-rate">
              <span>合格率:</span>
              <span :class="getRateClass(iqcStats.passRate)">{{ iqcStats.passRate }}%</span>
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="overview-card ipqc">
        <div class="overview-content">
          <div class="overview-icon">
            <el-icon><Operation /></el-icon>
          </div>
          <div class="overview-info">
            <div class="overview-title">过程检验 (IPQC)</div>
            <div class="overview-stats">
              <div class="stat-item">
                <span class="stat-value">{{ ipqcStats.pending }}</span>
                <span class="stat-label">待检</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ ipqcStats.pass }}</span>
                <span class="stat-label">合格</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ ipqcStats.fail }}</span>
                <span class="stat-label">不合格</span>
              </div>
            </div>
            <div class="overview-rate">
              <span>合格率:</span>
              <span :class="getRateClass(ipqcStats.passRate)">{{ ipqcStats.passRate }}%</span>
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="overview-card fqc">
        <div class="overview-content">
          <div class="overview-icon">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="overview-info">
            <div class="overview-title">成品检验 (FQC)</div>
            <div class="overview-stats">
              <div class="stat-item">
                <span class="stat-value">{{ fqcStats.pending }}</span>
                <span class="stat-label">待检</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ fqcStats.pass }}</span>
                <span class="stat-label">合格</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ fqcStats.fail }}</span>
                <span class="stat-label">不合格</span>
              </div>
            </div>
            <div class="overview-rate">
              <span>合格率:</span>
              <span :class="getRateClass(fqcStats.passRate)">{{ fqcStats.passRate }}%</span>
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="overview-card oqc">
        <div class="overview-content">
          <div class="overview-icon">
            <el-icon><Finished /></el-icon>
          </div>
          <div class="overview-info">
            <div class="overview-title">出货检验 (OQC)</div>
            <div class="overview-stats">
              <div class="stat-item">
                <span class="stat-value">{{ oqcStats.pending }}</span>
                <span class="stat-label">待检</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ oqcStats.pass }}</span>
                <span class="stat-label">合格</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ oqcStats.fail }}</span>
                <span class="stat-label">不合格</span>
              </div>
            </div>
            <div class="overview-rate">
              <span>合格率:</span>
              <span :class="getRateClass(oqcStats.passRate)">{{ oqcStats.passRate }}%</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 检验管理 -->
    <div class="inspection-content">
      <el-tabs v-model="activeTab" type="card">
        <!-- 检验任务 -->
        <el-tab-pane label="检验任务" name="tasks">
          <div class="tasks-section">
            <div class="section-filters">
              <el-select v-model="taskFilter.type" placeholder="检验类型" clearable>
                <el-option label="全部" value="" />
                <el-option label="来料检验" value="IQC" />
                <el-option label="过程检验" value="IPQC" />
                <el-option label="成品检验" value="FQC" />
                <el-option label="出货检验" value="OQC" />
              </el-select>

              <el-select v-model="taskFilter.status" placeholder="检验状态" clearable>
                <el-option label="全部" value="" />
                <el-option label="待检验" value="PENDING" />
                <el-option label="检验中" value="IN_PROGRESS" />
                <el-option label="已完成" value="COMPLETED" />
                <el-option label="已拒绝" value="REJECTED" />
              </el-select>

              <el-input
v-model="taskFilter.lotNumber" placeholder="批次号"
clearable
/>

              <el-input
v-model="taskFilter.inspector" placeholder="检验员"
clearable
/>

              <el-date-picker
                v-model="taskFilter.dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
              />
            </div>

            <el-table
v-loading="inspectionsLoading" :data="filteredInspections"
>
              <el-table-column prop="inspectionType" label="类型" width="80">
                <template #default="{ row }">
                  <el-tag :type="getInspectionTypeTagType(row.inspectionType)" size="small">
                    {{ row.inspectionType }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="lotNumber" label="批次号" width="140" />
              <el-table-column prop="materialCode" label="物料编码" width="160" />
              <el-table-column prop="sampleSize" label="样本量" width="80" />
              <el-table-column prop="inspector" label="检验员" width="100" />
              <el-table-column prop="inspectionDate" label="检验日期" width="120">
                <template #default="{ row }">
                  {{ formatDateTime(row.inspectionDate) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getInspectionStatusTagType(row.status)" size="small">
                    {{ getInspectionStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="conclusion" label="结论" width="120">
                <template #default="{ row }">
                  <el-tag
                    v-if="row.conclusion"
                    :type="getConclusionTagType(row.conclusion)"
                    size="small"
                  >
                    {{ getConclusionText(row.conclusion) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="检验项" width="80">
                <template #default="{ row }">
                  <el-badge :value="row.inspectionPlan.inspectionItems.length" type="info">
                    {{ row.inspectionPlan.inspectionItems.length }}
                  </el-badge>
                </template>
              </el-table-column>
              <el-table-column label="结果" width="80">
                <template #default="{ row }">
                  <el-badge
                    :value="row.results.length"
                    :type="row.results.length > 0 ? 'success' : 'info'"
                  >
                    {{ row.results.length }}
                  </el-badge>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="220" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" type="primary" link @click="viewInspection(row)">
                    查看
                  </el-button>
                  <el-button
                    v-if="row.status === 'PENDING'"
                    size="small"
                    type="success"
                    link
                    @click="startInspection(row)"
                  >
                    开始检验
                  </el-button>
                  <el-button
                    v-if="row.status === 'IN_PROGRESS'"
                    size="small"
                    type="warning"
                    link
                    @click="continueInspection(row)"
                  >
                    继续检验
                  </el-button>
                  <el-button
                    v-if="row.status === 'COMPLETED'"
                    size="small"
                    type="info"
                    link
                    @click="viewResults(row)"
                  >
                    查看结果
                  </el-button>
                  <el-button size="small" type="primary" link @click="printReport(row)">
                    打印
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 检验计划 -->
        <el-tab-pane label="检验计划" name="plans">
          <div class="plans-section">
            <div class="section-actions">
              <el-button
type="primary" @click="showCreatePlanDialog = true"
>
                <el-icon><Plus /></el-icon>
                新增计划
              </el-button>
            </div>

            <el-table
v-loading="plansLoading" :data="inspectionPlans"
>
              <el-table-column prop="planName" label="计划名称" min-width="200" />
              <el-table-column prop="applicableProducts" label="适用产品" min-width="200">
                <template #default="{ row }">
                  {{ row.applicableProducts.join(', ') }}
                </template>
              </el-table-column>
              <el-table-column label="检验项数量" width="120">
                <template #default="{ row }">
                  <el-badge :value="row.inspectionItems.length" type="primary">
                    {{ row.inspectionItems.length }}
                  </el-badge>
                </template>
              </el-table-column>
              <el-table-column prop="samplingPlan.aql" label="AQL" width="80" />
              <el-table-column prop="samplingPlan.sampleSize" label="样本量" width="80" />
              <el-table-column prop="samplingPlan.inspectionLevel" label="检验水平" width="100" />
              <el-table-column label="操作" width="160" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" type="primary" link @click="viewPlan(row)">
                    查看
                  </el-button>
                  <el-button size="small" type="warning" link @click="editPlan(row)">
                    编辑
                  </el-button>
                  <el-button size="small" type="danger" link @click="deletePlan(row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 统计分析 -->
        <el-tab-pane label="统计分析" name="statistics">
          <div class="statistics-section">
            <div class="chart-container">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-card class="chart-card">
                    <template #header>
                      <span>检验合格率趋势</span>
                    </template>
                    <div
ref="passRateChartRef" class="chart" />
                  </el-card>
                </el-col>
                <el-col :span="12">
                  <el-card class="chart-card">
                    <template #header>
                      <span>缺陷类型分布</span>
                    </template>
                    <div
ref="defectChartRef" class="chart" />
                  </el-card>
                </el-col>
              </el-row>

              <el-row :gutter="24" style="margin-top: 24px">
                <el-col :span="24">
                  <el-card class="chart-card">
                    <template #header>
                      <span>检验工作量统计</span>
                    </template>
                    <div
ref="workloadChartRef" class="chart" />
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 新增检验对话框 -->
    <el-dialog v-model="showCreateInspectionDialog" title="新增检验任务" width="700px">
      <el-form
        ref="inspectionFormRef"
        :model="inspectionForm"
        :rules="inspectionRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="检验类型" prop="inspectionType">
              <el-select v-model="inspectionForm.inspectionType" placeholder="选择检验类型">
                <el-option label="来料检验" value="IQC" />
                <el-option label="过程检验" value="IPQC" />
                <el-option label="成品检验" value="FQC" />
                <el-option label="出货检验" value="OQC" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="批次号" prop="lotNumber">
              <el-input v-model="inspectionForm.lotNumber" placeholder="输入批次号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物料编码" prop="materialCode">
              <el-input v-model="inspectionForm.materialCode" placeholder="输入物料编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="样本量" prop="sampleSize">
              <el-input-number
                v-model="inspectionForm.sampleSize"
                :min="1"
                placeholder="输入样本量"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="检验员" prop="inspector">
          <el-input v-model="inspectionForm.inspector" placeholder="输入检验员姓名" />
        </el-form-item>

        <el-form-item label="检验计划" prop="inspectionPlanId">
          <el-select v-model="inspectionForm.inspectionPlanId" placeholder="选择检验计划">
            <el-option
              v-for="plan in inspectionPlans"
              :key="plan.id"
              :label="plan.planName"
              :value="plan.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateInspectionDialog = false">取消</el-button>
        <el-button type="primary" @click="createInspection" :loading="inspectionCreating">
          创建检验任务
        </el-button>
      </template>
    </el-dialog>

    <!-- 检验详情对话框 -->
    <el-dialog v-model="showInspectionDialog" :title="inspectionDialogTitle" width="90%">
      <div v-if="currentInspection" class="inspection-details">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="检验类型">
            <el-tag :type="getInspectionTypeTagType(currentInspection.inspectionType)">
              {{ currentInspection.inspectionType }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="批次号">
            {{ currentInspection.lotNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="物料编码">
            {{ currentInspection.materialCode }}
          </el-descriptions-item>
          <el-descriptions-item label="样本量">
            {{ currentInspection.sampleSize }}
          </el-descriptions-item>
          <el-descriptions-item label="检验员">
            {{ currentInspection.inspector }}
          </el-descriptions-item>
          <el-descriptions-item label="检验日期">
            {{ formatDateTime(currentInspection.inspectionDate) }}
          </el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="left">检验项目</el-divider>

        <el-table :data="currentInspection.inspectionPlan.inspectionItems" border>
          <el-table-column prop="itemName" label="检验项目" width="200" />
          <el-table-column prop="testMethod" label="检验方法" width="150" />
          <el-table-column label="规格限" width="200">
            <template #default="{ row }">
              {{ row.specification.lsl }} ~ {{ row.specification.usl }} {{ row.specification.unit }}
            </template>
          </el-table-column>
          <el-table-column prop="criticalLevel" label="重要度" width="100">
            <template #default="{ row }">
              <el-tag :type="getCriticalLevelTagType(row.criticalLevel)" size="small">
                {{ row.criticalLevel }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="检验结果" width="120">
            <template #default="{ row }">
              <span v-if="getInspectionResult(row.id)">
                {{ getInspectionResult(row.id)?.measuredValue }} {{ row.specification.unit }}
              </span>
              <span v-else class="text-muted">未检验</span>
            </template>
          </el-table-column>
          <el-table-column label="判定结果" width="100">
            <template #default="{ row }">
              <el-tag
                v-if="getInspectionResult(row.id)"
                :type="getInspectionResult(row.id)?.result === 'PASS' ? 'success' : 'danger'"
                size="small"
              >
                {{ getInspectionResult(row.id)?.result === 'PASS' ? '合格' : '不合格' }}
              </el-tag>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="200">
            <template #default="{ row }">
              {{ getInspectionResult(row.id)?.notes || '-' }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, nextTick } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import {
    Plus,
    Setting,
    Download,
    Goods,
    Operation,
    CircleCheck,
    Finished
  } from '@element-plus/icons-vue'
  import * as echarts from 'echarts'
  import type { ECharts } from 'echarts'
  import type { QualityInspection, InspectionPlan, InspectionResult } from '@/types/quality'
  import { qualityInspections } from '@/utils/mockData/quality'

  // 响应式数据
  const activeTab = ref('tasks')
  const inspectionsLoading = ref(false)
  const plansLoading = ref(false)
  const inspectionCreating = ref(false)

  const showCreateInspectionDialog = ref(false)
  const showInspectionPlanDialog = ref(false)
  const showCreatePlanDialog = ref(false)
  const showInspectionDialog = ref(false)

  const currentInspection = ref<QualityInspection | null>(null)

  // 筛选条件
  const taskFilter = reactive({
    type: '',
    status: '',
    lotNumber: '',
    inspector: '',
    dateRange: null as [Date, Date] | null
  })

  // 表单数据
  const inspectionFormRef = ref()
  const inspectionForm = reactive<{
    inspectionType: string
    lotNumber: string
    materialCode: string
    sampleSize: number
    inspector: string
    inspectionPlanId: string
  }>({
    inspectionType: '',
    lotNumber: '',
    materialCode: '',
    sampleSize: 1,
    inspector: '',
    inspectionPlanId: ''
  })

  const inspectionRules = {
    inspectionType: [{ required: true, message: '请选择检验类型', trigger: 'change' }],
    lotNumber: [{ required: true, message: '请输入批次号', trigger: 'blur' }],
    materialCode: [{ required: true, message: '请输入物料编码', trigger: 'blur' }],
    sampleSize: [{ required: true, message: '请输入样本量', trigger: 'blur' }],
    inspector: [{ required: true, message: '请输入检验员', trigger: 'blur' }],
    inspectionPlanId: [{ required: true, message: '请选择检验计划', trigger: 'change' }]
  }

  // Mock数据
  const inspections = ref<QualityInspection[]>(qualityInspections)
  const inspectionPlans = ref<InspectionPlan[]>([
    {
      id: 'plan_iqc_wafer',
      planName: '晶圆来料检验计划',
      applicableProducts: ['WAFER_8INCH_CMOS18'],
      inspectionItems: [
        {
          id: 'item_thickness',
          itemName: '晶圆厚度',
          testMethod: '千分尺测量',
          specification: {
            usl: 750,
            lsl: 700,
            target: 725,
            ucl: 740,
            lcl: 710,
            unit: 'μm',
            parameter: '厚度'
          },
          criticalLevel: 'MAJOR',
          inspectionSequence: 1,
          equipmentRequired: ['千分尺001']
        },
        {
          id: 'item_resistance',
          itemName: '电阻率',
          testMethod: '四探针法',
          specification: {
            usl: 10,
            lsl: 1,
            target: 5,
            ucl: 8,
            lcl: 2,
            unit: 'Ω·cm',
            parameter: '电阻率'
          },
          criticalLevel: 'CRITICAL',
          inspectionSequence: 2,
          equipmentRequired: ['四探针测试仪001']
        }
      ],
      samplingPlan: {
        planType: 'SINGLE',
        aql: 1.0,
        sampleSize: 10,
        acceptanceNumber: 0,
        rejectionNumber: 1,
        inspectionLevel: 'II'
      },
      acceptanceCriteria: {
        overallAql: 1.0,
        criticalAql: 0.1,
        majorAql: 1.0,
        minorAql: 2.5
      }
    }
  ])

  // 图表引用
  const passRateChartRef = ref<HTMLDivElement>()
  const defectChartRef = ref<HTMLDivElement>()
  const workloadChartRef = ref<HTMLDivElement>()

  let passRateChart: ECharts | null = null
  let defectChart: ECharts | null = null
  let workloadChart: ECharts | null = null

  // 计算属性
  const filteredInspections = computed(() => {
    let filtered = inspections.value

    if (taskFilter.type) {
      filtered = filtered.filter(item => item.inspectionType === taskFilter.type)
    }

    if (taskFilter.status) {
      filtered = filtered.filter(item => item.status === taskFilter.status)
    }

    if (taskFilter.lotNumber) {
      filtered = filtered.filter(item =>
        item.lotNumber.toLowerCase().includes(taskFilter.lotNumber.toLowerCase())
      )
    }

    if (taskFilter.inspector) {
      filtered = filtered.filter(item =>
        item.inspector.toLowerCase().includes(taskFilter.inspector.toLowerCase())
      )
    }

    return filtered
  })

  const inspectionDialogTitle = computed(() => {
    if (!currentInspection.value) return ''
    return `检验详情 - ${currentInspection.value.lotNumber}`
  })

  // 统计数据
  const iqcStats = computed(() => {
    const iqcInspections = inspections.value.filter(item => item.inspectionType === 'IQC')
    const pending = iqcInspections.filter(item => item.status === 'PENDING').length
    const completed = iqcInspections.filter(item => item.status === 'COMPLETED')
    const pass = completed.filter(item => item.conclusion === 'ACCEPT').length
    const fail = completed.filter(item => item.conclusion === 'REJECT').length
    const passRate = completed.length > 0 ? Math.round((pass / completed.length) * 100) : 0

    return { pending, pass, fail, passRate }
  })

  const ipqcStats = computed(() => {
    const ipqcInspections = inspections.value.filter(item => item.inspectionType === 'IPQC')
    const pending = ipqcInspections.filter(item => item.status === 'PENDING').length
    const completed = ipqcInspections.filter(item => item.status === 'COMPLETED')
    const pass = completed.filter(item => item.conclusion === 'ACCEPT').length
    const fail = completed.filter(item => item.conclusion === 'REJECT').length
    const passRate = completed.length > 0 ? Math.round((pass / completed.length) * 100) : 0

    return { pending, pass, fail, passRate }
  })

  const fqcStats = computed(() => {
    const fqcInspections = inspections.value.filter(item => item.inspectionType === 'FQC')
    const pending = fqcInspections.filter(item => item.status === 'PENDING').length
    const completed = fqcInspections.filter(item => item.status === 'COMPLETED')
    const pass = completed.filter(item => item.conclusion === 'ACCEPT').length
    const fail = completed.filter(item => item.conclusion === 'REJECT').length
    const passRate = completed.length > 0 ? Math.round((pass / completed.length) * 100) : 0

    return { pending, pass, fail, passRate }
  })

  const oqcStats = computed(() => {
    const oqcInspections = inspections.value.filter(item => item.inspectionType === 'OQC')
    const pending = oqcInspections.filter(item => item.status === 'PENDING').length
    const completed = oqcInspections.filter(item => item.status === 'COMPLETED')
    const pass = completed.filter(item => item.conclusion === 'ACCEPT').length
    const fail = completed.filter(item => item.conclusion === 'REJECT').length
    const passRate = completed.length > 0 ? Math.round((pass / completed.length) * 100) : 0

    return { pending, pass, fail, passRate }
  })

  // 方法
  const formatDateTime = (date: Date): string => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  const getRateClass = (rate: number): string => {
    if (rate >= 95) return 'rate-excellent'
    if (rate >= 90) return 'rate-good'
    if (rate >= 85) return 'rate-warning'
    return 'rate-poor'
  }

  const getInspectionTypeTagType = (type: string): string => {
    const typeMap: Record<string, string> = {
      IQC: 'primary',
      IPQC: 'success',
      FQC: 'warning',
      OQC: 'danger'
    }
    return typeMap[type] || 'default'
  }

  const getInspectionStatusTagType = (status: string): string => {
    const statusMap: Record<string, string> = {
      PENDING: 'info',
      IN_PROGRESS: 'warning',
      COMPLETED: 'success',
      REJECTED: 'danger'
    }
    return statusMap[status] || 'default'
  }

  const getInspectionStatusText = (status: string): string => {
    const statusMap: Record<string, string> = {
      PENDING: '待检验',
      IN_PROGRESS: '检验中',
      COMPLETED: '已完成',
      REJECTED: '已拒绝'
    }
    return statusMap[status] || status
  }

  const getConclusionTagType = (conclusion: string): string => {
    const conclusionMap: Record<string, string> = {
      ACCEPT: 'success',
      REJECT: 'danger',
      CONDITIONAL_ACCEPT: 'warning'
    }
    return conclusionMap[conclusion] || 'default'
  }

  const getConclusionText = (conclusion: string): string => {
    const conclusionMap: Record<string, string> = {
      ACCEPT: '接受',
      REJECT: '拒绝',
      CONDITIONAL_ACCEPT: '有条件接受'
    }
    return conclusionMap[conclusion] || conclusion
  }

  const getCriticalLevelTagType = (level: string): string => {
    const levelMap: Record<string, string> = {
      CRITICAL: 'danger',
      MAJOR: 'warning',
      MINOR: 'info'
    }
    return levelMap[level] || 'default'
  }

  const getInspectionResult = (itemId: string): InspectionResult | undefined => {
    if (!currentInspection.value) return undefined
    return currentInspection.value.results.find(result => result.inspectionItemId === itemId)
  }

  // 检验操作
  const viewInspection = (inspection: QualityInspection) => {
    currentInspection.value = inspection
    showInspectionDialog.value = true
  }

  const startInspection = (inspection: QualityInspection) => {
    inspection.status = 'IN_PROGRESS'
    ElMessage.success('检验任务已开始')
  }

  const continueInspection = (inspection: QualityInspection) => {
    viewInspection(inspection)
  }

  const viewResults = (inspection: QualityInspection) => {
    viewInspection(inspection)
  }

  const printReport = (inspection: QualityInspection) => {
    ElMessage.success(`打印检验报告: ${inspection.lotNumber}`)
  }

  const createInspection = async () => {
    if (!inspectionFormRef.value) return

    const valid = await inspectionFormRef.value.validate()
    if (!valid) return

    inspectionCreating.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))

      const selectedPlan = inspectionPlans.value.find(
        plan => plan.id === inspectionForm.inspectionPlanId
      )
      if (!selectedPlan) {
        throw new Error('未找到选择的检验计划')
      }

      const newInspection: QualityInspection = {
        id: `inspect_${Date.now()}`,
        inspectionType: inspectionForm.inspectionType as any,
        lotNumber: inspectionForm.lotNumber,
        materialCode: inspectionForm.materialCode,
        sampleSize: inspectionForm.sampleSize,
        inspectionPlan: selectedPlan,
        results: [],
        inspector: inspectionForm.inspector,
        inspectionDate: new Date(),
        status: 'PENDING',
        conclusion: 'ACCEPT'
      }

      inspections.value.unshift(newInspection)
      showCreateInspectionDialog.value = false
      ElMessage.success('检验任务创建成功')

      // 重置表单
      Object.keys(inspectionForm).forEach(key => {
        if (key === 'sampleSize') {
          inspectionForm[key as keyof typeof inspectionForm] = 1
        } else {
          inspectionForm[key as keyof typeof inspectionForm] = ''
        }
      })
    } catch (error) {
      ElMessage.error('检验任务创建失败')
    } finally {
      inspectionCreating.value = false
    }
  }

  // 检验计划操作
  const viewPlan = (plan: InspectionPlan) => {
    ElMessage.info(`查看检验计划: ${plan.planName}`)
  }

  const editPlan = (plan: InspectionPlan) => {
    ElMessage.info(`编辑检验计划: ${plan.planName}`)
  }

  const deletePlan = (plan: InspectionPlan) => {
    ElMessageBox.confirm(`确认删除检验计划 "${plan.planName}" 吗？`, '删除确认', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        const index = inspectionPlans.value.findIndex(p => p.id === plan.id)
        if (index !== -1) {
          inspectionPlans.value.splice(index, 1)
          ElMessage.success('检验计划已删除')
        }
      })
      .catch(() => {
        // 用户取消
      })
  }

  const exportInspectionReport = () => {
    ElMessage.success('检验报告导出成功')
  }

  // 图表初始化
  const initCharts = () => {
    nextTick(() => {
      if (passRateChartRef.value) {
        passRateChart = echarts.init(passRateChartRef.value)
        const option = {
          title: {
            text: '检验合格率趋势',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['IQC', 'IPQC', 'FQC', 'OQC'],
            bottom: 0
          },
          xAxis: {
            type: 'category',
            data: ['1月', '2月', '3月', '4月', '5月', '6月']
          },
          yAxis: {
            type: 'value',
            min: 80,
            max: 100,
            axisLabel: {
              formatter: '{value}%'
            }
          },
          series: [
            {
              name: 'IQC',
              type: 'line',
              data: [95, 96, 94, 97, 98, 95],
              smooth: true
            },
            {
              name: 'IPQC',
              type: 'line',
              data: [98, 97, 99, 98, 99, 97],
              smooth: true
            },
            {
              name: 'FQC',
              type: 'line',
              data: [99, 98, 99, 99, 100, 99],
              smooth: true
            },
            {
              name: 'OQC',
              type: 'line',
              data: [100, 99, 100, 100, 100, 100],
              smooth: true
            }
          ]
        }
        passRateChart.setOption(option)
      }

      if (defectChartRef.value) {
        defectChart = echarts.init(defectChartRef.value)
        const option = {
          title: {
            text: '缺陷类型分布',
            left: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left'
          },
          series: [
            {
              name: '缺陷类型',
              type: 'pie',
              radius: '50%',
              data: [
                { value: 35, name: '尺寸偏差' },
                { value: 28, name: '外观缺陷' },
                { value: 22, name: '功能异常' },
                { value: 15, name: '其他缺陷' }
              ]
            }
          ]
        }
        defectChart.setOption(option)
      }

      if (workloadChartRef.value) {
        workloadChart = echarts.init(workloadChartRef.value)
        const option = {
          title: {
            text: '检验工作量统计',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            data: ['待检验', '检验中', '已完成'],
            bottom: 0
          },
          xAxis: {
            type: 'category',
            data: ['IQC', 'IPQC', 'FQC', 'OQC']
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '待检验',
              type: 'bar',
              stack: '总量',
              data: [5, 3, 2, 1],
              itemStyle: { color: '#E6A23C' }
            },
            {
              name: '检验中',
              type: 'bar',
              stack: '总量',
              data: [2, 1, 1, 0],
              itemStyle: { color: '#409EFF' }
            },
            {
              name: '已完成',
              type: 'bar',
              stack: '总量',
              data: [25, 18, 32, 15],
              itemStyle: { color: '#67C23A' }
            }
          ]
        }
        workloadChart.setOption(option)
      }
    })
  }

  // 生命周期
  onMounted(() => {
    initCharts()

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      passRateChart?.resize()
      defectChart?.resize()
      workloadChart?.resize()
    })
  })
</script>

<style lang="scss" scoped>
  .quality-inspection {
    padding: var(--spacing-6);

    &__header {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      padding-bottom: var(--spacing-4);
      margin-bottom: var(--spacing-6);
      border-bottom: 2px solid var(--color-border-light);

      .header-content {
        h1 {
          margin: 0 0 var(--spacing-2) 0;
          font-size: 28px;
          font-weight: 700;
          color: var(--color-text-primary);
        }

        p {
          margin: 0;
          font-size: 16px;
          color: var(--color-text-secondary);
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-2);
      }
    }

    .inspection-overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-6);

      .overview-card {
        &.iqc {
          border-left: 4px solid var(--color-primary);
        }

        &.ipqc {
          border-left: 4px solid var(--color-success);
        }

        &.fqc {
          border-left: 4px solid var(--color-warning);
        }

        &.oqc {
          border-left: 4px solid var(--color-danger);
        }

        .overview-content {
          display: flex;
          gap: var(--spacing-4);
          align-items: center;

          .overview-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            font-size: 32px;
            color: var(--color-primary);
            background: var(--color-primary-light);
            border-radius: var(--radius-base);
          }

          .overview-info {
            flex: 1;

            .overview-title {
              margin-bottom: var(--spacing-2);
              font-size: 18px;
              font-weight: 600;
              color: var(--color-text-primary);
            }

            .overview-stats {
              display: flex;
              gap: var(--spacing-4);
              margin-bottom: var(--spacing-2);

              .stat-item {
                display: flex;
                flex-direction: column;
                align-items: center;

                .stat-value {
                  margin-bottom: var(--spacing-1);
                  font-size: 24px;
                  font-weight: 700;
                  line-height: 1;
                  color: var(--color-text-primary);
                }

                .stat-label {
                  font-size: 12px;
                  color: var(--color-text-secondary);
                }
              }
            }

            .overview-rate {
              font-size: 14px;
              color: var(--color-text-secondary);

              .rate-excellent {
                font-weight: 600;
                color: var(--color-success);
              }

              .rate-good {
                font-weight: 600;
                color: var(--color-primary);
              }

              .rate-warning {
                font-weight: 600;
                color: var(--color-warning);
              }

              .rate-poor {
                font-weight: 600;
                color: var(--color-danger);
              }
            }
          }
        }
      }
    }

    .inspection-content {
      .section-filters {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-3);
        padding: var(--spacing-4);
        margin-bottom: var(--spacing-4);
        background: var(--color-bg-secondary);
        border-radius: var(--radius-base);

        .el-select,
        .el-input {
          min-width: 140px;
        }

        .el-date-picker {
          min-width: 280px;
        }
      }

      .section-actions {
        margin-bottom: var(--spacing-4);
      }

      .text-muted {
        color: var(--color-text-tertiary);
      }

      .chart-container {
        .chart-card {
          .chart {
            height: 300px;
          }
        }
      }
    }

    .inspection-details {
      .el-divider {
        margin: var(--spacing-6) 0;
      }
    }
  }

  // 响应式设计
  @media (width <= 768px) {
    .quality-inspection {
      padding: var(--spacing-4);

      &__header {
        flex-direction: column;
        gap: var(--spacing-4);
        align-items: stretch;

        .header-actions {
          justify-content: flex-start;
        }
      }

      .inspection-overview {
        grid-template-columns: 1fr;

        .overview-card {
          .overview-content {
            .overview-stats {
              justify-content: space-around;
            }
          }
        }
      }

      .inspection-content {
        .section-filters {
          flex-direction: column;

          .el-select,
          .el-input,
          .el-date-picker {
            min-width: auto;
          }
        }

        .chart-container {
          .chart-card {
            .chart {
              height: 250px;
            }
          }
        }
      }
    }
  }
</style>
