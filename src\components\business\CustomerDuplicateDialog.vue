<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="复制客户"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="duplicate-content">
      <el-alert
        title="复制客户说明"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>将基于现有客户信息创建新客户，请修改必要的字段以避免重复。</p>
          <p>系统将自动生成新的客户编码。</p>
        </template>
      </el-alert>

      <div v-if="customer" class="source-customer">
        <h4>原客户信息</h4>
        <div class="customer-summary">
          <el-avatar 
            :size="40"
            :src="customer.logo"
            :style="{ backgroundColor: getCustomerColor(customer.type) }"
          >
            {{ customer.shortName?.charAt(0) || customer.name.charAt(0) }}
          </el-avatar>
          <div class="customer-info">
            <div class="customer-name">{{ customer.name }}</div>
            <div class="customer-details">
              <el-tag size="small" :type="getCustomerTypeTagType(customer.type)">
                {{ getCustomerTypeLabel(customer.type) }}
              </el-tag>
              <span class="customer-code">{{ customer.code }}</span>
            </div>
          </div>
        </div>
      </div>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="duplicate-form"
      >
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="新客户编码" prop="code">
              <el-input 
                v-model="formData.code" 
                placeholder="系统自动生成"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户类型" prop="type" required>
              <el-select v-model="formData.type" placeholder="请选择客户类型" @change="handleTypeChange">
                <el-option
                  v-for="option in CUSTOMER_TYPE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="公司名称" prop="name" required>
          <el-input 
            v-model="formData.name" 
            placeholder="请输入公司全称（建议修改以避免重复）"
          />
        </el-form-item>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="英文名称" prop="englishName">
              <el-input v-model="formData.englishName" placeholder="请输入英文名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="简称" prop="shortName">
              <el-input v-model="formData.shortName" placeholder="请输入公司简称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="客户等级" prop="level" required>
              <el-select v-model="formData.level" placeholder="请选择客户等级">
                <el-option
                  v-for="option in CUSTOMER_LEVEL_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户规模" prop="scale" required>
              <el-select v-model="formData.scale" placeholder="请选择客户规模">
                <el-option
                  v-for="option in CUSTOMER_SCALE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="联系人姓名" prop="contact.name" required>
          <el-input 
            v-model="formData.contact.name" 
            placeholder="请输入联系人姓名（建议修改）"
          />
        </el-form-item>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="contact.email" required>
              <el-input 
                v-model="formData.contact.email" 
                placeholder="请输入邮箱地址（必须修改）"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话" prop="contact.phone" required>
              <el-input 
                v-model="formData.contact.phone" 
                placeholder="请输入电话号码"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-checkbox v-model="copyTechnicalInfo">复制技术信息（应用领域、工艺节点、封装偏好等）</el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="copyAddressInfo">复制地址信息</el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="copyFinancialInfo">复制财务信息（需手动修改银行信息）</el-checkbox>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">取消</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          创建副本
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
// Element Plus组件通过unplugin-auto-import自动导入
import type { FormInstance, FormRules } from 'element-plus'
import type { Customer, CreateCustomerData, CustomerType } from '@/types/customer'
import { 
  CUSTOMER_TYPE_OPTIONS,
  CUSTOMER_LEVEL_OPTIONS,
  CUSTOMER_SCALE_OPTIONS,
  generateNewCustomerCode
} from '@/utils/mockData/customerMaster'

interface Props {
  visible: boolean
  customer?: Customer | null
}

interface Emits {
  'update:visible': [visible: boolean]
  'save': [data: CreateCustomerData]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()

// 表单状态
const submitting = ref(false)
const copyTechnicalInfo = ref(true)
const copyAddressInfo = ref(true)
const copyFinancialInfo = ref(false)

// 表单数据
const formData = reactive<CreateCustomerData>({
  code: '',
  name: '',
  englishName: '',
  shortName: '',
  type: 'fabless',
  level: 'standard',
  scale: 'medium',
  industryType: 'communication',
  creditLevel: 'B',
  applicationFields: [],
  processNodes: [],
  packagePreferences: [],
  qualityStandard: [],
  complianceRequirements: [],
  contact: {
    customerId: '',
    name: '',
    englishName: '',
    position: '',
    department: '',
    email: '',
    phone: '',
    mobile: '',
    wechat: '',
    role: 'BusinessManager',
    contactType: ['Business'],
    status: 'active',
    importance: 3,
    trustLevel: 3,
    influenceScore: 5,
    isDecisionMaker: true,
    isPrimaryContact: true,
    createdAt: '',
    updatedAt: '',
    createdBy: ''
  },
  address: {
    country: '中国',
    province: '',
    city: '',
    district: '',
    street: '',
    postalCode: ''
  },
  financialInfo: {
    creditLimit: 10000000,
    paymentTerms: 'NET30',
    currency: 'CNY',
    taxId: ''
  }
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择客户类型', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择客户等级', trigger: 'change' }
  ],
  scale: [
    { required: true, message: '请选择客户规模', trigger: 'change' }
  ],
  'contact.name': [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' }
  ],
  'contact.email': [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  'contact.phone': [
    { required: true, message: '请输入电话号码', trigger: 'blur' }
  ]
}

// 获取客户颜色
const getCustomerColor = (type: CustomerType): string => {
  const colors = {
    fabless: '#409EFF',
    idm: '#67C23A', 
    foundry: '#E6A23C',
    distributor: '#909399',
    broker: '#F56C6C',
    ems: '#9580FF'
  }
  return colors[type]
}

// 获取客户类型标签类型
const getCustomerTypeTagType = (type: CustomerType): string => {
  const types = {
    fabless: 'primary',
    idm: 'success',
    foundry: 'warning', 
    distributor: 'info',
    broker: 'danger',
    ems: ''
  }
  return types[type] || ''
}

// 获取客户类型标签文本
const getCustomerTypeLabel = (type: CustomerType): string => {
  const option = CUSTOMER_TYPE_OPTIONS.find(opt => opt.value === type)
  return option?.label || type
}

// 生成客户编码
const generateCode = () => {
  if (formData.type) {
    formData.code = generateNewCustomerCode(formData.type)
  }
}

// 处理客户类型变化
const handleTypeChange = () => {
  generateCode()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  // 重新填充数据
  if (props.customer) {
    fillFormData()
  }
}

// 填充表单数据
const fillFormData = () => {
  if (!props.customer) return

  const customer = props.customer

  // 基本信息（需要用户手动修改的字段）
  Object.assign(formData, {
    name: customer.name + ' (副本)',
    englishName: customer.englishName ? customer.englishName + ' (Copy)' : '',
    shortName: customer.shortName ? customer.shortName + '副本' : '',
    type: customer.type,
    level: customer.level,
    scale: customer.scale,
    industryType: customer.industryType,
    creditLevel: customer.creditLevel
  })

  // 联系人信息（需要用户修改）
  Object.assign(formData.contact, {
    name: customer.contact.name,
    englishName: customer.contact.englishName,
    position: customer.contact.position,
    department: customer.contact.department,
    email: '', // 邮箱必须手动修改
    phone: customer.contact.phone,
    mobile: customer.contact.mobile,
    wechat: customer.contact.wechat,
    role: customer.contact.role,
    importance: customer.contact.importance
  })

  // 技术信息（根据选择复制）
  if (copyTechnicalInfo.value) {
    formData.applicationFields = [...customer.applicationFields]
    formData.processNodes = [...customer.processNodes]
    formData.packagePreferences = [...customer.packagePreferences]
    formData.qualityStandard = [...customer.qualityStandard]
    formData.complianceRequirements = [...customer.complianceRequirements]
  }

  // 地址信息（根据选择复制）
  if (copyAddressInfo.value && customer.address) {
    Object.assign(formData.address, customer.address)
  }

  // 财务信息（根据选择复制，但需要清空银行信息）
  if (copyFinancialInfo.value && customer.financialInfo) {
    Object.assign(formData.financialInfo, {
      ...customer.financialInfo,
      taxId: '', // 税号需要手动修改
      bankInfo: undefined // 银行信息不复制
    })
  }

  // 生成新的客户编码
  generateCode()
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 模拟提交延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    emit('save', formData)
    
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否完整')
  } finally {
    submitting.value = false
  }
}

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.customer) {
      nextTick(() => {
        fillFormData()
      })
    }
  }
)

// 监听复制选项变化
watch(
  [copyTechnicalInfo, copyAddressInfo, copyFinancialInfo],
  () => {
    if (props.visible && props.customer) {
      fillFormData()
    }
  }
)
</script>

<style lang="scss" scoped>
.duplicate-content {
  .source-customer {
    padding: 16px;
    margin: 20px 0;
    background: var(--color-bg-light);
    border-radius: 8px;

    h4 {
      margin: 0 0 12px;
      font-size: 14px;
      color: var(--color-text-regular);
    }

    .customer-summary {
      display: flex;
      gap: 12px;
      align-items: center;

      .customer-info {
        .customer-name {
          margin-bottom: 4px;
          font-weight: 500;
          color: var(--color-text-primary);
        }

        .customer-details {
          display: flex;
          gap: 8px;
          align-items: center;

          .customer-code {
            padding: 2px 6px;
            font-size: 12px;
            color: var(--color-text-secondary);
            background: var(--color-white);
            border-radius: 4px;
          }
        }
      }
    }
  }

  .duplicate-form {
    margin-top: 20px;
  }
}

.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

// 响应式调整
@media (width <= 768px) {
  .duplicate-content {
    .source-customer {
      .customer-summary {
        flex-direction: column;
        align-items: flex-start;
        text-align: center;
      }
    }
  }
}
</style>