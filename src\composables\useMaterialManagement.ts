// 物料与库存管理组合函数 - OSAT专业版
// 严格按照第一阶段规划实现半导体专用仓库管理和Die Bank管理

import { ref, computed, reactive, readonly } from 'vue'
import type {
  MaterialInventory,
  MaterialTransaction,
  WarehouseZone,
  DieBank,
  SupplierPerformance,
  MaterialQueryParams,
  TransactionQueryParams,
  MaterialCategory,
  MaterialStatus
} from '@/types/material'

import {
  mockMaterialInventory,
  mockMaterialTransactions,
  mockWarehouseZones,
  mockDieBanks,
  mockSupplierPerformance,
  getStockWarnings,
  getWarehouseUtilization
} from '@/utils/mockData/material'

// 物料库存管理组合函数
export function useMaterialInventory() {
  const loading = ref(false)
  const materials = ref<MaterialInventory[]>([])
  const totalCount = ref(0)

  // 加载物料库存数据
  const loadMaterials = async (params?: MaterialQueryParams) => {
    loading.value = true
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 800))

      let filteredData = [...mockMaterialInventory]

      // 按分类筛选
      if (params?.category) {
        filteredData = filteredData.filter(item => item.category === params.category)
      }

      // 按状态筛选
      if (params?.status) {
        filteredData = filteredData.filter(item => item.status === params.status)
      }

      // 按仓库区域筛选
      if (params?.warehouseZone) {
        filteredData = filteredData.filter(
          item => item.warehouseZone.zoneId === params.warehouseZone
        )
      }

      // 低库存筛选
      if (params?.lowStock) {
        filteredData = filteredData.filter(item => item.availableStock <= item.reorderPoint)
      }

      // 过期筛选
      if (params?.expired) {
        const now = new Date()
        filteredData = filteredData.filter(item => {
          if (!item.materialSpec.expiryDate) return false
          return new Date(item.materialSpec.expiryDate) <= now
        })
      }

      // 搜索功能
      if (params?.search) {
        const searchLower = params.search.toLowerCase()
        filteredData = filteredData.filter(
          item =>
            item.materialSpec.materialName.toLowerCase().includes(searchLower) ||
            item.materialSpec.materialCode.toLowerCase().includes(searchLower) ||
            item.materialSpec.manufacturer.toLowerCase().includes(searchLower)
        )
      }

      // 分页处理
      const page = params?.page || 1
      const pageSize = params?.pageSize || 20
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + pageSize

      materials.value = filteredData.slice(startIndex, endIndex)
      totalCount.value = filteredData.length
    } catch (error) {
      console.error('加载物料数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取库存统计信息
  const getInventoryStats = () => {
    const stats = mockMaterialInventory.reduce(
      (acc, item) => {
        acc.totalValue += item.totalValue
        acc.totalItems += 1

        if (item.availableStock <= item.reorderPoint) {
          acc.lowStockItems += 1
        }

        if (item.materialSpec.expiryDate) {
          const expiry = new Date(item.materialSpec.expiryDate)
          const thirtyDaysFromNow = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
          if (expiry <= thirtyDaysFromNow) {
            acc.expiringSoonItems += 1
          }
        }

        // 按分类统计
        const category = item.category
        if (!acc.byCategory[category]) {
          acc.byCategory[category] = { count: 0, value: 0 }
        }
        acc.byCategory[category].count += 1
        acc.byCategory[category].value += item.totalValue

        return acc
      },
      {
        totalValue: 0,
        totalItems: 0,
        lowStockItems: 0,
        expiringSoonItems: 0,
        byCategory: {} as Record<string, { count: number; value: number }>
      }
    )

    return stats
  }

  // 物料调拨
  const transferMaterial = async (
    materialCode: string,
    fromLocation: string,
    toLocation: string,
    quantity: number
  ) => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))

      // 这里应该调用真实的API来执行物料调拨
      console.log('执行物料调拨:', { materialCode, fromLocation, toLocation, quantity })

      // 刷新数据
      await loadMaterials()

      return { success: true, message: '物料调拨成功' }
    } catch (error) {
      console.error('物料调拨失败:', error)
      return { success: false, message: '物料调拨失败' }
    }
  }

  return {
    loading: readonly(loading),
    materials: readonly(materials),
    totalCount: readonly(totalCount),
    loadMaterials,
    getInventoryStats,
    transferMaterial
  }
}

// 物料事务管理组合函数
export function useMaterialTransactions() {
  const loading = ref(false)
  const transactions = ref<MaterialTransaction[]>([])
  const totalCount = ref(0)

  // 加载事务记录
  const loadTransactions = async (params?: TransactionQueryParams) => {
    loading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 600))

      let filteredData = [...mockMaterialTransactions]

      // 按事务类型筛选
      if (params?.transactionType) {
        filteredData = filteredData.filter(txn => txn.transactionType === params.transactionType)
      }

      // 按物料代码筛选
      if (params?.materialCode) {
        filteredData = filteredData.filter(txn =>
          txn.materialCode.toLowerCase().includes(params.materialCode!.toLowerCase())
        )
      }

      // 按日期范围筛选
      if (params?.dateFrom && params?.dateTo) {
        const from = new Date(params.dateFrom)
        const to = new Date(params.dateTo)
        filteredData = filteredData.filter(txn => {
          const txnDate = new Date(txn.timestamp)
          return txnDate >= from && txnDate <= to
        })
      }

      // 按操作员筛选
      if (params?.operator) {
        filteredData = filteredData.filter(txn =>
          txn.operator.toLowerCase().includes(params.operator!.toLowerCase())
        )
      }

      // 按时间倒序排列
      filteredData.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

      // 分页
      const page = params?.page || 1
      const pageSize = params?.pageSize || 20
      const startIndex = (page - 1) * pageSize

      transactions.value = filteredData.slice(startIndex, startIndex + pageSize)
      totalCount.value = filteredData.length
    } catch (error) {
      console.error('加载事务数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 创建新事务
  const createTransaction = async (transactionData: Partial<MaterialTransaction>) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 400))

      // 模拟创建事务
      const newTransaction: MaterialTransaction = {
        transactionId: `TXN-${Date.now()}`,
        timestamp: new Date().toISOString(),
        ...transactionData
      } as MaterialTransaction

      console.log('创建物料事务:', newTransaction)

      // 刷新数据
      await loadTransactions()

      return { success: true, message: '事务创建成功', data: newTransaction }
    } catch (error) {
      console.error('创建事务失败:', error)
      return { success: false, message: '事务创建失败' }
    }
  }

  return {
    loading: readonly(loading),
    transactions: readonly(transactions),
    totalCount: readonly(totalCount),
    loadTransactions,
    createTransaction
  }
}

// 仓库管理组合函数
export function useWarehouseManagement() {
  const zones = ref<WarehouseZone[]>([...mockWarehouseZones])

  // 获取仓库利用率统计
  const getUtilizationStats = () => {
    return getWarehouseUtilization()
  }

  // 获取环境监控数据
  const getEnvironmentalMonitoring = () => {
    return zones.value.map(zone => ({
      zoneId: zone.zoneId,
      zoneName: zone.zoneName,
      zoneType: zone.zoneType,
      temperature: zone.environmentalConditions.temperature,
      humidity: zone.environmentalConditions.humidity,
      cleanliness: zone.environmentalConditions.cleanliness,
      esdCompliance: zone.environmentalConditions.esdCompliance,
      status: getZoneStatus(zone),
      lastUpdate: new Date().toISOString()
    }))
  }

  // 判断区域状态
  const getZoneStatus = (zone: WarehouseZone) => {
    const { temperature, humidity } = zone.environmentalConditions

    // 根据不同区域类型判断状态
    switch (zone.zoneType) {
      case 'ESD_SAFE':
        return temperature >= 20 && temperature <= 25 && humidity < 50 ? 'NORMAL' : 'WARNING'
      case 'TEMP_CONTROLLED':
        return temperature >= 2 && temperature <= 8 ? 'NORMAL' : 'WARNING'
      case 'CLEAN_ROOM':
        return humidity >= 35 && humidity <= 45 ? 'NORMAL' : 'WARNING'
      default:
        return 'NORMAL'
    }
  }

  return {
    zones: readonly(zones),
    getUtilizationStats,
    getEnvironmentalMonitoring
  }
}

// Die Bank管理组合函数
export function useDieBankManagement() {
  const loading = ref(false)
  const dieBanks = ref<DieBank[]>([...mockDieBanks])

  // 获取Die Bank统计
  const getDieBankStats = () => {
    return dieBanks.value.reduce(
      (acc, bank) => {
        acc.totalDieBanks += 1
        acc.totalDies += bank.totalDieCount
        acc.availableDies += bank.availableDieCount
        acc.utilizationRate = ((acc.totalDies - acc.availableDies) / acc.totalDies) * 100

        // 按质量等级统计
        bank.binMap.forEach(bin => {
          if (!acc.byGrade[bin.qualityGrade]) {
            acc.byGrade[bin.qualityGrade] = 0
          }
          acc.byGrade[bin.qualityGrade] += bin.dieCount
        })

        return acc
      },
      {
        totalDieBanks: 0,
        totalDies: 0,
        availableDies: 0,
        utilizationRate: 0,
        byGrade: {} as Record<string, number>
      }
    )
  }

  // 获取Die Bank访问记录
  const getDieBankAccessLog = () => {
    return dieBanks.value.map(bank => ({
      dieBankId: bank.dieBankId,
      dieType: bank.dieType,
      lastAccessTime: bank.lastAccessTime,
      accessFrequency: Math.floor(Math.random() * 20) + 5, // 模拟访问频次
      storageCondition: {
        temperature: bank.temperature,
        humidity: bank.humidity,
        status: bank.temperature > 25 || bank.humidity > 50 ? 'WARNING' : 'NORMAL'
      }
    }))
  }

  return {
    loading: readonly(loading),
    dieBanks: readonly(dieBanks),
    getDieBankStats,
    getDieBankAccessLog
  }
}

// 供应商绩效管理组合函数
export function useSupplierPerformance() {
  const loading = ref(false)
  const suppliers = ref<SupplierPerformance[]>([...mockSupplierPerformance])

  // 加载供应商绩效数据
  const loadSupplierPerformance = async () => {
    loading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      // 模拟数据已经在初始化时加载
    } finally {
      loading.value = false
    }
  }

  // 获取供应商评级
  const getSupplierRanking = () => {
    return suppliers.value
      .map(supplier => {
        // 计算综合评分 (加权平均)
        const score =
          supplier.kpi.onTimeDelivery * 0.3 +
          supplier.kpi.qualityRating * 0.4 +
          supplier.kpi.costPerformance * 0.2 +
          (100 - supplier.kpi.defectRate / 10) * 0.1

        return {
          supplierId: supplier.supplierId,
          supplierName: supplier.supplierName,
          overallScore: Math.round(score * 100) / 100,
          ranking: 0, // 将在排序后填充
          kpi: supplier.kpi,
          recommendation:
            score >= 95
              ? '优秀供应商'
              : score >= 90
                ? '良好供应商'
                : score >= 80
                  ? '改进供应商'
                  : '考虑更换'
        }
      })
      .sort((a, b) => b.overallScore - a.overallScore)
      .map((supplier, index) => ({ ...supplier, ranking: index + 1 }))
  }

  return {
    loading: readonly(loading),
    suppliers: readonly(suppliers),
    loadSupplierPerformance,
    getSupplierRanking
  }
}

// 库存预警管理组合函数
export function useInventoryWarnings() {
  const warnings = computed(() => getStockWarnings())

  // 获取预警统计
  const getWarningStats = () => {
    const warningData = warnings.value
    return {
      totalWarnings: warningData.length,
      highUrgency: warningData.filter(w => w.urgencyLevel === 'HIGH').length,
      mediumUrgency: warningData.filter(w => w.urgencyLevel === 'MEDIUM').length,
      lowStockWarnings: warningData.filter(w => w.warningType === 'LOW_STOCK').length,
      expiringWarnings: warningData.filter(w => w.warningType === 'EXPIRING_SOON').length
    }
  }

  return {
    warnings,
    getWarningStats
  }
}
