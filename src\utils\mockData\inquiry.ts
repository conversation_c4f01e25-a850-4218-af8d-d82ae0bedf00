// IC封测CIM系统 - 询价模拟数据

import type {
  CustomerInquiry,
  InquiryStatus,
  ProductType,
  PackageType,
  TestRequirement,
  OrderPriority,
  CurrencyType,
  InquiryStats,
  InquiryListResponse,
  InquiryQueryParams
} from '@/types/order'

// 模拟询价数据
export const mockInquiries: CustomerInquiry[] = [
  {
    id: 'INQ001',
    inquiryNumber: 'INQ-2024-001',
    customerId: 'CUST001',
    customer: {
      id: 'CUST001',
      name: '博世汽车电子(中国)有限公司',
      code: 'BOSCH',
      contact: {
        name: '张工程师',
        phone: '021-6123-4567',
        email: '<EMAIL>',
        department: '汽车电子事业部'
      },
      industryType: ProductType.AUTOMOTIVE
    },
    productInfo: {
      productName: '车载ECU主控芯片',
      productCode: 'BCM2024-AUTO',
      productType: ProductType.AUTOMOTIVE,
      packageType: PackageType.BGA,
      quantity: 500,
      quantityLevel: 'K',
      waferSize: 8,
      dieSize: '6.5mm x 6.5mm',
      leadCount: 196,
      specifications:
        '32位ARM Cortex-M7处理器，主频180MHz，2MB Flash，512KB RAM，支持CAN-FD/LIN/Ethernet通信，工作温度-40°C~+125°C，符合AEC-Q100 Grade 1标准',
      datasheet: 'https://example.com/datasheets/BCM2024-AUTO.pdf'
    },
    testRequirements: {
      testType: TestRequirement.CP_FT,
      cpTestRequirement: 'DC参数测试、功能测试、高低温测试(-40°C~+125°C)',
      ftTestRequirement: '最终功能验证、CAN/LIN/Ethernet通信测试、EMC预测试',
      reliabilityTest: true,
      customTestSpec: '汽车级可靠性测试：HTOL 1000hrs@125°C, TC 1000cycles(-40°C~+125°C)',
      yieldRequirement: 99.5
    },
    schedule: {
      inquiryDate: '2024-01-15',
      expectedQuoteDate: '2024-01-22',
      targetDeliveryDate: '2024-04-15',
      productionStartDate: '2024-03-01',
      urgencyLevel: OrderPriority.HIGH
    },
    businessInfo: {
      budgetRange: {
        min: 28,
        max: 35,
        currency: CurrencyType.CNY
      },
      paymentTerms: 'T/T 30天',
      contractDuration: '2年框架合同',
      volumeCommitment: 2000
    },
    status: InquiryStatus.EVALUATING,
    priority: OrderPriority.HIGH,
    assignedSalesManager: '李经理',
    assignedEngineer: '王工程师',
    evaluation: {
      technicalFeasibility: 'feasible',
      capacityAvailability: 'available',
      costEstimation: {
        cpTestCost: 8.5,
        assemblyCost: 15.2,
        ftTestCost: 6.8,
        totalUnitCost: 30.5
      },
      riskAssessment: 'low',
      evaluationNotes: '技术成熟，产能充足，建议报价32元/K pcs'
    },
    createdAt: '2024-01-15T09:30:00Z',
    updatedAt: '2024-01-18T14:20:00Z',
    createdBy: '销售部-李经理',
    notes: '博世重点客户，年采购量大，优先处理',
    attachments: [
      {
        id: 'ATT001',
        name: 'BCM2024-AUTO数据手册.pdf',
        url: '/attachments/BCM2024-AUTO.pdf',
        type: 'pdf',
        uploadAt: '2024-01-15T09:30:00Z'
      }
    ]
  },
  {
    id: 'INQ002',
    inquiryNumber: 'INQ-2024-002',
    customerId: 'CUST002',
    customer: {
      id: 'CUST002',
      name: '小米通讯技术有限公司',
      code: 'XIAOMI',
      contact: {
        name: '刘采购经理',
        phone: '010-8765-4321',
        email: '<EMAIL>',
        department: '硬件采购部'
      },
      industryType: ProductType.COMMUNICATION
    },
    productInfo: {
      productName: '5G射频功放芯片',
      productCode: 'XM-RF-5G-001',
      productType: ProductType.COMMUNICATION,
      packageType: PackageType.QFN,
      quantity: 2000,
      quantityLevel: 'K',
      waferSize: 6,
      dieSize: '3.2mm x 2.8mm',
      leadCount: 32,
      specifications: '5G NR n77/n78频段功放，输出功率26dBm，效率35%，工作频率3.4-3.8GHz',
      datasheet: 'https://example.com/datasheets/XM-RF-5G-001.pdf'
    },
    testRequirements: {
      testType: TestRequirement.CP_FT,
      cpTestRequirement: 'RF参数测试：功率、效率、线性度、谐波',
      ftTestRequirement: '最终RF性能验证，温度补偿测试',
      reliabilityTest: false,
      yieldRequirement: 98.0
    },
    schedule: {
      inquiryDate: '2024-01-16',
      expectedQuoteDate: '2024-01-20',
      targetDeliveryDate: '2024-03-30',
      urgencyLevel: OrderPriority.MEDIUM
    },
    businessInfo: {
      budgetRange: {
        min: 12,
        max: 18,
        currency: CurrencyType.CNY
      },
      paymentTerms: 'T/T 45天',
      contractDuration: '1年',
      volumeCommitment: 8000
    },
    status: InquiryStatus.QUOTED,
    priority: OrderPriority.MEDIUM,
    assignedSalesManager: '陈经理',
    assignedEngineer: '赵工程师',
    evaluation: {
      technicalFeasibility: 'feasible',
      capacityAvailability: 'available',
      costEstimation: {
        cpTestCost: 4.2,
        assemblyCost: 7.8,
        ftTestCost: 3.5,
        totalUnitCost: 15.5
      },
      riskAssessment: 'low'
    },
    quotation: {
      quoteNumber: 'QUO-2024-002',
      unitPrice: 16.8,
      totalAmount: 33600000,
      currency: CurrencyType.CNY,
      validityPeriod: '2024-02-20',
      paymentTerms: 'T/T 45天',
      deliveryTerms: 'FCA 上海',
      quotedAt: '2024-01-19T16:45:00Z',
      quotedBy: '陈经理'
    },
    createdAt: '2024-01-16T10:15:00Z',
    updatedAt: '2024-01-19T16:45:00Z',
    createdBy: '销售部-陈经理'
  },
  {
    id: 'INQ003',
    inquiryNumber: 'INQ-2024-003',
    customerId: 'CUST003',
    customer: {
      id: 'CUST003',
      name: '比亚迪半导体有限公司',
      code: 'BYD',
      contact: {
        name: '王技术总监',
        phone: '0755-8901-2345',
        email: '<EMAIL>',
        department: '车规芯片事业部'
      },
      industryType: ProductType.AUTOMOTIVE
    },
    productInfo: {
      productName: '车载IGBT功率模块',
      productCode: 'BYD-IGBT-650V',
      productType: ProductType.AUTOMOTIVE,
      packageType: PackageType.FC,
      quantity: 100,
      quantityLevel: 'K',
      waferSize: 8,
      dieSize: '8.5mm x 8.5mm',
      leadCount: 0,
      specifications: '650V/75A车规级IGBT，Tj=175°C，符合AEC-Q101标准',
      datasheet: 'https://example.com/datasheets/BYD-IGBT-650V.pdf'
    },
    testRequirements: {
      testType: TestRequirement.RELIABILITY,
      cpTestRequirement: '电参数测试：Vce(sat), Vf, Ices, Iges',
      ftTestRequirement: '功率循环测试，热阻测试',
      reliabilityTest: true,
      customTestSpec: 'AEC-Q101汽车功率器件认证测试',
      yieldRequirement: 99.0
    },
    schedule: {
      inquiryDate: '2024-01-17',
      expectedQuoteDate: '2024-01-25',
      targetDeliveryDate: '2024-05-15',
      urgencyLevel: OrderPriority.HIGH
    },
    businessInfo: {
      budgetRange: {
        min: 45,
        max: 60,
        currency: CurrencyType.CNY
      },
      paymentTerms: '月结30天',
      contractDuration: '3年框架合同',
      volumeCommitment: 500
    },
    status: InquiryStatus.PENDING,
    priority: OrderPriority.HIGH,
    assignedSalesManager: '杨经理',
    createdAt: '2024-01-17T11:20:00Z',
    updatedAt: '2024-01-17T11:20:00Z',
    createdBy: '销售部-杨经理'
  },
  {
    id: 'INQ004',
    inquiryNumber: 'INQ-2024-004',
    customerId: 'CUST004',
    customer: {
      id: 'CUST004',
      name: '海思半导体有限公司',
      code: 'HISILICON',
      contact: {
        name: '周项目经理',
        phone: '0755-2876-5432',
        email: '<EMAIL>',
        department: '消费电子芯片部'
      },
      industryType: ProductType.CONSUMER
    },
    productInfo: {
      productName: '智能手机应用处理器',
      productCode: 'KIRIN-9000S',
      productType: ProductType.CONSUMER,
      packageType: PackageType.FC,
      quantity: 5000,
      quantityLevel: 'K',
      waferSize: 12,
      dieSize: '10.8mm x 10.8mm',
      leadCount: 0,
      specifications: '5nm工艺，8核CPU，24核GPU，双NPU，支持5G',
      datasheet: 'https://example.com/datasheets/KIRIN-9000S.pdf'
    },
    testRequirements: {
      testType: TestRequirement.CP_FT,
      cpTestRequirement: '5nm工艺全参数测试，多核CPU/GPU功能测试',
      ftTestRequirement: '最终系统级测试，5G通信测试',
      reliabilityTest: false,
      yieldRequirement: 85.0
    },
    schedule: {
      inquiryDate: '2024-01-18',
      expectedQuoteDate: '2024-01-28',
      targetDeliveryDate: '2024-06-30',
      urgencyLevel: OrderPriority.URGENT
    },
    businessInfo: {
      volumeCommitment: 20000
    },
    status: InquiryStatus.CONFIRMED,
    priority: OrderPriority.URGENT,
    assignedSalesManager: '孙经理',
    assignedEngineer: '李工程师',
    evaluation: {
      technicalFeasibility: 'challenging',
      capacityAvailability: 'limited',
      riskAssessment: 'medium',
      evaluationNotes: '5nm工艺复杂，需要高端设备，产能需要调配'
    },
    quotation: {
      quoteNumber: 'QUO-2024-004',
      unitPrice: 128.0,
      totalAmount: 640000000,
      currency: CurrencyType.CNY,
      validityPeriod: '2024-03-01',
      paymentTerms: '月结60天',
      deliveryTerms: 'DDP 深圳',
      quotedAt: '2024-01-25T09:30:00Z',
      quotedBy: '孙经理'
    },
    createdAt: '2024-01-18T14:35:00Z',
    updatedAt: '2024-01-25T09:30:00Z',
    createdBy: '销售部-孙经理',
    notes: '华为海思重要客户，技术要求高，需要技术团队重点支持'
  },
  {
    id: 'INQ005',
    inquiryNumber: 'INQ-2024-005',
    customerId: 'CUST005',
    customer: {
      id: 'CUST005',
      name: '联发科技股份有限公司',
      code: 'MTK',
      contact: {
        name: '林采购主管',
        phone: '+886-3-567-0766',
        email: '<EMAIL>',
        department: 'Global Sourcing'
      },
      industryType: ProductType.CONSUMER
    },
    productInfo: {
      productName: '智能电视主芯片',
      productCode: 'MT9950',
      productType: ProductType.CONSUMER,
      packageType: PackageType.BGA,
      quantity: 1000,
      quantityLevel: 'K',
      waferSize: 12,
      dieSize: '12.5mm x 12.5mm',
      leadCount: 529,
      specifications: '12nm工艺，四核A78，支持8K解码，WiFi6，HDMI2.1',
      datasheet: 'https://example.com/datasheets/MT9950.pdf'
    },
    testRequirements: {
      testType: TestRequirement.CP_FT,
      cpTestRequirement: '数字逻辑功能测试，模拟电路参数测试',
      ftTestRequirement: '多媒体解码测试，接口功能测试',
      reliabilityTest: false,
      yieldRequirement: 92.0
    },
    schedule: {
      inquiryDate: '2024-01-19',
      expectedQuoteDate: '2024-01-26',
      targetDeliveryDate: '2024-04-30',
      urgencyLevel: OrderPriority.MEDIUM
    },
    status: InquiryStatus.REJECTED,
    priority: OrderPriority.MEDIUM,
    assignedSalesManager: '钱经理',
    evaluation: {
      technicalFeasibility: 'feasible',
      capacityAvailability: 'unavailable',
      riskAssessment: 'high',
      evaluationNotes: '产能不足，交期冲突，建议延期或减量'
    },
    createdAt: '2024-01-19T15:45:00Z',
    updatedAt: '2024-01-22T10:15:00Z',
    createdBy: '销售部-钱经理',
    notes: '产能冲突，已与客户沟通调整交期方案'
  }
]

// 模拟询价统计数据
export const mockInquiryStats: InquiryStats = {
  totalInquiries: 28,
  pendingInquiries: 8,
  quotedInquiries: 12,
  confirmedInquiries: 6,
  conversionRate: 21.4,
  averageQuoteTime: 5.2,
  totalQuotedValue: 158600000,
  monthlyInquiryTrend: [
    { month: '2023-09', count: 15, value: 45800000 },
    { month: '2023-10', count: 18, value: 52300000 },
    { month: '2023-11', count: 22, value: 68900000 },
    { month: '2023-12', count: 25, value: 75200000 },
    { month: '2024-01', count: 28, value: 95600000 }
  ]
}

// 模拟API：获取询价列表
export function mockGetInquiries(params: InquiryQueryParams = {}): Promise<InquiryListResponse> {
  return new Promise(resolve => {
    setTimeout(() => {
      let filteredData = [...mockInquiries]

      // 过滤逻辑
      if (params.inquiryNumber) {
        filteredData = filteredData.filter(item =>
          item.inquiryNumber.toLowerCase().includes(params.inquiryNumber!.toLowerCase())
        )
      }

      if (params.customerName) {
        filteredData = filteredData.filter(item =>
          item.customer.name.toLowerCase().includes(params.customerName!.toLowerCase())
        )
      }

      if (params.status && params.status.length > 0) {
        filteredData = filteredData.filter(item => params.status!.includes(item.status))
      }

      if (params.priority && params.priority.length > 0) {
        filteredData = filteredData.filter(item => params.priority!.includes(item.priority))
      }

      if (params.productType && params.productType.length > 0) {
        filteredData = filteredData.filter(item =>
          params.productType!.includes(item.productInfo.productType)
        )
      }

      if (params.packageType && params.packageType.length > 0) {
        filteredData = filteredData.filter(item =>
          params.packageType!.includes(item.productInfo.packageType)
        )
      }

      // 分页
      const page = params.page || 1
      const pageSize = params.pageSize || 10
      const total = filteredData.length
      const startIndex = (page - 1) * pageSize
      const endIndex = Math.min(startIndex + pageSize, total)
      const data = filteredData.slice(startIndex, endIndex)

      resolve({
        data,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      })
    }, 300) // 模拟网络延迟
  })
}

// 模拟API：获取询价统计
export function mockGetInquiryStats(): Promise<InquiryStats> {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(mockInquiryStats)
    }, 200)
  })
}

// 模拟API：创建询价
export function mockCreateInquiry(data: any): Promise<CustomerInquiry> {
  return new Promise(resolve => {
    setTimeout(() => {
      const newInquiry: CustomerInquiry = {
        id: `INQ${String(mockInquiries.length + 1).padStart(3, '0')}`,
        inquiryNumber: `INQ-2024-${String(mockInquiries.length + 1).padStart(3, '0')}`,
        ...data,
        status: InquiryStatus.PENDING,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: '当前用户'
      }
      mockInquiries.push(newInquiry)
      resolve(newInquiry)
    }, 500)
  })
}

// 模拟API：更新询价状态
export function mockUpdateInquiryStatus(
  id: string,
  status: InquiryStatus
): Promise<CustomerInquiry> {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const inquiry = mockInquiries.find(item => item.id === id)
      if (inquiry) {
        inquiry.status = status
        inquiry.updatedAt = new Date().toISOString()
        resolve(inquiry)
      } else {
        reject(new Error('询价不存在'))
      }
    }, 300)
  })
}

// 模拟API：删除询价
export function mockDeleteInquiry(id: string): Promise<boolean> {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockInquiries.findIndex(item => item.id === id)
      if (index > -1) {
        mockInquiries.splice(index, 1)
        resolve(true)
      } else {
        reject(new Error('询价不存在'))
      }
    }, 300)
  })
}

// 状态选项
export const inquiryStatusOptions = [
  { label: '待评估', value: InquiryStatus.PENDING, color: '#909399' },
  { label: '评估中', value: InquiryStatus.EVALUATING, color: '#E6A23C' },
  { label: '已报价', value: InquiryStatus.QUOTED, color: '#409EFF' },
  { label: '已确认', value: InquiryStatus.CONFIRMED, color: '#67C23A' },
  { label: '已拒绝', value: InquiryStatus.REJECTED, color: '#F56C6C' },
  { label: '已过期', value: InquiryStatus.EXPIRED, color: '#C0C4CC' }
]

// 产品类型选项
export const productTypeOptions = [
  { label: '汽车电子IC', value: ProductType.AUTOMOTIVE },
  { label: '消费电子IC', value: ProductType.CONSUMER },
  { label: '通信IC', value: ProductType.COMMUNICATION },
  { label: '工业IC', value: ProductType.INDUSTRIAL },
  { label: '医疗IC', value: ProductType.MEDICAL },
  { label: '航空航天IC', value: ProductType.AEROSPACE },
  { label: '计算IC', value: ProductType.COMPUTING }
]

// 封装类型选项
export const packageTypeOptions = [
  { label: 'QFP', value: PackageType.QFP },
  { label: 'BGA', value: PackageType.BGA },
  { label: 'CSP', value: PackageType.CSP },
  { label: 'FC', value: PackageType.FC },
  { label: 'SOP', value: PackageType.SOP },
  { label: 'TSOP', value: PackageType.TSOP },
  { label: 'QFN', value: PackageType.QFN },
  { label: 'DFN', value: PackageType.DFN },
  { label: 'WLCSP', value: PackageType.WLCSP },
  { label: 'SSOP', value: PackageType.SSOP },
  { label: 'TQFP', value: PackageType.TQFP }
]

// 优先级选项
export const priorityOptions = [
  { label: '低', value: OrderPriority.LOW, color: '#909399' },
  { label: '中', value: OrderPriority.MEDIUM, color: '#E6A23C' },
  { label: '高', value: OrderPriority.HIGH, color: '#F56C6C' },
  { label: '紧急', value: OrderPriority.URGENT, color: '#F56C6C' }
]
