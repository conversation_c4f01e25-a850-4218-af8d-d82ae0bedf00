<template>
  <div class="equipment-dashboard">
    <!-- 顶部设备状态概览 -->
    <div class="dashboard-header">
      <div class="equipment-overview">
        <div class="overview-card">
          <div class="card-content">
            <div class="stat-circle">
              <el-progress
                type="circle"
                :width="120"
                :percentage="Math.round((equipmentSummary.running / equipmentSummary.total) * 100)"
                :color="'#67c23a'"
                :stroke-width="8"
              >
                <template #default="{ percentage }">
                  <div class="progress-content">
                    <div class="value">
                      {{ equipmentSummary.running }}
                    </div>
                    <div class="total">/ {{ equipmentSummary.total }}</div>
                    <div class="label">运行中</div>
                  </div>
                </template>
              </el-progress>
            </div>
            <div class="stats-grid">
              <div class="stat-item running">
                <div class="stat-value">
                  {{ equipmentSummary.running }}
                </div>
                <div class="stat-label">运行中</div>
              </div>
              <div class="stat-item idle">
                <div class="stat-value">
                  {{ equipmentSummary.idle }}
                </div>
                <div class="stat-label">空闲</div>
              </div>
              <div class="stat-item maintenance">
                <div class="stat-value">
                  {{ equipmentSummary.maintenance }}
                </div>
                <div class="stat-label">维护</div>
              </div>
              <div class="stat-item alarm">
                <div class="stat-value">
                  {{ equipmentSummary.alarm }}
                </div>
                <div class="stat-label">告警</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 整体OEE指标 -->
        <div class="oee-cards">
          <KPICard
            title="整体OEE"
            :value="overallOEE"
            unit="%"
            :trend="oeeTrend"
            target="85"
            type="primary"
            icon="analysis"
            size="large"
            subtitle="设备综合效率"
          />

          <KPICard
            title="可用性"
            :value="averageAvailability"
            unit="%"
            type="success"
            icon="monitor"
            size="large"
            subtitle="设备可用时间比例"
          />

          <KPICard
            title="性能指数"
            :value="averagePerformance"
            unit="%"
            type="warning"
            icon="trend"
            size="large"
            subtitle="设备运行效率"
          />
        </div>
      </div>
    </div>

    <!-- 中间内容区域 -->
    <div class="dashboard-content">
      <!-- 左侧：设备健康度热力图 -->
      <div class="heatmap-section">
        <ChartPanel
          title="设备健康度热力图"
          subtitle="实时设备运行状态和健康度分布"
          :options="heatmapChartOptions"
          :loading="loading"
          height="400px"
          :show-footer="true"
          :data-count="equipmentStatus.length"
          @refresh="refreshEquipmentData"
        />
      </div>

      <!-- 右侧：关键设备状态 -->
      <div class="critical-equipment">
        <div class="section-card">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon><Warning /></el-icon>
              关键设备状态
            </h3>
            <div class="header-actions">
              <el-button
                size="small"
                :icon="Refresh"
                :loading="loading"
                @click="refreshEquipmentData"
              >
                刷新
              </el-button>
            </div>
          </div>

          <div class="equipment-list">
            <div
              v-for="equipment in criticalEquipment"
              :key="equipment.equipmentId"
              :class="['equipment-item', `equipment-item--${equipment.status}`]"
            >
              <div class="equipment-header">
                <StatusIndicator
                  :status="getEquipmentStatus(equipment.status, equipment.health)"
                  :label="equipment.equipmentName"
                  :description="`${equipment.equipmentType} | ${equipment.location.area}`"
                  size="medium"
                />

                <div class="equipment-metrics">
                  <div class="metric">
                    <span class="value">{{ equipment.oee.overall.toFixed(1) }}%</span>
                    <span class="label">OEE</span>
                  </div>
                  <div class="metric">
                    <span class="value">{{ equipment.utilization.toFixed(0) }}%</span>
                    <span class="label">利用率</span>
                  </div>
                </div>
              </div>

              <div class="equipment-details">
                <div class="detail-row">
                  <span class="label">当前Recipe:</span>
                  <span class="value">{{ equipment.currentRecipe }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">操作员:</span>
                  <span class="value">{{ equipment.operator || '无' }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">告警数:</span>
                  <span :class="['value', equipment.alarmCount > 0 ? 'text-warning' : '']">
                    {{ equipment.alarmCount }}
                  </span>
                </div>
              </div>

              <!-- 设备参数 -->
              <div
v-if="equipment.temperature || equipment.pressure" class="parameter-grid"
>
                <div
v-if="equipment.temperature" class="parameter-item"
>
                  <div class="parameter-label">温度</div>
                  <div class="parameter-value">
{{ equipment.temperature.toFixed(1) }}°C
</div>
                </div>
                <div
v-if="equipment.pressure" class="parameter-item"
>
                  <div class="parameter-label">压力</div>
                  <div class="parameter-value">
{{ equipment.pressure.toFixed(1) }} kPa
</div>
                </div>
                <div
v-if="equipment.vibration" class="parameter-item"
>
                  <div class="parameter-label">振动</div>
                  <div class="parameter-value">
{{ equipment.vibration.toFixed(2) }} mm/s
</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部图表区域 -->
    <div class="dashboard-footer">
      <!-- OEE趋势图 -->
      <div class="chart-section">
        <ChartPanel
          title="OEE趋势分析"
          subtitle="设备综合效率变化趋势"
          :options="oeeTrendOptions"
          :loading="loading"
          :time-ranges="timeRanges"
          height="300px"
          @time-range-change="handleTimeRangeChange"
        />
      </div>

      <!-- 告警分布 -->
      <div class="chart-section">
        <ChartPanel
          title="告警分布统计"
          subtitle="设备告警类型和频次分析"
          :options="alarmDistributionOptions"
          :loading="loading"
          height="300px"
        />
      </div>

      <!-- 维护计划 -->
      <div class="maintenance-section">
        <div class="section-card">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon><Tools /></el-icon>
              维护计划
            </h3>
          </div>

          <div class="maintenance-timeline">
            <div
              v-for="item in maintenanceSchedule"
              :key="item.scheduleId"
              :class="['timeline-item', `timeline-item--${item.priority}`]"
            >
              <div class="timeline-marker" />
              <div class="timeline-content">
                <div class="timeline-header">
                  <span class="equipment-name">{{ item.equipmentName }}</span>
                  <span class="maintenance-type">
                    {{ getMaintenanceTypeName(item.maintenanceType) }}
                  </span>
                </div>
                <div class="timeline-body">
                  <div class="maintenance-date">
                    {{ formatMaintenanceDate(item.plannedDate) }}
                  </div>
                  <div class="maintenance-description">
                    {{ item.description }}
                  </div>
                  <div class="maintenance-assignee">
负责人: {{ item.assignedTechnician }}
</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 活跃告警浮动窗口 -->
    <div v-if="activeAlarms.length > 0" class="floating-alarms">
      <div class="alarms-header">
        <el-icon class="alarm-icon">
          <Warning />
        </el-icon>
        <span>活跃告警 ({{ activeAlarms.length }})</span>
        <el-button
size="small" text
@click="showAllAlarms = !showAllAlarms"
>
          {{ showAllAlarms ? '收起' : '展开' }}
        </el-button>
      </div>

      <div v-show="showAllAlarms" class="alarms-content">
        <div
          v-for="alarm in activeAlarms.slice(0, 5)"
          :key="alarm.alarmId"
          :class="['alarm-item', `alarm-item--${alarm.alarmType}`]"
        >
          <div class="alarm-header">
            <span class="alarm-equipment">{{ alarm.equipmentName }}</span>
            <span class="alarm-code">{{ alarm.alarmCode }}</span>
          </div>
          <div class="alarm-message">
            {{ alarm.alarmMessage }}
          </div>
          <div class="alarm-time">
            {{ formatAlarmTime(alarm.alarmTime) }}
          </div>
        </div>

        <div v-if="activeAlarms.length > 5" class="alarm-more">
          还有 {{ activeAlarms.length - 5 }} 条告警...
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted } from 'vue'
  import { Warning, Refresh, Tools } from '@element-plus/icons-vue'
  import { KPICard, ChartPanel, StatusIndicator } from '@/components/monitoring'
  import type { StatusType } from '@/components/monitoring'
  import { useMonitoring } from '@/composables/useMonitoring'
  import type { EChartsOption } from 'echarts'
  import type { EquipmentStatus, MaintenanceSchedule, EquipmentAlarm } from '@/types/monitoring'

  // 使用监控数据管理
  const { loading, equipmentStatus, equipmentSummary, refreshEquipmentData } = useMonitoring()

  // 响应式数据
  const showAllAlarms = ref(false)
  const timeRange = ref('24h')

  // 时间范围选项
  const timeRanges = [
    { label: '1小时', value: '1h' },
    { label: '4小时', value: '4h' },
    { label: '12小时', value: '12h' },
    { label: '24小时', value: '24h' },
    { label: '7天', value: '7d' }
  ]

  // 模拟数据
  const activeAlarms = ref<EquipmentAlarm[]>([
    {
      alarmId: 'ALM001',
      equipmentId: 'PRB001',
      equipmentName: 'Prober #1',
      alarmType: 'critical',
      alarmCode: 'E1001',
      alarmMessage: '探针卡寿命到期，需要更换',
      alarmTime: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      isActive: true
    },
    {
      alarmId: 'ALM002',
      equipmentId: 'BND001',
      equipmentName: 'Wire Bonder #1',
      alarmType: 'warning',
      alarmCode: 'W2001',
      alarmMessage: '线键合拉力参数超出警告范围',
      alarmTime: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
      isActive: true
    }
  ])

  const maintenanceSchedule = ref<MaintenanceSchedule[]>([
    {
      scheduleId: 'MS001',
      equipmentId: 'PRB001',
      equipmentName: 'Prober #1',
      maintenanceType: 'preventive',
      plannedDate: new Date(Date.now() + 2 * 24 * 3600 * 1000).toISOString(),
      estimatedDuration: 4,
      assignedTechnician: '张工程师',
      description: '探针卡校准和清洁维护',
      status: 'scheduled',
      priority: 'high'
    },
    {
      scheduleId: 'MS002',
      equipmentId: 'MOL001',
      equipmentName: 'Molding Machine #1',
      maintenanceType: 'predictive',
      plannedDate: new Date(Date.now() + 5 * 24 * 3600 * 1000).toISOString(),
      estimatedDuration: 8,
      assignedTechnician: '李技师',
      description: '预测性维护：加热模块检查',
      status: 'scheduled',
      priority: 'medium'
    }
  ])

  // 计算属性
  const criticalEquipment = computed(() => {
    return equipmentStatus.value
      .filter(eq => eq.status === 'running' || eq.alarmCount > 0)
      .sort((a, b) => b.alarmCount - a.alarmCount)
      .slice(0, 6)
  })

  const overallOEE = computed(() => {
    if (!equipmentStatus.value.length) return 0
    const totalOEE = equipmentStatus.value.reduce((sum, eq) => sum + eq.oee.overall, 0)
    return Math.round(totalOEE / equipmentStatus.value.length)
  })

  const averageAvailability = computed(() => {
    if (!equipmentStatus.value.length) return 0
    const total = equipmentStatus.value.reduce((sum, eq) => sum + eq.oee.availability, 0)
    return Math.round(total / equipmentStatus.value.length)
  })

  const averagePerformance = computed(() => {
    if (!equipmentStatus.value.length) return 0
    const total = equipmentStatus.value.reduce((sum, eq) => sum + eq.oee.performance, 0)
    return Math.round(total / equipmentStatus.value.length)
  })

  const oeeTrend = computed(() => {
    // 模拟OEE趋势
    return 1.2
  })

  // 设备健康度热力图
  const heatmapChartOptions = computed<EChartsOption>(() => {
    const data: any[] = []
    const xAxisData: string[] = []
    const yAxisData: string[] = []

    // 构建热力图数据
    equipmentStatus.value.forEach((equipment, index) => {
      const area = equipment.location.area
      const position = equipment.location.position

      if (!yAxisData.includes(area)) yAxisData.push(area)
      if (!xAxisData.includes(position)) xAxisData.push(position)

      const xIndex = xAxisData.indexOf(position)
      const yIndex = yAxisData.indexOf(area)

      // 健康度转换为数值
      const healthValue =
        {
          excellent: 100,
          good: 80,
          fair: 60,
          poor: 40,
          critical: 20
        }[equipment.health] || 50

      data.push([xIndex, yIndex, healthValue, equipment.equipmentName])
    })

    return {
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          const [x, y, value, name] = params.data
          return `设备: ${name}<br/>区域: ${yAxisData[y]}<br/>位置: ${xAxisData[x]}<br/>健康度: ${value}`
        }
      },
      grid: {
        left: '10%',
        right: '5%',
        bottom: '10%',
        top: '10%'
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLabel: {
          color: 'var(--color-text-secondary)'
        },
        axisLine: {
          lineStyle: {
            color: 'var(--color-border-light)'
          }
        }
      },
      yAxis: {
        type: 'category',
        data: yAxisData,
        axisLabel: {
          color: 'var(--color-text-secondary)'
        },
        axisLine: {
          lineStyle: {
            color: 'var(--color-border-light)'
          }
        }
      },
      visualMap: {
        min: 0,
        max: 100,
        calculable: true,
        orient: 'vertical',
        left: 'right',
        top: 'center',
        textStyle: {
          color: 'var(--color-text-secondary)'
        },
        inRange: {
          color: ['#d94848', '#f4664a', '#faac16', '#4ade80', '#06d6a0']
        }
      },
      series: [
        {
          name: '设备健康度',
          type: 'heatmap',
          data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
  })

  // OEE趋势图
  const oeeTrendOptions = computed<EChartsOption>(() => {
    const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`)
    const availabilityData = hours.map(() => 85 + Math.random() * 10)
    const performanceData = hours.map(() => 88 + Math.random() * 8)
    const qualityData = hours.map(() => 95 + Math.random() * 4)
    const oeeData = hours.map(
      (_, i) => (availabilityData[i] * performanceData[i] * qualityData[i]) / 10000
    )

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['可用性', '性能指数', '质量率', 'OEE'],
        bottom: 10,
        textStyle: {
          color: 'var(--color-text-primary)'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: hours,
        axisLabel: {
          color: 'var(--color-text-secondary)',
          interval: 3
        }
      },
      yAxis: {
        type: 'value',
        max: 100,
        axisLabel: {
          formatter: '{value}%',
          color: 'var(--color-text-secondary)'
        },
        splitLine: {
          lineStyle: {
            color: 'var(--color-border-lighter)',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '可用性',
          type: 'line',
          data: availabilityData,
          smooth: true,
          itemStyle: { color: '#1890ff' }
        },
        {
          name: '性能指数',
          type: 'line',
          data: performanceData,
          smooth: true,
          itemStyle: { color: '#52c41a' }
        },
        {
          name: '质量率',
          type: 'line',
          data: qualityData,
          smooth: true,
          itemStyle: { color: '#faad14' }
        },
        {
          name: 'OEE',
          type: 'line',
          data: oeeData,
          smooth: true,
          lineStyle: { width: 3 },
          itemStyle: { color: '#f5222d' }
        }
      ]
    }
  })

  // 告警分布图
  const alarmDistributionOptions = computed<EChartsOption>(() => ({
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '告警分布',
        type: 'pie',
        radius: '60%',
        center: ['50%', '50%'],
        data: [
          { value: 15, name: '温度异常', itemStyle: { color: '#f5222d' } },
          { value: 8, name: '压力异常', itemStyle: { color: '#faad14' } },
          { value: 5, name: '通信超时', itemStyle: { color: '#1890ff' } },
          { value: 12, name: 'Recipe异常', itemStyle: { color: '#722ed1' } },
          { value: 3, name: '设备故障', itemStyle: { color: '#eb2f96' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }))

  // 方法
  const getEquipmentStatus = (status: string, health: string): StatusType => {
    if (status === 'alarm') return 'alarm'
    if (status === 'offline') return 'offline'
    if (status === 'maintenance') return 'maintenance'
    if (health === 'critical' || health === 'poor') return 'warning'
    if (status === 'running') return 'running'
    return 'idle'
  }

  const getMaintenanceTypeName = (type: string): string => {
    const typeNames: Record<string, string> = {
      preventive: '预防性维护',
      predictive: '预测性维护',
      emergency: '应急维护'
    }
    return typeNames[type] || type
  }

  const formatMaintenanceDate = (dateString: string): string => {
    const date = new Date(dateString)
    const today = new Date()
    const diffDays = Math.ceil((date.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

    if (diffDays === 0) return '今天'
    if (diffDays === 1) return '明天'
    if (diffDays === 2) return '后天'
    if (diffDays > 0) return `${diffDays}天后`
    return `${Math.abs(diffDays)}天前`
  }

  const formatAlarmTime = (timeString: string): string => {
    const date = new Date(timeString)
    const now = new Date()
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffMinutes < 1) return '刚刚'
    if (diffMinutes < 60) return `${diffMinutes}分钟前`

    const diffHours = Math.floor(diffMinutes / 60)
    if (diffHours < 24) return `${diffHours}小时前`

    const diffDays = Math.floor(diffHours / 24)
    return `${diffDays}天前`
  }

  const handleTimeRangeChange = (range: string) => {
    timeRange.value = range
  }

  // 生命周期
  onMounted(() => {
    refreshEquipmentData()
  })
</script>

<style lang="scss" scoped>
  .equipment-dashboard {
    position: relative;
    min-height: 100vh;
    padding: var(--spacing-6);
    background: var(--color-bg-page);

    @media (width >= 1920px) {
      padding: var(--spacing-8);
    }
  }

  .dashboard-header {
    margin-bottom: var(--spacing-6);

    .equipment-overview {
      display: grid;
      grid-template-columns: 350px 1fr;
      gap: var(--spacing-6);

      @media (width <= 1200px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
      }

      .overview-card {
        background: var(--color-bg-primary);
        border: 1px solid var(--color-border-light);
        border-radius: var(--radius-lg);

        .card-content {
          display: flex;
          gap: var(--spacing-6);
          align-items: center;
          padding: var(--spacing-6);

          .stat-circle {
            flex-shrink: 0;
          }

          .progress-content {
            text-align: center;

            .value {
              font-size: 1.5rem;
              font-weight: var(--font-weight-bold);
              line-height: 1;
              color: var(--color-text-primary);
            }

            .total {
              font-size: var(--font-size-sm);
              color: var(--color-text-secondary);
            }

            .label {
              margin-top: var(--spacing-1);
              font-size: var(--font-size-xs);
              color: var(--color-text-secondary);
            }
          }

          .stats-grid {
            display: grid;
            flex: 1;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-4);

            .stat-item {
              text-align: center;

              .stat-value {
                font-size: 1.5rem;
                font-weight: var(--font-weight-bold);
                line-height: 1.2;
              }

              .stat-label {
                margin-top: var(--spacing-1);
                font-size: var(--font-size-sm);
                color: var(--color-text-secondary);
              }

              &.running .stat-value {
                color: var(--color-success);
              }

              &.idle .stat-value {
                color: var(--color-info);
              }

              &.maintenance .stat-value {
                color: var(--color-warning);
              }

              &.alarm .stat-value {
                color: var(--color-danger);
              }
            }
          }
        }
      }

      .oee-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-4);
      }
    }
  }

  .dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-6);

    @media (width <= 1200px) {
      grid-template-columns: 1fr;
    }
  }

  .dashboard-footer {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--spacing-6);

    @media (width <= 1200px) {
      grid-template-columns: 1fr;
    }

    @media (width >= 1200px) and (width <= 1600px) {
      grid-template-columns: 1fr 1fr;

      .maintenance-section {
        grid-column: 1 / -1;
      }
    }
  }

  .section-card {
    overflow: hidden;
    background: var(--color-bg-primary);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-lg);

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-5);
      border-bottom: 1px solid var(--color-border-lighter);

      .card-title {
        display: flex;
        align-items: center;
        margin: 0;
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--color-text-primary);

        .el-icon {
          margin-right: var(--spacing-2);
          color: var(--color-primary);
        }
      }
    }
  }

  .equipment-list {
    max-height: 400px;
    padding: var(--spacing-4);
    overflow-y: auto;

    .equipment-item {
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-3);
      border: 1px solid var(--color-border-lighter);
      border-radius: var(--radius-base);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: var(--shadow-sm);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .equipment-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: var(--spacing-3);

        .equipment-metrics {
          display: flex;
          gap: var(--spacing-3);

          .metric {
            text-align: center;

            .value {
              display: block;
              font-size: var(--font-size-base);
              font-weight: var(--font-weight-semibold);
              line-height: 1.2;
              color: var(--color-text-primary);
            }

            .label {
              font-size: var(--font-size-xs);
              color: var(--color-text-secondary);
            }
          }
        }
      }

      .equipment-details {
        margin-bottom: var(--spacing-3);

        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: var(--spacing-1);

          .label {
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
          }

          .value {
            font-size: var(--font-size-sm);
            color: var(--color-text-primary);

            &.text-warning {
              color: var(--color-warning);
            }
          }
        }
      }

      .parameter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
        gap: var(--spacing-2);

        .parameter-item {
          padding: var(--spacing-2);
          text-align: center;
          background: var(--color-bg-secondary);
          border-radius: var(--radius-sm);

          .parameter-label {
            margin-bottom: var(--spacing-1);
            font-size: var(--font-size-xs);
            color: var(--color-text-secondary);
          }

          .parameter-value {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--color-text-primary);
          }
        }
      }
    }
  }

  .maintenance-timeline {
    max-height: 300px;
    padding: var(--spacing-5);
    overflow-y: auto;

    .timeline-item {
      position: relative;
      display: flex;
      padding-left: var(--spacing-4);
      margin-bottom: var(--spacing-4);

      &:not(:last-child)::before {
        position: absolute;
        top: 24px;
        bottom: -16px;
        left: 6px;
        width: 2px;
        content: '';
        background: var(--color-border-light);
      }

      .timeline-marker {
        position: absolute;
        top: 8px;
        left: 0;
        width: 12px;
        height: 12px;
        background: var(--color-bg-primary);
        border: 2px solid var(--color-border-light);
        border-radius: 50%;
      }

      .timeline-content {
        flex: 1;
        padding: var(--spacing-3);
        background: var(--color-bg-secondary);
        border-left: 4px solid var(--color-border-light);
        border-radius: var(--radius-base);

        .timeline-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: var(--spacing-2);

          .equipment-name {
            font-weight: var(--font-weight-semibold);
            color: var(--color-text-primary);
          }

          .maintenance-type {
            padding: 2px 8px;
            font-size: var(--font-size-xs);
            color: var(--color-text-secondary);
            background: var(--color-bg-tertiary);
            border-radius: var(--radius-sm);
          }
        }

        .timeline-body {
          .maintenance-date {
            margin-bottom: var(--spacing-1);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--color-primary);
          }

          .maintenance-description {
            margin-bottom: var(--spacing-1);
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
          }

          .maintenance-assignee {
            font-size: var(--font-size-xs);
            color: var(--color-text-tertiary);
          }
        }
      }

      &--high {
        .timeline-marker {
          background: var(--color-danger);
          border-color: var(--color-danger);
        }

        .timeline-content {
          border-left-color: var(--color-danger);
        }
      }

      &--medium {
        .timeline-marker {
          background: var(--color-warning);
          border-color: var(--color-warning);
        }

        .timeline-content {
          border-left-color: var(--color-warning);
        }
      }

      &--low {
        .timeline-marker {
          background: var(--color-success);
          border-color: var(--color-success);
        }

        .timeline-content {
          border-left-color: var(--color-success);
        }
      }
    }
  }

  .floating-alarms {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    width: 300px;
    background: var(--color-bg-primary);
    border: 1px solid var(--color-danger);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);

    .alarms-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-3);
      color: white;
      background: var(--color-danger);
      border-radius: var(--radius-lg) var(--radius-lg) 0 0;

      .alarm-icon {
        margin-right: var(--spacing-2);
        animation: pulse 2s infinite;
      }
    }

    .alarms-content {
      max-height: 300px;
      overflow-y: auto;

      .alarm-item {
        padding: var(--spacing-3);
        border-bottom: 1px solid var(--color-border-lighter);

        &:last-child {
          border-bottom: none;
        }

        .alarm-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: var(--spacing-1);

          .alarm-equipment {
            font-weight: var(--font-weight-semibold);
            color: var(--color-text-primary);
          }

          .alarm-code {
            font-family: monospace;
            font-size: var(--font-size-xs);
            color: var(--color-text-secondary);
          }
        }

        .alarm-message {
          margin-bottom: var(--spacing-1);
          font-size: var(--font-size-sm);
          line-height: 1.4;
          color: var(--color-text-secondary);
        }

        .alarm-time {
          font-size: var(--font-size-xs);
          color: var(--color-text-tertiary);
        }

        &--critical {
          border-left: 4px solid var(--color-danger);
        }

        &--warning {
          border-left: 4px solid var(--color-warning);
        }

        &--info {
          border-left: 4px solid var(--color-info);
        }
      }

      .alarm-more {
        padding: var(--spacing-2);
        font-size: var(--font-size-xs);
        color: var(--color-text-secondary);
        text-align: center;
        background: var(--color-bg-secondary);
      }
    }
  }

  // 深色主题适配
  .dark {
    .equipment-dashboard {
      background: var(--color-bg-page);
    }

    .overview-card,
    .section-card {
      background: var(--color-bg-secondary);
      border-color: var(--color-border-dark);
    }

    .floating-alarms {
      background: var(--color-bg-secondary);
      border-color: var(--color-danger);
    }
  }

  @keyframes pulse {
    0%,
    100% {
      transform: scale(1);
    }

    50% {
      transform: scale(1.1);
    }
  }
</style>
