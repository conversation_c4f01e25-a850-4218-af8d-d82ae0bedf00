<!-- IC封测CIM系统 - 主题切换组件 -->
<!-- 基于设计规范的极简主题切换器 -->

<template>
  <button 
    class="theme-toggle"
    @click="toggleTheme"
    :aria-label="isDark ? '切换到浅色主题' : '切换到深色主题'"
  >
    <svg 
      class="theme-icon sun"
      :class="{ active: !isDark }"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
    >
      <circle cx="12" cy="12" r="5"/>
      <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
    </svg>
    
    <svg 
      class="theme-icon moon"
      :class="{ active: isDark }"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
    >
      <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
    </svg>
  </button>
</template>

<script setup lang="ts">
import { useTheme } from '@/composables/useTheme'

const { isDark, toggleTheme } = useTheme()
</script>

<style lang="scss" scoped>
.theme-toggle {
  position: relative;
  width: 40px;
  height: 40px;
  border: 1px solid var(--color-border-base);
  border-radius: var(--radius-lg);
  background: var(--color-bg-primary);
  color: var(--color-text-secondary);
  transition: all var(--transition-fast);
  
  &:hover {
    border-color: var(--color-border-dark);
    color: var(--color-text-primary);
  }
  
  .theme-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 18px;
    height: 18px;
    stroke-width: 1.5;
    opacity: 0;
    transform-origin: center;
    transition: all var(--transition-normal);
    
    &.active {
      opacity: 1;
      transform: translate(-50%, -50%) rotate(0deg);
    }
    
    &:not(.active) {
      transform: translate(-50%, -50%) rotate(45deg);
    }
  }
}
</style>