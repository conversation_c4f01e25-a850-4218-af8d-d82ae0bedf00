// IC封测CIM系统 - 字体排版系统
// 极简主义字体设计

// ===== 基础字体类 =====
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

// ===== 字重类 =====
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-bold { font-weight: var(--font-weight-bold); }

// ===== 行高类 =====
.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-loose { line-height: var(--line-height-loose); }

// ===== 文字颜色类 =====
.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-tertiary { color: var(--color-text-tertiary); }
.text-disabled { color: var(--color-text-disabled); }
.text-white { color: var(--color-text-white); }

.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }
.text-info { color: var(--color-info); }

// ===== 文字对齐 =====
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

// ===== 文字装饰 =====
.no-underline { text-decoration: none; }
.underline { text-decoration: underline; }
.line-through { text-decoration: line-through; }

// ===== 文字变换 =====
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

// ===== 字体族 =====
.font-sans { font-family: var(--font-family-base); }
.font-mono { font-family: var(--font-family-mono); }

// ===== 文字截断 =====
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
}

// 多行文字截断
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// ===== 专业标题样式 =====
.title-primary {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-6);
}

.title-secondary {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
}

.title-tertiary {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
}

// ===== 段落样式 =====
.paragraph {
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
}

.paragraph-sm {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-3);
}

// ===== 代码样式 =====
code {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
  padding: 2px 4px;
  border-radius: var(--radius-sm);
  background-color: var(--color-bg-tertiary);
  color: var(--color-text-primary);
}

pre {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  padding: var(--spacing-4);
  border-radius: var(--radius-base);
  background-color: var(--color-bg-tertiary);
  color: var(--color-text-primary);
  overflow-x: auto;
  
  code {
    padding: 0;
    background-color: transparent;
  }
}

// ===== IC封测专业术语样式 =====
.ic-term {
  font-family: var(--font-family-mono);
  font-size: 0.9em;
  padding: 1px 3px;
  border-radius: var(--radius-sm);
  background-color: var(--color-bg-tertiary);
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

// ===== 强调文本 =====
.emphasis {
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
}

.strong {
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

// ===== 标签文本 =====
.label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-1);
  display: block;
}

// ===== 引用样式 =====
blockquote {
  border-left: 4px solid var(--color-primary);
  padding-left: var(--spacing-4);
  margin: var(--spacing-4) 0;
  font-style: italic;
  color: var(--color-text-secondary);
}