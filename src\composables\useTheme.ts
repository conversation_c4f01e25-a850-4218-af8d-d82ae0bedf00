// src/composables/useTheme.ts
import { ref, computed, watch, readonly } from 'vue'

export type Theme = 'light' | 'dark'

// 简化的主题状态管理，不依赖VueUse
const theme = ref<Theme>('light')

// 从localStorage读取保存的主题
try {
  const saved = localStorage.getItem('cim-theme') as Theme
  if (saved === 'light' || saved === 'dark') {
    theme.value = saved
  }
} catch (error) {
  console.warn('无法读取主题设置:', error)
}

export function useTheme() {
  // 切换主题
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    // 保存到localStorage
    try {
      localStorage.setItem('cim-theme', theme.value)
    } catch (error) {
      console.warn('无法保存主题设置:', error)
    }
  }

  // 设置主题
  const setTheme = (newTheme: Theme) => {
    theme.value = newTheme
    // 保存到localStorage
    try {
      localStorage.setItem('cim-theme', newTheme)
    } catch (error) {
      console.warn('无法保存主题设置:', error)
    }
  }

  // 是否为深色主题
  const isDark = computed(() => theme.value === 'dark')

  // 应用主题到DOM
  const applyTheme = () => {
    const root = document.documentElement
    root.setAttribute('data-theme', theme.value)

    // 移除之前的主题类
    root.classList.remove('light-theme', 'dark-theme')
    // 添加当前主题类
    root.classList.add(`${theme.value}-theme`)
  }

  // 监听主题变化
  watch(theme, applyTheme, { immediate: true })

  return {
    theme: readonly(theme),
    isDark,
    toggleTheme,
    setTheme,
    applyTheme
  }
}
