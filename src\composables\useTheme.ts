// src/composables/useTheme.ts
import { ref, computed, watch } from 'vue'
import { useStorage } from '@vueuse/core'

export type Theme = 'light' | 'dark'

// 主题状态
const theme = useStorage<Theme>('cim-theme', 'light')

export function useTheme() {
  // 切换主题
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
  }
  
  // 设置主题
  const setTheme = (newTheme: Theme) => {
    theme.value = newTheme
  }
  
  // 是否为深色主题
  const isDark = computed(() => theme.value === 'dark')
  
  // 应用主题到DOM
  const applyTheme = () => {
    const root = document.documentElement
    root.setAttribute('data-theme', theme.value)
    
    // 移除之前的主题类
    root.classList.remove('light-theme', 'dark-theme')
    // 添加当前主题类
    root.classList.add(`${theme.value}-theme`)
  }
  
  // 监听主题变化
  watch(theme, applyTheme, { immediate: true })
  
  return {
    theme: readonly(theme),
    isDark,
    toggleTheme,
    setTheme,
    applyTheme
  }
}