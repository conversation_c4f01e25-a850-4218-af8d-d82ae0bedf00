// 主题系统类型定义

/**
 * 主题模式类型
 */
export type ThemeMode = 'light' | 'dark'

/**
 * 主题配置接口
 */
export interface ThemeConfig {
  /** 当前主题模式 */
  mode: ThemeMode
  /** 是否跟随系统主题 */
  followSystem: boolean
  /** 主题切换动画持续时间（ms） */
  transitionDuration: number
  /** 存储键名 */
  storageKey: string
}

/**
 * 主题色彩定义接口
 */
export interface ThemeColors {
  // 主色调
  primary: string
  primaryHover: string
  primaryActive: string
  primaryLight: string
  primaryDark: string

  // 功能色彩
  success: string
  warning: string
  error: string
  info: string

  // 文本色彩
  textPrimary: string
  textSecondary: string
  textTertiary: string
  textDisabled: string
  textPlaceholder: string

  // 背景色彩
  bgPrimary: string
  bgSecondary: string
  bgTertiary: string
  bgHover: string
  bgActive: string
  bgDisabled: string

  // 边框色彩
  borderLight: string
  borderBase: string
  borderDark: string
  borderFocus: string

  // IC封测专业色彩
  wafer: string
  diePass: string
  dieFail: string
  cpTest: string
  assembly: string
  ftTest: string
}

/**
 * 主题间距系统接口
 */
export interface ThemeSpacing {
  0: string
  1: string // 4px
  2: string // 8px
  3: string // 12px
  4: string // 16px
  5: string // 20px
  6: string // 24px
  8: string // 32px
  10: string // 40px
  12: string // 48px
  16: string // 64px
  20: string // 80px
  24: string // 96px
}

/**
 * 主题字体系统接口
 */
export interface ThemeFonts {
  // 字体族
  familyBase: string
  familyMono: string

  // 字体大小
  sizeXs: string // 12px
  sizeSm: string // 14px
  sizeBase: string // 16px
  sizeLg: string // 18px
  sizeXl: string // 20px
  size2xl: string // 24px
  size3xl: string // 32px

  // 字重
  weightNormal: number // 400
  weightMedium: number // 500
  weightBold: number // 600

  // 行高
  lineHeightTight: number // 1.25
  lineHeightBase: number // 1.5
  lineHeightLoose: number // 1.75
}

/**
 * 主题圆角系统接口
 */
export interface ThemeRadius {
  none: string
  sm: string // 4px
  base: string // 6px
  md: string // 8px
  lg: string // 12px
  full: string // 9999px
}

/**
 * 主题阴影系统接口
 */
export interface ThemeShadows {
  sm: string
  base: string
  md: string
  lg: string
}

/**
 * 主题过渡动画接口
 */
export interface ThemeTransitions {
  fast: string // 0.15s ease
  normal: string // 0.25s ease
  slow: string // 0.35s ease
}

/**
 * 完整主题定义接口
 */
export interface Theme {
  mode: ThemeMode
  colors: ThemeColors
  spacing: ThemeSpacing
  fonts: ThemeFonts
  radius: ThemeRadius
  shadows: ThemeShadows
  transitions: ThemeTransitions
}

/**
 * 主题上下文接口
 */
export interface ThemeContext {
  /** 当前主题配置 */
  config: ThemeConfig
  /** 当前主题数据 */
  theme: Theme
  /** 切换主题模式 */
  toggleMode: () => void
  /** 设置主题模式 */
  setMode: (mode: ThemeMode) => void
  /** 设置跟随系统主题 */
  setFollowSystem: (follow: boolean) => void
  /** 获取CSS变量值 */
  getCSSVar: (varName: string) => string
  /** 设置CSS变量值 */
  setCSSVar: (varName: string, value: string) => void
}

/**
 * 主题事件类型
 */
export type ThemeEventType = 'mode-changed' | 'system-changed' | 'config-updated'

/**
 * 主题事件接口
 */
export interface ThemeEvent {
  type: ThemeEventType
  oldValue?: any
  newValue?: any
  timestamp: number
}

/**
 * 主题监听器函数类型
 */
export type ThemeListener = (event: ThemeEvent) => void

/**
 * 响应式断点定义
 */
export interface Breakpoints {
  sm: number // 640px
  md: number // 768px
  lg: number // 1024px
  xl: number // 1280px
  '2xl': number // 1536px
}

/**
 * 媒体查询钩子返回类型
 */
export interface MediaQueryResult {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isLargeScreen: boolean
  currentBreakpoint: keyof Breakpoints | 'xs'
}
