<template>
  <el-card class="material-card" shadow="hover" @click="$emit('view', material)">
    <template #header>
      <div class="card-header">
        <div class="material-code">{{ material.basicInfo.materialCode }}</div>
        <div class="card-actions">
          <el-tag 
            size="small"
            :color="getCategoryColor(material.basicInfo.category)"
            style="color: white"
          >
            {{ getCategoryLabel(material.basicInfo.category) }}
          </el-tag>
        </div>
      </div>
    </template>

    <div class="card-content">
      <!-- 物料基本信息 -->
      <div class="material-basic-info">
        <h4 class="material-name">{{ material.basicInfo.materialName }}</h4>
        <p class="material-english">{{ material.basicInfo.englishName }}</p>
        <p class="material-spec">{{ material.basicInfo.specification }}</p>
        <p class="material-manufacturer">{{ material.basicInfo.manufacturer }}</p>
      </div>

      <!-- 状态标签 -->
      <div class="status-section">
        <el-tag 
          :type="getStatusTagType(material.basicInfo.status)"
          size="small"
        >
          {{ getStatusLabel(material.basicInfo.status) }}
        </el-tag>
        <el-tag
          v-if="material.alternatives.length > 0"
          size="small"
          type="warning"
          style="margin-left: 8px"
        >
          有替代料
        </el-tag>
      </div>

      <!-- 技术参数 -->
      <div class="tech-params">
        <!-- 晶圆信息 -->
        <div v-if="material.technicalParams.waferInfo" class="param-item">
          <span class="param-label">晶圆:</span>
          <el-tag size="small" type="info">
            {{ material.technicalParams.waferInfo.waferSize }}寸
          </el-tag>
          <span class="param-value">厚度 {{ material.technicalParams.waferInfo.thickness }}μm</span>
        </div>

        <!-- 封装信息 -->
        <div v-if="material.technicalParams.packageInfo" class="param-item">
          <span class="param-label">封装:</span>
          <el-tag size="small" type="success">
            {{ material.technicalParams.packageInfo.packageType?.toUpperCase() }}
          </el-tag>
          <span class="param-value">{{ material.technicalParams.packageInfo.pinCount }}Pin</span>
        </div>

        <!-- 线材信息 -->
        <div v-if="material.technicalParams.wirebondInfo" class="param-item">
          <span class="param-label">线材:</span>
          <span 
            class="wire-indicator" 
            :style="{ backgroundColor: getWireColor(material.technicalParams.wirebondInfo.wireType) }"
          ></span>
          <span class="param-value">{{ material.technicalParams.wirebondInfo.diameter }}μm</span>
        </div>

        <!-- 通用尺寸信息 -->
        <div v-if="material.technicalParams.dimensions && !material.technicalParams.waferInfo" class="param-item">
          <span class="param-label">尺寸:</span>
          <span class="param-value">{{ formatDimensions(material.technicalParams.dimensions) }}</span>
        </div>
      </div>

      <!-- 成本信息 -->
      <div class="cost-section">
        <div class="cost-price">
          <span class="price-label">当前价格:</span>
          <span class="price-value">${{ material.costInfo.currentPrice.toLocaleString() }}</span>
          <span class="price-unit">/ {{ material.costInfo.priceUnit }}</span>
        </div>
        <div class="price-trend" :class="getPriceTrend(material.costInfo)">
          {{ getPriceTrendText(material.costInfo) }}
        </div>
      </div>

      <!-- 库存信息 -->
      <div class="stock-section">
        <div class="stock-item">
          <span class="stock-label">安全库存:</span>
          <span class="stock-value">{{ material.stockInfo.safetyStock }} {{ material.stockInfo.unitOfMeasure }}</span>
        </div>
        <div class="stock-item">
          <span class="stock-label">存储位置:</span>
          <span class="stock-location">{{ material.stockInfo.storageLocation }}</span>
        </div>
      </div>

      <!-- 供应商信息 -->
      <div class="supplier-section">
        <div class="supplier-item">
          <span class="supplier-label">主供应商:</span>
          <span class="supplier-name">{{ getPrimarySupplier(material.suppliers)?.supplierName || '-' }}</span>
        </div>
        <div v-if="getPrimarySupplier(material.suppliers)" class="supplier-metrics">
          <span class="metric-item">周期: {{ getPrimarySupplier(material.suppliers).leadTime }}天</span>
          <span class="metric-item">质量: {{ getPrimarySupplier(material.suppliers).qualityRating }}%</span>
        </div>
      </div>

      <!-- 更新时间 -->
      <div class="update-time">
        更新: {{ formatDate(material.auditInfo.updatedAt) }}
      </div>
    </div>

    <template #footer>
      <div class="card-actions">
        <el-button size="small" @click.stop="$emit('view', material)">
          <el-icon><View /></el-icon>
          查看
        </el-button>
        <el-button size="small" @click.stop="$emit('edit', material)">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
        <el-button size="small" @click.stop="$emit('alternatives', material)">
          <el-icon><Connection /></el-icon>
          替代料
        </el-button>
        <el-button size="small" type="danger" @click.stop="$emit('delete', material)">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import type { MaterialMaster, MaterialMasterCategory, MaterialStatus, WireType, SupplierInfo } from '@/types/materialMaster'
import { 
  MATERIAL_CATEGORY_OPTIONS, 
  MATERIAL_STATUS_OPTIONS,
  WIRE_TYPE_OPTIONS
} from '@/utils/mockData/materialMaster'

interface Props {
  material: MaterialMaster
}

interface Emits {
  'view': [material: MaterialMaster]
  'edit': [material: MaterialMaster]
  'delete': [material: MaterialMaster]
  'alternatives': [material: MaterialMaster]
}

defineProps<Props>()
defineEmits<Emits>()

// 获取分类颜色
const getCategoryColor = (category: MaterialMasterCategory): string => {
  const option = MATERIAL_CATEGORY_OPTIONS.find(opt => opt.value === category)
  return option?.color || '#909399'
}

// 获取分类标签
const getCategoryLabel = (category: MaterialMasterCategory): string => {
  const option = MATERIAL_CATEGORY_OPTIONS.find(opt => opt.value === category)
  return option?.label || category
}

// 获取状态标签类型
const getStatusTagType = (status: MaterialStatus): string => {
  const option = MATERIAL_STATUS_OPTIONS.find(opt => opt.value === status)
  return option?.tagType || ''
}

// 获取状态标签文本
const getStatusLabel = (status: MaterialStatus): string => {
  const option = MATERIAL_STATUS_OPTIONS.find(opt => opt.value === status)
  return option?.label || status
}

// 获取线材颜色
const getWireColor = (wireType?: WireType): string => {
  if (!wireType) return '#909399'
  const option = WIRE_TYPE_OPTIONS.find(opt => opt.value === wireType)
  return option?.color || '#909399'
}

// 格式化尺寸
const formatDimensions = (dimensions: any): string => {
  if (!dimensions) return '-'
  
  const parts = []
  if (dimensions.length && dimensions.width) {
    parts.push(`${dimensions.length}×${dimensions.width}mm`)
  } else if (dimensions.diameter) {
    parts.push(`φ${dimensions.diameter/1000}mm`)
  }
  
  if (dimensions.thickness) {
    parts.push(`厚${dimensions.thickness}μm`)
  }
  
  return parts.join(' ')
}

// 获取主供应商
const getPrimarySupplier = (suppliers: SupplierInfo[]): SupplierInfo | undefined => {
  return suppliers.find(s => s.isPrimary) || suppliers[0]
}

// 获取价格趋势
const getPriceTrend = (costInfo: any): string => {
  const variance = costInfo.currentPrice - costInfo.standardCost
  if (variance > 0) return 'price-up'
  if (variance < 0) return 'price-down'
  return 'price-stable'
}

// 获取价格趋势文本
const getPriceTrendText = (costInfo: any): string => {
  const variance = costInfo.currentPrice - costInfo.standardCost
  const percent = Math.abs(variance / costInfo.standardCost * 100).toFixed(1)
  
  if (variance > 0) return `↑${percent}%`
  if (variance < 0) return `↓${percent}%`
  return '持平'
}

// 格式化日期
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}
</script>

<style lang="scss" scoped>
.material-card {
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 20px rgb(0 0 0 / 12%);
    transform: translateY(-2px);
  }

  :deep(.el-card__header) {
    padding: 16px 16px 8px;
  }

  :deep(.el-card__body) {
    padding: 0 16px 8px;
  }

  :deep(.el-card__footer) {
    padding: 8px 16px 16px;
    background: var(--color-bg-light);
    border-top: 1px solid var(--color-border-light);
  }

  .card-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;

    .material-code {
      font-family: Monaco, Consolas, monospace;
      font-size: 14px;
      font-weight: 600;
      color: var(--color-text-primary);
    }

    .card-actions {
      margin-left: 12px;
    }
  }

  .card-content {
    .material-basic-info {
      margin-bottom: 12px;

      .material-name {
        margin: 0 0 4px;
        font-size: 16px;
        font-weight: 600;
        line-height: 1.2;
        color: var(--color-text-primary);
      }

      .material-english {
        margin: 0 0 4px;
        font-size: 12px;
        line-height: 1.2;
        color: var(--color-text-secondary);
      }

      .material-spec {
        margin: 0 0 4px;
        font-size: 12px;
        line-height: 1.2;
        color: var(--color-text-regular);
      }

      .material-manufacturer {
        margin: 0;
        font-size: 12px;
        font-style: italic;
        line-height: 1.2;
        color: var(--color-text-secondary);
      }
    }

    .status-section {
      margin-bottom: 12px;
    }

    .tech-params {
      margin-bottom: 12px;

      .param-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;

        .param-label {
          min-width: 40px;
          margin-right: 6px;
          color: var(--color-text-secondary);
        }

        .param-value {
          margin-left: 6px;
          color: var(--color-text-regular);
        }

        .wire-indicator {
          width: 12px;
          height: 12px;
          margin: 0 4px;
          border: 1px solid var(--color-border-light);
          border-radius: 50%;
        }
      }
    }

    .cost-section {
      padding: 8px;
      margin-bottom: 12px;
      background: var(--color-bg-light);
      border-radius: 4px;

      .cost-price {
        display: flex;
        align-items: center;
        margin-bottom: 4px;

        .price-label {
          margin-right: 6px;
          font-size: 12px;
          color: var(--color-text-secondary);
        }

        .price-value {
          margin-right: 4px;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        .price-unit {
          font-size: 12px;
          color: var(--color-text-secondary);
        }
      }

      .price-trend {
        font-size: 12px;
        font-weight: 500;

        &.price-up { color: var(--color-danger); }

        &.price-down { color: var(--color-success); }

        &.price-stable { color: var(--color-text-secondary); }
      }
    }

    .stock-section {
      margin-bottom: 12px;

      .stock-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        font-size: 12px;

        .stock-label {
          color: var(--color-text-secondary);
        }

        .stock-value {
          font-weight: 500;
          color: var(--color-text-primary);
        }

        .stock-location {
          font-family: Monaco, Consolas, monospace;
          color: var(--color-text-regular);
        }
      }
    }

    .supplier-section {
      padding: 8px;
      margin-bottom: 12px;
      background: var(--color-bg-page);
      border-radius: 4px;

      .supplier-item {
        margin-bottom: 4px;

        .supplier-label {
          font-size: 12px;
          color: var(--color-text-secondary);
        }

        .supplier-name {
          margin-left: 4px;
          font-size: 12px;
          font-weight: 500;
          color: var(--color-text-primary);
        }
      }

      .supplier-metrics {
        display: flex;
        gap: 12px;

        .metric-item {
          font-size: 11px;
          color: var(--color-text-secondary);
        }
      }
    }

    .update-time {
      margin-top: 8px;
      font-size: 11px;
      color: var(--color-text-placeholder);
      text-align: right;
    }
  }

  .card-actions {
    display: flex;
    gap: 8px;
    justify-content: space-between;

    .el-button {
      flex: 1;
      padding: 6px 8px;
      font-size: 12px;

      .el-icon {
        margin-right: 2px;
      }
    }
  }
}

// 小屏幕适配
@media (width <= 768px) {
  .material-card {
    .card-content {
      .tech-params .param-item {
        flex-direction: column;
        align-items: flex-start;

        .param-label {
          margin-bottom: 2px;
        }
      }
    }

    .card-actions {
      flex-wrap: wrap;

      .el-button {
        flex: 0 0 48%;
        margin-bottom: 4px;
      }
    }
  }
}
</style>