<template>
  <el-dialog
    v-model="dialogVisible"
    title="替代料管理"
    width="80%"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div v-if="material" class="alternative-management">
      <!-- 物料信息 -->
      <el-card class="material-info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><Box /></el-icon>
            <span>当前物料</span>
          </div>
        </template>
        
        <div class="material-basic">
          <div class="material-code">{{ material.basicInfo.materialCode }}</div>
          <div class="material-name">{{ material.basicInfo.materialName }}</div>
          <div class="material-spec">{{ material.basicInfo.specification }}</div>
          <div class="material-category">
            <el-tag 
              :color="getCategoryColor(material.basicInfo.category)"
              style=" margin-right: 8px;color: white"
            >
              {{ getCategoryLabel(material.basicInfo.category) }}
            </el-tag>
            <el-tag :type="getStatusTagType(material.basicInfo.status)">
              {{ getStatusLabel(material.basicInfo.status) }}
            </el-tag>
          </div>
        </div>
      </el-card>

      <!-- 替代料列表 -->
      <el-card shadow="never">
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <el-icon><Connection /></el-icon>
              <span>替代料列表 ({{ alternatives.length }})</span>
            </div>
            <div class="header-actions">
              <el-button type="primary" @click="showAddDialog = true">
                <el-icon><Plus /></el-icon>
                添加替代料
              </el-button>
            </div>
          </div>
        </template>

        <div v-if="alternatives.length === 0" class="empty-alternatives">
          <el-empty description="暂无替代料" :image-size="120">
            <el-button type="primary" @click="showAddDialog = true">
              添加第一个替代料
            </el-button>
          </el-empty>
        </div>

        <el-table v-else :data="alternatives" border stripe>
          <el-table-column type="index" label="#" width="50" />
          
          <el-table-column label="替代料编码" width="150">
            <template #default="{ row }">
              <span class="alternative-code">{{ row.alternativeCode }}</span>
            </template>
          </el-table-column>
          
          <el-table-column label="替代料名称" min-width="200">
            <template #default="{ row }">
              <div class="alternative-info">
                <div class="alt-name">{{ row.alternativeName }}</div>
                <div class="alt-detail">{{ getAlternativeDetail(row.alternativeCode) }}</div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="替代类型" width="120" align="center">
            <template #default="{ row }">
              <el-tag 
                :type="getRelationTagType(row.relationType)"
                size="small"
              >
                {{ getRelationTypeLabel(row.relationType) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="替代比例" width="100" align="center">
            <template #default="{ row }">
              <span class="substitution-ratio">{{ (row.substitutionRatio * 100).toFixed(0) }}%</span>
            </template>
          </el-table-column>
          
          <el-table-column label="优先级" width="80" align="center" prop="priority" />
          
          <el-table-column label="有效期" width="200" align="center">
            <template #default="{ row }">
              <div class="validity-period">
                <div class="valid-from">{{ formatDate(row.validFrom) }}</div>
                <div class="valid-to">{{ row.validTo ? formatDate(row.validTo) : '长期有效' }}</div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag 
                :type="isValidAlternative(row) ? 'success' : 'danger'"
                size="small"
              >
                {{ isValidAlternative(row) ? '有效' : '失效' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="备注" min-width="150">
            <template #default="{ row }">
              <el-text class="remarks" truncated>{{ row.remarks || '-' }}</el-text>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row, $index }">
              <el-button-group>
                <el-button size="small" @click="editAlternative(row, $index)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button size="small" type="danger" @click="removeAlternative($index)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 替代料分析 -->
      <el-card v-if="alternatives.length > 0" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><DataAnalysis /></el-icon>
            <span>替代料分析</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="analysis-item">
              <div class="analysis-label">总替代料数量</div>
              <div class="analysis-value primary">{{ alternatives.length }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="analysis-item">
              <div class="analysis-label">完全替代料</div>
              <div class="analysis-value success">{{ getFullAlternativeCount() }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="analysis-item">
              <div class="analysis-label">有效替代料</div>
              <div class="analysis-value warning">{{ getValidAlternativeCount() }}</div>
            </div>
          </el-col>
        </el-row>
        
        <div class="analysis-details">
          <h5>替代策略建议</h5>
          <ul class="suggestions-list">
            <li v-if="getFullAlternativeCount() === 0" class="suggestion warning">
              <el-icon><Warning /></el-icon>
              建议添加完全替代料以降低供应风险
            </li>
            <li v-if="getValidAlternativeCount() < 2" class="suggestion info">
              <el-icon><InfoFilled /></el-icon>
              建议至少保持2个有效替代料选择
            </li>
            <li v-if="hasExpiringSoon()" class="suggestion danger">
              <el-icon><Clock /></el-icon>
              有替代料即将到期，请及时更新有效期
            </li>
            <li v-if="alternatives.length > 0" class="suggestion success">
              <el-icon><CircleCheckFilled /></el-icon>
              替代料配置完善，供应风险可控
            </li>
          </ul>
        </div>
      </el-card>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </span>
    </template>

    <!-- 添加/编辑替代料对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingIndex >= 0 ? '编辑替代料' : '添加替代料'"
      width="600px"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="alternativeFormRef"
        :model="alternativeForm"
        :rules="alternativeRules"
        label-width="120px"
      >
        <el-form-item label="替代料编码" prop="alternativeCode">
          <el-input 
            v-model="alternativeForm.alternativeCode" 
            placeholder="请输入替代料编码"
          />
        </el-form-item>
        
        <el-form-item label="替代料名称" prop="alternativeName">
          <el-input 
            v-model="alternativeForm.alternativeName" 
            placeholder="请输入替代料名称"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="替代类型" prop="relationType">
              <el-select v-model="alternativeForm.relationType" placeholder="请选择替代类型" style="width: 100%">
                <el-option label="完全替代" value="FULL" />
                <el-option label="部分替代" value="PARTIAL" />
                <el-option label="紧急替代" value="EMERGENCY" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="替代比例" prop="substitutionRatio">
              <el-slider 
                v-model="substitutionRatioPercent" 
                :marks="{ 25: '25%', 50: '50%', 75: '75%', 100: '100%' }"
                :step="5"
                show-input
                style="margin-right: 20px"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-input-number 
                v-model="alternativeForm.priority" 
                :min="1"
                :max="10"
                controls-position="right"
                style="width: 100%"
              />
              <div class="form-tip">数值越小优先级越高</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效日期" prop="validFrom">
              <el-date-picker
                v-model="alternativeForm.validFrom"
                type="date"
                placeholder="请选择生效日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="失效日期" prop="validTo">
          <el-date-picker
            v-model="alternativeForm.validTo"
            type="date"
            placeholder="请选择失效日期，留空为长期有效"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
            clearable
          />
          <div class="form-tip">留空表示长期有效</div>
        </el-form-item>
        
        <el-form-item label="备注" prop="remarks">
          <el-input 
            v-model="alternativeForm.remarks" 
            type="textarea" 
            :rows="3"
            placeholder="请输入备注信息，如适用条件、限制等"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="handleAddAlternative">
            {{ editingIndex >= 0 ? '更新' : '添加' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
// Element Plus组件通过unplugin-auto-import自动导入
import type { FormInstance, FormRules } from 'element-plus/es'
import type { 
  MaterialMaster, 
  AlternativeRelation,
  MaterialMasterCategory,
  MaterialStatus
} from '@/types/materialMaster'
import { 
  MATERIAL_CATEGORY_OPTIONS,
  MATERIAL_STATUS_OPTIONS,
  mockMaterialMasters
} from '@/utils/mockData/materialMaster'

interface Props {
  visible: boolean
  material: MaterialMaster | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'save', alternatives: AlternativeRelation[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 组件引用
const alternativeFormRef = ref<FormInstance>()

// 页面状态
const showAddDialog = ref(false)
const editingIndex = ref(-1)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 替代料列表
const alternatives = ref<AlternativeRelation[]>([])

// 替代料表单数据
const alternativeForm = reactive({
  alternativeCode: '',
  alternativeName: '',
  relationType: 'FULL' as const,
  substitutionRatio: 1,
  priority: 1,
  validFrom: '',
  validTo: '',
  remarks: ''
})

// 替代比例百分比（用于滑块显示）
const substitutionRatioPercent = computed({
  get: () => alternativeForm.substitutionRatio * 100,
  set: (value: number) => {
    alternativeForm.substitutionRatio = value / 100
  }
})

// 表单验证规则
const alternativeRules: FormRules = {
  alternativeCode: [
    { required: true, message: '请输入替代料编码', trigger: 'blur' }
  ],
  alternativeName: [
    { required: true, message: '请输入替代料名称', trigger: 'blur' }
  ],
  relationType: [
    { required: true, message: '请选择替代类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请输入优先级', trigger: 'blur' }
  ],
  validFrom: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ]
}

// 监听对话框打开，初始化数据
watch(() => props.visible, (visible) => {
  if (visible && props.material) {
    alternatives.value = [...props.material.alternatives]
  }
})

// 获取分类颜色
const getCategoryColor = (category: MaterialMasterCategory): string => {
  const option = MATERIAL_CATEGORY_OPTIONS.find(opt => opt.value === category)
  return option?.color || '#909399'
}

// 获取分类标签
const getCategoryLabel = (category: MaterialMasterCategory): string => {
  const option = MATERIAL_CATEGORY_OPTIONS.find(opt => opt.value === category)
  return option?.label || category
}

// 获取状态标签类型
const getStatusTagType = (status: MaterialStatus): string => {
  const option = MATERIAL_STATUS_OPTIONS.find(opt => opt.value === status)
  return option?.tagType || ''
}

// 获取状态标签文本
const getStatusLabel = (status: MaterialStatus): string => {
  const option = MATERIAL_STATUS_OPTIONS.find(opt => opt.value === status)
  return option?.label || status
}

// 获取替代关系类型标签
const getRelationTypeLabel = (relationType: string): string => {
  const typeMap: Record<string, string> = {
    'FULL': '完全替代',
    'PARTIAL': '部分替代',
    'EMERGENCY': '紧急替代'
  }
  return typeMap[relationType] || relationType
}

// 获取替代关系类型标签样式
const getRelationTagType = (relationType: string): string => {
  const typeMap: Record<string, string> = {
    'FULL': 'success',
    'PARTIAL': 'warning',
    'EMERGENCY': 'danger'
  }
  return typeMap[relationType] || ''
}

// 获取替代料详细信息
const getAlternativeDetail = (alternativeCode: string): string => {
  const material = mockMaterialMasters.find(m => m.basicInfo.materialCode === alternativeCode)
  if (material) {
    return material.basicInfo.specification
  }
  return '规格信息不可用'
}

// 检查替代料是否有效
const isValidAlternative = (alternative: AlternativeRelation): boolean => {
  const today = new Date()
  const validFrom = new Date(alternative.validFrom)
  const validTo = alternative.validTo ? new Date(alternative.validTo) : null
  
  return today >= validFrom && (!validTo || today <= validTo)
}

// 获取完全替代料数量
const getFullAlternativeCount = (): number => {
  return alternatives.value.filter(alt => alt.relationType === 'FULL' && isValidAlternative(alt)).length
}

// 获取有效替代料数量
const getValidAlternativeCount = (): number => {
  return alternatives.value.filter(alt => isValidAlternative(alt)).length
}

// 检查是否有即将到期的替代料
const hasExpiringSoon = (): boolean => {
  const thirtyDaysLater = new Date()
  thirtyDaysLater.setDate(thirtyDaysLater.getDate() + 30)
  
  return alternatives.value.some(alt => {
    if (!alt.validTo) return false
    const validTo = new Date(alt.validTo)
    return validTo <= thirtyDaysLater && validTo >= new Date()
  })
}

// 编辑替代料
const editAlternative = (alternative: AlternativeRelation, index: number) => {
  Object.assign(alternativeForm, alternative)
  editingIndex.value = index
  showAddDialog.value = true
}

// 删除替代料
const removeAlternative = async (index: number) => {
  const alternative = alternatives.value[index]
  
  try {
    await ElMessageBox.confirm(
      `确定要删除替代料 "${alternative.alternativeName}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    alternatives.value.splice(index, 1)
    ElMessage.success('删除成功')
    
  } catch (error) {
    // 用户取消删除
  }
}

// 添加/更新替代料
const handleAddAlternative = async () => {
  if (!alternativeFormRef.value) return
  
  try {
    await alternativeFormRef.value.validate()
    
    // 检查编码是否重复
    const existsIndex = alternatives.value.findIndex((alt, index) => 
      alt.alternativeCode === alternativeForm.alternativeCode && index !== editingIndex.value
    )
    
    if (existsIndex >= 0) {
      ElMessage.error('替代料编码已存在')
      return
    }
    
    // 检查是否是当前物料本身
    if (props.material && alternativeForm.alternativeCode === props.material.basicInfo.materialCode) {
      ElMessage.error('不能将自己设为替代料')
      return
    }
    
    const alternativeData: AlternativeRelation = {
      alternativeCode: alternativeForm.alternativeCode,
      alternativeName: alternativeForm.alternativeName,
      relationType: alternativeForm.relationType,
      substitutionRatio: alternativeForm.substitutionRatio,
      priority: alternativeForm.priority,
      validFrom: alternativeForm.validFrom,
      validTo: alternativeForm.validTo || undefined,
      remarks: alternativeForm.remarks
    }
    
    if (editingIndex.value >= 0) {
      // 更新现有替代料
      alternatives.value[editingIndex.value] = alternativeData
      ElMessage.success('更新成功')
    } else {
      // 添加新替代料
      alternatives.value.push(alternativeData)
      ElMessage.success('添加成功')
    }
    
    // 重置表单
    resetAlternativeForm()
    showAddDialog.value = false
    
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置替代料表单
const resetAlternativeForm = () => {
  Object.assign(alternativeForm, {
    alternativeCode: '',
    alternativeName: '',
    relationType: 'FULL',
    substitutionRatio: 1,
    priority: 1,
    validFrom: '',
    validTo: '',
    remarks: ''
  })
  editingIndex.value = -1
  alternativeFormRef.value?.resetFields()
}

// 保存替代料关系
const handleSave = () => {
  emit('save', alternatives.value)
}

// 格式化日期
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 监听添加对话框关闭，重置表单
watch(() => showAddDialog.value, (visible) => {
  if (!visible) {
    resetAlternativeForm()
  }
})
</script>

<style lang="scss" scoped>
.alternative-management {
  .material-info-card {
    margin-bottom: 20px;
    
    .material-basic {
      .material-code {
        margin-bottom: 8px;
        font-family: Monaco, Consolas, monospace;
        font-size: 18px;
        font-weight: 600;
        color: var(--color-primary);
      }
      
      .material-name {
        margin-bottom: 6px;
        font-size: 16px;
        font-weight: 500;
        color: var(--color-text-primary);
      }
      
      .material-spec {
        margin-bottom: 12px;
        font-size: 14px;
        color: var(--color-text-regular);
      }
      
      .material-category {
        display: flex;
        align-items: center;
      }
    }
  }
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .header-left {
      display: flex;
      gap: 8px;
      align-items: center;
      font-weight: 500;
    }
  }
  
  .empty-alternatives {
    padding: 60px 0;
    text-align: center;
  }
  
  .alternative-code {
    font-family: Monaco, Consolas, monospace;
    font-weight: 500;
    color: var(--color-primary);
  }
  
  .alternative-info {
    .alt-name {
      margin-bottom: 4px;
      font-weight: 500;
      color: var(--color-text-primary);
    }
    
    .alt-detail {
      font-size: 12px;
      color: var(--color-text-secondary);
    }
  }
  
  .substitution-ratio {
    font-weight: 500;
    color: var(--color-primary);
  }
  
  .validity-period {
    font-size: 12px;
    
    .valid-from {
      margin-bottom: 2px;
      color: var(--color-text-primary);
    }
    
    .valid-to {
      color: var(--color-text-secondary);
    }
  }
  
  .remarks {
    max-width: 150px;
  }
  
  .analysis-item {
    padding: 20px;
    text-align: center;
    background: var(--color-bg-light);
    border-radius: 8px;
    
    .analysis-label {
      margin-bottom: 8px;
      font-size: 14px;
      color: var(--color-text-secondary);
    }
    
    .analysis-value {
      font-size: 28px;
      font-weight: 600;
      
      &.primary { color: var(--color-primary); }

      &.success { color: var(--color-success); }

      &.warning { color: var(--color-warning); }
    }
  }
  
  .analysis-details {
    margin-top: 20px;
    
    h5 {
      margin: 0 0 12px;
      font-size: 14px;
      font-weight: 500;
      color: var(--color-text-primary);
    }
    
    .suggestions-list {
      padding: 0;
      margin: 0;
      list-style: none;
      
      .suggestion {
        display: flex;
        gap: 8px;
        align-items: center;
        padding: 8px 12px;
        margin-bottom: 8px;
        font-size: 13px;
        border-radius: 4px;
        
        &.success {
          color: var(--color-success-dark);
          background: var(--color-success-light);
        }
        
        &.warning {
          color: var(--color-warning-dark);
          background: var(--color-warning-light);
        }
        
        &.danger {
          color: var(--color-danger-dark);
          background: var(--color-danger-light);
        }
        
        &.info {
          color: var(--color-info-dark);
          background: var(--color-info-light);
        }
      }
    }
  }
}

.form-tip {
  margin-top: 4px;
  font-size: 12px;
  color: var(--color-text-placeholder);
}

.dialog-footer {
  text-align: right;
  
  .el-button {
    margin-left: 10px;
  }
}

:deep(.el-slider) {
  margin: 8px 0 16px;
}

:deep(.el-table) {
  .el-button-group {
    display: flex;
    
    .el-button {
      margin-left: 0;
      
      &:not(:first-child) {
        margin-left: -1px;
      }
    }
  }
}

// 响应式设计
@media (width <= 1200px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 20px auto !important;
  }
  
  .alternative-management {
    .card-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
      
      .header-actions {
        width: 100%;
        text-align: right;
      }
    }
    
    .analysis-item {
      margin-bottom: 16px;
    }
  }
}
</style>