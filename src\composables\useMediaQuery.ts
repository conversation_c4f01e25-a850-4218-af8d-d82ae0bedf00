import { computed, ref } from 'vue'
import { useMediaQuery } from '@vueuse/core'
import type { Breakpoints, MediaQueryResult } from '@/types/theme'

// 响应式断点定义
export const breakpoints: Breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
}

/**
 * 媒体查询组合式函数
 * 提供响应式断点检测和设备类型判断
 */
export function useBreakpoints(): MediaQueryResult {
  // 各断点的媒体查询
  const isSmUp = useMediaQuery(`(min-width: ${breakpoints.sm}px)`)
  const isMdUp = useMediaQuery(`(min-width: ${breakpoints.md}px)`)
  const isLgUp = useMediaQuery(`(min-width: ${breakpoints.lg}px)`)
  const isXlUp = useMediaQuery(`(min-width: ${breakpoints.xl}px)`)
  const is2xlUp = useMediaQuery(`(min-width: ${breakpoints['2xl']}px)`)
  
  // 设备类型判断
  const isMobile = computed(() => !isSmUp.value)
  const isTablet = computed(() => isSmUp.value && !isLgUp.value)
  const isDesktop = computed(() => isLgUp.value && !isXlUp.value)
  const isLargeScreen = computed(() => isXlUp.value)
  
  // 当前断点
  const currentBreakpoint = computed<keyof Breakpoints | 'xs'>(() => {
    if (is2xlUp.value) return '2xl'
    if (isXlUp.value) return 'xl'
    if (isLgUp.value) return 'lg'
    if (isMdUp.value) return 'md'
    if (isSmUp.value) return 'sm'
    return 'xs'
  })
  
  return {
    isMobile,
    isTablet,
    isDesktop,
    isLargeScreen,
    currentBreakpoint
  }
}

/**
 * 特定断点媒体查询钩子
 */
export function useBreakpoint(breakpoint: keyof Breakpoints) {
  return useMediaQuery(`(min-width: ${breakpoints[breakpoint]}px)`)
}

/**
 * 范围断点媒体查询钩子
 */
export function useBreakpointRange(
  min: keyof Breakpoints | 'xs',
  max?: keyof Breakpoints
) {
  const minWidth = min === 'xs' ? 0 : breakpoints[min]
  const maxWidth = max ? breakpoints[max] - 1 : undefined
  
  let query = `(min-width: ${minWidth}px)`
  if (maxWidth) {
    query += ` and (max-width: ${maxWidth}px)`
  }
  
  return useMediaQuery(query)
}

/**
 * 设备特征检测钩子
 */
export function useDeviceFeatures() {
  // 触摸设备检测
  const isTouchDevice = useMediaQuery('(pointer: coarse)')
  
  // 高DPI屏幕检测
  const isHighDPI = useMediaQuery('(min-resolution: 2dppx)')
  
  // 深色主题偏好
  const prefersDark = useMediaQuery('(prefers-color-scheme: dark)')
  
  // 减少动画偏好
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)')
  
  // 高对比度偏好
  const prefersHighContrast = useMediaQuery('(prefers-contrast: high)')
  
  // 横屏/竖屏
  const isLandscape = useMediaQuery('(orientation: landscape)')
  const isPortrait = useMediaQuery('(orientation: portrait)')
  
  return {
    isTouchDevice,
    isHighDPI,
    prefersDark,
    prefersReducedMotion,
    prefersHighContrast,
    isLandscape,
    isPortrait
  }
}

/**
 * 容器查询钩子（实验性功能）
 */
export function useContainerQuery(
  containerRef: Ref<HTMLElement | null>,
  query: string
) {
  const matches = ref(false)
  
  // TODO: 实现容器查询逻辑
  // 目前浏览器支持有限，可以使用 ResizeObserver 模拟
  
  return matches
}

/**
 * 响应式工具函数
 */
export const responsive = {
  /**
   * 根据断点返回不同的值
   */
  value<T>(values: Partial<Record<keyof Breakpoints | 'xs', T>>): ComputedRef<T | undefined> {
    const { currentBreakpoint } = useBreakpoints()
    
    return computed(() => {
      const breakpoint = currentBreakpoint.value
      
      // 按优先级查找匹配的值
      const priorities: (keyof Breakpoints | 'xs')[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs']
      const currentIndex = priorities.indexOf(breakpoint)
      
      // 从当前断点向下查找第一个有值的断点
      for (let i = currentIndex; i < priorities.length; i++) {
        const key = priorities[i]
        if (values[key] !== undefined) {
          return values[key]
        }
      }
      
      return undefined
    })
  },
  
  /**
   * 根据断点返回样式类名
   */
  class(classes: Partial<Record<keyof Breakpoints | 'xs', string>>): ComputedRef<string> {
    const value = responsive.value(classes)
    return computed(() => value.value || '')
  },
  
  /**
   * 根据断点返回布局配置
   */
  grid(configs: Partial<Record<keyof Breakpoints | 'xs', { cols: number; gap?: string }>>): ComputedRef<string> {
    const config = responsive.value(configs)
    return computed(() => {
      if (!config.value) return ''
      const { cols, gap = '16px' } = config.value
      return `grid-template-columns: repeat(${cols}, 1fr); gap: ${gap};`
    })
  }
}

// 常用的响应式断点预设
export const MOBILE_BREAKPOINT = 'md'
export const TABLET_BREAKPOINT = 'lg'
export const DESKTOP_BREAKPOINT = 'xl'

// 导出断点值供其他地方使用
export { breakpoints as defaultBreakpoints }