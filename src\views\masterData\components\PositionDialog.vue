<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      size="default"
      @submit.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="岗位名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入岗位名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="岗位编码" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="请输入岗位编码"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属部门" prop="departmentId">
            <el-select
              v-model="formData.departmentId"
              placeholder="请选择所属部门"
              style="width: 100%"
              :disabled="!!department"
            >
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="岗位级别" prop="level">
            <el-select v-model="formData.level" placeholder="请选择岗位级别" style="width: 100%">
              <el-option
                v-for="option in positionLevelOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="汇报岗位" prop="reportToId">
            <el-select
              v-model="formData.reportToId"
              placeholder="请选择汇报岗位"
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="pos in availableReportPositions"
                :key="pos.id"
                :label="`${pos.name} (${pos.code})`"
                :value="pos.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="编制人数" prop="headcount">
            <el-input-number
              v-model="formData.headcount"
              :min="1"
              :max="100"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="关键岗位" prop="isKeyPosition">
            <el-switch
              v-model="formData.isKeyPosition"
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="岗位描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入岗位描述"
        />
      </el-form-item>

      <el-form-item label="岗位职责" prop="responsibilities">
        <div class="list-manager">
          <div
            v-for="(item, index) in formData.responsibilities"
            :key="index"
            class="list-item"
          >
            <el-input
              v-model="formData.responsibilities[index]"
              placeholder="请输入职责描述"
              clearable
            />
            <el-button
              type="text"
              :icon="Delete"
              @click="removeItem(formData.responsibilities, index)"
            />
          </div>
          <el-button
            type="text"
            :icon="Plus"
            @click="addItem(formData.responsibilities)"
          >
            添加职责
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="任职要求" prop="requirements">
        <div class="list-manager">
          <div
            v-for="(item, index) in formData.requirements"
            :key="index"
            class="list-item"
          >
            <el-input
              v-model="formData.requirements[index]"
              placeholder="请输入任职要求"
              clearable
            />
            <el-button
              type="text"
              :icon="Delete"
              @click="removeItem(formData.requirements, index)"
            />
          </div>
          <el-button
            type="text"
            :icon="Plus"
            @click="addItem(formData.requirements)"
          >
            添加要求
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="技能要求" prop="skills">
        <div class="list-manager">
          <div
            v-for="(item, index) in formData.skills"
            :key="index"
            class="list-item"
          >
            <el-input
              v-model="formData.skills[index]"
              placeholder="请输入技能要求"
              clearable
            />
            <el-button
              type="text"
              :icon="Delete"
              @click="removeItem(formData.skills, index)"
            />
          </div>
          <el-button
            type="text"
            :icon="Plus"
            @click="addItem(formData.skills)"
          >
            添加技能
          </el-button>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import type { 
  Position, 
  Department, 
  PositionFormData, 
  PositionLevel 
} from '@/types/organization'

// Props
interface Props {
  visible: boolean
  position?: Position | null
  department?: Department | null
  positions: Position[]
}

// Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  position: null,
  department: null
})

const emit = defineEmits<Emits>()

// 组件引用
const formRef = ref<FormInstance>()

// 状态数据
const loading = ref(false)
const formData = ref<PositionFormData>({
  name: '',
  code: '',
  departmentId: '',
  level: 'specialist' as PositionLevel,
  reportToId: undefined,
  description: '',
  responsibilities: [''],
  requirements: [''],
  skills: [''],
  headcount: 1,
  isKeyPosition: false
})

// 岗位级别选项
const positionLevelOptions = [
  { label: '总监级', value: 'director' },
  { label: '经理级', value: 'manager' },
  { label: '主管级', value: 'supervisor' },
  { label: '专员级', value: 'specialist' },
  { label: '操作员级', value: 'operator' }
]

// 模拟部门数据（实际应从props传入）
const departments = computed(() => {
  // 这里应该获取所有部门列表
  if (props.department) {
    return [props.department]
  }
  return []
})

// 可选择的汇报岗位（排除自己）
const availableReportPositions = computed(() => {
  return props.positions.filter(pos => {
    // 排除自己
    if (props.position && pos.id === props.position.id) {
      return false
    }
    // 只显示同部门或上级部门的岗位
    return pos.departmentId === formData.value.departmentId
  })
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.position)

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑岗位' : '新增岗位'
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入岗位名称', trigger: 'blur' },
    { min: 2, max: 50, message: '岗位名称长度在2-50个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入岗位编码', trigger: 'blur' },
    { 
      pattern: /^[A-Z0-9_]+$/, 
      message: '岗位编码只能包含大写字母、数字和下划线', 
      trigger: 'blur' 
    }
  ],
  departmentId: [
    { required: true, message: '请选择所属部门', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择岗位级别', trigger: 'change' }
  ],
  headcount: [
    { required: true, message: '请输入编制人数', trigger: 'blur' },
    { type: 'number', min: 1, message: '编制人数至少为1', trigger: 'blur' }
  ]
}

// 列表管理函数
const addItem = (list: string[]) => {
  list.push('')
}

const removeItem = (list: string[], index: number) => {
  if (list.length > 1) {
    list.splice(index, 1)
  }
}

// 事件处理
const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 过滤空项
    const submitData = {
      ...formData.value,
      responsibilities: formData.value.responsibilities.filter(r => r.trim()),
      requirements: formData.value.requirements.filter(r => r.trim()),
      skills: formData.value.skills.filter(s => s.trim())
    }
    
    console.log('提交岗位数据:', submitData)
    
    ElMessage.success(isEdit.value ? '岗位更新成功' : '岗位创建成功')
    emit('success')
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    name: '',
    code: '',
    departmentId: '',
    level: 'specialist' as PositionLevel,
    reportToId: undefined,
    description: '',
    responsibilities: [''],
    requirements: [''],
    skills: [''],
    headcount: 1,
    isKeyPosition: false
  }
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 初始化表单数据
const initFormData = () => {
  if (props.position) {
    // 编辑模式
    formData.value = {
      name: props.position.name,
      code: props.position.code,
      departmentId: props.position.departmentId,
      level: props.position.level,
      reportToId: props.position.reportToId,
      description: props.position.description || '',
      responsibilities: props.position.responsibilities.length > 0 
        ? [...props.position.responsibilities] 
        : [''],
      requirements: props.position.requirements.length > 0 
        ? [...props.position.requirements] 
        : [''],
      skills: props.position.skills.length > 0 
        ? [...props.position.skills] 
        : [''],
      headcount: props.position.headcount,
      isKeyPosition: props.position.isKeyPosition
    }
  } else {
    // 新增模式
    resetForm()
    // 如果有默认部门，自动设置
    if (props.department) {
      formData.value.departmentId = props.department.id
    }
  }
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible) {
    initFormData()
  }
})

watch(() => props.position, () => {
  if (props.visible) {
    initFormData()
  }
})

watch(() => props.department, () => {
  if (props.visible && props.department) {
    formData.value.departmentId = props.department.id
  }
})
</script>

<style lang="scss" scoped>
.list-manager {
  width: 100%;

  .list-item {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
    margin-bottom: var(--spacing-2);

    .el-input {
      flex: 1;
    }

    .el-button {
      flex-shrink: 0;
      color: var(--el-color-danger);

      &:hover {
        background-color: var(--el-color-danger-light-9);
      }
    }
  }

  .el-button {
    margin-top: var(--spacing-2);
    color: var(--el-color-primary);

    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
}

.dialog-footer {
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
}

:deep(.el-dialog) {
  .el-dialog__header {
    padding-bottom: var(--spacing-3);
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .el-dialog__body {
    padding: var(--spacing-4);
  }

  .el-dialog__footer {
    padding-top: var(--spacing-3);
    border-top: 1px solid var(--el-border-color-lighter);
  }
}

// 响应式设计
@media (width <= 768px) {
  :deep(.el-dialog) {
    width: 90% !important;
    margin: var(--spacing-2);
  }
}
</style>