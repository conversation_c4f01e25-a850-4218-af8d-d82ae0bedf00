<template>
  <div :class="cardClass">
    <div class="kpi-card__header">
      <div class="kpi-card__icon">
        <component :is="iconComponent" />
      </div>
      <div class="kpi-card__title">
        {{ title }}
      </div>
    </div>

    <div class="kpi-card__content">
      <div class="kpi-card__value">
        {{ formattedValue }}
        <span v-if="unit" class="kpi-card__unit">{{ unit }}</span>
      </div>

      <div v-if="trend !== undefined" class="kpi-card__trend">
        <el-icon :class="trendClass">
          <ArrowUp v-if="trend > 0" />
          <ArrowDown v-if="trend < 0" />
          <Minus v-if="trend === 0" />
        </el-icon>
        <span :class="trendTextClass">{{ formattedTrend }}</span>
      </div>

      <div v-if="target !== undefined" class="kpi-card__progress">
        <div class="progress-info">
          <span class="progress-label">目标进度</span>
          <span class="progress-value">{{ progressPercentage }}%</span>
        </div>
        <el-progress
          :percentage="progressPercentage"
          :color="progressColor"
          :show-text="false"
          :stroke-width="4"
        />
      </div>

      <div v-if="subtitle" class="kpi-card__subtitle">
        {{ subtitle }}
      </div>
    </div>

    <div v-if="updateTime" class="kpi-card__footer">
      <el-icon class="update-icon">
        <Clock />
      </el-icon>
      <span class="update-time">{{ formattedUpdateTime }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import {
    ArrowUp,
    ArrowDown,
    Minus,
    Clock,
    TrendCharts,
    DataAnalysis,
    Monitor,
    Warning
  } from '@element-plus/icons-vue'
  import { formatNumber } from '@/utils'

  interface Props {
    /** KPI标题 */
    title: string
    /** KPI数值 */
    value: number
    /** 单位 */
    unit?: string
    /** 趋势变化（正数上升，负数下降，0持平） */
    trend?: number
    /** 目标值（用于进度条显示） */
    target?: number
    /** 副标题/描述 */
    subtitle?: string
    /** 更新时间 */
    updateTime?: string
    /** 卡片类型（影响颜色主题） */
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
    /** 卡片大小 */
    size?: 'small' | 'medium' | 'large'
    /** 图标类型 */
    icon?: 'trend' | 'analysis' | 'monitor' | 'warning'
    /** 小数位数 */
    precision?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    type: 'primary',
    size: 'medium',
    icon: 'trend',
    precision: 1
  })

  // 卡片样式类
  const cardClass = computed(() => [
    'kpi-card',
    `kpi-card--${props.type}`,
    `kpi-card--${props.size}`
  ])

  // 趋势箭头样式类
  const trendClass = computed(() => [
    'trend-icon',
    {
      'trend-icon--up': props.trend && props.trend > 0,
      'trend-icon--down': props.trend && props.trend < 0,
      'trend-icon--stable': props.trend === 0
    }
  ])

  // 趋势文字样式类
  const trendTextClass = computed(() => [
    'trend-text',
    {
      'trend-text--up': props.trend && props.trend > 0,
      'trend-text--down': props.trend && props.trend < 0,
      'trend-text--stable': props.trend === 0
    }
  ])

  // 图标组件
  const iconComponent = computed(() => {
    switch (props.icon) {
      case 'analysis':
        return DataAnalysis
      case 'monitor':
        return Monitor
      case 'warning':
        return Warning
      default:
        return TrendCharts
    }
  })

  // 格式化数值
  const formattedValue = computed(() => {
    return formatNumber(props.value, props.precision)
  })

  // 格式化趋势
  const formattedTrend = computed(() => {
    if (props.trend === undefined) return ''
    const abs = Math.abs(props.trend)
    return `${abs.toFixed(1)}%`
  })

  // 进度百分比
  const progressPercentage = computed(() => {
    if (props.target === undefined) return 0
    return Math.min(Math.round((props.value / props.target) * 100), 100)
  })

  // 进度条颜色
  const progressColor = computed(() => {
    const percentage = progressPercentage.value
    if (percentage >= 100) return '#67c23a'
    if (percentage >= 80) return '#409eff'
    if (percentage >= 60) return '#e6a23c'
    return '#f56c6c'
  })

  // 格式化更新时间
  const formattedUpdateTime = computed(() => {
    if (!props.updateTime) return ''
    const date = new Date(props.updateTime)
    return date.toLocaleTimeString()
  })
</script>

<style lang="scss" scoped>
  .kpi-card {
    position: relative;
    padding: var(--spacing-6);
    overflow: hidden;
    background: var(--color-bg-primary);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-lg);
      transform: translateY(-2px);
    }

    // 大屏模式优化
    @media (width >= 1920px) {
      padding: var(--spacing-8);
      border-radius: var(--radius-xl);
    }

    &__header {
      display: flex;
      align-items: center;
      margin-bottom: var(--spacing-4);
    }

    &__icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      margin-right: var(--spacing-3);
      font-size: 16px;
      border-radius: var(--radius-base);

      @media (width >= 1920px) {
        width: 40px;
        height: 40px;
        font-size: 20px;
      }
    }

    &__title {
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-secondary);

      @media (width >= 1920px) {
        font-size: var(--font-size-lg);
      }
    }

    &__content {
      margin-bottom: var(--spacing-4);
    }

    &__value {
      margin-bottom: var(--spacing-2);
      font-size: 2rem;
      font-weight: var(--font-weight-bold);
      line-height: 1.2;
      color: var(--color-text-primary);

      @media (width >= 1920px) {
        font-size: 2.5rem;
      }
    }

    &__unit {
      margin-left: var(--spacing-1);
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-normal);
      color: var(--color-text-secondary);
    }

    &__trend {
      display: flex;
      align-items: center;
      margin-bottom: var(--spacing-3);

      .trend-icon {
        margin-right: var(--spacing-1);

        &--up {
          color: var(--color-success);
        }

        &--down {
          color: var(--color-danger);
        }

        &--stable {
          color: var(--color-info);
        }
      }

      .trend-text {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);

        &--up {
          color: var(--color-success);
        }

        &--down {
          color: var(--color-danger);
        }

        &--stable {
          color: var(--color-info);
        }
      }
    }

    &__progress {
      margin-bottom: var(--spacing-3);

      .progress-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: var(--spacing-1);

        .progress-label {
          font-size: var(--font-size-xs);
          color: var(--color-text-secondary);
        }

        .progress-value {
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-medium);
          color: var(--color-text-primary);
        }
      }
    }

    &__subtitle {
      font-size: var(--font-size-sm);
      line-height: 1.4;
      color: var(--color-text-secondary);
    }

    &__footer {
      display: flex;
      align-items: center;
      padding-top: var(--spacing-3);
      font-size: var(--font-size-xs);
      color: var(--color-text-tertiary);
      border-top: 1px solid var(--color-border-lighter);

      .update-icon {
        margin-right: var(--spacing-1);
      }
    }

    // 类型样式
    &--primary {
      .kpi-card__icon {
        color: #409eff;
        background: linear-gradient(135deg, #409eff, #409eff20);
      }
    }

    &--success {
      .kpi-card__icon {
        color: #67c23a;
        background: linear-gradient(135deg, #67c23a, #67c23a20);
      }
    }

    &--warning {
      .kpi-card__icon {
        color: #e6a23c;
        background: linear-gradient(135deg, #e6a23c, #e6a23c20);
      }
    }

    &--danger {
      .kpi-card__icon {
        color: #f56c6c;
        background: linear-gradient(135deg, #f56c6c, #f56c6c20);
      }
    }

    &--info {
      .kpi-card__icon {
        color: #909399;
        background: linear-gradient(135deg, #909399, #90939920);
      }
    }

    // 尺寸样式
    &--small {
      padding: var(--spacing-4);

      .kpi-card__icon {
        width: 24px;
        height: 24px;
        font-size: 12px;
      }

      .kpi-card__value {
        font-size: 1.5rem;
      }
    }

    &--large {
      padding: var(--spacing-8);

      .kpi-card__icon {
        width: 48px;
        height: 48px;
        font-size: 24px;
      }

      .kpi-card__value {
        font-size: 3rem;
      }
    }

    // 深色主题优化
    .dark & {
      background: var(--color-bg-secondary);
      border-color: var(--color-border-dark);

      &:hover {
        background: var(--color-bg-tertiary);
        box-shadow: 0 8px 32px rgb(0 0 0 / 30%);
      }
    }
  }
</style>
