<template>
  <div class="product-selector">
    <el-select
      v-model="selectedValue"
      :placeholder="placeholder"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="remote"
      :remote="remote"
      :remote-method="handleRemoteSearch"
      :loading="loading"
      :disabled="disabled"
      class="w-full"
      @change="handleChange"
      @clear="handleClear"
    >
      <template v-if="showCreateOption && !remote" #header>
        <el-button 
          type="primary" 
          size="small" 
          class="w-full mb-2"
          @click="handleCreateNew"
        >
          <Plus class="w-4 h-4 mr-1" />
          新建产品
        </el-button>
      </template>
      
      <el-option
        v-for="product in currentOptions"
        :key="product.id"
        :label="formatOptionLabel(product)"
        :value="returnValue === 'id' ? product.id : product"
        class="product-option"
      >
        <div class="product-option-content">
          <div class="product-info">
            <span class="product-code">{{ product.productCode }}</span>
            <span class="product-name">{{ product.productName }}</span>
          </div>
          <div class="product-details">
            <el-tag size="small" type="info">{{ product.category }}</el-tag>
            <span class="package-types">
              {{ product.packageTypes?.slice(0, 2).join(', ') }}
              <span v-if="product.packageTypes?.length > 2">...</span>
            </span>
          </div>
        </div>
      </el-option>
      
      <template v-if="currentOptions.length === 0 && !loading" #empty>
        <div class="empty-content">
          <div class="empty-icon">📦</div>
          <div class="empty-text">暂无产品数据</div>
          <el-button 
            v-if="showCreateOption" 
            type="primary" 
            size="small" 
            @click="handleCreateNew"
          >
            新建产品
          </el-button>
        </div>
      </template>
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import type { Product } from '@/types/basicData'
import { searchProductSuggestions } from '@/api/basicData'
import { debounce } from '@/utils'

interface Props {
  /** 当前选中的值 */
  modelValue?: string | string[] | Product | Product[]
  /** 占位符文本 */
  placeholder?: string
  /** 是否多选 */
  multiple?: boolean
  /** 是否可清空 */
  clearable?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 是否远程搜索 */
  remote?: boolean
  /** 本地选项数据 */
  options?: Product[]
  /** 返回值类型 */
  returnValue?: 'id' | 'object'
  /** 是否显示创建选项 */
  showCreateOption?: boolean
  /** 最小搜索长度 */
  minSearchLength?: number
}

interface Emits {
  'update:modelValue': [value: string | string[] | Product | Product[] | null]
  'change': [product: Product | Product[] | null]
  'select': [product: Product]
  'create-new': []
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择产品',
  multiple: false,
  clearable: true,
  disabled: false,
  remote: true,
  returnValue: 'object',
  showCreateOption: false,
  minSearchLength: 2
})

const emit = defineEmits<Emits>()

// 状态
const loading = ref(false)
const remoteOptions = ref<Product[]>([])
const searchKeyword = ref('')

// 计算属性
const selectedValue = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

const currentOptions = computed(() => {
  if (props.remote) {
    return remoteOptions.value
  }
  return props.options || []
})

// 格式化选项标签
const formatOptionLabel = (product: Product) => {
  return `${product.productCode} - ${product.productName}`
}

// 远程搜索处理
const handleRemoteSearch = debounce(async (query: string) => {
  if (!props.remote) return
  
  searchKeyword.value = query
  if (!query || query.length < props.minSearchLength) {
    remoteOptions.value = []
    return
  }

  loading.value = true
  try {
    const suggestions = await searchProductSuggestions(query)
    // 转换搜索建议为Product对象（实际应用中需要完整的Product数据）
    remoteOptions.value = suggestions.map(suggestion => ({
      id: suggestion.id,
      productCode: suggestion.code,
      productName: suggestion.name,
      category: 'microcontroller' as any, // 临时填充，实际应该从API获取完整数据
      packageTypes: [],
      specifications: {
        workingVoltage: '',
        workingTemperature: '',
        pinCount: 0,
        dieSize: '',
        applications: []
      },
      processRequirements: {
        waferSize: 8,
        probeCardType: '',
        assemblyProcess: [],
        testProgram: ''
      },
      qualityStandards: {
        yieldTarget: 95,
        reliabilityLevel: 'A' as any,
        qualificationStandard: []
      },
      status: 'active' as any,
      createdAt: '',
      updatedAt: '',
      createdBy: ''
    }))
  } catch (error) {
    console.error('搜索产品失败:', error)
    remoteOptions.value = []
  } finally {
    loading.value = false
  }
}, 300)

// 选择变化处理
const handleChange = (value: any) => {
  if (value === null || value === undefined) {
    emit('change', null)
    return
  }

  if (props.multiple) {
    const products = Array.isArray(value) ? value : [value]
    if (props.returnValue === 'id') {
      const selectedProducts = currentOptions.value.filter(option => 
        products.includes(option.id)
      )
      emit('change', selectedProducts)
    } else {
      emit('change', products as Product[])
    }
  } else {
    if (props.returnValue === 'id') {
      const selectedProduct = currentOptions.value.find(option => option.id === value)
      emit('change', selectedProduct || null)
      if (selectedProduct) {
        emit('select', selectedProduct)
      }
    } else {
      emit('change', value as Product)
      emit('select', value as Product)
    }
  }
}

// 清空处理
const handleClear = () => {
  emit('change', null)
  if (props.remote) {
    remoteOptions.value = []
    searchKeyword.value = ''
  }
}

// 创建新产品
const handleCreateNew = () => {
  emit('create-new')
}

// 初始化
onMounted(() => {
  if (!props.remote && props.options) {
    // 非远程模式，使用传入的options
  }
})

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (props.remote && newValue) {
      // 远程模式下，如果有初始值，需要加载对应的选项数据
      // 这里可以根据需要实现初始值的数据加载
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.product-selector {
  .product-option-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;
  }
  
  .product-info {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .product-code {
      font-weight: 600;
      color: var(--color-text-primary);
      font-size: var(--font-size-sm);
    }
    
    .product-name {
      color: var(--color-text-secondary);
      font-size: var(--font-size-sm);
      flex: 1;
    }
  }
  
  .product-details {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .package-types {
      font-size: var(--font-size-xs);
      color: var(--color-text-tertiary);
    }
  }
  
  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: var(--spacing-4);
    
    .empty-icon {
      font-size: 24px;
      opacity: 0.5;
    }
    
    .empty-text {
      color: var(--color-text-secondary);
      font-size: var(--font-size-sm);
    }
  }
}

:deep(.el-select-dropdown__item) {
  height: auto;
  padding: 8px 12px;
  line-height: 1.4;
}
</style>