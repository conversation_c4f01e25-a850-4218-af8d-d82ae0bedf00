<template>
  <div id="app" class="app-layout">
    <div class="simple-header">
      <h1>IC封测CIM系统 - 简化版</h1>
    </div>
    <main class="simple-content">
      <h2>系统正常运行</h2>
      <p>当前时间: {{ currentTime }}</p>
      <button @click="updateTime">更新时间</button>
      <router-view />
    </main>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'

  const currentTime = ref(new Date().toLocaleString())

  const updateTime = () => {
    currentTime.value = new Date().toLocaleString()
  }

  onMounted(() => {
    console.log('✅ 简化版App.vue已挂载')
  })
</script>

<style scoped>
  .app-layout {
    min-height: 100vh;
    color: #333;
    background: #fff;
  }

  .simple-header {
    padding: 20px;
    color: white;
    background: #2563eb;
  }

  .simple-content {
    padding: 20px;
  }

  h1,
  h2 {
    margin: 0 0 16px;
  }

  button {
    padding: 8px 16px;
    color: white;
    cursor: pointer;
    background: #2563eb;
    border: none;
    border-radius: 4px;
  }

  button:hover {
    background: #3b82f6;
  }
</style>
