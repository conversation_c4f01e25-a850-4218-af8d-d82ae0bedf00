<template>
  <div class="data-table">
    <!-- 表格工具栏 -->
    <div v-if="showToolbar" class="table-toolbar">
      <div class="toolbar-left">
        <div v-if="showSelection && selectedRows.length > 0" class="selection-info">
          <span class="selection-count">已选择 {{ selectedRows.length }} 项</span>
          <el-button
            v-if="showBatchDelete"
            type="danger"
            size="small"
            :disabled="loading"
            @click="handleBatchDelete"
          >
            批量删除
          </el-button>
          <slot name="batch-actions" :selected-rows="selectedRows" />
        </div>
      </div>

      <div class="toolbar-right">
        <el-tooltip content="刷新数据" placement="top">
          <el-button
icon="Refresh" circle
size="small" :loading="loading" @click="handleRefresh"
/>
        </el-tooltip>

        <el-tooltip content="列设置" placement="top">
          <el-button
icon="Setting" circle
size="small" @click="showColumnSettings = true"
/>
        </el-tooltip>

        <slot name="toolbar-actions" />
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="data"
      :height="height"
      :max-height="maxHeight"
      :stripe="stripe"
      :border="border"
      :size="size"
      :empty-text="emptyText"
      :row-key="rowKey"
      :default-sort="defaultSort"
      :highlight-current-row="highlightCurrentRow"
      :row-class-name="rowClassName"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      @row-click="handleRowClick"
      @row-dblclick="handleRowDblClick"
    >
      <!-- 选择列 -->
      <el-table-column
        v-if="showSelection"
        type="selection"
        width="55"
        align="center"
        fixed="left"
      />

      <!-- 序号列 -->
      <el-table-column
        v-if="showIndex"
        type="index"
        label="序号"
        width="65"
        align="center"
        :fixed="indexFixed ? 'left' : false"
        :index="getRowIndex"
      />

      <!-- 动态列 -->
      <template v-for="column in visibleColumns" :key="column.prop">
        <el-table-column
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :align="column.align || 'left'"
          :sortable="column.sortable"
          :show-overflow-tooltip="column.showOverflowTooltip !== false"
        >
          <template #default="{ row, column: col, $index }">
            <!-- 自定义插槽 -->
            <slot
              v-if="$slots[column.prop]"
              :name="column.prop"
              :row="row"
              :column="col"
              :index="$index"
              :value="row[column.prop]"
            />

            <!-- 状态标签 -->
            <StatusBadge
              v-else-if="column.type === 'status'"
              :status="row[column.prop]"
              :type="column.statusType"
              :size="column.statusSize || 'small'"
            />

            <!-- 货币格式 -->
            <span
v-else-if="column.type === 'currency'" class="currency-value"
>
              {{ formatCurrency(row[column.prop], column.currency) }}
            </span>

            <!-- 数字格式 -->
            <span
v-else-if="column.type === 'number'" class="number-value"
>
              {{ formatNumber(row[column.prop], column.precision) }}
            </span>

            <!-- 百分比格式 -->
            <span
v-else-if="column.type === 'percent'" class="percent-value"
>
              {{ formatPercent(row[column.prop]) }}
            </span>

            <!-- 日期格式 -->
            <span
v-else-if="column.type === 'date'" class="date-value"
>
              {{ formatDate(row[column.prop], column.dateFormat) }}
            </span>

            <!-- 标签格式 -->
            <div
              v-else-if="column.type === 'tags' && Array.isArray(row[column.prop])"
              class="tags-cell"
            >
              <el-tag
                v-for="tag in row[column.prop].slice(0, column.maxTags || 3)"
                :key="tag"
                size="small"
                class="tag-item"
              >
                {{ tag }}
              </el-tag>
              <el-tag
                v-if="row[column.prop].length > (column.maxTags || 3)"
                size="small"
                type="info"
              >
                +{{ row[column.prop].length - (column.maxTags || 3) }}
              </el-tag>
            </div>

            <!-- 默认文本 -->
            <span v-else>{{ row[column.prop] }}</span>
          </template>

          <!-- 表头自定义 -->
          <template v-if="$slots[`${column.prop}-header`]" #header>
            <slot :name="`${column.prop}-header`" :column="column" />
          </template>
        </el-table-column>
      </template>

      <!-- 操作列 -->
      <el-table-column
        v-if="showActions"
        label="操作"
        :width="actionWidth"
        :fixed="actionFixed"
        align="center"
      >
        <template #default="{ row, $index }">
          <div class="action-buttons">
            <slot name="actions" :row="row" :index="$index" />
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div v-if="showPagination && pagination" class="table-pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="currentPageSize"
        :total="pagination.total"
        :page-sizes="pageSizes"
        :small="paginationSmall"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 列设置对话框 -->
    <el-dialog
v-model="showColumnSettings" title="列设置"
width="400px" :modal="false"
>
      <div class="column-settings">
        <el-checkbox
          v-model="checkAll"
          :indeterminate="isIndeterminate"
          @change="handleCheckAllChange"
        >
          全选
        </el-checkbox>

        <el-divider />

        <el-checkbox-group v-model="visibleColumnProps" @change="handleColumnVisibilityChange">
          <div
v-for="column in columns" :key="column.prop"
class="column-item"
>
            <el-checkbox :value="column.prop">
              {{ column.label }}
            </el-checkbox>
          </div>
        </el-checkbox-group>
      </div>

      <template #footer>
        <el-button @click="resetColumnSettings">重置</el-button>
        <el-button
type="primary" @click="showColumnSettings = false">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, nextTick } from 'vue'
  import { ElTable, ElMessageBox, ElMessage } from 'element-plus'
  import StatusBadge from './StatusBadge.vue'
  import { formatCurrency, formatNumber, formatDate } from '@/utils'

  interface TableColumn {
    prop: string
    label: string
    width?: number
    minWidth?: number
    fixed?: boolean | 'left' | 'right'
    align?: 'left' | 'center' | 'right'
    sortable?: boolean | 'custom'
    showOverflowTooltip?: boolean
    type?: 'text' | 'status' | 'currency' | 'number' | 'percent' | 'date' | 'tags'
    statusType?: string
    statusSize?: 'small' | 'medium' | 'large'
    currency?: string
    precision?: number
    dateFormat?: string
    maxTags?: number
    visible?: boolean
  }

  interface TablePagination {
    current: number
    pageSize: number
    total: number
  }

  interface Props {
    /** 表格数据 */
    data: any[]
    /** 表格列配置 */
    columns: TableColumn[]
    /** 是否显示加载状态 */
    loading?: boolean
    /** 表格高度 */
    height?: string | number
    /** 表格最大高度 */
    maxHeight?: string | number
    /** 是否显示斑马纹 */
    stripe?: boolean
    /** 是否显示边框 */
    border?: boolean
    /** 表格尺寸 */
    size?: 'large' | 'default' | 'small'
    /** 空数据提示文本 */
    emptyText?: string
    /** 行数据的Key */
    rowKey?: string
    /** 默认排序 */
    defaultSort?: { prop: string; order: 'ascending' | 'descending' }
    /** 是否高亮当前行 */
    highlightCurrentRow?: boolean
    /** 行的className */
    rowClassName?: string | ((params: { row: any; rowIndex: number }) => string)

    /** 是否显示选择列 */
    showSelection?: boolean
    /** 是否显示序号列 */
    showIndex?: boolean
    /** 序号列是否固定 */
    indexFixed?: boolean
    /** 是否显示操作列 */
    showActions?: boolean
    /** 操作列宽度 */
    actionWidth?: number
    /** 操作列是否固定 */
    actionFixed?: boolean | 'left' | 'right'

    /** 是否显示工具栏 */
    showToolbar?: boolean
    /** 是否显示批量删除 */
    showBatchDelete?: boolean

    /** 分页配置 */
    pagination?: TablePagination
    /** 是否显示分页 */
    showPagination?: boolean
    /** 分页尺寸选项 */
    pageSizes?: number[]
    /** 分页是否使用小尺寸 */
    paginationSmall?: boolean
  }

  interface Emits {
    'selection-change': [selection: any[]]
    'sort-change': [params: { column: any; prop: string; order: string }]
    'row-click': [row: any, column: any, event: Event]
    'row-dblclick': [row: any, column: any, event: Event]
    'page-change': [page: number]
    'page-size-change': [size: number]
    refresh: []
    'batch-delete': [rows: any[]]
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
    stripe: true,
    border: true,
    size: 'default',
    emptyText: '暂无数据',
    highlightCurrentRow: false,
    showSelection: false,
    showIndex: false,
    indexFixed: false,
    showActions: true,
    actionWidth: 150,
    actionFixed: 'right',
    showToolbar: true,
    showBatchDelete: true,
    showPagination: true,
    pageSizes: () => [10, 20, 50, 100],
    paginationSmall: false
  })

  const emit = defineEmits<Emits>()

  // 引用
  const tableRef = ref<InstanceType<typeof ElTable>>()

  // 状态
  const selectedRows = ref<any[]>([])
  const showColumnSettings = ref(false)
  const visibleColumnProps = ref<string[]>([])

  // 计算属性
  const currentPage = computed({
    get: () => props.pagination?.current || 1,
    set: (value: number) => emit('page-change', value)
  })

  const currentPageSize = computed({
    get: () => props.pagination?.pageSize || 20,
    set: (value: number) => emit('page-size-change', value)
  })

  const visibleColumns = computed(() => {
    return props.columns.filter(column => visibleColumnProps.value.includes(column.prop))
  })

  const checkAll = computed({
    get: () => visibleColumnProps.value.length === props.columns.length,
    set: (value: boolean) => {
      if (value) {
        visibleColumnProps.value = props.columns.map(col => col.prop)
      } else {
        visibleColumnProps.value = []
      }
    }
  })

  const isIndeterminate = computed(() => {
    const count = visibleColumnProps.value.length
    return count > 0 && count < props.columns.length
  })

  // 初始化可见列
  const initVisibleColumns = () => {
    visibleColumnProps.value = props.columns
      .filter(col => col.visible !== false)
      .map(col => col.prop)
  }

  // 获取行索引
  const getRowIndex = (index: number) => {
    if (props.pagination) {
      return (props.pagination.current - 1) * props.pagination.pageSize + index + 1
    }
    return index + 1
  }

  // 格式化百分比
  const formatPercent = (value: number) => {
    if (value == null) return '-'
    return `${(value * 100).toFixed(1)}%`
  }

  // 事件处理
  const handleSelectionChange = (selection: any[]) => {
    selectedRows.value = selection
    emit('selection-change', selection)
  }

  const handleSortChange = (params: { column: any; prop: string; order: string }) => {
    emit('sort-change', params)
  }

  const handleRowClick = (row: any, column: any, event: Event) => {
    emit('row-click', row, column, event)
  }

  const handleRowDblClick = (row: any, column: any, event: Event) => {
    emit('row-dblclick', row, column, event)
  }

  const handleCurrentChange = (page: number) => {
    emit('page-change', page)
  }

  const handleSizeChange = (size: number) => {
    emit('page-size-change', size)
  }

  const handleRefresh = () => {
    emit('refresh')
  }

  const handleBatchDelete = async () => {
    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${selectedRows.value.length} 项数据吗？`,
        '批量删除确认',
        {
          type: 'warning',
          confirmButtonText: '确定删除',
          cancelButtonText: '取消'
        }
      )

      emit('batch-delete', [...selectedRows.value])
    } catch {
      // 用户取消操作
    }
  }

  // 列设置
  const handleCheckAllChange = (checked: boolean) => {
    checkAll.value = checked
  }

  const handleColumnVisibilityChange = () => {
    // 列可见性变化时的处理
  }

  const resetColumnSettings = () => {
    initVisibleColumns()
  }

  // 公共方法
  const clearSelection = () => {
    tableRef.value?.clearSelection()
  }

  const toggleRowSelection = (row: any, selected?: boolean) => {
    tableRef.value?.toggleRowSelection(row, selected)
  }

  const setCurrentRow = (row: any) => {
    tableRef.value?.setCurrentRow(row)
  }

  const scrollTo = (options: ScrollToOptions) => {
    tableRef.value?.scrollTo(options)
  }

  // 暴露方法
  defineExpose({
    clearSelection,
    toggleRowSelection,
    setCurrentRow,
    scrollTo,
    tableRef
  })

  // 初始化
  initVisibleColumns()
</script>

<style lang="scss" scoped>
  .data-table {
    .table-toolbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-3) 0;
      margin-bottom: var(--spacing-4);

      .toolbar-left {
        flex: 1;

        .selection-info {
          display: flex;
          gap: var(--spacing-3);
          align-items: center;

          .selection-count {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--color-text-primary);
          }
        }
      }

      .toolbar-right {
        display: flex;
        gap: var(--spacing-2);
        align-items: center;
      }
    }

    .table-pagination {
      display: flex;
      justify-content: flex-end;
      padding: var(--spacing-3) 0;
      margin-top: var(--spacing-4);
    }

    .action-buttons {
      display: flex;
      gap: var(--spacing-2);
      align-items: center;
      justify-content: center;
    }

    // 特殊单元格样式
    .currency-value {
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }

    .number-value {
      font-family: var(--font-mono);
      text-align: right;
    }

    .percent-value {
      font-weight: var(--font-weight-medium);
    }

    .date-value {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .tags-cell {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      align-items: center;

      .tag-item {
        margin: 0;
      }
    }
  }

  .column-settings {
    .column-item {
      padding: var(--spacing-2) 0;
      border-bottom: 1px solid var(--color-border-light);

      &:last-child {
        border-bottom: none;
      }
    }
  }

  // Element Plus 表格样式覆盖
  :deep(.el-table) {
    .el-table__header {
      th {
        font-weight: var(--font-weight-semibold);
        color: var(--color-text-primary);
        background-color: var(--color-bg-tertiary);
        border-bottom: 2px solid var(--color-border-light);
      }
    }

    .el-table__row {
      &:hover {
        background-color: var(--color-bg-hover);
      }
    }

    .current-row {
      background-color: var(--color-primary-light);
    }
  }

  :deep(.el-pagination) {
    .el-pagination__total {
      color: var(--color-text-secondary);
    }
  }
</style>
