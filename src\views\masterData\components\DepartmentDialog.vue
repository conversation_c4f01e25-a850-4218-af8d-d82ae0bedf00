<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="700px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      size="default"
      @submit.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="部门名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入部门名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门编码" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="请输入部门编码"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="部门类型" prop="type">
            <el-select v-model="formData.type" placeholder="请选择部门类型" style="width: 100%">
              <el-option
                v-for="option in departmentTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上级部门" prop="parentId">
            <el-tree-select
              v-model="formData.parentId"
              :data="parentDepartmentOptions"
              :props="treeSelectProps"
              placeholder="请选择上级部门"
              check-strictly
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="部门负责人" prop="managerId">
            <el-select
              v-model="formData.managerId"
              placeholder="请选择部门负责人"
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="employee in employees"
                :key="employee.id"
                :label="`${employee.name} (${employee.employeeNumber})`"
                :value="employee.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="成本中心代码" prop="costCenterCode">
            <el-input
              v-model="formData.costCenterCode"
              placeholder="请输入成本中心代码"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="办公地点" prop="location">
            <el-input
              v-model="formData.location"
              placeholder="请输入办公地点"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门电话" prop="phoneNumber">
            <el-input
              v-model="formData.phoneNumber"
              placeholder="请输入部门电话"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="部门邮箱" prop="email">
        <el-input
          v-model="formData.email"
          placeholder="请输入部门邮箱"
          clearable
        />
      </el-form-item>

      <el-form-item label="部门描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入部门描述"
        />
      </el-form-item>

      <el-form-item label="主要职责" prop="responsibilities">
        <div class="responsibility-manager">
          <div
            v-for="(responsibility, index) in formData.responsibilities"
            :key="index"
            class="responsibility-item"
          >
            <el-input
              v-model="formData.responsibilities[index]"
              placeholder="请输入职责描述"
              clearable
            />
            <el-button
              type="text"
              :icon="Delete"
              @click="removeResponsibility(index)"
            />
          </div>
          <el-button
            type="text"
            :icon="Plus"
            @click="addResponsibility"
          >
            添加职责
          </el-button>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import type { 
  Department, 
  Employee, 
  DepartmentFormData, 
  DepartmentType 
} from '@/types/organization'

// Props
interface Props {
  visible: boolean
  department?: Department | null
  parentDepartment?: Department | null
  departments: Department[]
  employees: Employee[]
}

// Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  department: null,
  parentDepartment: null
})

const emit = defineEmits<Emits>()

// 组件引用
const formRef = ref<FormInstance>()

// 状态数据
const loading = ref(false)
const formData = ref<DepartmentFormData>({
  name: '',
  code: '',
  type: 'production' as DepartmentType,
  parentId: undefined,
  managerId: undefined,
  description: '',
  responsibilities: [''],
  costCenterCode: '',
  location: '',
  phoneNumber: '',
  email: ''
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.department)

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑部门' : '新增部门'
})

// 部门类型选项
const departmentTypeOptions = [
  { label: '生产部门', value: 'production' },
  { label: '技术部门', value: 'technical' },
  { label: '支持部门', value: 'support' },
  { label: '管理部门', value: 'management' }
]

// 树形选择器配置
const treeSelectProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}

// 上级部门选项（排除当前编辑的部门及其子部门）
const parentDepartmentOptions = computed(() => {
  const buildTree = (depts: Department[]): Department[] => {
    const map = new Map<string, Department>()
    const roots: Department[] = []
    
    // 创建映射，排除当前编辑的部门
    depts.forEach(dept => {
      if (dept.id !== props.department?.id) {
        map.set(dept.id, { ...dept, children: [] })
      }
    })
    
    // 构建树形结构
    depts.forEach(dept => {
      if (dept.id !== props.department?.id) {
        const node = map.get(dept.id)!
        if (dept.parentId && map.has(dept.parentId)) {
          const parent = map.get(dept.parentId)!
          parent.children!.push(node)
        } else {
          roots.push(node)
        }
      }
    })
    
    return roots
  }
  
  return buildTree(props.departments)
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 50, message: '部门名称长度在2-50个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入部门编码', trigger: 'blur' },
    { 
      pattern: /^[A-Z0-9_]+$/, 
      message: '部门编码只能包含大写字母、数字和下划线', 
      trigger: 'blur' 
    }
  ],
  type: [
    { required: true, message: '请选择部门类型', trigger: 'change' }
  ],
  email: [
    { 
      type: 'email', 
      message: '请输入正确的邮箱地址', 
      trigger: 'blur' 
    }
  ],
  phoneNumber: [
    { 
      pattern: /^[\d-]+$/, 
      message: '请输入正确的电话号码', 
      trigger: 'blur' 
    }
  ]
}

// 职责管理
const addResponsibility = () => {
  formData.value.responsibilities.push('')
}

const removeResponsibility = (index: number) => {
  if (formData.value.responsibilities.length > 1) {
    formData.value.responsibilities.splice(index, 1)
  }
}

// 事件处理
const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 过滤空的职责
    const filteredResponsibilities = formData.value.responsibilities.filter(r => r.trim())
    
    const submitData = {
      ...formData.value,
      responsibilities: filteredResponsibilities
    }
    
    console.log('提交部门数据:', submitData)
    
    ElMessage.success(isEdit.value ? '部门更新成功' : '部门创建成功')
    emit('success')
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    name: '',
    code: '',
    type: 'production' as DepartmentType,
    parentId: undefined,
    managerId: undefined,
    description: '',
    responsibilities: [''],
    costCenterCode: '',
    location: '',
    phoneNumber: '',
    email: ''
  }
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 初始化表单数据
const initFormData = () => {
  if (props.department) {
    // 编辑模式
    formData.value = {
      name: props.department.name,
      code: props.department.code,
      type: props.department.type,
      parentId: props.department.parentId,
      managerId: props.department.managerId,
      description: props.department.description || '',
      responsibilities: props.department.responsibilities.length > 0 
        ? [...props.department.responsibilities] 
        : [''],
      costCenterCode: props.department.costCenterCode || '',
      location: props.department.location || '',
      phoneNumber: props.department.phoneNumber || '',
      email: props.department.email || ''
    }
  } else {
    // 新增模式
    resetForm()
    // 如果有父部门，自动设置
    if (props.parentDepartment) {
      formData.value.parentId = props.parentDepartment.id
    }
  }
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible) {
    initFormData()
  }
})

watch(() => props.department, () => {
  if (props.visible) {
    initFormData()
  }
})
</script>

<style lang="scss" scoped>
.responsibility-manager {
  width: 100%;

  .responsibility-item {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
    margin-bottom: var(--spacing-2);

    .el-input {
      flex: 1;
    }

    .el-button {
      flex-shrink: 0;
      color: var(--el-color-danger);

      &:hover {
        background-color: var(--el-color-danger-light-9);
      }
    }
  }

  .el-button {
    margin-top: var(--spacing-2);
    color: var(--el-color-primary);

    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
}

.dialog-footer {
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
}

:deep(.el-dialog) {
  .el-dialog__header {
    padding-bottom: var(--spacing-3);
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .el-dialog__body {
    padding: var(--spacing-4);
  }

  .el-dialog__footer {
    padding-top: var(--spacing-3);
    border-top: 1px solid var(--el-border-color-lighter);
  }
}

// 响应式设计
@media (width <= 768px) {
  :deep(.el-dialog) {
    width: 90% !important;
    margin: var(--spacing-2);
  }
}
</style>