/**
 * IC封测CIM系统 - API模拟服务器
 * Mock API Server for Development Environment
 */

import type { ApiResponse, ApiPageResponse } from './config'
import { MockApiHelper, ApiEndpoints, ApiErrorCode } from './config'

// 导入模拟数据
import { allMockCustomers, customerStats } from '@/utils/mockData/customers'
import { allMockOrders, orderStats, orderStatCards } from '@/utils/mockData/orders'
import {
  mockProducts,
  mockEquipment,
  mockProcessParameters,
  mockQualityStandards,
  mockSuppliers,
  packageTypeInfos,
  mockBasicDataStats
} from '@/utils/mockData/basicData'

/**
 * Mock API路由处理器
 */
export class MockApiServer {
  private routes: Map<string, Function> = new Map()

  constructor() {
    this.initializeRoutes()
  }

  /**
   * 初始化所有API路由
   */
  private initializeRoutes(): void {
    // 客户管理路由
    this.registerCustomerRoutes()

    // 订单管理路由
    this.registerOrderRoutes()

    // 基础数据管理路由
    this.registerBasicDataRoutes()

    // 系统管理路由
    this.registerSystemRoutes()
  }

  /**
   * 注册客户管理路由
   */
  private registerCustomerRoutes(): void {
    // 获取客户列表
    this.routes.set('GET /customers', async (params: any) => {
      await MockApiHelper.delay('SEARCH')

      const {
        page = 1,
        pageSize = 20,
        keyword,
        customerType,
        customerLevel,
        industry,
        status
      } = params
      let filteredCustomers = [...allMockCustomers]

      // 关键词搜索
      if (keyword) {
        const kw = keyword.toLowerCase()
        filteredCustomers = filteredCustomers.filter(
          customer =>
            customer.name.toLowerCase().includes(kw) ||
            customer.code.toLowerCase().includes(kw) ||
            customer.englishName?.toLowerCase().includes(kw)
        )
      }

      // 筛选条件
      if (customerType) {
        filteredCustomers = filteredCustomers.filter(c => c.type === customerType)
      }
      if (customerLevel) {
        filteredCustomers = filteredCustomers.filter(c => c.level === customerLevel)
      }
      if (industry) {
        filteredCustomers = filteredCustomers.filter(c => c.industryType === industry)
      }
      if (status) {
        filteredCustomers = filteredCustomers.filter(c => c.status === status)
      }

      const { data, total, totalPages } = MockApiHelper.applyPagination(
        filteredCustomers,
        page,
        pageSize
      )

      return MockApiHelper.createPageResponse(data, total, page, pageSize)
    })

    // 获取客户详情
    this.routes.set('GET /customers/:id', async (params: any, pathParams: any) => {
      await MockApiHelper.delay()

      const customer = allMockCustomers.find(c => c.id === pathParams.id)
      if (!customer) {
        return MockApiHelper.createErrorResponse(ApiErrorCode.NOT_FOUND, '客户不存在')
      }

      return MockApiHelper.createSuccessResponse(customer)
    })

    // 创建客户
    this.routes.set('POST /customers', async (params: any, pathParams: any, data: any) => {
      await MockApiHelper.delay('CREATE')

      // 验证必填字段
      const requiredFields = ['customerName', 'customerType', 'customerLevel']
      const validationErrors = MockApiHelper.validateRequiredFields(data, requiredFields)
      if (validationErrors.length > 0) {
        return MockApiHelper.formatValidationErrors(validationErrors)
      }

      const newCustomer = {
        id: MockApiHelper.generateId('customer'),
        customerCode: `C${String(allMockCustomers.length + 1).padStart(3, '0')}`,
        ...data,
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'current_user'
      }

      allMockCustomers.unshift(newCustomer)
      return MockApiHelper.createSuccessResponse(newCustomer, '客户创建成功')
    })

    // 客户统计信息
    this.routes.set('GET /customers/stats', async () => {
      await MockApiHelper.delay()
      return MockApiHelper.createSuccessResponse(customerStats)
    })

    // 客户搜索建议
    this.routes.set('GET /customers/suggestions', async (params: any) => {
      await MockApiHelper.delay('SEARCH')

      const { keyword } = params
      if (!keyword || keyword.length < 2) {
        return MockApiHelper.createSuccessResponse([])
      }

      const suggestions = allMockCustomers
        .filter(
          customer =>
            customer.name.toLowerCase().includes(keyword.toLowerCase()) ||
            customer.code.toLowerCase().includes(keyword.toLowerCase())
        )
        .slice(0, 10)
        .map(customer => ({
          id: customer.id,
          name: customer.name,
          code: customer.code
        }))

      return MockApiHelper.createSuccessResponse(suggestions)
    })
  }

  /**
   * 注册订单管理路由
   */
  private registerOrderRoutes(): void {
    // 获取订单列表
    this.routes.set('GET /orders', async (params: any) => {
      await MockApiHelper.delay('SEARCH')

      const { page = 1, pageSize = 20, orderNumber, customerId, status, priority } = params
      let filteredOrders = [...allMockOrders]

      // 筛选条件
      if (orderNumber) {
        filteredOrders = filteredOrders.filter(order =>
          order.orderNumber.toLowerCase().includes(orderNumber.toLowerCase())
        )
      }
      if (customerId) {
        filteredOrders = filteredOrders.filter(order => order.customerId === customerId)
      }
      if (status && Array.isArray(status) && status.length > 0) {
        filteredOrders = filteredOrders.filter(order => status.includes(order.status))
      }
      if (priority && Array.isArray(priority) && priority.length > 0) {
        filteredOrders = filteredOrders.filter(order => priority.includes(order.priority))
      }

      const { data, total, totalPages } = MockApiHelper.applyPagination(
        filteredOrders,
        page,
        pageSize
      )

      return MockApiHelper.createPageResponse(data, total, page, pageSize)
    })

    // 订单统计信息
    this.routes.set('GET /orders/stats', async () => {
      await MockApiHelper.delay()
      return MockApiHelper.createSuccessResponse(orderStats)
    })

    // 订单统计卡片
    this.routes.set('GET /orders/stat-cards', async () => {
      await MockApiHelper.delay()
      return MockApiHelper.createSuccessResponse(orderStatCards)
    })
  }

  /**
   * 注册基础数据管理路由
   */
  private registerBasicDataRoutes(): void {
    // 产品管理
    this.routes.set('GET /basic-data/products', async (params: any) => {
      await MockApiHelper.delay('SEARCH')

      const { page = 1, pageSize = 20, keyword, category, status } = params
      let filteredProducts = [...mockProducts]

      // 筛选条件
      if (keyword) {
        const kw = keyword.toLowerCase()
        filteredProducts = filteredProducts.filter(
          product =>
            product.productCode.toLowerCase().includes(kw) ||
            product.productName.toLowerCase().includes(kw)
        )
      }
      if (category) {
        filteredProducts = filteredProducts.filter(p => p.category === category)
      }
      if (status) {
        filteredProducts = filteredProducts.filter(p => p.status === status)
      }

      const { data, total, totalPages } = MockApiHelper.applyPagination(
        filteredProducts,
        page,
        pageSize
      )

      return MockApiHelper.createPageResponse(data, total, page, pageSize)
    })

    // 设备管理
    this.routes.set('GET /basic-data/equipment', async (params: any) => {
      await MockApiHelper.delay('SEARCH')

      const { page = 1, pageSize = 20, keyword, category, status } = params
      let filteredEquipment = [...mockEquipment]

      // 筛选条件
      if (keyword) {
        const kw = keyword.toLowerCase()
        filteredEquipment = filteredEquipment.filter(
          equipment =>
            equipment.equipmentCode.toLowerCase().includes(kw) ||
            equipment.equipmentName.toLowerCase().includes(kw) ||
            equipment.model.toLowerCase().includes(kw)
        )
      }
      if (category) {
        filteredEquipment = filteredEquipment.filter(e => e.type === category)
      }
      if (status) {
        filteredEquipment = filteredEquipment.filter(e => e.status === status)
      }

      const { data, total, totalPages } = MockApiHelper.applyPagination(
        filteredEquipment,
        page,
        pageSize
      )

      return MockApiHelper.createPageResponse(data, total, page, pageSize)
    })

    // 工艺参数
    this.routes.set('GET /basic-data/process-parameters', async (params: any) => {
      await MockApiHelper.delay('SEARCH')

      const { page = 1, pageSize = 20, keyword, category } = params
      let filteredParameters = [...mockProcessParameters]

      if (keyword) {
        const kw = keyword.toLowerCase()
        filteredParameters = filteredParameters.filter(
          param =>
            param.parameterName.toLowerCase().includes(kw) ||
            param.parameterCode.toLowerCase().includes(kw)
        )
      }
      if (category) {
        filteredParameters = filteredParameters.filter(p => p.processType === category)
      }

      const { data, total, totalPages } = MockApiHelper.applyPagination(
        filteredParameters,
        page,
        pageSize
      )

      return MockApiHelper.createPageResponse(data, total, page, pageSize)
    })

    // 质量标准
    this.routes.set('GET /basic-data/quality-standards', async (params: any) => {
      await MockApiHelper.delay('SEARCH')

      const { page = 1, pageSize = 20, keyword, category, status } = params
      let filteredStandards = [...mockQualityStandards]

      if (keyword) {
        const kw = keyword.toLowerCase()
        filteredStandards = filteredStandards.filter(
          std =>
            std.standardCode.toLowerCase().includes(kw) ||
            std.standardName.toLowerCase().includes(kw)
        )
      }
      if (category) {
        filteredStandards = filteredStandards.filter(s => s.type === category)
      }
      if (status) {
        filteredStandards = filteredStandards.filter(s => s.status === status)
      }

      const { data, total, totalPages } = MockApiHelper.applyPagination(
        filteredStandards,
        page,
        pageSize
      )

      return MockApiHelper.createPageResponse(data, total, page, pageSize)
    })

    // 供应商
    this.routes.set('GET /basic-data/suppliers', async (params: any) => {
      await MockApiHelper.delay('SEARCH')

      const { page = 1, pageSize = 20, keyword, category, status } = params
      let filteredSuppliers = [...mockSuppliers]

      if (keyword) {
        const kw = keyword.toLowerCase()
        filteredSuppliers = filteredSuppliers.filter(
          supplier =>
            supplier.supplierCode.toLowerCase().includes(kw) ||
            supplier.supplierName.toLowerCase().includes(kw) ||
            supplier.supplierNameEn?.toLowerCase().includes(kw)
        )
      }
      if (category) {
        filteredSuppliers = filteredSuppliers.filter(s => s.type === category)
      }
      if (status) {
        filteredSuppliers = filteredSuppliers.filter(s => s.status === status)
      }

      const { data, total, totalPages } = MockApiHelper.applyPagination(
        filteredSuppliers,
        page,
        pageSize
      )

      return MockApiHelper.createPageResponse(data, total, page, pageSize)
    })

    // 封装类型信息
    this.routes.set('GET /basic-data/package-types', async () => {
      await MockApiHelper.delay()
      return MockApiHelper.createSuccessResponse(packageTypeInfos)
    })

    // 基础数据统计
    this.routes.set('GET /basic-data/stats', async () => {
      await MockApiHelper.delay()
      return MockApiHelper.createSuccessResponse(mockBasicDataStats)
    })
  }

  /**
   * 注册系统管理路由
   */
  private registerSystemRoutes(): void {
    // 系统设置
    this.routes.set('GET /system/settings', async () => {
      await MockApiHelper.delay()
      return MockApiHelper.createSuccessResponse({
        systemName: 'IC封测CIM系统',
        version: '1.0.0',
        company: 'IC封测公司',
        timezone: 'Asia/Shanghai',
        language: 'zh-CN'
      })
    })

    // 用户信息
    this.routes.set('GET /auth/profile', async () => {
      await MockApiHelper.delay()
      return MockApiHelper.createSuccessResponse({
        id: 'user_001',
        username: 'admin',
        name: '系统管理员',
        email: '<EMAIL>',
        avatar: '/avatars/admin.png',
        role: 'admin',
        permissions: ['*'],
        lastLoginAt: new Date().toISOString()
      })
    })
  }

  /**
   * 处理API请求
   */
  async handleRequest(
    method: string,
    path: string,
    params?: any,
    data?: any
  ): Promise<ApiResponse | ApiPageResponse> {
    try {
      // 解析路径参数
      const { routeKey, pathParams } = this.parseRoute(method, path)

      // 查找对应的处理函数
      const handler = this.routes.get(routeKey)
      if (!handler) {
        return MockApiHelper.createErrorResponse(
          ApiErrorCode.NOT_FOUND,
          `API路由不存在: ${method} ${path}`
        )
      }

      // 执行处理函数
      return await handler(params, pathParams, data)
    } catch (error) {
      console.error('Mock API处理错误:', error)
      return MockApiHelper.createErrorResponse(
        ApiErrorCode.INTERNAL_SERVER_ERROR,
        error instanceof Error ? error.message : '服务器内部错误'
      )
    }
  }

  /**
   * 解析路由和路径参数
   */
  private parseRoute(method: string, path: string): { routeKey: string; pathParams: any } {
    const pathParams: any = {}

    // 查找匹配的路由模式
    for (const [routeKey] of this.routes) {
      const [routeMethod, routePath] = routeKey.split(' ')
      if (routeMethod !== method) continue

      const routeRegex = routePath.replace(/:([^/]+)/g, '([^/]+)')
      const regex = new RegExp(`^${routeRegex}$`)
      const match = path.match(regex)

      if (match) {
        // 提取路径参数
        const paramNames = (routePath.match(/:([^/]+)/g) || []).map(p => p.slice(1))
        paramNames.forEach((name, index) => {
          pathParams[name] = match[index + 1]
        })

        return { routeKey, pathParams }
      }
    }

    return { routeKey: `${method} ${path}`, pathParams }
  }

  /**
   * 获取所有注册的路由
   */
  getRoutes(): string[] {
    return Array.from(this.routes.keys())
  }
}

// 创建全局Mock API服务器实例
export const mockApiServer = new MockApiServer()

export default mockApiServer
