/**
 * 合同管理相关类型定义
 * Contract Management Type Definitions
 */

// 合同状态枚举
export enum ContractStatus {
  DRAFT = 'draft', // 草稿
  PENDING_REVIEW = 'pending_review', // 待审核
  PENDING_SIGN = 'pending_sign', // 待签署
  EXECUTING = 'executing', // 执行中
  COMPLETED = 'completed', // 已完成
  TERMINATED = 'terminated', // 已终止
  EXPIRED = 'expired' // 已过期
}

// 合同类型枚举
export enum ContractType {
  MASS_PRODUCTION = 'mass_production', // 量产合同
  NPI_DEVELOPMENT = 'npi_development', // NPI开发合同
  FRAMEWORK = 'framework', // 框架合同
  URGENT_ORDER = 'urgent_order', // 紧急订单合同
  CUSTOM_SERVICE = 'custom_service' // 定制服务合同
}

// 签署状态枚举
export enum SignatureStatus {
  UNSIGNED = 'unsigned', // 未签署
  PARTIAL = 'partial', // 部分签署
  COMPLETED = 'completed', // 签署完成
  REJECTED = 'rejected' // 签署拒绝
}

// 合同变更类型
export enum ChangeType {
  ECN = 'ecn', // 工程变更
  PRICE_ADJUSTMENT = 'price_adjustment', // 价格调整
  DELIVERY_CHANGE = 'delivery_change', // 交期变更
  QUANTITY_CHANGE = 'quantity_change', // 数量变更
  SPEC_CHANGE = 'spec_change' // 规格变更
}

// 合同条款类型
export interface ContractClause {
  id: string
  type: 'technical' | 'commercial' | 'quality' | 'legal' | 'risk'
  title: string
  content: string
  isRequired: boolean
  category?: string
  riskLevel?: 'low' | 'medium' | 'high'
}

// 技术规格条款
export interface TechnicalSpec {
  packageType: string // 封装类型
  testProgram: string // 测试程序
  qualityStandard: string // 质量标准
  reliabilitySpec: string // 可靠性规范
  yieldRequirement: number // 良率要求
  electricalSpec: Record<string, any> // 电性参数
}

// 商务条款
export interface CommercialTerms {
  priceModel: 'fixed' | 'tiered' | 'cost_plus' // 价格模式
  paymentTerms: string // 付款条件
  deliveryTerms: string // 交期条件
  penaltyClause: string // 违约条款
  priceValidityPeriod: number // 价格有效期（天）
  minimumOrderQuantity: number // 最小订单量
}

// 质量条款
export interface QualityTerms {
  iatfCompliance: boolean // IATF16949符合性
  customerQualityReq: string // 客户质量要求
  returnPolicy: string // 退货政策
  qualityEscalation: string // 质量升级流程
  ppapRequirement: boolean // PPAP要求
  statisticalRequirement: string // 统计要求
}

// 签署信息
export interface Signature {
  id: string
  signerName: string
  signerRole: string
  signerCompany: string
  signedAt?: Date
  signatureMethod: 'electronic' | 'physical'
  signatureData?: string // 签名数据
  ipAddress?: string
  location?: string
}

// 合同变更记录
export interface ContractChange {
  id: string
  changeType: ChangeType
  changeReason: string
  changeDescription: string
  oldValue: any
  newValue: any
  requestedBy: string
  requestedAt: Date
  approvedBy?: string
  approvedAt?: Date
  status: 'pending' | 'approved' | 'rejected'
  impact: {
    cost?: number
    schedule?: number // 天数
    risk?: 'low' | 'medium' | 'high'
  }
}

// 合同执行监控
export interface ExecutionMonitoring {
  contractId: string
  deliveryProgress: {
    planned: number
    actual: number
    onTimeRate: number
  }
  paymentProgress: {
    totalAmount: number
    paidAmount: number
    pendingAmount: number
    overdueAmount: number
  }
  qualityMetrics: {
    yieldRate: number
    defectRate: number
    customerComplaints: number
    returnRate: number
  }
  riskIndicators: {
    deliveryRisk: 'low' | 'medium' | 'high'
    qualityRisk: 'low' | 'medium' | 'high'
    paymentRisk: 'low' | 'medium' | 'high'
  }
}

// 主合同接口
export interface Contract {
  id: string
  contractNumber: string // 合同编号
  title: string // 合同标题
  type: ContractType
  status: ContractStatus

  // 关联信息
  customerId: string
  customerName: string
  quotationId?: string // 关联报价单ID
  orderId?: string // 关联订单ID

  // 合同基本信息
  signedDate?: Date
  effectiveDate: Date
  expiryDate: Date
  totalValue: number
  currency: string

  // 合同条款
  technicalSpec: TechnicalSpec
  commercialTerms: CommercialTerms
  qualityTerms: QualityTerms
  customClauses: ContractClause[]

  // 签署信息
  signatures: Signature[]
  signatureStatus: SignatureStatus

  // 变更记录
  changes: ContractChange[]

  // 执行监控
  executionMonitoring?: ExecutionMonitoring

  // 文档信息
  documentVersion: string
  documentUrl?: string
  templateId?: string

  // 审核信息
  reviewComments: {
    reviewer: string
    role: 'legal' | 'technical' | 'commercial' | 'management'
    comment: string
    status: 'approved' | 'rejected' | 'revision_required'
    reviewedAt: Date
  }[]

  // 元数据
  createdBy: string
  createdAt: Date
  updatedBy: string
  updatedAt: Date

  // 附件
  attachments: {
    id: string
    name: string
    type: string
    size: number
    url: string
    uploadedAt: Date
  }[]
}

// 合同模板
export interface ContractTemplate {
  id: string
  name: string
  type: ContractType
  version: string
  description: string
  clauses: ContractClause[]
  defaultTerms: {
    technical?: Partial<TechnicalSpec>
    commercial?: Partial<CommercialTerms>
    quality?: Partial<QualityTerms>
  }
  isActive: boolean
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

// 合同查询参数
export interface ContractQueryParams {
  keyword?: string
  status?: ContractStatus
  type?: ContractType
  customerId?: string
  dateRange?: [Date, Date]
  signatureStatus?: SignatureStatus
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 合同统计
export interface ContractStatistics {
  totalContracts: number
  statusDistribution: Record<ContractStatus, number>
  typeDistribution: Record<ContractType, number>
  totalValue: number
  averageValue: number
  expiringContracts: number // 30天内到期
  overduePayments: number
  riskContracts: number // 高风险合同数量
}

// API响应类型
export interface ContractListResponse {
  contracts: Contract[]
  total: number
  statistics: ContractStatistics
}

export interface CreateContractData {
  title: string
  type: ContractType
  customerId: string
  quotationId?: string
  effectiveDate: Date
  expiryDate: Date
  totalValue: number
  currency: string
  technicalSpec: TechnicalSpec
  commercialTerms: CommercialTerms
  qualityTerms: QualityTerms
  templateId?: string
}

export interface UpdateContractData extends Partial<CreateContractData> {
  id: string
}
