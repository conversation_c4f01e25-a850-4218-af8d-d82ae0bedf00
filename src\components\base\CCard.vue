<template>
  <div :class="cardClasses" @click="handleClick">
    <div v-if="$slots.header || title" class="c-card__header">
      <slot name="header">
        <h3 v-if="title" class="c-card__title">
          {{ title }}
        </h3>
      </slot>
      <div v-if="$slots.extra" class="c-card__extra">
        <slot name="extra" />
      </div>
    </div>

    <div class="c-card__body">
      <slot />
    </div>

    <div v-if="$slots.footer" class="c-card__footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'

  export interface CCardProps {
    /** 卡片标题 */
    title?: string
    /** 是否有阴影 */
    shadow?: 'never' | 'hover' | 'always'
    /** 是否有边框 */
    bordered?: boolean
    /** 是否可点击 */
    hoverable?: boolean
    /** 自定义内边距 */
    padding?: string
    /** 是否加载中 */
    loading?: boolean
  }

  const props = withDefaults(defineProps<CCardProps>(), {
    shadow: 'hover',
    bordered: true,
    hoverable: false,
    loading: false
  })

  const emit = defineEmits<{
    click: [event: MouseEvent]
  }>()

  // 计算样式类名
  const cardClasses = computed(() => [
    'c-card',
    {
      'c-card--shadow-never': props.shadow === 'never',
      'c-card--shadow-hover': props.shadow === 'hover',
      'c-card--shadow-always': props.shadow === 'always',
      'c-card--bordered': props.bordered,
      'c-card--hoverable': props.hoverable,
      'c-card--loading': props.loading
    }
  ])

  // 处理点击事件
  const handleClick = (event: MouseEvent) => {
    emit('click', event)
  }
</script>

<style lang="scss">
  .c-card {
    @include card;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &--shadow-never {
      box-shadow: none;

      &:hover {
        box-shadow: none;
      }
    }

    &--shadow-hover {
      box-shadow: var(--shadow-sm);

      &:hover {
        box-shadow: var(--shadow-base);
      }
    }

    &--shadow-always {
      box-shadow: var(--shadow-base);

      &:hover {
        box-shadow: var(--shadow-md);
      }
    }

    &--bordered {
      border: 1px solid var(--color-border-light);
    }

    &--hoverable {
      cursor: pointer;
      transition: all var(--transition-fast);

      &:hover {
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
      }
    }

    &--loading {
      position: relative;

      &::before {
        position: absolute;
        inset: 0;
        z-index: 1;
        content: '';
        background: rgb(255 255 255 / 80%);
        backdrop-filter: blur(1px);
      }

      &::after {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 2;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        content: '';
        border: 2px solid var(--color-border-base);
        border-top-color: var(--color-primary);
        border-radius: 50%;
        animation: card-loading 1s linear infinite;
      }
    }

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--card-padding);
      padding-bottom: var(--spacing-3);
      border-bottom: 1px solid var(--color-border-light);
    }

    &__title {
      margin: 0;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-bold);
      color: var(--color-text-primary);
    }

    &__extra {
      flex-shrink: 0;
      margin-left: var(--spacing-3);
    }

    &__body {
      flex: 1;
      padding: var(--card-padding);
    }

    &__footer {
      padding: var(--card-padding);
      padding-top: var(--spacing-3);
      background: var(--color-bg-secondary);
      border-top: 1px solid var(--color-border-light);
    }

    // 卡片内容区域无标题时的样式调整
    &:not(:has(.c-card__header)) {
      .c-card__body {
        padding-top: var(--card-padding);
      }
    }

    // 卡片内容区域无底部时的样式调整
    &:not(:has(.c-card__footer)) {
      .c-card__body {
        padding-bottom: var(--card-padding);
      }
    }
  }

  // 加载动画
  @keyframes card-loading {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  // 卡片组合布局
  .c-card-grid {
    display: grid;
    gap: var(--card-margin);

    &--cols-1 {
      grid-template-columns: 1fr;
    }

    &--cols-2 {
      grid-template-columns: repeat(2, 1fr);
    }

    &--cols-3 {
      grid-template-columns: repeat(3, 1fr);
    }

    &--cols-4 {
      grid-template-columns: repeat(4, 1fr);
    }

    // 响应式
    @include media(md) {
      &--responsive-2 {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @include media(lg) {
      &--responsive-3 {
        grid-template-columns: repeat(3, 1fr);
      }

      &--responsive-4 {
        grid-template-columns: repeat(4, 1fr);
      }
    }
  }

  // 卡片特殊样式变体
  .c-card--minimal {
    background: transparent;
    border: none;
    box-shadow: none;

    .c-card__header {
      padding: var(--spacing-4) 0 var(--spacing-3) 0;
      border-bottom: 1px solid var(--color-border-light);
    }

    .c-card__body {
      padding: var(--spacing-4) 0;
    }

    .c-card__footer {
      padding: var(--spacing-3) 0 var(--spacing-4) 0;
      background: transparent;
      border-top: 1px solid var(--color-border-light);
    }
  }

  .c-card--compact {
    .c-card__header,
    .c-card__body,
    .c-card__footer {
      padding: var(--spacing-3);
    }
  }

  // IC封测专用卡片样式
  .c-card--wafer-status {
    border-left: 4px solid var(--color-wafer);

    &.c-card--pass {
      border-left-color: var(--color-die-pass);
    }

    &.c-card--fail {
      border-left-color: var(--color-die-fail);
    }
  }

  .c-card--process-step {
    position: relative;

    &::before {
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      content: '';
      background: var(--color-info);
    }

    &.c-card--cp {
      &::before {
        background: var(--color-cp-test);
      }
    }

    &.c-card--assembly {
      &::before {
        background: var(--color-assembly);
      }
    }

    &.c-card--ft {
      &::before {
        background: var(--color-ft-test);
      }
    }
  }
</style>
