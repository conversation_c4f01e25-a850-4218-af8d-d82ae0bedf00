// SPC统计过程控制计算工具库
import type {
  SPCData,
  SPCPoint,
  ControlLimits,
  SPCStatistics,
  QualitySpecification
} from '@/types/quality'

export class SPCCalculator {
  // A2, D3, D4 常数表（用于X̄-R控制图）
  private static readonly A2_CONSTANTS: Record<number, number> = {
    2: 1.88,
    3: 1.023,
    4: 0.729,
    5: 0.577,
    6: 0.483,
    7: 0.419,
    8: 0.373,
    9: 0.337,
    10: 0.308,
    15: 0.223,
    20: 0.18
  }

  private static readonly D3_CONSTANTS: Record<number, number> = {
    2: 0,
    3: 0,
    4: 0,
    5: 0,
    6: 0,
    7: 0.076,
    8: 0.136,
    9: 0.184,
    10: 0.223,
    15: 0.347,
    20: 0.415
  }

  private static readonly D4_CONSTANTS: Record<number, number> = {
    2: 3.268,
    3: 2.574,
    4: 2.282,
    5: 2.114,
    6: 2.004,
    7: 1.924,
    8: 1.864,
    9: 1.816,
    10: 1.777,
    15: 1.653,
    20: 1.586
  }

  // B3, B4 常数表（用于S控制图）
  private static readonly B3_CONSTANTS: Record<number, number> = {
    2: 0,
    3: 0,
    4: 0,
    5: 0,
    6: 0.03,
    7: 0.118,
    8: 0.185,
    9: 0.239,
    10: 0.284,
    15: 0.428,
    20: 0.51
  }

  private static readonly B4_CONSTANTS: Record<number, number> = {
    2: 3.267,
    3: 2.568,
    4: 2.266,
    5: 2.089,
    6: 1.97,
    7: 1.882,
    8: 1.815,
    9: 1.761,
    10: 1.716,
    15: 1.572,
    20: 1.49
  }

  // c4 常数（用于标准偏差估算）
  private static readonly C4_CONSTANTS: Record<number, number> = {
    2: 0.7979,
    3: 0.8862,
    4: 0.9213,
    5: 0.94,
    6: 0.9515,
    7: 0.9594,
    8: 0.965,
    9: 0.9693,
    10: 0.9727,
    15: 0.9823,
    20: 0.9869
  }

  /**
   * 计算X̄-R控制图的控制限
   */
  static calculateXbarRControlLimits(data: number[][], targetMean?: number): ControlLimits {
    const subgroupSize = data[0]?.length || 2
    const means = data.map(subgroup => this.calculateMean(subgroup))
    const ranges = data.map(subgroup => this.calculateRange(subgroup))

    const grandMean = targetMean || this.calculateMean(means)
    const meanRange = this.calculateMean(ranges)

    const A2 = this.A2_CONSTANTS[subgroupSize] || this.A2_CONSTANTS[10]
    const D3 = this.D3_CONSTANTS[subgroupSize] || this.D3_CONSTANTS[10]
    const D4 = this.D4_CONSTANTS[subgroupSize] || this.D4_CONSTANTS[10]

    return {
      xbar: {
        ucl: grandMean + A2 * meanRange,
        cl: grandMean,
        lcl: grandMean - A2 * meanRange
      },
      r: {
        ucl: D4 * meanRange,
        cl: meanRange,
        lcl: D3 * meanRange
      },
      sigma: {
        ucl: 0,
        cl: 0,
        lcl: 0
      }
    }
  }

  /**
   * 计算X̄-S控制图的控制限
   */
  static calculateXbarSControlLimits(data: number[][], targetMean?: number): ControlLimits {
    const subgroupSize = data[0]?.length || 2
    const means = data.map(subgroup => this.calculateMean(subgroup))
    const standardDeviations = data.map(subgroup => this.calculateStandardDeviation(subgroup))

    const grandMean = targetMean || this.calculateMean(means)
    const meanStd = this.calculateMean(standardDeviations)

    const A3 = this.A2_CONSTANTS[subgroupSize] || this.A2_CONSTANTS[10] // 简化处理，实际需要A3常数表
    const B3 = this.B3_CONSTANTS[subgroupSize] || this.B3_CONSTANTS[10]
    const B4 = this.B4_CONSTANTS[subgroupSize] || this.B4_CONSTANTS[10]

    return {
      xbar: {
        ucl: grandMean + A3 * meanStd,
        cl: grandMean,
        lcl: grandMean - A3 * meanStd
      },
      r: {
        ucl: 0,
        cl: 0,
        lcl: 0
      },
      sigma: {
        ucl: B4 * meanStd,
        cl: meanStd,
        lcl: B3 * meanStd
      }
    }
  }

  /**
   * 计算过程能力指数
   */
  static calculateProcessCapability(
    data: number[],
    specification: QualitySpecification
  ): SPCStatistics {
    const mean = this.calculateMean(data)
    const std = this.calculateStandardDeviation(data)
    const { usl, lsl, target } = specification

    // 计算基本统计量
    const cp = (usl - lsl) / (6 * std)
    const cpk = Math.min((usl - mean) / (3 * std), (mean - lsl) / (3 * std))

    // 计算长期能力（假设1.5σ漂移）
    const longTermStd = std * 1.5
    const pp = (usl - lsl) / (6 * longTermStd)
    const ppk = Math.min((usl - mean) / (3 * longTermStd), (mean - lsl) / (3 * longTermStd))

    // 计算准确度指数
    const ca = Math.abs(mean - target) / ((usl - lsl) / 2)

    // 计算良率
    const yieldValue = this.calculateYield(data, specification)

    return {
      cpk,
      ppk,
      cp,
      pp,
      ca,
      mean,
      standardDeviation: std,
      yield: yieldValue
    }
  }

  /**
   * 检查Nelson规则违反
   */
  static checkNelsonRules(points: SPCPoint[], controlLimits: ControlLimits): string[] {
    const violations: string[] = []
    const xbarPoints = points.map(p => p.mean)
    const { ucl, cl, lcl } = controlLimits.xbar
    const sigma = (ucl - lcl) / 6

    // 规则1: 单点超出3σ控制限
    for (let i = 0; i < xbarPoints.length; i++) {
      if (xbarPoints[i] > ucl || xbarPoints[i] < lcl) {
        violations.push(`规则1违反 - 点${i + 1}: 超出3σ控制限`)
      }
    }

    // 规则2: 连续9点在中心线同侧
    for (let i = 8; i < xbarPoints.length; i++) {
      const slice = xbarPoints.slice(i - 8, i + 1)
      if (slice.every(p => p > cl) || slice.every(p => p < cl)) {
        violations.push(`规则2违反 - 点${i - 7}-${i + 1}: 连续9点在中心线同侧`)
      }
    }

    // 规则3: 连续6点递增或递减
    for (let i = 5; i < xbarPoints.length; i++) {
      const slice = xbarPoints.slice(i - 5, i + 1)
      const increasing = slice.every((p, idx) => idx === 0 || p > slice[idx - 1])
      const decreasing = slice.every((p, idx) => idx === 0 || p < slice[idx - 1])
      if (increasing || decreasing) {
        violations.push(`规则3违反 - 点${i - 4}-${i + 1}: 连续6点${increasing ? '递增' : '递减'}`)
      }
    }

    // 规则4: 连续14点交替上下
    for (let i = 13; i < xbarPoints.length; i++) {
      const slice = xbarPoints.slice(i - 13, i + 1)
      let alternating = true
      for (let j = 1; j < slice.length - 1; j++) {
        const prev = slice[j - 1] - cl
        const curr = slice[j] - cl
        const next = slice[j + 1] - cl
        if (
          (prev > 0 && curr > 0) ||
          (prev < 0 && curr < 0) ||
          (curr > 0 && next > 0) ||
          (curr < 0 && next < 0)
        ) {
          alternating = false
          break
        }
      }
      if (alternating) {
        violations.push(`规则4违反 - 点${i - 12}-${i + 1}: 连续14点交替上下`)
      }
    }

    // 规则5: 3点中有2点在2σ范围外
    const twoSigmaUpper = cl + 2 * sigma
    const twoSigmaLower = cl - 2 * sigma
    for (let i = 2; i < xbarPoints.length; i++) {
      const slice = xbarPoints.slice(i - 2, i + 1)
      const outsideTwoSigma = slice.filter(p => p > twoSigmaUpper || p < twoSigmaLower)
      if (outsideTwoSigma.length >= 2) {
        violations.push(`规则5违反 - 点${i - 1}-${i + 1}: 3点中有2点在2σ范围外`)
      }
    }

    // 规则6: 5点中有4点在1σ范围外
    const oneSigmaUpper = cl + sigma
    const oneSigmaLower = cl - sigma
    for (let i = 4; i < xbarPoints.length; i++) {
      const slice = xbarPoints.slice(i - 4, i + 1)
      const outsideOneSigma = slice.filter(p => p > oneSigmaUpper || p < oneSigmaLower)
      if (outsideOneSigma.length >= 4) {
        violations.push(`规则6违反 - 点${i - 3}-${i + 1}: 5点中有4点在1σ范围外`)
      }
    }

    // 规则7: 连续15点在1σ范围内
    for (let i = 14; i < xbarPoints.length; i++) {
      const slice = xbarPoints.slice(i - 14, i + 1)
      if (slice.every(p => p >= oneSigmaLower && p <= oneSigmaUpper)) {
        violations.push(`规则7违反 - 点${i - 13}-${i + 1}: 连续15点在1σ范围内`)
      }
    }

    // 规则8: 连续8点在1σ范围外
    for (let i = 7; i < xbarPoints.length; i++) {
      const slice = xbarPoints.slice(i - 7, i + 1)
      if (slice.every(p => p > oneSigmaUpper || p < oneSigmaLower)) {
        violations.push(`规则8违反 - 点${i - 6}-${i + 1}: 连续8点在1σ范围外`)
      }
    }

    return violations
  }

  /**
   * 计算平均值
   */
  private static calculateMean(values: number[]): number {
    return values.reduce((sum, value) => sum + value, 0) / values.length
  }

  /**
   * 计算极差
   */
  private static calculateRange(values: number[]): number {
    return Math.max(...values) - Math.min(...values)
  }

  /**
   * 计算标准偏差
   */
  private static calculateStandardDeviation(values: number[]): number {
    const mean = this.calculateMean(values)
    const variance =
      values.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / (values.length - 1)
    return Math.sqrt(variance)
  }

  /**
   * 计算良率
   */
  private static calculateYield(data: number[], specification: QualitySpecification): number {
    const withinSpec = data.filter(
      value => value >= specification.lsl && value <= specification.usl
    )
    return (withinSpec.length / data.length) * 100
  }

  /**
   * 生成SPC点数据
   */
  static generateSPCPoint(
    id: string,
    timestamp: Date,
    sampleNumber: number,
    values: number[]
  ): SPCPoint {
    const mean = this.calculateMean(values)
    const range = this.calculateRange(values)
    const standardDeviation = this.calculateStandardDeviation(values)

    return {
      id,
      timestamp,
      sampleNumber,
      values,
      mean,
      range,
      standardDeviation,
      result: 'NORMAL',
      violatedRules: []
    }
  }

  /**
   * 更新SPC数据点的违规状态
   */
  static updateViolationStatus(points: SPCPoint[], controlLimits: ControlLimits): SPCPoint[] {
    const violations = this.checkNelsonRules(points, controlLimits)

    return points.map((point, index) => {
      const pointViolations = violations.filter(v => v.includes(`点${index + 1}`))
      const hasViolations = pointViolations.length > 0

      return {
        ...point,
        result: hasViolations
          ? 'OUT_OF_CONTROL'
          : point.mean > controlLimits.xbar.ucl || point.mean < controlLimits.xbar.lcl
            ? 'WARNING'
            : 'NORMAL',
        violatedRules: pointViolations
      }
    })
  }

  /**
   * 计算过程西格玛水平
   */
  static calculateSigmaLevel(dpmo: number): number {
    // DPMO to Sigma Level conversion (simplified)
    if (dpmo >= 690000) return 1.0
    if (dpmo >= 308000) return 2.0
    if (dpmo >= 66800) return 3.0
    if (dpmo >= 6210) return 4.0
    if (dpmo >= 233) return 5.0
    if (dpmo >= 3.4) return 6.0
    return 6.0
  }

  /**
   * 计算DPMO（百万机会缺陷数）
   */
  static calculateDPMO(defects: number, opportunities: number, units: number): number {
    return (defects / (opportunities * units)) * 1000000
  }
}
