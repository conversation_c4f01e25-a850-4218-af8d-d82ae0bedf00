<template>
  <div class="equipment-config">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">设备配置管理</h1>
        <p class="page-description">设备参数配置、Recipe版本管理、设备校准记录和性能基准设置</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateConfigDialog = true">
          <el-icon><Plus /></el-icon>
          新建配置
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 设备选择和配置概览 -->
    <div class="config-overview">
      <el-row :gutter="24">
        <!-- 设备选择 -->
        <el-col :span="8">
          <el-card>
            <template #header>
              <span class="card-title">设备选择</span>
            </template>
            <el-select
              v-model="selectedEquipmentId"
              placeholder="选择设备"
              style="width: 100%"
              @change="handleEquipmentChange"
            >
              <el-option
v-for="eq in equipmentList" :key="eq.id"
:label="eq.name" :value="eq.id"
>
                <div class="equipment-option">
                  <div>
                    <div class="equipment-name">
                      {{ eq.name }}
                    </div>
                    <div class="equipment-code">
                      {{ eq.code }} - {{ getEquipmentTypeText(eq.type) }}
                    </div>
                  </div>
                  <el-tag
:type="eq.isConnected ? 'success' : 'danger'" size="small"
>
                    {{ eq.isConnected ? '在线' : '离线' }}
                  </el-tag>
                </div>
              </el-option>
            </el-select>
          </el-card>
        </el-col>

        <!-- 配置状态 -->
        <el-col :span="8">
          <el-card>
            <template #header>
              <span class="card-title">配置状态</span>
            </template>
            <div v-if="selectedEquipment" class="config-status">
              <div class="status-item">
                <span class="label">当前Recipe:</span>
                <el-tag type="success">
                  {{ currentRecipe?.name || '无' }}
                </el-tag>
              </div>
              <div class="status-item">
                <span class="label">Recipe版本:</span>
                <span class="value">{{ currentRecipe?.version || '-' }}</span>
              </div>
              <div class="status-item">
                <span class="label">最后校准:</span>
                <span class="value">{{ lastCalibrationDate || '未校准' }}</span>
              </div>
            </div>
            <div v-else class="no-equipment">
              <el-empty description="请先选择设备" />
            </div>
          </el-card>
        </el-col>

        <!-- 配置统计 -->
        <el-col :span="8">
          <el-card>
            <template #header>
              <span class="card-title">配置统计</span>
            </template>
            <div class="config-stats">
              <div class="stat-item">
                <el-statistic title="Recipe数量" :value="recipeCount">
                  <template #suffix>
                    <el-icon><Document /></el-icon>
                  </template>
                </el-statistic>
              </div>
              <div class="stat-item">
                <el-statistic title="校准记录" :value="calibrationCount">
                  <template #suffix>
                    <el-icon><Setting /></el-icon>
                  </template>
                </el-statistic>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- Tab页签 -->
    <div class="main-content">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 设备参数配置 -->
        <el-tab-pane label="参数配置" name="parameters">
          <div class="parameters-config">
            <div class="config-toolbar">
              <el-alert
                v-if="!selectedEquipment"
                title="请先选择设备"
                type="warning"
                show-icon
                :closable="false"
              />
              <div v-else class="toolbar-actions">
                <el-button type="primary" @click="saveParameters">
                  <el-icon><Check /></el-icon>
                  保存参数
                </el-button>
                <el-button @click="resetParameters">
                  <el-icon><RefreshLeft /></el-icon>
                  重置
                </el-button>
                <el-button @click="loadDefaultParameters">
                  <el-icon><Download /></el-icon>
                  加载默认值
                </el-button>
              </div>
            </div>

            <div v-if="selectedEquipment" class="parameters-form">
              <el-form :model="parameterForm" label-width="150px">
                <div
                  v-for="category in parameterCategories"
                  :key="category"
                  class="parameter-category"
                >
                  <h3 class="category-title">
                    {{ getCategoryTitle(category) }}
                  </h3>
                  <div class="category-parameters">
                    <div
                      v-for="param in getCategoryParameters(category)"
                      :key="param.name"
                      class="parameter-item"
                    >
                      <el-form-item :label="param.label">
                        <!-- 数值类型参数 -->
                        <el-input-number
                          v-if="param.type === 'number'"
                          v-model="parameterForm[param.name]"
                          :min="param.min"
                          :max="param.max"
                          :precision="param.precision"
                          :step="param.step"
                          :controls-position="'right'"
                          style="width: 200px"
                        />

                        <!-- 文本类型参数 -->
                        <el-input
                          v-else-if="param.type === 'string'"
                          v-model="parameterForm[param.name]"
                          :placeholder="param.placeholder"
                          style="width: 200px"
                        />

                        <!-- 布尔类型参数 -->
                        <el-switch
                          v-else-if="param.type === 'boolean'"
                          v-model="parameterForm[param.name]"
                          :active-text="param.activeText"
                          :inactive-text="param.inactiveText"
                        />

                        <!-- 选择类型参数 -->
                        <el-select
                          v-else-if="param.type === 'select'"
                          v-model="parameterForm[param.name]"
                          style="width: 200px"
                        >
                          <el-option
                            v-for="option in param.options"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                          />
                        </el-select>

                        <div class="parameter-info">
                          <span
v-if="param.unit" class="unit"
>{{ param.unit }}</span>
                          <span
                            v-if="param.min !== undefined && param.max !== undefined"
                            class="range"
                          >
                            ({{ param.min }} ~ {{ param.max }})
                          </span>
                          <el-tooltip :content="param.description" placement="top">
                            <el-icon class="info-icon">
                              <InfoFilled />
                            </el-icon>
                          </el-tooltip>
                        </div>
                      </el-form-item>
                    </div>
                  </div>
                </div>
              </el-form>
            </div>
          </div>
        </el-tab-pane>

        <!-- Recipe版本管理 -->
        <el-tab-pane label="Recipe管理" name="recipes">
          <div class="recipe-version-management">
            <div class="recipe-toolbar">
              <el-row :gutter="16" align="middle">
                <el-col :span="8">
                  <el-select
                    v-model="recipeEquipmentFilter"
                    placeholder="设备筛选"
                    clearable
                    @change="refreshRecipes"
                  >
                    <el-option
                      v-for="eq in equipmentList"
                      :key="eq.id"
                      :label="eq.name"
                      :value="eq.id"
                    />
                  </el-select>
                </el-col>
                <el-col :span="16">
                  <div class="toolbar-actions">
                    <el-button type="primary" @click="showCreateRecipeDialog = true">
                      <el-icon><Plus /></el-icon>
                      新建Recipe
                    </el-button>
                    <el-button @click="importRecipe">
                      <el-icon><Upload /></el-icon>
                      导入Recipe
                    </el-button>
                    <el-button @click="exportRecipe">
                      <el-icon><Download /></el-icon>
                      导出Recipe
                    </el-button>
                  </div>
                </el-col>
              </el-row>
            </div>

            <div class="recipe-list">
              <el-table :data="recipes" stripe @row-click="handleViewRecipe">
                <el-table-column prop="name" label="Recipe名称" width="200" />
                <el-table-column prop="version" label="版本" width="120" />
                <el-table-column label="设备" width="150">
                  <template #default="{ row }">
                    {{ getEquipmentName(row.equipmentId) }}
                  </template>
                </el-table-column>
                <el-table-column label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.isActive ? 'success' : 'info'">
                      {{ row.isActive ? '激活' : '未激活' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="createdBy" label="创建者" width="120" />
                <el-table-column prop="createdAt" label="创建时间" width="160">
                  <template #default="{ row }">
                    {{ formatDateTime(row.createdAt) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="250">
                  <template #default="{ row }">
                    <el-button
                      type="primary"
                      size="small"
                      :disabled="row.isActive"
                      @click="handleActivateRecipe(row)"
                    >
                      激活
                    </el-button>
                    <el-button size="small"
@click="handleEditRecipe(row)"
>
编辑
</el-button>
                    <el-button size="small"
@click="handleCloneRecipe(row)"
>
克隆
</el-button>
                    <el-button
type="danger" size="small"
@click="handleDeleteRecipe(row)"
>
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <!-- 校准记录 -->
        <el-tab-pane label="校准记录" name="calibration">
          <div class="calibration-management">
            <div class="calibration-toolbar">
              <el-button type="primary" @click="showCalibrationDialog = true">
                <el-icon><Plus /></el-icon>
                新建校准
              </el-button>
              <el-button @click="scheduleCalibration">
                <el-icon><Calendar /></el-icon>
                计划校准
              </el-button>
            </div>

            <div class="calibration-list">
              <el-table :data="calibrationRecords" stripe>
                <el-table-column prop="equipmentName" label="设备" width="150" />
                <el-table-column prop="calibrationType" label="校准类型" width="120" />
                <el-table-column prop="calibrationDate" label="校准日期" width="160">
                  <template #default="{ row }">
                    {{ formatDateTime(row.calibrationDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="calibratedBy" label="校准人员" width="120" />
                <el-table-column label="校准结果" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.result === 'PASS' ? 'success' : 'danger'">
                      {{ row.result === 'PASS' ? '通过' : '失败' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="nextDueDate" label="下次校准" width="160">
                  <template #default="{ row }">
                    {{ formatDateTime(row.nextDueDate) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="{ row }">
                    <el-button
type="text" size="small"
@click="handleViewCalibration(row)"
>
                      查看详情
                    </el-button>
                    <el-button
type="text" size="small"
@click="handleDownloadCertificate(row)"
>
                      下载证书
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <!-- 性能基准 -->
        <el-tab-pane label="性能基准" name="benchmarks">
          <div class="benchmark-management">
            <div class="benchmark-toolbar">
              <el-alert
                v-if="!selectedEquipment"
                title="请先选择设备"
                type="warning"
                show-icon
                :closable="false"
              />
              <div v-else class="toolbar-actions">
                <el-button type="primary" @click="runBenchmark">
                  <el-icon><VideoPlay /></el-icon>
                  运行基准测试
                </el-button>
                <el-button @click="saveBenchmarks">
                  <el-icon><Check /></el-icon>
                  保存基准
                </el-button>
              </div>
            </div>

            <div v-if="selectedEquipment" class="benchmark-settings">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-card>
                    <template #header>
                      <span class="card-title">性能基准设置</span>
                    </template>
                    <el-form :model="benchmarkForm" label-width="120px">
                      <el-form-item label="吞吐率目标">
                        <el-input-number
                          v-model="benchmarkForm.targetThroughput"
                          :min="0"
                          :step="100"
                          controls-position="right"
                        />
                        <span class="unit">UPH</span>
                      </el-form-item>

                      <el-form-item label="良率目标">
                        <el-input-number
                          v-model="benchmarkForm.targetYield"
                          :min="0"
                          :max="100"
                          :precision="2"
                          controls-position="right"
                        />
                        <span class="unit">%</span>
                      </el-form-item>

                      <el-form-item label="平均周期时间">
                        <el-input-number
                          v-model="benchmarkForm.avgCycleTime"
                          :min="0"
                          :precision="2"
                          controls-position="right"
                        />
                        <span class="unit">秒</span>
                      </el-form-item>

                      <el-form-item label="OEE目标">
                        <el-input-number
                          v-model="benchmarkForm.targetOEE"
                          :min="0"
                          :max="100"
                          :precision="1"
                          controls-position="right"
                        />
                        <span class="unit">%</span>
                      </el-form-item>
                    </el-form>
                  </el-card>
                </el-col>

                <el-col :span="12">
                  <el-card>
                    <template #header>
                      <span class="card-title">当前性能指标</span>
                    </template>
                    <div class="current-performance">
                      <div class="performance-item">
                        <span class="label">当前吞吐率:</span>
                        <span class="value">{{ currentPerformance.throughput }} UPH</span>
                        <el-tag
                          :type="
                            getPerformanceStatus(
                              currentPerformance.throughput,
                              benchmarkForm.targetThroughput
                            )
                          "
                          size="small"
                        >
                          {{
                            getPerformanceText(
                              currentPerformance.throughput,
                              benchmarkForm.targetThroughput
                            )
                          }}
                        </el-tag>
                      </div>

                      <div class="performance-item">
                        <span class="label">当前良率:</span>
                        <span class="value">{{ currentPerformance.yield }}%</span>
                        <el-tag
                          :type="
                            getPerformanceStatus(
                              currentPerformance.yield,
                              benchmarkForm.targetYield
                            )
                          "
                          size="small"
                        >
                          {{
                            getPerformanceText(currentPerformance.yield, benchmarkForm.targetYield)
                          }}
                        </el-tag>
                      </div>

                      <div class="performance-item">
                        <span class="label">当前周期时间:</span>
                        <span class="value">{{ currentPerformance.cycleTime }}秒</span>
                        <el-tag
                          :type="
                            currentPerformance.cycleTime <= benchmarkForm.avgCycleTime
                              ? 'success'
                              : 'warning'
                          "
                          size="small"
                        >
                          {{
                            currentPerformance.cycleTime <= benchmarkForm.avgCycleTime
                              ? '正常'
                              : '偏高'
                          }}
                        </el-tag>
                      </div>

                      <div class="performance-item">
                        <span class="label">当前OEE:</span>
                        <span class="value">{{ currentPerformance.oee }}%</span>
                        <el-tag
                          :type="
                            getPerformanceStatus(currentPerformance.oee, benchmarkForm.targetOEE)
                          "
                          size="small"
                        >
                          {{ getPerformanceText(currentPerformance.oee, benchmarkForm.targetOEE) }}
                        </el-tag>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 校准详情弹窗 -->
    <el-dialog
v-model="showCalibrationDialog" title="设备校准"
width="60%"
>
      <div class="calibration-form">
        <el-form :model="calibrationForm" label-width="120px">
          <el-form-item label="校准设备">
            <el-select v-model="calibrationForm.equipmentId" placeholder="选择设备">
              <el-option
v-for="eq in equipmentList" :key="eq.id"
:label="eq.name" :value="eq.id"
/>
            </el-select>
          </el-form-item>

          <el-form-item label="校准类型">
            <el-select v-model="calibrationForm.type" placeholder="选择校准类型">
              <el-option label="电气校准" value="ELECTRICAL" />
              <el-option label="机械校准" value="MECHANICAL" />
              <el-option label="光学校准" value="OPTICAL" />
              <el-option label="温度校准" value="TEMPERATURE" />
              <el-option label="压力校准" value="PRESSURE" />
            </el-select>
          </el-form-item>

          <el-form-item label="校准标准">
            <el-input
v-model="calibrationForm.standard" placeholder="如: NIST, ISO, 企业标准"
/>
          </el-form-item>

          <el-form-item label="校准周期">
            <el-select v-model="calibrationForm.interval" placeholder="选择校准周期">
              <el-option label="1个月" value="1M" />
              <el-option label="3个月" value="3M" />
              <el-option label="6个月" value="6M" />
              <el-option label="12个月" value="12M" />
            </el-select>
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              v-model="calibrationForm.notes"
              type="textarea"
              :rows="3"
              placeholder="校准说明和注意事项"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showCalibrationDialog = false">取消</el-button>
        <el-button type="primary"
@click="saveCalibration"
>
开始校准
</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import {
    Plus,
    Refresh,
    Document,
    Setting,
    Check,
    RefreshLeft,
    Download,
    Upload,
    Calendar,
    VideoPlay,
    InfoFilled
  } from '@element-plus/icons-vue'
  import { useEquipmentStore } from '@/stores/equipment'
  import type { Equipment, Recipe } from '@/types/equipment'
  import { EquipmentType } from '@/types/equipment'

  // 状态管理
  const equipmentStore = useEquipmentStore()

  // 页面状态
  const selectedEquipmentId = ref('')
  const activeTab = ref('parameters')
  const recipeEquipmentFilter = ref('')

  // 弹窗状态
  const showCreateConfigDialog = ref(false)
  const showCreateRecipeDialog = ref(false)
  const showCalibrationDialog = ref(false)

  // 表单数据
  const parameterForm = ref<Record<string, any>>({
    // 电源参数
    voltage: 3.3,
    current: 100,
    powerLimit: 500,
    // 时序参数
    clockFrequency: 100,
    setupTime: 2.5,
    holdTime: 1.5,
    // 温度参数
    operatingTemp: 25,
    maxTemp: 85,
    // 压力参数
    testPressure: 1.2,
    // 其他参数
    testTimeout: 30,
    retryCount: 3,
    enableLogging: true
  })

  const benchmarkForm = ref({
    targetThroughput: 5000,
    targetYield: 95.0,
    avgCycleTime: 12.5,
    targetOEE: 85.0
  })

  const calibrationForm = ref({
    equipmentId: '',
    type: '',
    standard: '',
    interval: '6M',
    notes: ''
  })

  // 参数配置定义
  const parameterCategories = ['POWER', 'TIMING', 'THERMAL', 'PRESSURE', 'PROCESS', 'SYSTEM']

  const parameterDefinitions = {
    POWER: [
      {
        name: 'voltage',
        label: '测试电压',
        type: 'number',
        min: 0,
        max: 5,
        precision: 2,
        step: 0.1,
        unit: 'V',
        description: '设备测试时的供电电压'
      },
      {
        name: 'current',
        label: '电流限制',
        type: 'number',
        min: 0,
        max: 1000,
        step: 10,
        unit: 'mA',
        description: '最大允许电流'
      },
      {
        name: 'powerLimit',
        label: '功率限制',
        type: 'number',
        min: 0,
        max: 2000,
        step: 50,
        unit: 'mW',
        description: '最大允许功率'
      }
    ],
    TIMING: [
      {
        name: 'clockFrequency',
        label: '时钟频率',
        type: 'number',
        min: 1,
        max: 1000,
        step: 10,
        unit: 'MHz',
        description: '测试时钟频率'
      },
      {
        name: 'setupTime',
        label: '建立时间',
        type: 'number',
        min: 0,
        max: 10,
        precision: 1,
        step: 0.5,
        unit: 'ns',
        description: '信号建立时间'
      },
      {
        name: 'holdTime',
        label: '保持时间',
        type: 'number',
        min: 0,
        max: 10,
        precision: 1,
        step: 0.5,
        unit: 'ns',
        description: '信号保持时间'
      }
    ],
    THERMAL: [
      {
        name: 'operatingTemp',
        label: '工作温度',
        type: 'number',
        min: -40,
        max: 150,
        step: 5,
        unit: '°C',
        description: '设备工作温度'
      },
      {
        name: 'maxTemp',
        label: '最高温度',
        type: 'number',
        min: 0,
        max: 200,
        step: 5,
        unit: '°C',
        description: '允许的最高温度'
      }
    ],
    PRESSURE: [
      {
        name: 'testPressure',
        label: '测试压力',
        type: 'number',
        min: 0,
        max: 5,
        precision: 1,
        step: 0.1,
        unit: 'bar',
        description: '测试时的压力设定'
      }
    ],
    PROCESS: [
      {
        name: 'testTimeout',
        label: '测试超时',
        type: 'number',
        min: 1,
        max: 300,
        step: 5,
        unit: '秒',
        description: '单次测试的超时时间'
      },
      {
        name: 'retryCount',
        label: '重试次数',
        type: 'number',
        min: 0,
        max: 10,
        step: 1,
        description: '测试失败时的重试次数'
      }
    ],
    SYSTEM: [
      {
        name: 'enableLogging',
        label: '启用日志',
        type: 'boolean',
        activeText: '启用',
        inactiveText: '禁用',
        description: '是否启用详细日志记录'
      }
    ]
  }

  // 模拟数据
  const calibrationRecords = ref([
    {
      id: 'CAL001',
      equipmentName: '测试机台-ATE01',
      calibrationType: 'ELECTRICAL',
      calibrationDate: '2024-01-15T10:00:00Z',
      calibratedBy: 'engineer01',
      result: 'PASS',
      nextDueDate: '2024-07-15T10:00:00Z',
      certificate: 'cert_001.pdf'
    }
  ])

  const currentPerformance = ref({
    throughput: 4800,
    yield: 96.5,
    cycleTime: 11.8,
    oee: 87.2
  })

  // 计算属性
  const equipmentList = computed(() => equipmentStore.equipment)
  const recipes = computed(() => equipmentStore.recipes)
  const selectedEquipment = computed(() =>
    equipmentList.value.find(eq => eq.id === selectedEquipmentId.value)
  )

  const currentRecipe = computed(() =>
    recipes.value.find(
      recipe => recipe.equipmentId === selectedEquipmentId.value && recipe.isActive
    )
  )

  const recipeCount = computed(
    () => recipes.value.filter(recipe => recipe.equipmentId === selectedEquipmentId.value).length
  )

  const calibrationCount = computed(
    () =>
      calibrationRecords.value.filter(
        record => record.equipmentName === selectedEquipment.value?.name
      ).length
  )

  const lastCalibrationDate = computed(() => {
    const lastRecord = calibrationRecords.value
      .filter(record => record.equipmentName === selectedEquipment.value?.name)
      .sort(
        (a, b) => new Date(b.calibrationDate).getTime() - new Date(a.calibrationDate).getTime()
      )[0]

    return lastRecord ? formatDateTime(lastRecord.calibrationDate) : null
  })

  // 工具方法
  const getEquipmentTypeText = (type: EquipmentType): string => {
    const textMap: Record<EquipmentType, string> = {
      [EquipmentType.ATE]: '测试设备',
      [EquipmentType.BONDER]: '键合机',
      [EquipmentType.DIE_ATTACH]: '贴片机',
      [EquipmentType.MOLDING]: '塑封机',
      [EquipmentType.HANDLER]: '分选机',
      [EquipmentType.TRIM_FORM]: '切筋成形机',
      [EquipmentType.PROBER]: '探针台',
      [EquipmentType.DICING_SAW]: '切割机',
      [EquipmentType.PICK_PLACE]: '取放机',
      [EquipmentType.BURN_IN]: '老化炉'
    }
    return textMap[type] || type
  }

  const getEquipmentName = (equipmentId: string): string => {
    const equipment = equipmentList.value.find(eq => eq.id === equipmentId)
    return equipment?.name || equipmentId
  }

  const getCategoryTitle = (category: string): string => {
    const titleMap: Record<string, string> = {
      POWER: '电源参数',
      TIMING: '时序参数',
      THERMAL: '热控参数',
      PRESSURE: '压力参数',
      PROCESS: '工艺参数',
      SYSTEM: '系统参数'
    }
    return titleMap[category] || category
  }

  const getCategoryParameters = (category: string) => {
    return parameterDefinitions[category as keyof typeof parameterDefinitions] || []
  }

  const getPerformanceStatus = (current: number, target: number): string => {
    const ratio = current / target
    if (ratio >= 0.95) return 'success'
    if (ratio >= 0.85) return 'warning'
    return 'danger'
  }

  const getPerformanceText = (current: number, target: number): string => {
    const ratio = current / target
    if (ratio >= 0.95) return '达标'
    if (ratio >= 0.85) return '接近'
    return '偏低'
  }

  const formatDateTime = (dateTime: string): string => {
    return new Date(dateTime).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 事件处理
  const refreshData = () => {
    equipmentStore.fetchEquipmentList()
    equipmentStore.fetchRecipes(recipeEquipmentFilter.value)
  }

  const handleEquipmentChange = (equipmentId: string) => {
    selectedEquipmentId.value = equipmentId
    refreshRecipes()
  }

  const handleTabChange = (tabName: string) => {
    switch (tabName) {
      case 'recipes':
        refreshRecipes()
        break
      case 'calibration':
        // 刷新校准记录
        break
    }
  }

  const refreshRecipes = () => {
    equipmentStore.fetchRecipes(recipeEquipmentFilter.value)
  }

  const saveParameters = () => {
    ElMessage.success('参数配置已保存')
  }

  const resetParameters = () => {
    // 重置参数到默认值
    ElMessage.info('参数已重置')
  }

  const loadDefaultParameters = () => {
    // 加载默认参数
    ElMessage.info('默认参数已加载')
  }

  const handleViewRecipe = (recipe: Recipe) => {
    ElMessage.info('查看Recipe详情功能待实现')
  }

  const handleActivateRecipe = (recipe: Recipe) => {
    equipmentStore.activateRecipe(recipe.id, recipe.equipmentId)
  }

  const handleEditRecipe = (recipe: Recipe) => {
    ElMessage.info('编辑Recipe功能待实现')
  }

  const handleCloneRecipe = (recipe: Recipe) => {
    ElMessage.info('克隆Recipe功能待实现')
  }

  const handleDeleteRecipe = async (recipe: Recipe) => {
    try {
      await ElMessageBox.confirm('确定要删除此Recipe吗？', '确认删除', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      })
      ElMessage.success('Recipe删除成功')
      refreshRecipes()
    } catch (error) {
      // 用户取消
    }
  }

  const importRecipe = () => {
    ElMessage.info('导入Recipe功能待实现')
  }

  const exportRecipe = () => {
    ElMessage.info('导出Recipe功能待实现')
  }

  const handleViewCalibration = (record: any) => {
    ElMessage.info('查看校准详情功能待实现')
  }

  const handleDownloadCertificate = (record: any) => {
    ElMessage.info('下载校准证书功能待实现')
  }

  const scheduleCalibration = () => {
    ElMessage.info('计划校准功能待实现')
  }

  const saveCalibration = () => {
    ElMessage.success('校准任务已创建')
    showCalibrationDialog.value = false
  }

  const runBenchmark = () => {
    ElMessage.info('基准测试正在运行...')
  }

  const saveBenchmarks = () => {
    ElMessage.success('性能基准已保存')
  }

  // 生命周期
  onMounted(() => {
    refreshData()

    // 选择第一个设备
    if (equipmentList.value.length > 0) {
      selectedEquipmentId.value = equipmentList.value[0].id
      handleEquipmentChange(selectedEquipmentId.value)
    }
  })
</script>

<style lang="scss" scoped>
  .equipment-config {
    padding: var(--spacing-4);

    .page-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: var(--spacing-6);

      .header-left {
        .page-title {
          margin: 0 0 var(--spacing-2);
          font-size: 1.75rem;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        .page-description {
          margin: 0;
          color: var(--color-text-secondary);
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-2);
      }
    }

    .config-overview {
      margin-bottom: var(--spacing-6);

      .card-title {
        font-weight: 600;
        color: var(--color-text-primary);
      }

      .equipment-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .equipment-name {
          font-weight: 500;
          color: var(--color-text-primary);
        }

        .equipment-code {
          font-size: 0.875rem;
          color: var(--color-text-secondary);
        }
      }

      .config-status {
        .status-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: var(--spacing-3);

          .label {
            font-weight: 500;
            color: var(--color-text-secondary);
          }

          .value {
            color: var(--color-text-primary);
          }
        }
      }

      .no-equipment {
        padding: var(--spacing-4) 0;
        text-align: center;
      }

      .config-stats {
        .stat-item {
          margin-bottom: var(--spacing-3);
        }
      }
    }

    .main-content {
      .parameters-config {
        .config-toolbar {
          padding: var(--spacing-4);
          margin-bottom: var(--spacing-4);
          background-color: var(--color-bg-soft);
          border-radius: var(--radius-base);

          .toolbar-actions {
            display: flex;
            gap: var(--spacing-2);
            justify-content: flex-end;
          }
        }

        .parameters-form {
          .parameter-category {
            margin-bottom: var(--spacing-6);

            .category-title {
              padding-bottom: var(--spacing-2);
              margin-bottom: var(--spacing-4);
              font-size: 1.1rem;
              font-weight: 600;
              color: var(--color-text-primary);
              border-bottom: 2px solid var(--color-border-light);
            }

            .category-parameters {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
              gap: var(--spacing-4);

              .parameter-item {
                .parameter-info {
                  display: flex;
                  gap: var(--spacing-2);
                  align-items: center;
                  margin-top: var(--spacing-1);
                  margin-left: 150px;

                  .unit {
                    font-size: 0.875rem;
                    color: var(--color-text-secondary);
                  }

                  .range {
                    font-size: 0.75rem;
                    color: var(--color-text-tertiary);
                  }

                  .info-icon {
                    color: var(--color-text-tertiary);
                    cursor: help;
                  }
                }
              }
            }
          }
        }
      }

      .recipe-version-management {
        .recipe-toolbar {
          padding: var(--spacing-4);
          margin-bottom: var(--spacing-4);
          background-color: var(--color-bg-soft);
          border-radius: var(--radius-base);

          .toolbar-actions {
            display: flex;
            gap: var(--spacing-2);
            justify-content: flex-end;
          }
        }

        .recipe-list {
          padding: var(--spacing-2);
          background-color: var(--color-bg-primary);
          border-radius: var(--radius-base);
        }
      }

      .calibration-management {
        .calibration-toolbar {
          display: flex;
          gap: var(--spacing-2);
          margin-bottom: var(--spacing-4);
        }

        .calibration-list {
          padding: var(--spacing-2);
          background-color: var(--color-bg-primary);
          border-radius: var(--radius-base);
        }
      }

      .benchmark-management {
        .benchmark-toolbar {
          padding: var(--spacing-4);
          margin-bottom: var(--spacing-4);
          background-color: var(--color-bg-soft);
          border-radius: var(--radius-base);

          .toolbar-actions {
            display: flex;
            gap: var(--spacing-2);
            justify-content: flex-end;
          }
        }

        .benchmark-settings {
          .card-title {
            font-weight: 600;
            color: var(--color-text-primary);
          }

          .unit {
            margin-left: var(--spacing-2);
            font-size: 0.875rem;
            color: var(--color-text-secondary);
          }

          .current-performance {
            .performance-item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: var(--spacing-3);

              .label {
                font-weight: 500;
                color: var(--color-text-secondary);
              }

              .value {
                font-weight: 600;
                color: var(--color-text-primary);
              }
            }
          }
        }
      }
    }

    .calibration-form {
      margin: var(--spacing-4) 0;
    }
  }
</style>
