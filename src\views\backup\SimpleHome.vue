<template>
  <div class="simple-home">
    <header class="header">
      <h1>IC封测CIM系统</h1>
      <p>简化版本测试页面</p>
    </header>
    
    <main class="main-content">
      <div class="hero-section">
        <h2>欢迎使用IC封测CIM系统</h2>
        <p>专业的半导体封装测试制造执行系统</p>
        
        <div class="action-buttons">
          <button @click="$router.push('/orders')" class="btn btn-primary">
            订单管理
          </button>
          <button @click="$router.push('/production')" class="btn btn-secondary">
            生产计划
          </button>
          <button @click="$router.push('/demo')" class="btn btn-secondary">
            组件演示
          </button>
        </div>
      </div>
      
      <div class="features">
        <div class="feature-card">
          <h3>📋 订单管理</h3>
          <p>完整的订单生命周期管理，从接单到交付</p>
        </div>
        <div class="feature-card">
          <h3>📊 生产计划</h3>
          <p>智能化生产计划管理和甘特图展示</p>
        </div>
        <div class="feature-card">
          <h3>⚡ 实时监控</h3>
          <p>设备状态实时监控和数据分析</p>
        </div>
      </div>
    </main>
    
    <footer class="footer">
      <p>&copy; 2024 IC封测CIM系统</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
// 简化版本，不使用复杂的composables
console.log('SimpleHome component loaded')
</script>

<style scoped>
.simple-home {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.header {
  background: white;
  padding: 2rem 1rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
  color: #2563eb;
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
}

.header p {
  color: #6b7280;
  margin: 0;
}

.main-content {
  flex: 1;
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.hero-section {
  text-align: center;
  margin-bottom: 4rem;
}

.hero-section h2 {
  font-size: 2.5rem;
  color: #111827;
  margin-bottom: 1rem;
}

.hero-section p {
  font-size: 1.2rem;
  color: #6b7280;
  margin-bottom: 2rem;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: #2563eb;
  color: white;
}

.btn-primary:hover {
  background: #1d4ed8;
  transform: translateY(-1px);
}

.btn-secondary {
  background: white;
  color: #2563eb;
  border: 1px solid #e5e7eb;
}

.btn-secondary:hover {
  border-color: #2563eb;
  transform: translateY(-1px);
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: left;
}

.feature-card h3 {
  color: #111827;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.feature-card p {
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

.footer {
  background: white;
  text-align: center;
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
  color: #6b7280;
}

.footer p {
  margin: 0;
}

@media (max-width: 768px) {
  .hero-section h2 {
    font-size: 2rem;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 200px;
  }
  
  .features {
    grid-template-columns: 1fr;
  }
}
</style>