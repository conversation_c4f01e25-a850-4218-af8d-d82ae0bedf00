<template>
  <div class="simple-home">
    <header class="header">
      <h1>IC封测CIM系统</h1>
      <p>简化版本测试页面</p>
    </header>

    <main class="main-content">
      <div class="hero-section">
        <h2>欢迎使用IC封测CIM系统</h2>
        <p>专业的半导体封装测试制造执行系统</p>

        <div class="action-buttons">
          <button class="btn btn-primary"
@click="$router.push('/orders')"
>
订单管理
</button>
          <button class="btn btn-secondary"
@click="$router.push('/production')"
>
生产计划
</button>
          <button class="btn btn-secondary"
@click="$router.push('/demo')"
>
组件演示
</button>
        </div>
      </div>

      <div class="features">
        <div class="feature-card">
          <h3>📋 订单管理</h3>
          <p>完整的订单生命周期管理，从接单到交付</p>
        </div>
        <div class="feature-card">
          <h3>📊 生产计划</h3>
          <p>智能化生产计划管理和甘特图展示</p>
        </div>
        <div class="feature-card">
          <h3>⚡ 实时监控</h3>
          <p>设备状态实时监控和数据分析</p>
        </div>
      </div>
    </main>

    <footer class="footer">
      <p>&copy; 2024 IC封测CIM系统</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
  // 简化版本，不使用复杂的composables
  console.log('SimpleHome component loaded')
</script>

<style scoped>
  .simple-home {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: #f8fafc;
  }

  .header {
    padding: 2rem 1rem;
    text-align: center;
    background: white;
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
  }

  .header h1 {
    margin: 0 0 0.5rem;
    font-size: 2rem;
    color: #2563eb;
  }

  .header p {
    margin: 0;
    color: #6b7280;
  }

  .main-content {
    flex: 1;
    width: 100%;
    max-width: 1200px;
    padding: 2rem 1rem;
    margin: 0 auto;
  }

  .hero-section {
    margin-bottom: 4rem;
    text-align: center;
  }

  .hero-section h2 {
    margin-bottom: 1rem;
    font-size: 2.5rem;
    color: #111827;
  }

  .hero-section p {
    margin-bottom: 2rem;
    font-size: 1.2rem;
    color: #6b7280;
  }

  .action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
  }

  .btn {
    display: inline-block;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    border: none;
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .btn-primary {
    color: white;
    background: #2563eb;
  }

  .btn-primary:hover {
    background: #1d4ed8;
    transform: translateY(-1px);
  }

  .btn-secondary {
    color: #2563eb;
    background: white;
    border: 1px solid #e5e7eb;
  }

  .btn-secondary:hover {
    border-color: #2563eb;
    transform: translateY(-1px);
  }

  .features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
  }

  .feature-card {
    padding: 2rem;
    text-align: left;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
  }

  .feature-card h3 {
    margin: 0 0 1rem;
    font-size: 1.2rem;
    color: #111827;
  }

  .feature-card p {
    margin: 0;
    line-height: 1.6;
    color: #6b7280;
  }

  .footer {
    padding: 1rem;
    color: #6b7280;
    text-align: center;
    background: white;
    border-top: 1px solid #e5e7eb;
  }

  .footer p {
    margin: 0;
  }

  @media (width <= 768px) {
    .hero-section h2 {
      font-size: 2rem;
    }

    .action-buttons {
      flex-direction: column;
      align-items: center;
    }

    .btn {
      width: 200px;
    }

    .features {
      grid-template-columns: 1fr;
    }
  }
</style>
