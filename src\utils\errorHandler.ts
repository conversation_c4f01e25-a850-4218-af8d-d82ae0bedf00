/**
 * IC封测CIM系统 - 全局错误处理系统
 * Global Error Handler System for IC Packaging & Testing CIM System
 */

import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { apiClient } from '@/api'

/**
 * 错误级别枚举
 */
export enum ErrorLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

/**
 * 错误类型枚举
 */
export enum ErrorType {
  // 网络错误
  NETWORK_ERROR = 'network_error',
  API_ERROR = 'api_error',
  TIMEOUT_ERROR = 'timeout_error',

  // 业务错误
  VALIDATION_ERROR = 'validation_error',
  AUTHENTICATION_ERROR = 'authentication_error',
  AUTHORIZATION_ERROR = 'authorization_error',
  BUSINESS_ERROR = 'business_error',

  // 系统错误
  RUNTIME_ERROR = 'runtime_error',
  RESOURCE_ERROR = 'resource_error',
  CONFIGURATION_ERROR = 'configuration_error',

  // 设备错误
  EQUIPMENT_ERROR = 'equipment_error',
  SECS_GEM_ERROR = 'secs_gem_error',
  COMMUNICATION_ERROR = 'communication_error',

  // 数据错误
  DATA_ERROR = 'data_error',
  STORAGE_ERROR = 'storage_error',
  CACHE_ERROR = 'cache_error',

  // 未知错误
  UNKNOWN_ERROR = 'unknown_error'
}

/**
 * 错误上下文接口
 */
export interface ErrorContext {
  userId?: string
  sessionId?: string
  requestId?: string
  url?: string
  userAgent?: string
  timestamp: Date
  stackTrace?: string
  additionalData?: Record<string, any>
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  id: string
  type: ErrorType
  level: ErrorLevel
  code?: string | number
  message: string
  originalError?: Error
  context: ErrorContext
  handled: boolean
  retryCount: number
  reportedToServer: boolean
  userNotified: boolean
}

/**
 * 错误处理选项接口
 */
export interface ErrorHandlingOptions {
  showToUser?: boolean
  logToConsole?: boolean
  reportToServer?: boolean
  allowRetry?: boolean
  maxRetries?: number
  retryDelay?: number
  customMessage?: string
  customAction?: () => void
  silent?: boolean
}

/**
 * 恢复策略接口
 */
export interface RecoveryStrategy {
  id: string
  name: string
  description: string
  applicableErrorTypes: ErrorType[]
  execute: (error: ErrorInfo) => Promise<boolean>
  priority: number
}

/**
 * 错误报告接口
 */
export interface ErrorReport {
  errorId: string
  type: ErrorType
  level: ErrorLevel
  message: string
  stackTrace?: string
  context: ErrorContext
  userAgent: string
  timestamp: string
  additionalMetrics?: {
    memoryUsage?: number
    performanceData?: any
    systemInfo?: any
  }
}

/**
 * 全局错误处理器类
 */
export class GlobalErrorHandler {
  private static instance: GlobalErrorHandler
  private errors: Map<string, ErrorInfo> = new Map()
  private recoveryStrategies: Map<string, RecoveryStrategy> = new Map()
  private errorQueue: ErrorInfo[] = []
  private isProcessingQueue = false
  private reportTimer: number | null = null
  private maxErrorHistory = 1000
  private reportBatchSize = 10
  private reportInterval = 30000 // 30秒

  private constructor() {
    this.initializeRecoveryStrategies()
    this.setupGlobalErrorHandlers()
    this.startErrorReporting()
  }

  static getInstance(): GlobalErrorHandler {
    if (!GlobalErrorHandler.instance) {
      GlobalErrorHandler.instance = new GlobalErrorHandler()
    }
    return GlobalErrorHandler.instance
  }

  /**
   * 处理错误
   */
  handleError(
    error: Error | string,
    type: ErrorType = ErrorType.UNKNOWN_ERROR,
    level: ErrorLevel = ErrorLevel.ERROR,
    options: ErrorHandlingOptions = {}
  ): string {
    const errorId = this.generateErrorId()

    // 创建错误上下文
    const context = this.createErrorContext(error)

    // 创建错误信息
    const errorInfo: ErrorInfo = {
      id: errorId,
      type,
      level,
      message: typeof error === 'string' ? error : error.message,
      originalError: typeof error === 'string' ? undefined : error,
      context,
      handled: false,
      retryCount: 0,
      reportedToServer: false,
      userNotified: false
    }

    // 添加到错误历史
    this.addToErrorHistory(errorInfo)

    // 处理错误
    this.processError(errorInfo, options)

    return errorId
  }

  /**
   * 处理API错误
   */
  handleApiError(error: any, requestConfig?: any, options: ErrorHandlingOptions = {}): string {
    let errorType = ErrorType.API_ERROR
    let level = ErrorLevel.ERROR
    let message = '请求失败'

    // 判断错误类型
    if (error.code === 'NETWORK_ERROR' || error.code === 'ECONNREFUSED') {
      errorType = ErrorType.NETWORK_ERROR
      message = '网络连接失败'
    } else if (error.code === 'TIMEOUT') {
      errorType = ErrorType.TIMEOUT_ERROR
      message = '请求超时'
    } else if (error.response) {
      const status = error.response.status
      if (status === 401) {
        errorType = ErrorType.AUTHENTICATION_ERROR
        level = ErrorLevel.WARNING
        message = '身份验证失败'
      } else if (status === 403) {
        errorType = ErrorType.AUTHORIZATION_ERROR
        level = ErrorLevel.WARNING
        message = '权限不足'
      } else if (status === 422) {
        errorType = ErrorType.VALIDATION_ERROR
        level = ErrorLevel.WARNING
        message = '数据验证失败'
      } else if (status >= 500) {
        level = ErrorLevel.CRITICAL
        message = '服务器错误'
      }
    }

    // 添加请求上下文
    const context = requestConfig
      ? {
          url: requestConfig.url,
          method: requestConfig.method,
          params: requestConfig.params,
          data: requestConfig.data
        }
      : {}

    return this.handleError(error, errorType, level, {
      ...options,
      customMessage: options.customMessage || message
    })
  }

  /**
   * 处理设备错误
   */
  handleEquipmentError(
    equipmentId: string,
    error: Error | string,
    errorCode?: string,
    options: ErrorHandlingOptions = {}
  ): string {
    const message = typeof error === 'string' ? error : error.message
    const context = {
      equipmentId,
      errorCode,
      additionalData: { source: 'equipment' }
    }

    return this.handleError(
      new Error(`设备 ${equipmentId} 发生错误: ${message}`),
      ErrorType.EQUIPMENT_ERROR,
      ErrorLevel.ERROR,
      {
        ...options,
        reportToServer: true
      }
    )
  }

  /**
   * 处理SECS/GEM错误
   */
  handleSecsGemError(
    equipmentId: string,
    messageType: string,
    error: Error | string,
    options: ErrorHandlingOptions = {}
  ): string {
    const message = typeof error === 'string' ? error : error.message
    const context = {
      equipmentId,
      messageType,
      additionalData: { source: 'secs_gem' }
    }

    return this.handleError(
      new Error(`SECS/GEM通信错误 [${equipmentId}][${messageType}]: ${message}`),
      ErrorType.SECS_GEM_ERROR,
      ErrorLevel.WARNING,
      {
        ...options,
        reportToServer: true
      }
    )
  }

  /**
   * 处理验证错误
   */
  handleValidationError(
    fieldName: string,
    validationMessage: string,
    options: ErrorHandlingOptions = {}
  ): string {
    return this.handleError(
      `字段验证失败 [${fieldName}]: ${validationMessage}`,
      ErrorType.VALIDATION_ERROR,
      ErrorLevel.WARNING,
      {
        showToUser: true,
        logToConsole: false,
        reportToServer: false,
        ...options
      }
    )
  }

  /**
   * 重试错误
   */
  async retryError(errorId: string): Promise<boolean> {
    const errorInfo = this.errors.get(errorId)
    if (!errorInfo) {
      console.warn(`Error ${errorId} not found for retry`)
      return false
    }

    // 检查重试次数
    if (errorInfo.retryCount >= 3) {
      console.warn(`Error ${errorId} has exceeded max retry count`)
      return false
    }

    errorInfo.retryCount++

    // 尝试恢复策略
    const strategy = this.findApplicableStrategy(errorInfo)
    if (strategy) {
      try {
        const success = await strategy.execute(errorInfo)
        if (success) {
          errorInfo.handled = true
          ElMessage.success('错误已自动恢复')
          return true
        }
      } catch (retryError) {
        console.error('Recovery strategy failed:', retryError)
      }
    }

    return false
  }

  /**
   * 获取错误信息
   */
  getError(errorId: string): ErrorInfo | undefined {
    return this.errors.get(errorId)
  }

  /**
   * 获取错误列表
   */
  getErrors(filter?: {
    type?: ErrorType
    level?: ErrorLevel
    handled?: boolean
    startTime?: Date
    endTime?: Date
  }): ErrorInfo[] {
    let errors = Array.from(this.errors.values())

    if (filter) {
      if (filter.type) {
        errors = errors.filter(e => e.type === filter.type)
      }
      if (filter.level) {
        errors = errors.filter(e => e.level === filter.level)
      }
      if (filter.handled !== undefined) {
        errors = errors.filter(e => e.handled === filter.handled)
      }
      if (filter.startTime) {
        errors = errors.filter(e => e.context.timestamp >= filter.startTime!)
      }
      if (filter.endTime) {
        errors = errors.filter(e => e.context.timestamp <= filter.endTime!)
      }
    }

    return errors.sort((a, b) => b.context.timestamp.getTime() - a.context.timestamp.getTime())
  }

  /**
   * 获取错误统计
   */
  getErrorStats(timeRange?: { start: Date; end: Date }): {
    total: number
    byType: Record<ErrorType, number>
    byLevel: Record<ErrorLevel, number>
    handled: number
    unhandled: number
    reportedToServer: number
  } {
    let errors = Array.from(this.errors.values())

    if (timeRange) {
      errors = errors.filter(
        e => e.context.timestamp >= timeRange.start && e.context.timestamp <= timeRange.end
      )
    }

    const stats = {
      total: errors.length,
      byType: {} as Record<ErrorType, number>,
      byLevel: {} as Record<ErrorLevel, number>,
      handled: 0,
      unhandled: 0,
      reportedToServer: 0
    }

    errors.forEach(error => {
      // 按类型统计
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1

      // 按级别统计
      stats.byLevel[error.level] = (stats.byLevel[error.level] || 0) + 1

      // 处理状态统计
      if (error.handled) {
        stats.handled++
      } else {
        stats.unhandled++
      }

      // 上报状态统计
      if (error.reportedToServer) {
        stats.reportedToServer++
      }
    })

    return stats
  }

  /**
   * 清理错误历史
   */
  clearErrors(olderThan?: Date): number {
    let clearCount = 0

    this.errors.forEach((error, id) => {
      if (!olderThan || error.context.timestamp < olderThan) {
        this.errors.delete(id)
        clearCount++
      }
    })

    return clearCount
  }

  /**
   * 导出错误报告
   */
  exportErrorReport(timeRange?: { start: Date; end: Date }): string {
    const errors = this.getErrors({
      startTime: timeRange?.start,
      endTime: timeRange?.end
    })

    const report = {
      title: 'IC封测CIM系统错误报告',
      generatedAt: new Date().toISOString(),
      timeRange,
      statistics: this.getErrorStats(timeRange),
      errors: errors.map(error => ({
        id: error.id,
        type: error.type,
        level: error.level,
        message: error.message,
        timestamp: error.context.timestamp.toISOString(),
        handled: error.handled,
        retryCount: error.retryCount,
        context: {
          url: error.context.url,
          userId: error.context.userId,
          additionalData: error.context.additionalData
        }
      }))
    }

    return JSON.stringify(report, null, 2)
  }

  /**
   * 销毁错误处理器
   */
  destroy(): void {
    if (this.reportTimer) {
      clearInterval(this.reportTimer)
      this.reportTimer = null
    }

    this.errors.clear()
    this.errorQueue = []
    this.recoveryStrategies.clear()
  }

  // 私有方法

  private processError(errorInfo: ErrorInfo, options: ErrorHandlingOptions): void {
    const {
      showToUser = true,
      logToConsole = true,
      reportToServer = false,
      allowRetry = false,
      silent = false
    } = options

    // 控制台日志
    if (logToConsole && !silent) {
      this.logErrorToConsole(errorInfo)
    }

    // 用户通知
    if (showToUser && !silent) {
      this.notifyUser(errorInfo, options)
      errorInfo.userNotified = true
    }

    // 服务器报告
    if (reportToServer) {
      this.queueForServerReport(errorInfo)
    }

    // 尝试自动恢复
    if (allowRetry && !errorInfo.handled) {
      setTimeout(() => {
        this.retryError(errorInfo.id)
      }, options.retryDelay || 1000)
    }

    errorInfo.handled = true
  }

  private createErrorContext(error: Error | string): ErrorContext {
    return {
      userId: this.getCurrentUserId(),
      sessionId: this.getSessionId(),
      requestId: this.generateRequestId(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date(),
      stackTrace: typeof error !== 'string' ? error.stack : undefined,
      additionalData: this.gatherAdditionalData()
    }
  }

  private addToErrorHistory(errorInfo: ErrorInfo): void {
    this.errors.set(errorInfo.id, errorInfo)

    // 限制历史记录数量
    if (this.errors.size > this.maxErrorHistory) {
      const oldestEntry = Array.from(this.errors.entries()).sort(
        ([, a], [, b]) => a.context.timestamp.getTime() - b.context.timestamp.getTime()
      )[0]

      this.errors.delete(oldestEntry[0])
    }
  }

  private logErrorToConsole(errorInfo: ErrorInfo): void {
    const logMethod = this.getLogMethod(errorInfo.level)

    logMethod(`[${errorInfo.level.toUpperCase()}] ${errorInfo.type}: ${errorInfo.message}`, {
      id: errorInfo.id,
      context: errorInfo.context,
      originalError: errorInfo.originalError
    })
  }

  private notifyUser(errorInfo: ErrorInfo, options: ErrorHandlingOptions): void {
    const message = options.customMessage || this.getUserFriendlyMessage(errorInfo)

    switch (errorInfo.level) {
      case ErrorLevel.INFO:
        ElMessage.info(message)
        break
      case ErrorLevel.WARNING:
        ElMessage.warning(message)
        break
      case ErrorLevel.ERROR:
        ElMessage.error(message)
        break
      case ErrorLevel.CRITICAL:
        ElNotification.error({
          title: '严重错误',
          message,
          duration: 0,
          showClose: true
        })

        // 关键错误显示确认对话框
        ElMessageBox.alert(message, '系统错误', {
          type: 'error',
          callback: options.customAction
        })
        break
    }
  }

  private queueForServerReport(errorInfo: ErrorInfo): void {
    this.errorQueue.push(errorInfo)

    if (!this.isProcessingQueue) {
      this.processErrorQueue()
    }
  }

  private async processErrorQueue(): Promise<void> {
    if (this.isProcessingQueue || this.errorQueue.length === 0) {
      return
    }

    this.isProcessingQueue = true

    try {
      const batch = this.errorQueue.splice(0, this.reportBatchSize)
      const reports = batch.map(error => this.createErrorReport(error))

      await this.sendErrorReports(reports)

      batch.forEach(error => {
        error.reportedToServer = true
      })
    } catch (error) {
      console.error('Failed to report errors to server:', error)
    } finally {
      this.isProcessingQueue = false

      // 继续处理队列
      if (this.errorQueue.length > 0) {
        setTimeout(() => this.processErrorQueue(), 1000)
      }
    }
  }

  private createErrorReport(errorInfo: ErrorInfo): ErrorReport {
    return {
      errorId: errorInfo.id,
      type: errorInfo.type,
      level: errorInfo.level,
      message: errorInfo.message,
      stackTrace: errorInfo.context.stackTrace,
      context: errorInfo.context,
      userAgent: navigator.userAgent,
      timestamp: errorInfo.context.timestamp.toISOString(),
      additionalMetrics: {
        memoryUsage: this.getMemoryUsage(),
        performanceData: this.getPerformanceData(),
        systemInfo: this.getSystemInfo()
      }
    }
  }

  private async sendErrorReports(reports: ErrorReport[]): Promise<void> {
    try {
      await apiClient.post(
        '/system/error-reports',
        { reports },
        {
          showError: false,
          showLoading: false
        }
      )
    } catch (error) {
      // 静默处理上报失败
      console.warn('Failed to send error reports:', error)
    }
  }

  private setupGlobalErrorHandlers(): void {
    // 捕获未处理的JavaScript错误
    window.addEventListener('error', event => {
      this.handleError(
        event.error || new Error(event.message),
        ErrorType.RUNTIME_ERROR,
        ErrorLevel.ERROR,
        { reportToServer: true }
      )
    })

    // 捕获未处理的Promise拒绝
    window.addEventListener('unhandledrejection', event => {
      this.handleError(event.reason, ErrorType.RUNTIME_ERROR, ErrorLevel.ERROR, {
        reportToServer: true
      })
    })

    // 捕获资源加载错误
    window.addEventListener(
      'error',
      event => {
        if (event.target !== window) {
          this.handleError(
            new Error(`资源加载失败: ${(event.target as any)?.src || (event.target as any)?.href}`),
            ErrorType.RESOURCE_ERROR,
            ErrorLevel.WARNING,
            { showToUser: false, reportToServer: true }
          )
        }
      },
      true
    )
  }

  private initializeRecoveryStrategies(): void {
    // 网络错误恢复策略
    this.recoveryStrategies.set('network_retry', {
      id: 'network_retry',
      name: '网络重试',
      description: '重试网络请求',
      applicableErrorTypes: [ErrorType.NETWORK_ERROR, ErrorType.TIMEOUT_ERROR],
      execute: async (error: ErrorInfo) => {
        // 等待网络恢复
        await this.waitForNetworkRecovery()
        return navigator.onLine
      },
      priority: 1
    })

    // 认证错误恢复策略
    this.recoveryStrategies.set('auth_refresh', {
      id: 'auth_refresh',
      name: '刷新认证',
      description: '尝试刷新用户认证状态',
      applicableErrorTypes: [ErrorType.AUTHENTICATION_ERROR],
      execute: async (error: ErrorInfo) => {
        try {
          // 尝试刷新Token
          const tokenManager = apiClient.token
          const newToken = await tokenManager.refreshTokenIfNeeded()
          return !!newToken
        } catch {
          return false
        }
      },
      priority: 2
    })

    // 缓存错误恢复策略
    this.recoveryStrategies.set('cache_clear', {
      id: 'cache_clear',
      name: '清理缓存',
      description: '清理损坏的缓存数据',
      applicableErrorTypes: [ErrorType.CACHE_ERROR, ErrorType.STORAGE_ERROR],
      execute: async (error: ErrorInfo) => {
        try {
          // 清理相关缓存
          apiClient.cache.clear()
          localStorage.removeItem(error.context.additionalData?.cacheKey)
          return true
        } catch {
          return false
        }
      },
      priority: 3
    })
  }

  private findApplicableStrategy(errorInfo: ErrorInfo): RecoveryStrategy | null {
    const strategies = Array.from(this.recoveryStrategies.values())
      .filter(strategy => strategy.applicableErrorTypes.includes(errorInfo.type))
      .sort((a, b) => a.priority - b.priority)

    return strategies[0] || null
  }

  private startErrorReporting(): void {
    this.reportTimer = window.setInterval(() => {
      if (this.errorQueue.length > 0) {
        this.processErrorQueue()
      }
    }, this.reportInterval)
  }

  private async waitForNetworkRecovery(): Promise<void> {
    return new Promise(resolve => {
      const checkNetwork = () => {
        if (navigator.onLine) {
          resolve()
        } else {
          setTimeout(checkNetwork, 1000)
        }
      }
      checkNetwork()
    })
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private getCurrentUserId(): string | undefined {
    // 从认证store获取用户ID
    try {
      const userInfo = localStorage.getItem('cim_user_info')
      return userInfo ? JSON.parse(userInfo).id : undefined
    } catch {
      return undefined
    }
  }

  private getSessionId(): string | undefined {
    return sessionStorage.getItem('cim_session_id') || undefined
  }

  private gatherAdditionalData(): Record<string, any> {
    return {
      pathname: window.location.pathname,
      search: window.location.search,
      referrer: document.referrer,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine
    }
  }

  private getLogMethod(level: ErrorLevel): Function {
    switch (level) {
      case ErrorLevel.INFO:
        return console.info
      case ErrorLevel.WARNING:
        return console.warn
      case ErrorLevel.ERROR:
        return console.error
      case ErrorLevel.CRITICAL:
        return console.error
      default:
        return console.log
    }
  }

  private getUserFriendlyMessage(errorInfo: ErrorInfo): string {
    const messages: Record<ErrorType, string> = {
      [ErrorType.NETWORK_ERROR]: '网络连接异常，请检查网络设置',
      [ErrorType.API_ERROR]: '服务请求失败，请稍后重试',
      [ErrorType.TIMEOUT_ERROR]: '请求超时，请稍后重试',
      [ErrorType.VALIDATION_ERROR]: '数据格式不正确，请检查输入',
      [ErrorType.AUTHENTICATION_ERROR]: '身份验证失败，请重新登录',
      [ErrorType.AUTHORIZATION_ERROR]: '权限不足，无法执行此操作',
      [ErrorType.BUSINESS_ERROR]: '业务处理异常，请联系系统管理员',
      [ErrorType.RUNTIME_ERROR]: '系统运行异常，页面将自动刷新',
      [ErrorType.RESOURCE_ERROR]: '资源加载失败，请刷新页面',
      [ErrorType.CONFIGURATION_ERROR]: '系统配置异常，请联系系统管理员',
      [ErrorType.EQUIPMENT_ERROR]: '设备通信异常，请检查设备状态',
      [ErrorType.SECS_GEM_ERROR]: 'SECS/GEM通信异常，请检查设备连接',
      [ErrorType.COMMUNICATION_ERROR]: '通信异常，请检查网络连接',
      [ErrorType.DATA_ERROR]: '数据处理异常，请重试或联系管理员',
      [ErrorType.STORAGE_ERROR]: '数据存储异常，请清理浏览器缓存',
      [ErrorType.CACHE_ERROR]: '缓存异常，正在自动清理',
      [ErrorType.UNKNOWN_ERROR]: '未知错误，请联系系统管理员'
    }

    return messages[errorInfo.type] || errorInfo.message
  }

  private getMemoryUsage(): number | undefined {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize
    }
    return undefined
  }

  private getPerformanceData(): any {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    return {
      loadComplete: navigation?.loadEventEnd - navigation?.loadEventStart,
      domContentLoaded:
        navigation?.domContentLoadedEventEnd - navigation?.domContentLoadedEventStart,
      responseTime: navigation?.responseEnd - navigation?.requestStart
    }
  }

  private getSystemInfo(): any {
    return {
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screenResolution: `${screen.width}x${screen.height}`,
      colorDepth: screen.colorDepth,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    }
  }
}

/**
 * 错误处理工具类
 */
export class ErrorUtils {
  /**
   * 检查是否为网络错误
   */
  static isNetworkError(error: any): boolean {
    return !!(
      error.code === 'NETWORK_ERROR' ||
      error.code === 'ECONNREFUSED' ||
      error.message?.includes('Network Error') ||
      error.message?.includes('fetch')
    )
  }

  /**
   * 检查是否为超时错误
   */
  static isTimeoutError(error: any): boolean {
    return !!(
      error.code === 'TIMEOUT' ||
      error.message?.includes('timeout') ||
      error.message?.includes('Request timeout')
    )
  }

  /**
   * 检查是否为认证错误
   */
  static isAuthError(error: any): boolean {
    return !!(error.response?.status === 401 || error.code === 'AUTHENTICATION_ERROR')
  }

  /**
   * 检查是否为授权错误
   */
  static isAuthorizationError(error: any): boolean {
    return !!(error.response?.status === 403 || error.code === 'AUTHORIZATION_ERROR')
  }

  /**
   * 检查是否为验证错误
   */
  static isValidationError(error: any): boolean {
    return !!(error.response?.status === 422 || error.code === 'VALIDATION_ERROR')
  }

  /**
   * 提取错误消息
   */
  static extractErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error
    }

    if (error.response?.data?.message) {
      return error.response.data.message
    }

    if (error.message) {
      return error.message
    }

    return '未知错误'
  }

  /**
   * 格式化错误堆栈
   */
  static formatStackTrace(error: Error): string {
    if (!error.stack) return ''

    return error.stack
      .split('\n')
      .slice(1)
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .join('\n')
  }

  /**
   * 创建错误摘要
   */
  static createErrorSummary(error: any): string {
    const message = this.extractErrorMessage(error)
    const type = error.constructor?.name || 'Error'
    const timestamp = new Date().toLocaleString()

    return `[${timestamp}] ${type}: ${message}`
  }
}

/**
 * 装饰器：错误处理
 */
export function HandleError(options: ErrorHandlingOptions = {}) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: any[]) {
      try {
        return await originalMethod.apply(this, args)
      } catch (error) {
        const errorHandler = GlobalErrorHandler.getInstance()
        errorHandler.handleError(error, ErrorType.RUNTIME_ERROR, ErrorLevel.ERROR, options)

        if (!options.silent) {
          throw error
        }
      }
    }

    return descriptor
  }
}

/**
 * 高阶组件：错误边界
 */
export function withErrorBoundary<T extends any>(component: T, fallback?: () => any): T {
  // Vue 3 错误边界实现
  return component // 简化实现
}

// 全局错误处理器实例
export const globalErrorHandler = GlobalErrorHandler.getInstance()

// 导出常用函数
export const handleError = (
  error: Error | string,
  type?: ErrorType,
  level?: ErrorLevel,
  options?: ErrorHandlingOptions
) => globalErrorHandler.handleError(error, type, level, options)

export const handleApiError = (error: any, requestConfig?: any, options?: ErrorHandlingOptions) =>
  globalErrorHandler.handleApiError(error, requestConfig, options)

export const handleEquipmentError = (
  equipmentId: string,
  error: Error | string,
  errorCode?: string,
  options?: ErrorHandlingOptions
) => globalErrorHandler.handleEquipmentError(equipmentId, error, errorCode, options)

export const handleValidationError = (
  fieldName: string,
  message: string,
  options?: ErrorHandlingOptions
) => globalErrorHandler.handleValidationError(fieldName, message, options)

// 默认导出
export default {
  GlobalErrorHandler,
  ErrorUtils,
  HandleError,
  withErrorBoundary,
  globalErrorHandler,
  handleError,
  handleApiError,
  handleEquipmentError,
  handleValidationError,
  ErrorType,
  ErrorLevel
}
