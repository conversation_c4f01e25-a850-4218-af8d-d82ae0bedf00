// ================================
// IC产品主数据模拟数据
// ================================

import {
  Product,
  ICProductCategory,
  PackageType,
  ProcessNode,
  ApplicationField,
  ProductStatus,
  SubstrateType,
  WireBondType,
  ProductStatistics,
  TechnicalSpecification,
  PackageInfo,
  CPTestParameters,
  FTTestParameters,
  QualityRequirements,
  CostParameters
} from '@/types/product'
import { mockCustomers } from './customers'

// ================================
// 基础数据选项
// ================================

export const productCategoryOptions = [
  { label: 'CPU处理器', value: ICProductCategory.CPU },
  { label: 'GPU图形处理器', value: ICProductCategory.GPU },
  { label: 'MCU微控制器', value: ICProductCategory.MCU },
  { label: '传感器', value: ICProductCategory.SENSOR },
  { label: '电源管理IC', value: ICProductCategory.POWER },
  { label: 'RF射频IC', value: ICProductCategory.RF },
  { label: '模拟IC', value: ICProductCategory.ANALOG },
  { label: '存储器', value: ICProductCategory.MEMORY },
  { label: '接口IC', value: ICProductCategory.INTERFACE },
  { label: '其他', value: ICProductCategory.OTHER }
]

export const packageTypeOptions = [
  { label: 'DIP双列直插', value: PackageType.DIP },
  { label: 'QFP方形扁平封装', value: PackageType.QFP },
  { label: 'TQFP薄型QFP', value: PackageType.TQFP },
  { label: 'LQFP低剖面QFP', value: PackageType.LQFP },
  { label: 'QFN方形无引脚封装', value: PackageType.QFN },
  { label: 'BGA球栅阵列', value: PackageType.BGA },
  { label: 'CSP芯片级封装', value: PackageType.CSP },
  { label: 'WLCSP晶圆级CSP', value: PackageType.WLCSP },
  { label: 'SOP小外形封装', value: PackageType.SOP },
  { label: 'SOIC小外形IC', value: PackageType.SOIC }
]

export const processNodeOptions = [
  { label: '3nm', value: ProcessNode.N_3NM },
  { label: '5nm', value: ProcessNode.N_5NM },
  { label: '7nm', value: ProcessNode.N_7NM },
  { label: '14nm', value: ProcessNode.N_14NM },
  { label: '28nm', value: ProcessNode.N_28NM },
  { label: '40nm', value: ProcessNode.N_40NM },
  { label: '65nm', value: ProcessNode.N_65NM },
  { label: '90nm', value: ProcessNode.N_90NM },
  { label: '130nm', value: ProcessNode.N_130NM },
  { label: '180nm', value: ProcessNode.N_180NM }
]

export const applicationFieldOptions = [
  { label: '汽车电子', value: ApplicationField.AUTOMOTIVE },
  { label: '消费电子', value: ApplicationField.CONSUMER },
  { label: '工业控制', value: ApplicationField.INDUSTRIAL },
  { label: '通信设备', value: ApplicationField.COMMUNICATION },
  { label: '医疗设备', value: ApplicationField.MEDICAL },
  { label: '航空航天', value: ApplicationField.AEROSPACE },
  { label: '物联网', value: ApplicationField.IOT },
  { label: '服务器', value: ApplicationField.SERVER }
]

export const productStatusOptions = [
  { label: '开发中', value: ProductStatus.DEVELOPMENT, color: 'warning' },
  { label: '认证中', value: ProductStatus.QUALIFICATION, color: 'info' },
  { label: '量产', value: ProductStatus.MASS_PRODUCTION, color: 'success' },
  { label: '停产', value: ProductStatus.EOL, color: 'danger' },
  { label: '淘汰', value: ProductStatus.OBSOLETE, color: 'info' }
]

// ================================
// 模拟数据生成函数
// ================================

// 生成技术规格
const generateTechnicalSpec = (category: ICProductCategory, processNode: ProcessNode): TechnicalSpecification => {
  const baseSpecs = {
    [ICProductCategory.CPU]: {
      dieSizeX: 15.0, dieSizeY: 15.0, pinCount: 1000,
      packageSizeX: 40.0, packageSizeY: 40.0, packageHeight: 2.5,
      operatingTempMin: 0, operatingTempMax: 70,
      supplyVoltageMin: 0.9, supplyVoltageMax: 1.1,
      powerConsumption: 65, frequency: 3000
    },
    [ICProductCategory.MCU]: {
      dieSizeX: 5.0, dieSizeY: 5.0, pinCount: 64,
      packageSizeX: 10.0, packageSizeY: 10.0, packageHeight: 1.0,
      operatingTempMin: -40, operatingTempMax: 85,
      supplyVoltageMin: 1.8, supplyVoltageMax: 3.6,
      powerConsumption: 0.5, frequency: 168
    },
    [ICProductCategory.SENSOR]: {
      dieSizeX: 2.0, dieSizeY: 2.0, pinCount: 16,
      packageSizeX: 3.0, packageSizeY: 3.0, packageHeight: 0.8,
      operatingTempMin: -40, operatingTempMax: 125,
      supplyVoltageMin: 2.7, supplyVoltageMax: 5.5,
      powerConsumption: 0.01, frequency: 1
    },
    [ICProductCategory.POWER]: {
      dieSizeX: 3.0, dieSizeY: 3.0, pinCount: 8,
      packageSizeX: 5.0, packageSizeY: 6.0, packageHeight: 1.5,
      operatingTempMin: -40, operatingTempMax: 150,
      supplyVoltageMin: 4.5, supplyVoltageMax: 60,
      powerConsumption: 2.0, frequency: 500
    }
  }

  const spec = baseSpecs[category] || baseSpecs[ICProductCategory.MCU]
  return {
    processNode,
    ...spec
  }
}

// 生成封装信息
const generatePackageInfo = (packageType: PackageType): PackageInfo => {
  const packageConfigs = {
    [PackageType.QFP]: {
      substrateType: SubstrateType.FR4,
      wireBondType: WireBondType.AU_25UM,
      leadFrame: 'Cu194',
      moldingCompound: 'EME-7300H',
      platingType: 'NiPdAu',
      markingMethod: '激光标记'
    },
    [PackageType.BGA]: {
      substrateType: SubstrateType.FR4,
      wireBondType: WireBondType.AU_20UM,
      leadFrame: 'BT基板',
      moldingCompound: 'EME-7340H',
      platingType: 'ENIG',
      markingMethod: '激光标记'
    },
    [PackageType.QFN]: {
      substrateType: SubstrateType.FR4,
      wireBondType: WireBondType.AU_18UM,
      leadFrame: 'Cu194',
      moldingCompound: 'EME-7200',
      platingType: 'NiPdAu',
      markingMethod: '印刷标记'
    }
  }

  return {
    packageType,
    ...(packageConfigs[packageType] || packageConfigs[PackageType.QFP])
  }
}

// 生成测试参数
const generateCPTestParams = (category: ICProductCategory): CPTestParameters => {
  return {
    testTemperature: [-40, 25, 85, 125],
    testVoltage: [3.0, 3.3, 3.6],
    testCurrent: [0.001, 0.1, 1.0],
    testItems: ['开路测试', '短路测试', '漏电流测试', 'VIH/VIL测试', 'VOH/VOL测试'],
    yieldTarget: 99.5,
    testTime: category === ICProductCategory.CPU ? 0.5 : 0.2
  }
}

const generateFTTestParams = (category: ICProductCategory): FTTestParameters => {
  const baseTests = {
    functionalTests: ['数字功能测试', '模拟功能测试', '时序测试'],
    electricalTests: ['DC参数测试', 'AC参数测试', '功耗测试'],
    thermalTests: ['热阻测试', '温度循环测试'],
    reliabilityTests: ['HTOL', 'HTGB', 'TC', 'HTS']
  }

  return {
    ...baseTests,
    burnInHours: category === ICProductCategory.CPU ? 168 : 48,
    burnInTemp: 125,
    finalYieldTarget: 99.0
  }
}

// 生成质量要求
const generateQualityReq = (applicationField: ApplicationField): QualityRequirements => {
  const automotive = {
    defectRate: 1,
    reliabilityLevel: 'Grade 0',
    certificationStandards: ['AEC-Q100', 'TS16949', 'ISO26262'],
    qualityGrade: 'Automotive',
    inspectionLevel: 'S4'
  }

  const consumer = {
    defectRate: 10,
    reliabilityLevel: 'Grade 1',
    certificationStandards: ['RoHS', 'REACH'],
    qualityGrade: 'Consumer',
    inspectionLevel: 'S3'
  }

  return applicationField === ApplicationField.AUTOMOTIVE ? automotive : consumer
}

// 生成成本参数
const generateCostParams = (category: ICProductCategory): CostParameters => {
  const baseCosts = {
    [ICProductCategory.CPU]: {
      dieCost: 80.0, substrateCost: 15.0, wireCost: 5.0, moldingCost: 3.0,
      cpTestCost: 2.0, assemblyCost: 8.0, ftTestCost: 1.5,
      packagingCost: 0.5, shippingCost: 0.3, overheadRate: 15, profitMargin: 20
    },
    [ICProductCategory.MCU]: {
      dieCost: 3.0, substrateCost: 0.8, wireCost: 0.2, moldingCost: 0.1,
      cpTestCost: 0.05, assemblyCost: 0.3, ftTestCost: 0.1,
      packagingCost: 0.02, shippingCost: 0.01, overheadRate: 12, profitMargin: 25
    }
  }

  return baseCosts[category] || baseCosts[ICProductCategory.MCU]
}

// ================================
// 模拟产品数据
// ================================

export const mockProducts: Product[] = [
  // CPU产品
  {
    id: 'p001',
    productCode: 'CPU-A7-3200',
    productName: 'ARM Cortex-A7 3.2GHz处理器',
    customerCode: mockCustomers[0].customerCode,
    customer: mockCustomers[0],
    category: ICProductCategory.CPU,
    applicationField: ApplicationField.CONSUMER,
    status: ProductStatus.MASS_PRODUCTION,
    description: '基于ARM Cortex-A7架构的高性能处理器，适用于平板电脑和智能手机',
    features: ['双核架构', '3.2GHz主频', '集成GPU', '低功耗设计'],
    applications: ['平板电脑', '智能手机', '数字电视', '车载娱乐系统'],
    technicalSpec: generateTechnicalSpec(ICProductCategory.CPU, ProcessNode.N_28NM),
    packageInfo: generatePackageInfo(PackageType.BGA),
    cpTestParams: generateCPTestParams(ICProductCategory.CPU),
    ftTestParams: generateFTTestParams(ICProductCategory.CPU),
    qualityReq: generateQualityReq(ApplicationField.CONSUMER),
    costParams: generateCostParams(ICProductCategory.CPU),
    version: 'V1.2',
    revisionHistory: [
      {
        id: 'r001',
        version: 'V1.2',
        changeDescription: '优化功耗参数，提升测试良率',
        changedBy: '李工程师',
        changedAt: new Date('2024-01-15'),
        approvedBy: '张经理',
        approvedAt: new Date('2024-01-16'),
        reason: '客户要求降低功耗'
      }
    ],
    attachments: [],
    engineerName: '李工程师',
    businessName: '王商务',
    approvalStatus: '已审批',
    effectiveDate: new Date('2024-01-01'),
    createdAt: new Date('2023-10-01'),
    updatedAt: new Date('2024-01-15')
  },

  // MCU产品
  {
    id: 'p002',
    productCode: 'MCU-STM32F407',
    productName: 'STM32F407高性能微控制器',
    customerCode: mockCustomers[1].customerCode,
    customer: mockCustomers[1],
    category: ICProductCategory.MCU,
    applicationField: ApplicationField.INDUSTRIAL,
    status: ProductStatus.MASS_PRODUCTION,
    description: '基于ARM Cortex-M4内核的32位微控制器，集成丰富外设',
    features: ['168MHz Cortex-M4内核', '1MB Flash', '192KB SRAM', '丰富外设接口'],
    applications: ['工业控制器', '电机驱动', '医疗设备', '仪器仪表'],
    technicalSpec: generateTechnicalSpec(ICProductCategory.MCU, ProcessNode.N_90NM),
    packageInfo: generatePackageInfo(PackageType.LQFP),
    cpTestParams: generateCPTestParams(ICProductCategory.MCU),
    ftTestParams: generateFTTestParams(ICProductCategory.MCU),
    qualityReq: generateQualityReq(ApplicationField.INDUSTRIAL),
    costParams: generateCostParams(ICProductCategory.MCU),
    version: 'V2.0',
    revisionHistory: [],
    attachments: [],
    engineerName: '陈工程师',
    businessName: '刘商务',
    approvalStatus: '已审批',
    effectiveDate: new Date('2023-08-01'),
    createdAt: new Date('2023-06-01'),
    updatedAt: new Date('2023-12-20')
  },

  // 汽车电子MCU
  {
    id: 'p003',
    productCode: 'MCU-AUTO-ECU',
    productName: '汽车ECU专用微控制器',
    customerCode: mockCustomers[2].customerCode,
    customer: mockCustomers[2],
    category: ICProductCategory.MCU,
    applicationField: ApplicationField.AUTOMOTIVE,
    status: ProductStatus.QUALIFICATION,
    description: '符合AEC-Q100标准的汽车级微控制器，用于发动机控制单元',
    features: ['AEC-Q100认证', '宽温度范围', 'AUTOSAR支持', 'CAN总线接口'],
    applications: ['发动机ECU', '变速箱控制', 'ESP系统', '车身控制'],
    technicalSpec: {
      ...generateTechnicalSpec(ICProductCategory.MCU, ProcessNode.N_40NM),
      operatingTempMin: -40,
      operatingTempMax: 150
    },
    packageInfo: generatePackageInfo(PackageType.QFP),
    cpTestParams: generateCPTestParams(ICProductCategory.MCU),
    ftTestParams: generateFTTestParams(ICProductCategory.MCU),
    qualityReq: generateQualityReq(ApplicationField.AUTOMOTIVE),
    costParams: generateCostParams(ICProductCategory.MCU),
    version: 'V1.0',
    revisionHistory: [],
    attachments: [],
    engineerName: '汽车电子工程师',
    businessName: '汽车商务',
    approvalStatus: '认证中',
    effectiveDate: new Date('2024-06-01'),
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-02-15')
  },

  // 传感器产品
  {
    id: 'p004',
    productCode: 'SENSOR-IMU-6DOF',
    productName: '六轴惯性测量单元',
    customerCode: mockCustomers[3].customerCode,
    customer: mockCustomers[3],
    category: ICProductCategory.SENSOR,
    applicationField: ApplicationField.IOT,
    status: ProductStatus.DEVELOPMENT,
    description: '集成三轴加速度计和三轴陀螺仪的惯性测量单元',
    features: ['6轴检测', '低功耗', 'I2C/SPI接口', '可编程中断'],
    applications: ['可穿戴设备', '无人机', '机器人', '运动监测'],
    technicalSpec: generateTechnicalSpec(ICProductCategory.SENSOR, ProcessNode.N_180NM),
    packageInfo: generatePackageInfo(PackageType.QFN),
    cpTestParams: generateCPTestParams(ICProductCategory.SENSOR),
    ftTestParams: generateFTTestParams(ICProductCategory.SENSOR),
    qualityReq: generateQualityReq(ApplicationField.IOT),
    costParams: {
      dieCost: 1.5, substrateCost: 0.3, wireCost: 0.1, moldingCost: 0.05,
      cpTestCost: 0.02, assemblyCost: 0.15, ftTestCost: 0.08,
      packagingCost: 0.01, shippingCost: 0.005, overheadRate: 10, profitMargin: 30
    },
    version: 'V0.5',
    revisionHistory: [],
    attachments: [],
    engineerName: '传感器工程师',
    businessName: '物联网商务',
    approvalStatus: '开发中',
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-28')
  },

  // 电源管理IC
  {
    id: 'p005',
    productCode: 'PMU-LDO-3V3',
    productName: '3.3V低压差稳压器',
    customerCode: mockCustomers[4].customerCode,
    customer: mockCustomers[4],
    category: ICProductCategory.POWER,
    applicationField: ApplicationField.CONSUMER,
    status: ProductStatus.MASS_PRODUCTION,
    description: '高精度低压差线性稳压器，输出3.3V稳定电压',
    features: ['低压差', '高精度', '低静态电流', '过热保护'],
    applications: ['手机充电器', '便携设备', '电池管理', '电源适配器'],
    technicalSpec: generateTechnicalSpec(ICProductCategory.POWER, ProcessNode.N_130NM),
    packageInfo: generatePackageInfo(PackageType.SOP),
    cpTestParams: generateCPTestParams(ICProductCategory.POWER),
    ftTestParams: generateFTTestParams(ICProductCategory.POWER),
    qualityReq: generateQualityReq(ApplicationField.CONSUMER),
    costParams: generateCostParams(ICProductCategory.POWER),
    version: 'V1.1',
    revisionHistory: [],
    attachments: [],
    engineerName: '电源工程师',
    businessName: '消费电子商务',
    approvalStatus: '已审批',
    effectiveDate: new Date('2023-05-01'),
    createdAt: new Date('2023-03-01'),
    updatedAt: new Date('2023-11-10')
  },

  // 停产产品
  {
    id: 'p006',
    productCode: 'MCU-LEGACY-8051',
    productName: '8051兼容微控制器（停产）',
    customerCode: mockCustomers[0].customerCode,
    customer: mockCustomers[0],
    category: ICProductCategory.MCU,
    applicationField: ApplicationField.INDUSTRIAL,
    status: ProductStatus.EOL,
    description: '基于8051内核的8位微控制器，已停产',
    features: ['8051兼容', '256B RAM', '8KB Flash', '32个I/O口'],
    applications: ['简单控制器', '传统工控设备'],
    technicalSpec: {
      processNode: ProcessNode.N_350NM,
      dieSizeX: 8.0, dieSizeY: 8.0, pinCount: 44,
      packageSizeX: 18.0, packageSizeY: 18.0, packageHeight: 2.0,
      operatingTempMin: -40, operatingTempMax: 85,
      supplyVoltageMin: 4.5, supplyVoltageMax: 5.5,
      powerConsumption: 0.2, frequency: 12
    },
    packageInfo: generatePackageInfo(PackageType.QFP),
    cpTestParams: generateCPTestParams(ICProductCategory.MCU),
    ftTestParams: generateFTTestParams(ICProductCategory.MCU),
    qualityReq: generateQualityReq(ApplicationField.INDUSTRIAL),
    costParams: {
      dieCost: 0.8, substrateCost: 0.2, wireCost: 0.05, moldingCost: 0.03,
      cpTestCost: 0.02, assemblyCost: 0.1, ftTestCost: 0.05,
      packagingCost: 0.01, shippingCost: 0.005, overheadRate: 8, profitMargin: 15
    },
    version: 'V3.0',
    revisionHistory: [],
    attachments: [],
    engineerName: '老工程师',
    businessName: '传统商务',
    approvalStatus: '停产',
    effectiveDate: new Date('2020-01-01'),
    expiryDate: new Date('2023-12-31'),
    createdAt: new Date('2019-08-01'),
    updatedAt: new Date('2023-12-31')
  }
]

// ================================
// 统计数据
// ================================

export const mockProductStatistics: ProductStatistics = {
  categoryStats: [
    { category: ICProductCategory.MCU, count: 3, percentage: 50.0 },
    { category: ICProductCategory.CPU, count: 1, percentage: 16.7 },
    { category: ICProductCategory.SENSOR, count: 1, percentage: 16.7 },
    { category: ICProductCategory.POWER, count: 1, percentage: 16.7 }
  ],
  packageStats: [
    { packageType: PackageType.QFP, count: 2, percentage: 33.3 },
    { packageType: PackageType.BGA, count: 1, percentage: 16.7 },
    { packageType: PackageType.LQFP, count: 1, percentage: 16.7 },
    { packageType: PackageType.QFN, count: 1, percentage: 16.7 },
    { packageType: PackageType.SOP, count: 1, percentage: 16.7 }
  ],
  statusStats: [
    { status: ProductStatus.MASS_PRODUCTION, count: 3, percentage: 50.0 },
    { status: ProductStatus.QUALIFICATION, count: 1, percentage: 16.7 },
    { status: ProductStatus.DEVELOPMENT, count: 1, percentage: 16.7 },
    { status: ProductStatus.EOL, count: 1, percentage: 16.7 }
  ],
  processNodeStats: [
    { processNode: ProcessNode.N_28NM, count: 1, percentage: 16.7 },
    { processNode: ProcessNode.N_40NM, count: 1, percentage: 16.7 },
    { processNode: ProcessNode.N_90NM, count: 1, percentage: 16.7 },
    { processNode: ProcessNode.N_130NM, count: 1, percentage: 16.7 },
    { processNode: ProcessNode.N_180NM, count: 1, percentage: 16.7 },
    { processNode: ProcessNode.N_350NM, count: 1, percentage: 16.7 }
  ]
}

// ================================
// 辅助函数
// ================================

// 根据ID获取产品
export const getProductById = (id: string): Product | undefined => {
  return mockProducts.find(product => product.id === id)
}

// 根据客户代码获取产品列表
export const getProductsByCustomer = (customerCode: string): Product[] => {
  return mockProducts.filter(product => product.customerCode === customerCode)
}

// 根据分类获取产品列表
export const getProductsByCategory = (category: ICProductCategory): Product[] => {
  return mockProducts.filter(product => product.category === category)
}

// 根据状态获取产品列表  
export const getProductsByStatus = (status: ProductStatus): Product[] => {
  return mockProducts.filter(product => product.status === status)
}