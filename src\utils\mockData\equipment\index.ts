/**
 * 设备管理模拟数据
 * Equipment Management Mock Data
 */

import type {
  Equipment,
  EquipmentStatusDetail,
  EquipmentAlarm,
  EquipmentEvent,
  Recipe,
  RecipeParameter,
  EquipmentConstant,
  MaintenancePlan,
  MaintenanceTask,
  MaintenanceRecord,
  SecsMessage,
  EquipmentStatistics,
  MaintenanceStatistics,
  EquipmentPerformance
} from '@/types/equipment'
import { EquipmentType, EquipmentStatus, GemState } from '@/types/equipment'

// 设备列表模拟数据
export const mockEquipmentList: Equipment[] = [
  {
    id: 'EQ001',
    name: '测试机台-ATE01',
    code: 'ATE-001',
    type: EquipmentType.ATE,
    model: 'Advantest T2000',
    manufacturer: 'Advantest',
    serialNumber: 'AT2000-001',
    location: '测试区域A',
    installDate: '2023-01-15',
    status: EquipmentStatus.RUN,
    gemState: GemState.ONLINE_REMOTE,
    isConnected: true,
    description: '高速自动测试设备，支持多站点并行测试',
    specifications: {
      testChannels: 512,
      maxFrequency: '1GHz',
      testPower: '5kW',
      accuracy: '±0.1%'
    },
    capabilities: ['DC测试', 'AC测试', '功能测试', '老化测试'],
    createdAt: '2023-01-15T08:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z'
  },
  {
    id: 'EQ002',
    name: '键合机-BD01',
    code: 'BD-001',
    type: EquipmentType.BONDER,
    model: 'ASM Eagle 60AP',
    manufacturer: 'ASM Pacific',
    serialNumber: 'ASM-BD-001',
    location: '封装区域B',
    installDate: '2023-02-20',
    status: EquipmentStatus.IDLE,
    gemState: GemState.ONLINE_LOCAL,
    isConnected: true,
    description: '全自动球焊键合机，支持金线和铜线键合',
    specifications: {
      wireSize: '15-50μm',
      bondAccuracy: '±2μm',
      bondSpeed: '12 bonds/sec',
      substrate: 'up to 100mm'
    },
    capabilities: ['金线键合', '铜线键合', '楔焊', '球焊'],
    createdAt: '2023-02-20T09:00:00Z',
    updatedAt: '2024-01-15T14:20:00Z'
  },
  {
    id: 'EQ003',
    name: '贴片机-DA01',
    code: 'DA-001',
    type: EquipmentType.DIE_ATTACH,
    model: 'Palomar 3500-II',
    manufacturer: 'Palomar Technologies',
    serialNumber: 'PAL-DA-001',
    location: '封装区域A',
    installDate: '2023-03-10',
    status: EquipmentStatus.DOWN,
    gemState: GemState.HOST_OFFLINE,
    isConnected: false,
    description: '高精度贴片机，适用于各种封装类型',
    specifications: {
      placementAccuracy: '±5μm',
      dieSize: '0.3-25mm',
      throughput: '2000 UPH',
      forceControl: '0.1-50N'
    },
    capabilities: ['贴片', '点胶', '固化', '检测'],
    createdAt: '2023-03-10T10:00:00Z',
    updatedAt: '2024-01-15T16:45:00Z'
  },
  {
    id: 'EQ004',
    name: '分选机-HD01',
    code: 'HD-001',
    type: EquipmentType.HANDLER,
    model: 'Multitest MT9510',
    manufacturer: 'Multitest',
    serialNumber: 'MT-HD-001',
    location: '测试区域B',
    installDate: '2023-04-05',
    status: EquipmentStatus.PM,
    gemState: GemState.ONLINE_LOCAL,
    isConnected: true,
    description: '高通量测试分选机，支持多种封装',
    specifications: {
      throughput: '20000 UPH',
      temperature: '-40°C to +150°C',
      binCapacity: '16 bins',
      packageSupport: 'QFP, BGA, SOP'
    },
    capabilities: ['温度测试', '高速分选', '激光标记', '视觉检测'],
    createdAt: '2023-04-05T11:00:00Z',
    updatedAt: '2024-01-15T13:15:00Z'
  },
  {
    id: 'EQ005',
    name: '塑封机-MD01',
    code: 'MD-001',
    type: EquipmentType.MOLDING,
    model: 'Towa YPS-300',
    manufacturer: 'Towa',
    serialNumber: 'TW-MD-001',
    location: '封装区域C',
    installDate: '2023-05-15',
    status: EquipmentStatus.RUN,
    gemState: GemState.ONLINE_REMOTE,
    isConnected: true,
    description: '全自动塑封机，支持多种封装形式',
    specifications: {
      moldForce: '300 tons',
      moldSize: '200x200mm',
      cycle: '120 sec',
      strips: '4 strips'
    },
    capabilities: ['塑封', '后固化', '飞边切除', '外观检测'],
    createdAt: '2023-05-15T12:00:00Z',
    updatedAt: '2024-01-15T15:30:00Z'
  }
]

// 设备状态详情模拟数据
export const mockEquipmentStatus: EquipmentStatusDetail[] = mockEquipmentList.map(eq => ({
  equipmentId: eq.id,
  status: eq.status,
  gemState: eq.gemState,
  isConnected: eq.isConnected,
  uptime: Math.floor(Math.random() * 86400),
  downtime: Math.floor(Math.random() * 3600),
  lastStatusChange: new Date(Date.now() - Math.random() * 3600000).toISOString(),
  currentRecipe: eq.status === EquipmentStatus.RUN ? `Recipe_${eq.code}_v1.2` : undefined,
  currentLot:
    eq.status === EquipmentStatus.RUN ? `LOT${Math.floor(Math.random() * 10000)}` : undefined,
  processingCount: Math.floor(Math.random() * 1000),
  totalCount: Math.floor(Math.random() * 50000) + 10000,
  errorCount: Math.floor(Math.random() * 10),
  warningCount: Math.floor(Math.random() * 50),
  temperature: eq.type === EquipmentType.HANDLER ? 25 + Math.random() * 10 : undefined,
  pressure: eq.type === EquipmentType.MOLDING ? 150 + Math.random() * 50 : undefined,
  humidity: Math.random() * 20 + 40,
  oeeData: {
    availability: 85 + Math.random() * 10,
    performance: 90 + Math.random() * 8,
    quality: 95 + Math.random() * 4,
    oee: 75 + Math.random() * 15
  }
}))

// 设备告警模拟数据
export const mockAlarms: EquipmentAlarm[] = [
  {
    id: 'ALM001',
    equipmentId: 'EQ003',
    equipmentName: '贴片机-DA01',
    alarmCode: 'E1001',
    alarmText: '真空泵压力不足',
    severity: 'CRITICAL',
    category: 'MECHANICAL',
    timestamp: '2024-01-15T16:45:00Z',
    isActive: true,
    description: '真空泵系统压力低于设定值，可能影响贴片精度',
    recommendations: ['检查真空泵', '检查真空管路', '更换密封件']
  },
  {
    id: 'ALM002',
    equipmentId: 'EQ001',
    equipmentName: '测试机台-ATE01',
    alarmCode: 'W2005',
    alarmText: '测试头温度偏高',
    severity: 'MAJOR',
    category: 'ELECTRICAL',
    timestamp: '2024-01-15T15:20:00Z',
    acknowledgedBy: 'operator01',
    acknowledgedAt: '2024-01-15T15:25:00Z',
    isActive: true,
    description: '测试头温度超过正常范围，可能影响测试精度',
    recommendations: ['检查冷却系统', '清洁散热器', '检查风扇运行']
  },
  {
    id: 'ALM003',
    equipmentId: 'EQ002',
    equipmentName: '键合机-BD01',
    alarmCode: 'W3010',
    alarmText: '键合线张力异常',
    severity: 'MINOR',
    category: 'PROCESS',
    timestamp: '2024-01-15T14:10:00Z',
    acknowledgedBy: 'operator02',
    acknowledgedAt: '2024-01-15T14:15:00Z',
    clearedAt: '2024-01-15T14:30:00Z',
    isActive: false,
    description: '键合线张力超出规格范围',
    recommendations: ['调整键合参数', '检查线轴', '校准传感器']
  }
]

// 设备事件日志模拟数据
export const mockEvents: EquipmentEvent[] = [
  {
    id: 'EVT001',
    equipmentId: 'EQ001',
    eventType: 'STATUS_CHANGE',
    message: '设备状态从IDLE变更为RUN',
    data: { previousStatus: 'IDLE', currentStatus: 'RUN' },
    timestamp: '2024-01-15T08:00:00Z',
    operator: 'operator01',
    severity: 'INFO'
  },
  {
    id: 'EVT002',
    equipmentId: 'EQ003',
    eventType: 'ALARM',
    message: '产生告警：真空泵压力不足',
    data: { alarmCode: 'E1001', severity: 'CRITICAL' },
    timestamp: '2024-01-15T16:45:00Z',
    severity: 'ERROR'
  },
  {
    id: 'EVT003',
    equipmentId: 'EQ002',
    eventType: 'RECIPE_CHANGE',
    message: 'Recipe变更为Recipe_BD-001_v2.1',
    data: { previousRecipe: 'Recipe_BD-001_v2.0', currentRecipe: 'Recipe_BD-001_v2.1' },
    timestamp: '2024-01-15T13:30:00Z',
    operator: 'engineer01',
    severity: 'INFO'
  }
]

// Recipe模拟数据
export const mockRecipes: Recipe[] = [
  {
    id: 'RCP001',
    name: 'ATE标准测试Recipe',
    version: 'v1.2',
    equipmentId: 'EQ001',
    equipmentType: EquipmentType.ATE,
    description: 'IC芯片标准电气测试参数',
    parameters: [
      {
        name: 'VDD_VOLTAGE',
        value: 3.3,
        unit: 'V',
        min: 3.0,
        max: 3.6,
        description: '供电电压',
        category: 'POWER'
      },
      {
        name: 'TEST_FREQUENCY',
        value: 100,
        unit: 'MHz',
        min: 10,
        max: 1000,
        description: '测试频率',
        category: 'TIMING'
      },
      {
        name: 'CURRENT_LIMIT',
        value: 100,
        unit: 'mA',
        min: 1,
        max: 500,
        description: '电流限制',
        category: 'PROTECTION'
      }
    ],
    isActive: true,
    createdBy: 'engineer01',
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-15T09:00:00Z',
    approvedBy: 'manager01',
    approvedAt: '2024-01-15T09:30:00Z'
  },
  {
    id: 'RCP002',
    name: '键合标准Recipe',
    version: 'v2.1',
    equipmentId: 'EQ002',
    equipmentType: EquipmentType.BONDER,
    description: '金线键合标准工艺参数',
    parameters: [
      {
        name: 'BOND_FORCE',
        value: 80,
        unit: 'mN',
        min: 50,
        max: 120,
        description: '键合力',
        category: 'BONDING'
      },
      {
        name: 'BOND_TIME',
        value: 15,
        unit: 'ms',
        min: 10,
        max: 30,
        description: '键合时间',
        category: 'BONDING'
      },
      {
        name: 'WIRE_TEMPERATURE',
        value: 150,
        unit: '°C',
        min: 100,
        max: 200,
        description: '线材温度',
        category: 'THERMAL'
      }
    ],
    isActive: true,
    createdBy: 'engineer02',
    createdAt: '2024-01-12T14:00:00Z',
    updatedAt: '2024-01-15T13:30:00Z',
    approvedBy: 'manager01',
    approvedAt: '2024-01-15T14:00:00Z'
  }
]

// 设备常量模拟数据
export const mockEquipmentConstants: EquipmentConstant[] = [
  {
    id: 'EC001',
    name: 'MAX_TEMPERATURE',
    value: 85,
    unit: '°C',
    min: 50,
    max: 100,
    description: '设备最高工作温度',
    equipmentId: 'EQ001',
    isReadOnly: false
  },
  {
    id: 'EC002',
    name: 'MIN_PRESSURE',
    value: 0.8,
    unit: 'bar',
    min: 0.5,
    max: 2.0,
    description: '最小工作压力',
    equipmentId: 'EQ001',
    isReadOnly: false
  },
  {
    id: 'EC003',
    name: 'DEVICE_ID',
    value: 'ATE-001-2024',
    description: '设备唯一标识符',
    equipmentId: 'EQ001',
    isReadOnly: true
  }
]

// 维护计划模拟数据
export const mockMaintenancePlans: MaintenancePlan[] = [
  {
    id: 'MP001',
    equipmentId: 'EQ001',
    equipmentName: '测试机台-ATE01',
    type: 'PREVENTIVE',
    title: '测试头定期保养',
    description: '对测试头进行清洁、校准和更换易损件',
    frequency: '0 0 1 * *', // 每月1号
    estimatedDuration: 240, // 4小时
    priority: 'MEDIUM',
    isActive: true,
    nextDue: '2024-02-01T08:00:00Z',
    lastCompleted: '2024-01-01T10:00:00Z',
    createdBy: 'maintenance01',
    createdAt: '2023-12-01T10:00:00Z',
    checklist: [
      {
        id: 'CI001',
        name: '清洁测试头',
        description: '使用专用清洁剂清洁测试头接触面',
        isCompleted: false,
        checkType: 'VISUAL'
      },
      {
        id: 'CI002',
        name: '校准测试参数',
        description: '校准电压、电流测试精度',
        isCompleted: false,
        checkType: 'TEST',
        expectedValue: '±0.1%',
        unit: 'accuracy'
      }
    ],
    requiredParts: [
      {
        partNumber: 'TP-CLEAN-001',
        partName: '测试头清洁剂',
        quantity: 2,
        unit: '瓶',
        isAvailable: true,
        stockQuantity: 10,
        minStock: 3
      }
    ]
  }
]

// 维护任务模拟数据
export const mockMaintenanceTasks: MaintenanceTask[] = [
  {
    id: 'MT001',
    planId: 'MP001',
    equipmentId: 'EQ001',
    equipmentName: '测试机台-ATE01',
    type: 'PREVENTIVE',
    title: '测试头定期保养',
    description: '对测试头进行清洁、校准和更换易损件',
    scheduledDate: '2024-01-20T08:00:00Z',
    status: 'SCHEDULED',
    priority: 'MEDIUM',
    assignedTo: ['maintenance01', 'technician01'],
    estimatedDuration: 240,
    checklist: [
      {
        id: 'CI001',
        name: '清洁测试头',
        description: '使用专用清洁剂清洁测试头接触面',
        isCompleted: false,
        checkType: 'VISUAL'
      }
    ],
    requiredParts: [
      {
        partNumber: 'TP-CLEAN-001',
        partName: '测试头清洁剂',
        quantity: 2,
        unit: '瓶',
        isAvailable: true,
        stockQuantity: 10,
        minStock: 3
      }
    ],
    usedParts: [],
    createdAt: '2024-01-15T10:00:00Z'
  }
]

// SECS消息模拟数据
export const mockSecsMessages: SecsMessage[] = [
  {
    id: 'MSG001',
    stream: 1,
    function: 1,
    direction: 'H→E',
    timestamp: '2024-01-15T10:00:00Z',
    data: { deviceId: 'ATE-001' },
    status: 'SENT',
    description: 'Are You There (S1F1) - 检查设备连接状态'
  },
  {
    id: 'MSG002',
    stream: 1,
    function: 2,
    direction: 'E→H',
    timestamp: '2024-01-15T10:00:01Z',
    data: { deviceId: 'ATE-001', status: 'ONLINE' },
    status: 'RECEIVED',
    description: 'Online Data (S1F2) - 设备在线响应'
  },
  {
    id: 'MSG003',
    stream: 2,
    function: 31,
    direction: 'H→E',
    timestamp: '2024-01-15T10:05:00Z',
    data: { recipeId: 'RCP001', parameters: {} },
    status: 'SENT',
    description: 'Date and Time Request (S2F31) - 时间同步请求'
  }
]

// 统计数据模拟
export const mockEquipmentStatistics: EquipmentStatistics = {
  totalEquipment: mockEquipmentList.length,
  runningEquipment: mockEquipmentList.filter(eq => eq.status === EquipmentStatus.RUN).length,
  idleEquipment: mockEquipmentList.filter(eq => eq.status === EquipmentStatus.IDLE).length,
  downEquipment: mockEquipmentList.filter(eq => eq.status === EquipmentStatus.DOWN).length,
  pmEquipment: mockEquipmentList.filter(eq => eq.status === EquipmentStatus.PM).length,
  alarmEquipment: mockAlarms.filter(alarm => alarm.isActive).length,
  overallOEE: 82.5,
  avgAvailability: 88.2,
  avgPerformance: 92.1,
  avgQuality: 96.8
}

export const mockMaintenanceStatistics: MaintenanceStatistics = {
  totalTasks: 24,
  completedTasks: 18,
  overdueTasks: 2,
  scheduledTasks: 4,
  avgCompletionTime: 180, // 分钟
  totalCost: 45000, // RMB
  monthlyTrend: [
    { month: '2023-07', completed: 12, overdue: 1, cost: 20000 },
    { month: '2023-08', completed: 15, overdue: 0, cost: 22000 },
    { month: '2023-09', completed: 18, overdue: 2, cost: 25000 },
    { month: '2023-10', completed: 16, overdue: 1, cost: 23000 },
    { month: '2023-11', completed: 20, overdue: 0, cost: 28000 },
    { month: '2023-12', completed: 18, overdue: 2, cost: 45000 }
  ]
}

// 设备性能数据模拟
export const mockPerformanceData: EquipmentPerformance[] = mockEquipmentList.map((eq, index) => ({
  equipmentId: eq.id,
  date: '2024-01-15',
  uptime: 20 + Math.random() * 4, // 小时
  downtime: Math.random() * 2, // 小时
  mtbf: 120 + Math.random() * 80, // 小时
  mttr: 0.5 + Math.random() * 2, // 小时
  availability: 85 + Math.random() * 10,
  performance: 90 + Math.random() * 8,
  quality: 95 + Math.random() * 4,
  oee: 75 + Math.random() * 15,
  throughput: 1000 + Math.random() * 500, // 件/小时
  yieldRate: 95 + Math.random() * 4, // %
  cycleTime: 10 + Math.random() * 5, // 秒
  utilizationRate: 80 + Math.random() * 15 // %
}))
